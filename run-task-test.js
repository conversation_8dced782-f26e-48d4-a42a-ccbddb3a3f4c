#!/usr/bin/env node

/**
 * 运行 ImageLibraryTaskService 测试的脚本
 */

import { spawn } from 'child_process'
import path from 'path'
import { fileURLToPath } from 'url'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

console.log('🚀 开始运行 ImageLibraryTaskService 测试...')
console.log('📁 测试目录: C:\\Users\\<USER>\\Pictures\\测试图片2')
console.log('⚠️  请确保测试目录存在且包含图片文件')
console.log('⚠️  请确保 .env 文件中配置了正确的 AI 服务地址')
console.log('')

// 运行特定的测试文件
const testProcess = spawn('npx', [
  'vitest', 
  'run', 
  'test/services/image-library-task-service.test.ts',
  '--reporter=verbose'
], {
  cwd: __dirname,
  stdio: 'inherit',
  shell: true
})

testProcess.on('close', (code) => {
  if (code === 0) {
    console.log('\n✅ 测试完成！')
  } else {
    console.log(`\n❌ 测试失败，退出码: ${code}`)
  }
  process.exit(code)
})

testProcess.on('error', (error) => {
  console.error('❌ 启动测试失败:', error)
  process.exit(1)
})