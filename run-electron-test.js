#!/usr/bin/env node

import { spawn } from 'child_process'
import fs from 'fs'
import path from 'path'
import { fileURLToPath } from 'url'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

console.log('🚀 使用 Electron 环境运行 ImageLibraryTaskService 测试')
console.log(`📁 当前目录: ${process.cwd()}`)

// 设置环境变量
process.env.NODE_ENV = 'test'

console.log('\n🔍 检查文件是否存在...')
const testFile = 'test/services/task-service-simple.test.ts'
if (fs.existsSync(testFile)) {
  console.log(`✅ 测试文件存在: ${testFile}`)
} else {
  console.log(`❌ 测试文件不存在: ${testFile}`)
  process.exit(1)
}

// 创建临时的 Electron 测试脚本（使用 CommonJS 语法）
const electronTestScript = `
const { app } = require('electron');
const { spawn } = require('child_process');

// 禁用 GPU 加速，避免在无头环境中出现问题
app.disableHardwareAcceleration();

app.whenReady().then(() => {
  console.log('🔧 Electron 环境已准备就绪');
  console.log('📊 Node.js 版本:', process.version);
  console.log('🔧 Electron 版本:', process.versions.electron);
  console.log('📦 模块版本:', process.versions.modules);
  
  // 在 Electron 环境中运行 vitest
  const testProcess = spawn('npx', [
    'vitest', 
    'run', 
    '${testFile}',
    '--reporter=verbose',
    '--no-coverage'
  ], {
    stdio: 'inherit',
    shell: true,
    cwd: process.cwd(),
    env: { ...process.env, NODE_ENV: 'test' }
  });
  
  testProcess.on('close', (code) => {
    console.log('\\n📊 测试完成，退出码:', code);
    app.quit();
    process.exit(code);
  });
  
  testProcess.on('error', (error) => {
    console.error('❌ 测试进程错误:', error.message);
    app.quit();
    process.exit(1);
  });
});

app.on('window-all-closed', () => {
  app.quit();
});

// 处理未捕获的异常
process.on('uncaughtException', (error) => {
  console.error('❌ 未捕获的异常:', error);
  app.quit();
  process.exit(1);
});
`;

// 将脚本写入临时文件（使用 .cjs 扩展名）
const tempScriptPath = 'temp-electron-test.cjs';
fs.writeFileSync(tempScriptPath, electronTestScript);

console.log('\n🏃 使用 Electron 启动测试...')
console.log('💡 这将使用 Electron 的 Node.js 环境，与 better-sqlite3 编译环境匹配')

const testProcess = spawn('npx', [
  'electron',
  '--no-sandbox',
  '--disable-dev-shm-usage',
  tempScriptPath
], {
  stdio: 'inherit',
  shell: true,
  cwd: process.cwd()
})

testProcess.on('close', (code) => {
  // 清理临时文件
  try {
    fs.unlinkSync(tempScriptPath);
  } catch (e) {
    // 忽略清理错误
  }
  
  if (code === 0) {
    console.log('\n✅ Electron 测试运行完成')
  } else {
    console.log(`\n❌ Electron 测试失败，退出码: ${code}`)
  }
  process.exit(code)
})

testProcess.on('error', (error) => {
  // 清理临时文件
  try {
    fs.unlinkSync(tempScriptPath);
  } catch (e) {
    // 忽略清理错误
  }
  
  console.error('❌ 启动 Electron 测试进程失败:', error.message)
  console.log('\n🔧 故障排除建议:')
  console.log('   1. 确保已安装 Electron: npm install electron')
  console.log('   2. 重新编译 better-sqlite3: npm run rebuild')
  console.log('   3. 检查 Node.js 和 Electron 版本兼容性')
  process.exit(1)
})