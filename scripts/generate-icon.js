import sharp from 'sharp';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

async function generateIcon() {
  const svgPath = path.join(__dirname, '../public/logo.svg');
  const buildDir = path.join(__dirname, '../build');
  
  // Ensure build directory exists
  if (!fs.existsSync(buildDir)) {
    fs.mkdirSync(buildDir, { recursive: true });
  }
  
  // Read SVG file
  const svgBuffer = fs.readFileSync(svgPath);
  
  // Generate different sizes for different platforms
  const sizes = [
    { size: 16, name: 'icon_16x16.png' },
    { size: 32, name: 'icon_32x32.png' },
    { size: 64, name: 'icon_64x64.png' },
    { size: 128, name: 'icon_128x128.png' },
    { size: 256, name: 'icon_256x256.png' },
    { size: 512, name: 'icon_512x512.png' },
    { size: 1024, name: 'icon.png' } // Main icon
  ];
  
  for (const { size, name } of sizes) {
    await sharp(svgBuffer)
      .resize(size, size)
      .png()
      .toFile(path.join(buildDir, name));
    console.log(`Generated ${name}`);
  }
  
  console.log('All icons generated successfully!');
}

generateIcon().catch(console.error);