#!/bin/bash
# 启动服务脚本 (Linux/Mac)
# 用于快速启动 Milvus 数据库服务

echo "=== PicMind AI 图片管理系统 - 服务启动脚本 ==="
echo ""

# 检查Docker是否安装
if ! command -v docker &> /dev/null; then
    echo "✗ Docker未安装，请先安装Docker"
    echo "下载地址: https://www.docker.com/products/docker-desktop"
    exit 1
fi

echo "✓ Docker已安装: $(docker --version)"

# 检查Docker是否运行
if ! docker ps &> /dev/null; then
    echo "✗ Docker服务未运行，请启动Docker"
    exit 1
fi

echo "✓ Docker服务正在运行"
echo ""
echo "正在准备Milvus数据库..."

# 创建docker-compose.yml文件
cat > docker-compose.yml << 'EOF'
version: '3.5'

services:
  etcd:
    container_name: milvus-etcd
    image: quay.io/coreos/etcd:v3.5.5
    environment:
      - ETCD_AUTO_COMPACTION_MODE=revision
      - ETCD_AUTO_COMPACTION_RETENTION=1000
      - ETCD_QUOTA_BACKEND_BYTES=**********
      - ETCD_SNAPSHOT_COUNT=50000
    volumes:
      - etcd:/etcd
    command: etcd -advertise-client-urls=http://127.0.0.1:2379 -listen-client-urls http://0.0.0.0:2379 --data-dir /etcd
    healthcheck:
      test: ["CMD", "etcdctl", "endpoint", "health"]
      interval: 30s
      timeout: 20s
      retries: 3

  minio:
    container_name: milvus-minio
    image: minio/minio:RELEASE.2023-03-20T20-16-18Z
    environment:
      MINIO_ACCESS_KEY: minioadmin
      MINIO_SECRET_KEY: minioadmin
    ports:
      - "9001:9001"
      - "9000:9000"
    volumes:
      - minio:/minio_data
    command: minio server /minio_data --console-address ":9001"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
      interval: 30s
      timeout: 20s
      retries: 3

  standalone:
    container_name: milvus-standalone
    image: milvusdb/milvus:v2.4.13
    command: ["milvus", "run", "standalone"]
    security_opt:
    - seccomp:unconfined
    environment:
      ETCD_ENDPOINTS: etcd:2379
      MINIO_ADDRESS: minio:9000
    volumes:
      - milvus:/var/lib/milvus
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9091/healthz"]
      interval: 30s
      start_period: 90s
      timeout: 20s
      retries: 3
    ports:
      - "19530:19530"
      - "9091:9091"
    depends_on:
      - "etcd"
      - "minio"

volumes:
  etcd:
    driver: local
  minio:
    driver: local
  milvus:
    driver: local
EOF

echo "✓ 已创建 docker-compose.yml 配置文件"

# 启动Milvus服务
echo "正在启动Milvus服务..."
echo "这可能需要几分钟时间来下载镜像..."

if docker-compose up -d; then
    echo "✓ Milvus服务启动命令已执行"
else
    echo "✗ 启动失败"
    exit 1
fi

# 等待服务启动
echo ""
echo "等待服务启动..."
max_attempts=30
attempt=0

while [ $attempt -lt $max_attempts ]; do
    attempt=$((attempt + 1))
    echo "检查服务状态... ($attempt/$max_attempts)"
    
    if nc -z localhost 19530 2>/dev/null; then
        echo "✓ Milvus服务已启动成功！"
        break
    fi
    
    if [ $attempt -eq $max_attempts ]; then
        echo "✗ 服务启动超时，请检查Docker日志"
        echo "使用以下命令查看日志:"
        echo "docker-compose logs milvus-standalone"
        break
    fi
    
    sleep 3
done

# 显示服务状态
echo ""
echo "=== 服务状态 ==="
docker-compose ps

echo ""
echo "=== 下一步操作 ==="
echo "1. 配置API密钥（在 electron/main.ts 中）"
echo "2. 启动应用: yarn dev"
echo "3. 访问'系统测试'页面验证服务"
echo ""
echo "如需停止服务，运行: docker-compose down" 