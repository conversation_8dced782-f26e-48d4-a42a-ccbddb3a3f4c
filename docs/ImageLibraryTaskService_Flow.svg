<svg width="800" height="1000" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: Arial, sans-serif; font-size: 18px; font-weight: bold; fill: #2c3e50; }
      .box { fill: #3498db; stroke: #2980b9; stroke-width: 2; rx: 8; }
      .decision { fill: #e74c3c; stroke: #c0392b; stroke-width: 2; }
      .process { fill: #2ecc71; stroke: #27ae60; stroke-width: 2; rx: 8; }
      .text { font-family: Arial, sans-serif; font-size: 12px; fill: white; text-anchor: middle; }
      .text-dark { font-family: Arial, sans-serif; font-size: 12px; fill: #2c3e50; text-anchor: middle; }
      .arrow { stroke: #34495e; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
      .label { font-family: Arial, sans-serif; font-size: 10px; fill: #7f8c8d; }
    </style>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#34495e" />
    </marker>
  </defs>
  
  <!-- 标题 -->
  <text x="400" y="30" class="title">ImageLibraryTaskService 图片分析任务流程</text>
  
  <!-- 开始 -->
  <ellipse cx="400" cy="70" rx="60" ry="25" class="box"/>
  <text x="400" y="76" class="text">开始扫描库</text>
  
  <!-- 扫描库任务 -->
  <rect x="320" y="120" width="160" height="40" class="box"/>
  <text x="400" y="145" class="text">addScanLibraryTask()</text>
  
  <!-- 处理扫描任务 -->
  <rect x="300" y="190" width="200" height="40" class="box"/>
  <text x="400" y="215" class="text">processScanLibraryTask()</text>
  
  <!-- 发现文件 -->
  <rect x="320" y="260" width="160" height="40" class="process"/>
  <text x="400" y="285" class="text">discoverFiles()</text>
  
  <!-- 遍历文件 -->
  <rect x="320" y="330" width="160" height="40" class="process"/>
  <text x="400" y="355" class="text">遍历每个图片文件</text>
  
  <!-- 处理图片文件 -->
  <rect x="300" y="400" width="200" height="40" class="process"/>
  <text x="400" y="425" class="text">processImageFile()</text>
  
  <!-- 创建图片记录 -->
  <rect x="320" y="470" width="160" height="40" class="process"/>
  <text x="400" y="495" class="text">创建 ImageRecord</text>
  
  <!-- 判断是否需要分析 -->
  <polygon points="350,540 450,540 470,570 450,600 350,600 330,570" class="decision"/>
  <text x="400" y="575" class="text">shouldAddAnalysisTask()?</text>
  
  <!-- AI服务检查 -->
  <polygon points="150,540 250,540 270,570 250,600 150,600 130,570" class="decision"/>
  <text x="200" y="575" class="text">AI服务可用?</text>
  
  <!-- 添加分析任务 -->
  <rect x="300" y="650" width="200" height="40" class="process"/>
  <text x="400" y="675" class="text">addAnalyzeImageTask()</text>
  
  <!-- 创建分析任务 -->
  <rect x="320" y="720" width="160" height="40" class="process"/>
  <text x="400" y="745" class="text">创建 AnalyzeImageTask</text>
  
  <!-- 加入任务队列 -->
  <rect x="300" y="790" width="200" height="40" class="process"/>
  <text x="400" y="815" class="text">addTaskToQueue()</text>
  
  <!-- 保存到数据库 -->
  <rect x="550" y="470" width="160" height="40" class="process"/>
  <text x="630" y="495" class="text">imageDAO.create()</text>
  
  <!-- 批量分析入口 -->
  <rect x="50" y="120" width="200" height="40" class="box"/>
  <text x="150" y="145" class="text">addBatchAnalyzeLibraryTasks()</text>
  
  <!-- 获取所有图片 -->
  <rect x="70" y="190" width="160" height="40" class="process"/>
  <text x="150" y="215" class="text">获取库中所有图片</text>
  
  <!-- 遍历图片 -->
  <rect x="70" y="260" width="160" height="40" class="process"/>
  <text x="150" y="285" class="text">遍历每张图片</text>
  
  <!-- 任务处理器 -->
  <rect x="320" y="870" width="160" height="40" class="box"/>
  <text x="400" y="895" class="text">任务处理器处理</text>
  
  <!-- 分析完成 -->
  <ellipse cx="400" cy="940" rx="60" ry="25" class="process"/>
  <text x="400" y="946" class="text">分析完成</text>
  
  <!-- 连接线 -->
  <line x1="400" y1="95" x2="400" y2="120" class="arrow"/>
  <line x1="400" y1="160" x2="400" y2="190" class="arrow"/>
  <line x1="400" y1="230" x2="400" y2="260" class="arrow"/>
  <line x1="400" y1="300" x2="400" y2="330" class="arrow"/>
  <line x1="400" y1="370" x2="400" y2="400" class="arrow"/>
  <line x1="400" y1="440" x2="400" y2="470" class="arrow"/>
  <line x1="400" y1="510" x2="400" y2="540" class="arrow"/>
  <line x1="330" y1="570" x2="270" y2="570" class="arrow"/>
  <line x1="400" y1="600" x2="400" y2="650" class="arrow"/>
  <line x1="400" y1="690" x2="400" y2="720" class="arrow"/>
  <line x1="400" y1="760" x2="400" y2="790" class="arrow"/>
  <line x1="400" y1="830" x2="400" y2="870" class="arrow"/>
  <line x1="400" y1="910" x2="400" y2="915" class="arrow"/>
  
  <!-- 保存数据库连接 -->
  <line x1="480" y1="490" x2="550" y2="490" class="arrow"/>
  
  <!-- 批量分析流程 -->
  <line x1="150" y1="160" x2="150" y2="190" class="arrow"/>
  <line x1="150" y1="230" x2="150" y2="260" class="arrow"/>
  <line x1="150" y1="300" x2="150" y2="540" class="arrow"/>
  <line x1="200" y1="600" x2="200" y2="670" class="arrow"/>
  <line x1="300" y1="675" x2="200" y2="675" class="arrow"/>
  
  <!-- 标签 -->
  <text x="280" y="585" class="label">是</text>
  <text x="410" y="625" class="label">是</text>
  <text x="120" y="625" class="label">否</text>
  <text x="520" y="485" class="label">保存记录</text>
  <text x="250" y="670" class="label">批量分析路径</text>
  
  <!-- 说明框 -->
  <rect x="550" y="650" width="220" height="120" fill="#ecf0f1" stroke="#bdc3c7" stroke-width="1" rx="5"/>
  <text x="560" y="670" class="text-dark" text-anchor="start">关键检查点:</text>
  <text x="560" y="690" class="text-dark" text-anchor="start">1. AI服务是否可用</text>
  <text x="560" y="710" class="text-dark" text-anchor="start">2. 图片是否已有标签/描述</text>
  <text x="560" y="730" class="text-dark" text-anchor="start">3. 是否已有分析任务</text>
  <text x="560" y="750" class="text-dark" text-anchor="start">4. 避免重复分析</text>
</svg>