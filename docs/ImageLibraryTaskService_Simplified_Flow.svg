<svg viewBox="0 0 1000 800" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font: bold 20px sans-serif; fill: #2563eb; }
      .subtitle { font: 16px sans-serif; fill: #64748b; }
      .box { fill: #f8fafc; stroke: #e2e8f0; stroke-width: 2; rx: 8; }
      .entry-box { fill: #dbeafe; stroke: #3b82f6; stroke-width: 3; rx: 8; }
      .process-box { fill: #ecfdf5; stroke: #10b981; stroke-width: 2; rx: 8; }
      .ai-box { fill: #fef3c7; stroke: #f59e0b; stroke-width: 2; rx: 8; }
      .text { font: 14px sans-serif; fill: #1f2937; }
      .small-text { font: 12px sans-serif; fill: #6b7280; }
      .arrow { stroke: #374151; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
      .auto-arrow { stroke: #10b981; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
      .decision { fill: #fef2f2; stroke: #ef4444; stroke-width: 2; }
    </style>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#374151" />
    </marker>
  </defs>

  <!-- 标题 -->
  <text x="500" y="30" text-anchor="middle" class="title">ImageLibraryTaskService 简化版流程图</text>
  <text x="500" y="55" text-anchor="middle" class="subtitle">单一入口 + 自动AI分析 + 元数据提取</text>

  <!-- 唯一入口 -->
  <rect x="400" y="80" width="200" height="60" class="entry-box"/>
  <text x="500" y="105" text-anchor="middle" class="text">scanLibrary()</text>
  <text x="500" y="125" text-anchor="middle" class="small-text">唯一公共入口</text>

  <!-- 配置选项 -->
  <rect x="50" y="80" width="300" height="120" class="box"/>
  <text x="200" y="105" text-anchor="middle" class="text">配置选项</text>
  <text x="70" y="125" class="small-text">• recursive: 递归扫描</text>
  <text x="70" y="145" class="small-text">• enableAIAnalysis: 启用AI分析</text>
  <text x="70" y="165" class="small-text">• analysisTypes: 分析类型</text>
  <text x="70" y="185" class="small-text">• forceRescan: 强制重扫</text>

  <!-- 扫描库任务 -->
  <rect x="400" y="180" width="200" height="60" class="process-box"/>
  <text x="500" y="205" text-anchor="middle" class="text">SCAN_LIBRARY 任务</text>
  <text x="500" y="225" text-anchor="middle" class="small-text">优先级: 5 (高)</text>

  <!-- 文件发现 -->
  <rect x="400" y="280" width="200" height="60" class="process-box"/>
  <text x="500" y="305" text-anchor="middle" class="text">发现图片文件</text>
  <text x="500" y="325" text-anchor="middle" class="small-text">递归扫描目录</text>

  <!-- 处理每个文件 -->
  <rect x="150" y="380" width="180" height="80" class="process-box"/>
  <text x="240" y="405" text-anchor="middle" class="text">处理图片文件</text>
  <text x="240" y="425" text-anchor="middle" class="small-text">• 计算哈希</text>
  <text x="240" y="445" text-anchor="middle" class="small-text">• 创建记录</text>

  <!-- 智能检查 -->
  <rect x="400" y="380" width="200" height="80" class="decision"/>
  <text x="500" y="405" text-anchor="middle" class="text">智能检查</text>
  <text x="500" y="425" text-anchor="middle" class="small-text">是否需要AI分析？</text>
  <text x="500" y="445" text-anchor="middle" class="small-text">避免重复分析</text>

  <!-- AI分析任务 -->
  <rect x="670" y="380" width="180" height="80" class="ai-box"/>
  <text x="760" y="405" text-anchor="middle" class="text">ANALYZE_IMAGE</text>
  <text x="760" y="425" text-anchor="middle" class="small-text">优先级: 7 (中)</text>
  <text x="760" y="445" text-anchor="middle" class="small-text">自动添加</text>

  <!-- AI分析详细步骤 -->
  <rect x="670" y="500" width="180" height="120" class="ai-box"/>
  <text x="760" y="525" text-anchor="middle" class="text">AI分析执行</text>
  <text x="690" y="545" class="small-text">1. 提取元数据</text>
  <text x="690" y="565" class="small-text">2. AI标签生成</text>
  <text x="690" y="585" class="small-text">3. 描述生成</text>
  <text x="690" y="605" class="small-text">4. 更新记录</text>

  <!-- 任务队列管理 -->
  <rect x="50" y="500" width="280" height="120" class="box"/>
  <text x="190" y="525" text-anchor="middle" class="text">任务队列管理</text>
  <text x="70" y="545" class="small-text">• 优先级调度</text>
  <text x="70" y="565" class="small-text">• 最多3个并发任务</text>
  <text x="70" y="585" class="small-text">• 失败重试机制</text>
  <text x="70" y="605" class="small-text">• 实时进度跟踪</text>

  <!-- 完成状态 -->
  <rect x="400" y="660" width="200" height="60" class="process-box"/>
  <text x="500" y="685" text-anchor="middle" class="text">扫描完成</text>
  <text x="500" y="705" text-anchor="middle" class="small-text">所有图片已处理</text>

  <!-- 箭头连接 -->
  <line x1="350" y1="110" x2="400" y2="110" class="arrow"/>
  <line x1="500" y1="140" x2="500" y2="180" class="arrow"/>
  <line x1="500" y1="240" x2="500" y2="280" class="arrow"/>
  <line x1="500" y1="340" x2="500" y2="380" class="arrow"/>
  
  <!-- 分支到处理文件 -->
  <line x1="450" y1="420" x2="330" y2="420" class="arrow"/>
  
  <!-- 智能检查到AI分析 -->
  <line x1="600" y1="420" x2="670" y2="420" class="auto-arrow"/>
  <text x="635" y="415" class="small-text">是</text>
  
  <!-- AI分析任务到执行 -->
  <line x1="760" y1="460" x2="760" y2="500" class="auto-arrow"/>
  
  <!-- 队列管理连接 -->
  <line x1="190" y1="500" x2="190" y2="460" class="arrow"/>
  <line x1="190" y1="460" x2="400" y2="420" class="arrow"/>
  
  <!-- 完成连接 -->
  <line x1="500" y1="460" x2="500" y2="660" class="arrow"/>

  <!-- 特性标注 -->
  <rect x="50" y="650" width="280" height="100" class="box"/>
  <text x="190" y="675" text-anchor="middle" class="text">核心特性</text>
  <text x="70" y="695" class="small-text">✓ 单一入口，简化API</text>
  <text x="70" y="715" class="small-text">✓ 自动AI分析，无需手动触发</text>
  <text x="70" y="735" class="small-text">✓ 智能去重，避免重复处理</text>

  <!-- 任务类型说明 -->
  <rect x="670" y="650" width="280" height="100" class="box"/>
  <text x="810" y="675" text-anchor="middle" class="text">任务类型</text>
  <text x="690" y="695" class="small-text">SCAN_LIBRARY: 扫描发现文件</text>
  <text x="690" y="715" class="small-text">ANALYZE_IMAGE: AI分析+元数据</text>
  <text x="690" y="735" class="small-text">移除: 缩略图、重复检测</text>

</svg>