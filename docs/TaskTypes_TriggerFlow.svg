<svg width="1200" height="1400" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: Arial, sans-serif; font-size: 20px; font-weight: bold; fill: #2c3e50; }
      .subtitle { font-family: Arial, sans-serif; font-size: 14px; font-weight: bold; fill: #34495e; }
      .scan-task { fill: #3498db; stroke: #2980b9; stroke-width: 2; rx: 8; }
      .analyze-task { fill: #e74c3c; stroke: #c0392b; stroke-width: 2; rx: 8; }
      .thumbnail-task { fill: #f39c12; stroke: #e67e22; stroke-width: 2; rx: 8; }
      .metadata-task { fill: #9b59b6; stroke: #8e44ad; stroke-width: 2; rx: 8; }
      .duplicate-task { fill: #1abc9c; stroke: #16a085; stroke-width: 2; rx: 8; }
      .trigger { fill: #95a5a6; stroke: #7f8c8d; stroke-width: 2; rx: 5; }
      .text { font-family: Arial, sans-serif; font-size: 11px; fill: white; text-anchor: middle; }
      .text-small { font-family: Arial, sans-serif; font-size: 9px; fill: white; text-anchor: middle; }
      .text-dark { font-family: Arial, sans-serif; font-size: 10px; fill: #2c3e50; text-anchor: middle; }
      .arrow { stroke: #34495e; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
      .priority { font-family: Arial, sans-serif; font-size: 8px; fill: #e74c3c; font-weight: bold; }
    </style>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#34495e" />
    </marker>
  </defs>
  
  <!-- 标题 -->
  <text x="600" y="30" class="title">ImageLibraryTaskService 任务类型与触发时机</text>
  
  <!-- 任务优先级说明 -->
  <rect x="50" y="50" width="300" height="80" fill="#ecf0f1" stroke="#bdc3c7" stroke-width="1" rx="5"/>
  <text x="200" y="70" class="subtitle">任务优先级 (数字越小优先级越高)</text>
  <text x="60" y="90" class="text-dark" text-anchor="start">扫描库: 5 | 重复检测: 6 | 图片分析: 7</text>
  <text x="60" y="110" class="text-dark" text-anchor="start">缩略图生成: 8 | 元数据提取: 6</text>
  
  <!-- 1. SCAN_LIBRARY 任务 -->
  <text x="100" y="170" class="subtitle">1. SCAN_LIBRARY (扫描库)</text>
  <rect x="50" y="180" width="200" height="40" class="scan-task"/>
  <text x="150" y="205" class="text">addScanLibraryTask()</text>
  <text x="260" y="190" class="priority">优先级: 5</text>
  
  <!-- 扫描库触发时机 -->
  <rect x="50" y="240" width="150" height="30" class="trigger"/>
  <text x="125" y="260" class="text-small">用户创建新库</text>
  
  <rect x="220" y="240" width="150" height="30" class="trigger"/>
  <text x="295" y="260" class="text-small">手动触发扫描</text>
  
  <rect x="390" y="240" width="150" height="30" class="trigger"/>
  <text x="465" y="260" class="text-small">定时扫描任务</text>
  
  <!-- 扫描库功能 -->
  <rect x="50" y="290" width="500" height="60" fill="#ecf0f1" stroke="#bdc3c7" stroke-width="1" rx="5"/>
  <text x="60" y="310" class="text-dark" text-anchor="start">功能: 递归扫描目录，发现图片文件，创建ImageRecord</text>
  <text x="60" y="325" class="text-dark" text-anchor="start">配置: recursive, includeHidden, maxDepth, supportedFormats, forceRescan</text>
  <text x="60" y="340" class="text-dark" text-anchor="start">自动触发: 扫描过程中为每个新图片自动添加分析任务</text>
  
  <!-- 2. ANALYZE_IMAGE 任务 -->
  <text x="100" y="390" class="subtitle">2. ANALYZE_IMAGE (图片分析)</text>
  <rect x="50" y="400" width="200" height="40" class="analyze-task"/>
  <text x="150" y="425" class="text">addAnalyzeImageTask()</text>
  <text x="260" y="410" class="priority">优先级: 7</text>
  
  <!-- 分析任务触发时机 -->
  <rect x="50" y="460" width="120" height="30" class="trigger"/>
  <text x="110" y="480" class="text-small">扫描库时自动</text>
  
  <rect x="180" y="460" width="120" height="30" class="trigger"/>
  <text x="240" y="480" class="text-small">批量分析</text>
  
  <rect x="310" y="460" width="120" height="30" class="trigger"/>
  <text x="370" y="480" class="text-small">单张图片分析</text>
  
  <rect x="440" y="460" width="120" height="30" class="trigger"/>
  <text x="500" y="480" class="text-small">用户手动触发</text>
  
  <!-- 分析任务功能 -->
  <rect x="50" y="510" width="500" height="60" fill="#ecf0f1" stroke="#bdc3c7" stroke-width="1" rx="5"/>
  <text x="60" y="530" class="text-dark" text-anchor="start">功能: 使用AI服务分析图片内容，生成标签和描述</text>
  <text x="60" y="545" class="text-dark" text-anchor="start">分析类型: tags, description, objects, faces</text>
  <text x="60" y="560" class="text-dark" text-anchor="start">智能检查: 避免重复分析已有标签/描述的图片</text>
  
  <!-- 3. GENERATE_THUMBNAIL 任务 -->
  <text x="100" y="610" class="subtitle">3. GENERATE_THUMBNAIL (生成缩略图)</text>
  <rect x="50" y="620" width="200" height="40" class="thumbnail-task"/>
  <text x="150" y="645" class="text">addGenerateThumbnailTask()</text>
  <text x="260" y="630" class="priority">优先级: 8</text>
  
  <!-- 缩略图任务触发时机 -->
  <rect x="50" y="680" width="150" height="30" class="trigger"/>
  <text x="125" y="700" class="text-small">图片首次访问</text>
  
  <rect x="220" y="680" width="150" height="30" class="trigger"/>
  <text x="295" y="700" class="text-small">批量生成缩略图</text>
  
  <rect x="390" y="680" width="150" height="30" class="trigger"/>
  <text x="465" y="700" class="text-small">用户手动触发</text>
  
  <!-- 缩略图功能 -->
  <rect x="50" y="730" width="500" height="60" fill="#ecf0f1" stroke="#bdc3c7" stroke-width="1" rx="5"/>
  <text x="60" y="750" class="text-dark" text-anchor="start">功能: 为图片生成不同尺寸的缩略图</text>
  <text x="60" y="765" class="text-dark" text-anchor="start">默认尺寸: [150, 300, 600] 像素</text>
  <text x="60" y="780" class="text-dark" text-anchor="start">用途: 提高图片浏览性能，减少加载时间</text>
  
  <!-- 4. EXTRACT_METADATA 任务 -->
  <text x="100" y="830" class="subtitle">4. EXTRACT_METADATA (提取元数据)</text>
  <rect x="50" y="840" width="200" height="40" class="metadata-task"/>
  <text x="150" y="865" class="text">addExtractMetadataTask()</text>
  <text x="260" y="850" class="priority">优先级: 6</text>
  
  <!-- 元数据任务触发时机 -->
  <rect x="50" y="900" width="150" height="30" class="trigger"/>
  <text x="125" y="920" class="text-small">图片导入时</text>
  
  <rect x="220" y="900" width="150" height="30" class="trigger"/>
  <text x="295" y="920" class="text-small">批量元数据提取</text>
  
  <rect x="390" y="900" width="150" height="30" class="trigger"/>
  <text x="465" y="920" class="text-small">用户手动触发</text>
  
  <!-- 元数据功能 -->
  <rect x="50" y="950" width="500" height="60" fill="#ecf0f1" stroke="#bdc3c7" stroke-width="1" rx="5"/>
  <text x="60" y="970" class="text-dark" text-anchor="start">功能: 提取EXIF数据、拍摄信息、地理位置等</text>
  <text x="60" y="985" class="text-dark" text-anchor="start">信息: 相机型号、拍摄时间、GPS坐标、图片尺寸等</text>
  <text x="60" y="1000" class="text-dark" text-anchor="start">用途: 图片搜索、分类、地理位置展示</text>
  
  <!-- 5. DETECT_DUPLICATES 任务 -->
  <text x="100" y="1050" class="subtitle">5. DETECT_DUPLICATES (检测重复)</text>
  <rect x="50" y="1060" width="200" height="40" class="duplicate-task"/>
  <text x="150" y="1085" class="text">addDetectDuplicatesTask()</text>
  <text x="260" y="1070" class="priority">优先级: 6</text>
  
  <!-- 重复检测触发时机 -->
  <rect x="50" y="1120" width="150" height="30" class="trigger"/>
  <text x="125" y="1140" class="text-small">扫描完成后</text>
  
  <rect x="220" y="1120" width="150" height="30" class="trigger"/>
  <text x="295" y="1140" class="text-small">定期清理任务</text>
  
  <rect x="390" y="1120" width="150" height="30" class="trigger"/>
  <text x="465" y="1140" class="text-small">用户手动触发</text>
  
  <!-- 重复检测功能 -->
  <rect x="50" y="1170" width="500" height="60" fill="#ecf0f1" stroke="#bdc3c7" stroke-width="1" rx="5"/>
  <text x="60" y="1190" class="text-dark" text-anchor="start">功能: 基于文件哈希或图片相似度检测重复图片</text>
  <text x="60" y="1205" class="text-dark" text-anchor="start">阈值: 默认0.95相似度，可配置</text>
  <text x="60" y="1220" class="text-dark" text-anchor="start">用途: 节省存储空间，清理重复文件</text>
  
  <!-- 右侧任务流程图 -->
  <text x="800" y="170" class="subtitle">任务执行流程</text>
  
  <!-- 任务队列 -->
  <rect x="700" y="180" width="200" height="40" fill="#34495e" stroke="#2c3e50" stroke-width="2" rx="8"/>
  <text x="800" y="205" class="text">任务队列 (按优先级排序)</text>
  
  <!-- 并发处理 -->
  <rect x="700" y="240" width="200" height="40" fill="#27ae60" stroke: #229954" stroke-width="2" rx="8"/>
  <text x="800" y="265" class="text">并发处理 (最多3个任务)</text>
  
  <!-- 任务处理器 -->
  <rect x="700" y="300" width="200" height="40" fill="#8e44ad" stroke="#7d3c98" stroke-width="2" rx="8"/>
  <text x="800" y="325" class="text">任务处理器分发</text>
  
  <!-- 重试机制 -->
  <rect x="700" y="360" width="200" height="40" fill="#e67e22" stroke="#d35400" stroke-width="2" rx="8"/>
  <text x="800" y="385" class="text">失败重试 (最多3次)</text>
  
  <!-- 状态更新 -->
  <rect x="700" y="420" width="200" height="40" fill="#16a085" stroke="#138d75" stroke-width="2" rx="8"/>
  <text x="800" y="445" class="text">状态更新与通知</text>
  
  <!-- 连接线 -->
  <line x1="150" y1="220" x2="150" y2="240" class="arrow"/>
  <line x1="295" y1="220" x2="295" y2="240" class="arrow"/>
  <line x1="465" y1="220" x2="465" y2="240" class="arrow"/>
  
  <line x1="150" y1="440" x2="150" y2="460" class="arrow"/>
  <line x1="240" y1="440" x2="240" y2="460" class="arrow"/>
  <line x1="370" y1="440" x2="370" y2="460" class="arrow"/>
  <line x1="500" y1="440" x2="500" y2="460" class="arrow"/>
  
  <line x1="150" y1="660" x2="150" y2="680" class="arrow"/>
  <line x1="295" y1="660" x2="295" y2="680" class="arrow"/>
  <line x1="465" y1="660" x2="465" y2="680" class="arrow"/>
  
  <line x1="150" y1="880" x2="150" y2="900" class="arrow"/>
  <line x1="295" y1="880" x2="295" y2="900" class="arrow"/>
  <line x1="465" y1="880" x2="465" y2="900" class="arrow"/>
  
  <line x1="150" y1="1100" x2="150" y2="1120" class="arrow"/>
  <line x1="295" y1="1100" x2="295" y2="1120" class="arrow"/>
  <line x1="465" y1="1100" x2="465" y2="1120" class="arrow"/>
  
  <!-- 右侧流程连接 -->
  <line x1="800" y1="220" x2="800" y2="240" class="arrow"/>
  <line x1="800" y1="280" x2="800" y2="300" class="arrow"/>
  <line x1="800" y1="340" x2="800" y2="360" class="arrow"/>
  <line x1="800" y1="400" x2="800" y2="420" class="arrow"/>
  
  <!-- 任务关系说明 -->
  <rect x="600" y="500" width="350" height="120" fill="#ecf0f1" stroke="#bdc3c7" stroke-width="1" rx="5"/>
  <text x="610" y="520" class="subtitle">任务间关系</text>
  <text x="610" y="540" class="text-dark" text-anchor="start">• 扫描库任务会自动触发图片分析任务</text>
  <text x="610" y="555" class="text-dark" text-anchor="start">• 图片分析任务优先级低于扫描任务</text>
  <text x="610" y="570" class="text-dark" text-anchor="start">• 缩略图生成可以与其他任务并行</text>
  <text x="610" y="585" class="text-dark" text-anchor="start">• 重复检测通常在扫描完成后执行</text>
  <text x="610" y="600" class="text-dark" text-anchor="start">• 元数据提取可以独立执行</text>
  
  <!-- 性能优化说明 -->
  <rect x="600" y="640" width="350" height="100" fill="#ecf0f1" stroke="#bdc3c7" stroke-width="1" rx="5"/>
  <text x="610" y="660" class="subtitle">性能优化</text>
  <text x="610" y="680" class="text-dark" text-anchor="start">• 最多3个任务并发执行</text>
  <text x="610" y="695" class="text-dark" text-anchor="start">• 智能去重避免重复分析</text>
  <text x="610" y="710" class="text-dark" text-anchor="start">• 任务优先级确保重要任务先执行</text>
  <text x="610" y="725" class="text-dark" text-anchor="start">• 失败重试机制提高成功率</text>
</svg>