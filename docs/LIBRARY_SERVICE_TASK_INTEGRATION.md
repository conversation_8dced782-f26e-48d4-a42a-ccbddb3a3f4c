# LibraryService 与 ImageLibraryTaskService 集成文档

## 概述

本文档描述了如何将 `LibraryService` 与 `ImageLibraryTaskService` 集成，实现基于任务队列的图片库管理系统。这种架构遵循 Controller-Service-DAO 模式，并引入了任务驱动的后台处理机制。

## 架构设计

### 核心组件

1. **LibraryService** - 图片库业务逻辑服务
2. **ImageLibraryTaskService** - 基于队列的任务处理服务
3. **LibraryDAO** - 数据访问层
4. **ImageDAO** - 图片数据访问层

### 集成方式

```typescript
// 依赖注入方式
const libraryService = new LibraryService(libraryDAO, taskService)
```

## 主要功能

### 1. 任务驱动的扫描

传统的同步扫描被替换为异步任务队列：

```typescript
// 启动扫描任务
const result = await libraryService.startLibraryScanTask(libraryId, {
  recursive: true,
  includeHidden: false,
  maxDepth: 10,
  priority: 5
})

// 监控进度
const status = await libraryService.getScanTaskStatus(result.taskId)
```

### 2. 支持的任务类型

- **SCAN_LIBRARY** - 扫描图片库
- **ANALYZE_IMAGE** - 分析图片内容
- **GENERATE_THUMBNAIL** - 生成缩略图
- **EXTRACT_METADATA** - 提取元数据
- **DETECT_DUPLICATES** - 检测重复图片

### 3. 任务管理

```typescript
// 获取图片库的所有任务
const tasks = await libraryService.getLibraryTasks(libraryId)

// 取消任务
await libraryService.cancelScanTask(taskId)
```

## API 变更

### 新增方法

#### LibraryService

```typescript
// 任务驱动的扫描操作
async startLibraryScanTask(libraryId: string, options?: ScanOptions): Promise<TaskResult>
async getScanTaskStatus(taskId: string): Promise<TaskStatus | null>
async cancelScanTask(taskId: string): Promise<OperationResult>
async getLibraryTasks(libraryId: string): Promise<TaskInfo[]>

// 其他任务操作
async startImageAnalysisTask(libraryId: string, imageId: string, imagePath: string, analysisTypes?: string[], priority?: number): Promise<TaskResult>
async startThumbnailGenerationTask(libraryId: string, imageId: string, imagePath: string, sizes?: number[], priority?: number): Promise<TaskResult>
async startDuplicateDetectionTask(libraryId: string, threshold?: number, priority?: number): Promise<TaskResult>
```

#### ImageLibraryTaskService

```typescript
// 任务添加方法
async addScanLibraryTask(libraryId: string, rootPath: string, options?: ScanOptions, priority?: number): Promise<string>
async addAnalyzeImageTask(libraryId: string, imagePath: string, imageId: string, analysisTypes?: string[], priority?: number): Promise<string>
async addGenerateThumbnailTask(libraryId: string, imagePath: string, imageId: string, sizes?: number[], priority?: number): Promise<string>
async addExtractMetadataTask(libraryId: string, imagePath: string, imageId: string, priority?: number): Promise<string>
async addDetectDuplicatesTask(libraryId: string, threshold?: number, priority?: number): Promise<string>

// 任务查询和管理
getTaskStatus(taskId: string): TaskStatusInfo | null
getLibraryTasks(libraryId: string): TaskInfo[]
cancelTask(taskId: string): boolean
```

## 使用示例

### 基本使用流程

```typescript
// 1. 初始化服务
const libraryDAO = new SQLiteLibraryDAO()
const imageDAO = new SQLiteImageDAO()
const taskService = new ImageLibraryTaskService(imageDAO, libraryDAO, aiService, logger, progressManager)
const libraryService = new LibraryService(libraryDAO, taskService)

// 2. 创建图片库
const libraryId = await libraryService.createLibrary({
  name: '我的图片库',
  rootPath: '/path/to/images'
})

// 3. 启动扫描任务
const scanResult = await libraryService.startLibraryScanTask(libraryId)

// 4. 监控进度
const status = await libraryService.getScanTaskStatus(scanResult.taskId)
console.log(`进度: ${status.progress.current}/${status.progress.total}`)
```

### 高级功能

```typescript
// 批量处理
const libraries = await libraryService.getActiveLibraries()
for (const library of libraries) {
  await libraryService.startLibraryScanTask(library.id, { priority: 5 })
}

// 任务管理
const tasks = await libraryService.getLibraryTasks(libraryId)
const pendingTasks = tasks.filter(task => task.status === 'pending')

// 启动图片分析
await libraryService.startImageAnalysisTask(
  libraryId, 
  imageId, 
  imagePath, 
  ['tags', 'description', 'objects']
)
```

## 配置选项

### 扫描选项

```typescript
interface ScanOptions {
  recursive?: boolean          // 递归扫描子目录
  includeHidden?: boolean     // 包含隐藏文件
  maxDepth?: number           // 最大扫描深度
  supportedFormats?: string[] // 支持的文件格式
  forceRescan?: boolean       // 强制重新扫描
  priority?: number           // 任务优先级
}
```

### 任务配置

```typescript
// 任务服务配置
const MAX_CONCURRENT_TASKS = 3    // 最大并发任务数
const PROCESSING_INTERVAL = 1000  // 处理间隔（毫秒）
const DEFAULT_MAX_RETRIES = 3     // 默认最大重试次数
```

## 错误处理

### 任务失败处理

- 自动重试机制（最多3次）
- 错误日志记录
- 任务状态跟踪

### 常见错误

1. **路径无效** - 图片库路径不存在或无法访问
2. **权限不足** - 无法读取目录或文件
3. **任务服务不可用** - TaskService 未正确初始化
4. **并发限制** - 超过最大并发任务数

## 性能优化

### 任务优先级

- 数字越小优先级越高
- 扫描任务：优先级 5
- 重复检测：优先级 6
- 图片分析：优先级 7
- 缩略图生成：优先级 8

### 并发控制

- 最大并发任务数：3
- 队列自动排序
- 任务状态实时更新

## 监控和调试

### 任务状态

```typescript
enum TaskStatus {
  PENDING = 'pending',
  PROCESSING = 'processing',
  COMPLETED = 'completed',
  FAILED = 'failed',
  CANCELLED = 'cancelled'
}
```

### 进度信息

```typescript
interface TaskProgress {
  current: number    // 当前进度
  total: number      // 总数
  message?: string   // 进度消息
}
```

### 日志记录

- 任务创建日志
- 处理进度日志
- 错误和警告日志
- 性能统计日志

## 迁移指南

### 从传统扫描迁移

1. 替换直接扫描调用为任务启动
2. 添加进度监控逻辑
3. 处理异步任务结果
4. 更新错误处理机制

### 兼容性

- 保留原有的同步方法
- 新增任务驱动方法
- 向后兼容现有代码

## 最佳实践

1. **任务优先级设置** - 根据业务重要性设置合适的优先级
2. **进度监控** - 定期检查任务状态，提供用户反馈
3. **错误处理** - 妥善处理任务失败情况
4. **资源管理** - 合理控制并发任务数量
5. **日志记录** - 记录关键操作和错误信息

## 未来扩展

- 支持更多任务类型
- 任务依赖关系管理
- 分布式任务处理
- 任务调度策略优化
- 实时进度推送