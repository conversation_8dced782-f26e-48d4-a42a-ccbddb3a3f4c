# ImageLibraryTaskService 简化版设计文档

## 概述

ImageLibraryTaskService 已经重构为一个简化的图片库任务处理系统，专注于核心功能：图片库扫描和AI分析。

## 设计原则

### 1. 单一入口
- **唯一公共方法**: `scanLibrary()` 
- **简化API**: 用户只需要调用一个方法即可完成所有操作
- **自动化流程**: 扫描、分析、元数据提取全自动完成

### 2. 功能整合
- **元数据提取** → 合并到AI分析任务中
- **缩略图生成** → 移除（不需要）
- **重复检测** → 移除（暂不需要）

### 3. 智能化处理
- **自动AI分析**: 扫描时自动为新图片添加分析任务
- **智能去重**: 避免重复分析已有标签/描述的图片
- **可配置选项**: 支持灵活的扫描和分析配置

## 任务类型

### 1. SCAN_LIBRARY (扫描库任务)
- **优先级**: 5 (高优先级)
- **功能**: 扫描指定路径，发现图片文件
- **自动触发**: 为每个新图片自动添加分析任务

### 2. ANALYZE_IMAGE (图片分析任务)
- **优先级**: 7 (中等优先级)
- **功能**: AI分析 + 元数据提取
- **分析类型**: 标签、描述、物体识别、人脸识别
- **元数据**: EXIF、GPS、拍摄信息等

## 核心API

### scanLibrary() - 唯一入口

```typescript
async scanLibrary(
  libraryId: string,
  rootPath: string,
  options: {
    recursive?: boolean                    // 递归扫描子目录 (默认: true)
    includeHidden?: boolean               // 包含隐藏文件 (默认: false)
    maxDepth?: number                     // 最大扫描深度 (默认: 20)
    supportedFormats?: string[]           // 支持的文件格式
    forceRescan?: boolean                 // 强制重新扫描 (默认: false)
    enableAIAnalysis?: boolean            // 启用AI分析 (默认: true)
    analysisTypes?: ('tags' | 'description' | 'objects' | 'faces')[]
  } = {}
): Promise<string>  // 返回任务ID
```

### 使用示例

```typescript
// 基本扫描
const taskId = await taskService.scanLibrary(
  'library-123',
  '/path/to/images'
)

// 高级配置
const taskId = await taskService.scanLibrary(
  'library-123', 
  '/path/to/images',
  {
    recursive: true,
    enableAIAnalysis: true,
    analysisTypes: ['tags', 'description', 'objects'],
    forceRescan: false
  }
)

// 监控进度
const status = taskService.getTaskStatus(taskId)
console.log(status.progress) // { current: 10, total: 100, message: "正在处理..." }
```

## 执行流程

```
用户调用 scanLibrary()
    ↓
创建 SCAN_LIBRARY 任务
    ↓
扫描指定路径，发现图片文件
    ↓
对每个图片文件：
    ├── 计算文件哈希
    ├── 创建 ImageRecord
    ├── 保存到数据库
    └── 自动添加 ANALYZE_IMAGE 任务 (如果启用AI分析)
    ↓
ANALYZE_IMAGE 任务执行：
    ├── 提取元数据 (EXIF、GPS等)
    ├── AI分析 (标签、描述、物体、人脸)
    └── 更新图片记录
    ↓
完成
```

## 智能特性

### 1. 自动去重
`shouldAddAnalysisTask()` 方法检查：
- 图片是否已有标签或描述
- 是否已有待处理的分析任务
- 是否有正在处理的分析任务
- AI服务是否可用

### 2. 任务优先级
- 扫描任务优先级高，确保快速发现文件
- 分析任务优先级较低，避免阻塞扫描
- 支持并发处理，最多3个任务同时执行

### 3. 错误处理
- 失败任务自动重试（最多3次）
- 单个文件失败不影响整体扫描
- 详细的错误信息和进度反馈

## 配置选项详解

### 扫描选项
- `recursive`: 是否递归扫描子目录
- `includeHidden`: 是否包含隐藏文件
- `maxDepth`: 最大扫描深度，防止无限递归
- `supportedFormats`: 支持的图片格式 (jpg, png, webp等)
- `forceRescan`: 是否强制重新扫描已存在的文件

### AI分析选项
- `enableAIAnalysis`: 是否启用AI分析功能
- `analysisTypes`: 分析类型数组
  - `tags`: 生成图片标签
  - `description`: 生成图片描述
  - `objects`: 识别图片中的物体
  - `faces`: 识别图片中的人脸

## 性能优化

### 1. 并发控制
- 最多3个任务并发执行
- 任务按优先级排序
- 避免资源竞争

### 2. 智能跳过
- 跳过已存在的图片（除非强制重扫）
- 跳过已分析的图片
- 减少不必要的处理

### 3. 内存管理
- 流式处理大量文件
- 及时释放任务资源
- 避免内存泄漏

## 监控和状态

### 任务状态
- `PENDING`: 等待执行
- `PROCESSING`: 正在执行
- `COMPLETED`: 执行完成
- `FAILED`: 执行失败
- `CANCELLED`: 已取消

### 进度信息
```typescript
{
  current: number,    // 当前进度
  total: number,      // 总数
  message?: string    // 进度消息
}
```

### 服务状态
```typescript
{
  isProcessing: boolean,      // 是否正在处理
  queueLength: number,        // 队列长度
  activeTasks: number,        // 活跃任务数
  maxConcurrentTasks: number  // 最大并发数
}
```

## 与其他服务的集成

### LibraryService 集成
```typescript
// LibraryService 中调用
async createLibrary(data: CreateLibraryData) {
  const library = await this.libraryDAO.create(data)
  
  // 自动扫描新创建的库
  await this.taskService.scanLibrary(
    library.id,
    library.rootPath,
    { enableAIAnalysis: true }
  )
  
  return library
}
```

### AI服务集成
- 自动检测AI服务可用性
- 根据AI服务状态决定是否添加分析任务
- 支持多种AI分析类型

## 总结

重构后的 ImageLibraryTaskService 具有以下优势：

1. **简化API**: 只有一个公共入口，易于使用
2. **自动化**: 扫描和分析全自动完成
3. **智能化**: 自动去重，避免重复处理
4. **高效**: 并发处理，优先级调度
5. **可配置**: 灵活的扫描和分析选项
6. **可监控**: 详细的进度和状态信息

这个设计满足了"只有一个入口"的需求，同时保持了系统的灵活性和扩展性。