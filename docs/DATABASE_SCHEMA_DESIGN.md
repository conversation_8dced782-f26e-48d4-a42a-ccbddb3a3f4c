# 图片数据库结构设计方案

## 设计目标

1. **高效重复检测**：支持多维度的重复检测（文件路径、MD5、内容相似度）
2. **批量导入优化**：支持大规模文件夹批量导入
3. **文件路径管理**：支持文件移动、重命名等场景
4. **元数据完整性**：记录完整的文件元数据和处理历史

## 核心表结构

### 1. 主图片表 (image_collection)

```typescript
interface OptimizedImageRecord {
  // === 主键和唯一标识 ===
  id: string;                    // 主键，UUID格式
  
  // === 文件标识和路径 ===
  filePath: string;              // 当前文件相对路径
  originalPath: string;          // 原始导入路径（不变）
  filename: string;              // 原始文件名
  
  // === 文件完整性和重复检测 ===
  fileChecksum: string;          // MD5校验和
  fileSize: number;              // 文件大小（字节）
  contentHash: string;           // 内容哈希（用于检测相似图片）
  
  // === 文件元数据 ===
  format: string;                // 图片格式 (jpg, png, etc.)
  dimensions: string;            // 尺寸 "1920x1080"
  colorSpace: string;            // 色彩空间
  
  // === 位置信息 ===
  latitude: number;              // 纬度（从EXIF GPS信息提取）
  longitude: number;             // 经度（从EXIF GPS信息提取）
  altitude: number;              // 海拔高度（米）
  locationAddress: string;       // 地址信息（通过逆地理编码获取）
  locationCity: string;          // 城市
  locationCountry: string;       // 国家
  locationSource: string;        // 位置信息来源 ('exif' | 'manual' | 'geocoding')
  
  // === 拍摄信息 ===
  capturedAt: number;            // 实际拍摄时间（Unix时间戳，从EXIF提取）
  cameraInfo: string;            // 相机信息（JSON字符串）
  shootingParams: string;        // 拍摄参数（JSON字符串，包含光圈、快门、ISO等）
  
  // === 时间戳 ===
  fileCreatedAt: number;         // 文件创建时间（Unix时间戳）
  fileModifiedAt: number;        // 文件修改时间
  importedAt: number;            // 导入到数据库的时间
  lastProcessedAt: number;       // 最后AI处理时间
  
  // === AI分析结果 ===
  description: string;           // AI生成的描述
  tags: string[];               // 原始标签数组
  tags_flat: string[];          // 扁平化标签（用于高效搜索）
  description_vector: number[]; // 描述向量
  
  // === 结构化元数据 ===
  structured_metadata: string;  // JSON字符串，包含详细分析结果
  
  // === 处理状态 ===
  processingStatus: string;      // 'pending' | 'processing' | 'completed' | 'failed'
  processingVersion: string;     // AI模型版本号
  
  // === 扩展字段 ===
  metadata: string;             // 其他元数据（JSON字符串）
}
```

### 2. 文件重复检测索引表 (file_duplicates)

```typescript
interface FileDuplicateRecord {
  id: string;                   // 主键
  checksum: string;             // MD5校验和
  contentHash: string;          // 内容哈希
  fileSize: number;             // 文件大小
  imageIds: string[];           // 关联的图片ID列表
  createdAt: number;            // 创建时间
  updatedAt: number;            // 更新时间
}
```

### 3. 导入历史表 (import_history)

```typescript
interface ImportHistoryRecord {
  id: string;                   // 主键
  batchId: string;              // 批次ID
  folderPath: string;           // 导入的文件夹路径
  totalFiles: number;           // 总文件数
  processedFiles: number;       // 已处理文件数
  skippedFiles: number;         // 跳过文件数
  failedFiles: number;          // 失败文件数
  status: string;               // 'running' | 'completed' | 'failed'
  startedAt: number;            // 开始时间
  completedAt: number;          // 完成时间
  errorLog: string;             // 错误日志（JSON字符串）
}
```

## 索引设计

### 主图片表索引
```sql
-- 文件路径索引（用于快速查找）
CREATE INDEX idx_filepath ON image_collection(filePath);

-- MD5校验和索引（用于重复检测）
CREATE INDEX idx_checksum ON image_collection(fileChecksum);

-- 内容哈希索引（用于相似图片检测）
CREATE INDEX idx_content_hash ON image_collection(contentHash);

-- 文件大小索引（用于快速过滤）
CREATE INDEX idx_file_size ON image_collection(fileSize);

-- 导入时间索引（用于按时间查询）
CREATE INDEX idx_imported_at ON image_collection(importedAt);

-- 拍摄时间索引（用于按拍摄时间查询）
CREATE INDEX idx_captured_at ON image_collection(capturedAt);

-- 位置索引（用于地理位置查询）
CREATE INDEX idx_location ON image_collection(latitude, longitude);
CREATE INDEX idx_location_city ON image_collection(locationCity);
CREATE INDEX idx_location_country ON image_collection(locationCountry);

-- 处理状态索引（用于查找待处理图片）
CREATE INDEX idx_processing_status ON image_collection(processingStatus);

-- 向量索引（用于语义搜索）
CREATE VECTOR INDEX idx_description_vector ON image_collection(description_vector);

-- 标签索引（用于标签搜索）
CREATE INVERTED INDEX idx_tags_flat ON image_collection(tags_flat);
```

### 重复检测索引
```sql
-- 校验和索引
CREATE INDEX idx_dup_checksum ON file_duplicates(checksum);

-- 内容哈希索引
CREATE INDEX idx_dup_content_hash ON file_duplicates(contentHash);

-- 文件大小索引
CREATE INDEX idx_dup_file_size ON file_duplicates(fileSize);
```

## 重复检测策略

### 1. 多层次检测
```typescript
interface DuplicateCheckResult {
  isDuplicate: boolean;
  duplicateType: 'exact' | 'similar' | 'none';
  existingImageId?: string;
  confidence: number;
  checkDetails: {
    checksumMatch: boolean;
    contentHashMatch: boolean;
    sizeMatch: boolean;
    pathMatch: boolean;
  };
}
```

### 2. 检测流程
1. **快速检测**：MD5 + 文件大小
2. **内容检测**：内容哈希比较
3. **路径检测**：文件路径比较
4. **相似度检测**：向量相似度比较（可选）

## 批量导入优化

### 1. 分批处理
```typescript
interface BatchImportConfig {
  batchSize: number;           // 每批处理的文件数量
  concurrency: number;         // 并发处理数量
  skipDuplicates: boolean;     // 是否跳过重复文件
  forceReprocess: boolean;     // 是否强制重新处理
  aiProcessing: boolean;       // 是否进行AI分析
}
```

### 2. 处理流程
1. **文件扫描**：递归扫描文件夹，收集图片文件信息
2. **重复检测**：批量检查文件是否已存在
3. **分批导入**：将新文件分批导入数据库
4. **AI处理**：异步进行AI分析和向量化
5. **状态更新**：更新处理状态和结果

## API接口设计

### 1. 重复检测接口
```typescript
async checkImageExists(
  filePath: string, 
  md5?: string, 
  fileSize?: number
): Promise<DuplicateCheckResult>

async batchCheckImages(
  files: Array<{
    path: string;
    md5?: string;
    size?: number;
  }>
): Promise<DuplicateCheckResult[]>
```

### 2. 批量导入接口
```typescript
async startBatchImport(
  folderPath: string,
  config: BatchImportConfig
): Promise<{ batchId: string; success: boolean }>

async getBatchImportStatus(
  batchId: string
): Promise<ImportHistoryRecord>

async cancelBatchImport(
  batchId: string
): Promise<{ success: boolean }>
```

### 3. 位置和时间查询接口
```typescript
// 按地理位置查询图片
async queryImagesByLocation(params: {
  latitude: number;
  longitude: number;
  radius: number;              // 搜索半径（公里）
  limit?: number;
}): Promise<PathBasedImageRecord[]>

// 按城市/国家查询图片
async queryImagesByPlace(params: {
  city?: string;
  country?: string;
  limit?: number;
}): Promise<PathBasedImageRecord[]>

// 按拍摄时间范围查询图片
async queryImagesByTimeRange(params: {
  startTime: Date;
  endTime: Date;
  limit?: number;
}): Promise<PathBasedImageRecord[]>

// 按拍摄日期查询图片（按年、月、日分组）
async queryImagesByDate(params: {
  year?: number;
  month?: number;
  day?: number;
  groupBy?: 'year' | 'month' | 'day';
  limit?: number;
}): Promise<{
  groups: Array<{
    date: string;
    count: number;
    images: PathBasedImageRecord[];
  }>;
  total: number;
}>

// 获取图片的EXIF元数据
async getImageExifData(
  imageId: string
): Promise<ExifMetadata | null>

// 更新图片位置信息
async updateImageLocation(
  imageId: string,
  location: {
    latitude: number;
    longitude: number;
    address?: string;
    city?: string;
    country?: string;
    source: 'manual' | 'geocoding';
  }
): Promise<{ success: boolean; error?: string }>

// 批量地理编码（将GPS坐标转换为地址）
async batchGeocode(
  imageIds: string[]
): Promise<Array<{
  imageId: string;
  success: boolean;
  location?: {
    address: string;
    city: string;
    country: string;
  };
  error?: string;
}>>
```

## 性能优化策略

### 1. 数据库层面
- 使用适当的索引策略
- 分批插入和更新
- 连接池管理
- 查询优化

### 2. 应用层面
- 异步处理AI分析
- 缓存重复检测结果
- 并发控制
- 内存管理

### 3. 文件系统层面
- 文件路径规范化
- 文件锁管理
- 临时文件清理

## 迁移策略

### 1. 数据迁移
```typescript
interface MigrationPlan {
  version: string;
  description: string;
  steps: Array<{
    name: string;
    sql: string;
    rollback: string;
  }>;
}
```

### 2. 向后兼容
- 保持现有API接口
- 数据格式转换
- 渐进式迁移

## 监控和维护

### 1. 性能监控
- 查询性能统计
- 存储空间监控
- 重复文件统计

### 2. 数据维护
- 定期清理无效记录
- 重建索引
- 数据一致性检查

这个设计方案提供了完整的文件夹批量导入支持，包括高效的重复检测、批量处理优化和完整的元数据管理。