# ImageLibraryTaskService 任务触发机制详解

## 概述

ImageLibraryTaskService 实现了一个基于队列的后台任务处理系统，支持5种不同类型的任务，每种任务都有特定的触发时机和执行逻辑。

## 任务类型与优先级

| 任务类型 | 优先级 | 描述 |
|---------|--------|------|
| SCAN_LIBRARY | 5 | 扫描图片库，发现新文件 |
| EXTRACT_METADATA | 6 | 提取图片元数据 |
| DETECT_DUPLICATES | 6 | 检测重复图片 |
| ANALYZE_IMAGE | 7 | AI分析图片内容 |
| GENERATE_THUMBNAIL | 8 | 生成缩略图 |

*注：数字越小优先级越高*

## 1. SCAN_LIBRARY (扫描库任务)

### 触发时机
- **用户创建新库时**: `LibraryService.createLibrary()` 自动触发
- **手动扫描**: `LibraryService.startLibraryScanTask()` 手动触发
- **定时扫描**: 可通过定时任务触发 (需要额外实现)

### 执行流程
```
addScanLibraryTask() 
  → processScanLibraryTask()
    → discoverFiles() (递归发现文件)
      → processImageFile() (处理每个图片文件)
        → 创建 ImageRecord
        → 自动调用 addAnalyzeImageTask() (如果AI服务可用)
        → 保存到数据库
```

### 配置选项
- `recursive`: 是否递归扫描子目录 (默认: true)
- `includeHidden`: 是否包含隐藏文件 (默认: false)
- `maxDepth`: 最大扫描深度 (默认: 20)
- `supportedFormats`: 支持的文件格式
- `forceRescan`: 是否强制重新扫描已存在的文件

### 自动触发的子任务
- 为每个新发现的图片自动添加 `ANALYZE_IMAGE` 任务

## 2. ANALYZE_IMAGE (图片分析任务)

### 触发时机
- **扫描库期间自动触发**: 每发现一个新图片文件时
- **批量分析**: `addBatchAnalyzeLibraryTasks()` 为整个库的所有图片添加分析任务
- **单张图片分析**: `LibraryService.startImageAnalysisTask()` 手动触发
- **用户手动触发**: 通过UI界面手动分析特定图片

### 智能检查机制
`shouldAddAnalysisTask()` 方法会检查：
- 图片是否已有标签或描述
- 是否已有待处理的分析任务
- 是否有正在处理的分析任务
- AI服务是否可用

### 分析类型
- `tags`: 生成图片标签
- `description`: 生成图片描述
- `objects`: 识别图片中的物体
- `faces`: 识别图片中的人脸

### 执行逻辑
```typescript
private async processAnalyzeImageTask(task: AnalyzeImageTask): Promise<void> {
  // 调用AI服务分析图片
  // 更新图片记录的AI分析结果
  // 更新任务进度
}
```

## 3. GENERATE_THUMBNAIL (生成缩略图任务)

### 触发时机
- **图片首次访问**: 当用户首次查看图片时
- **批量生成**: `LibraryService.startThumbnailGenerationTask()` 批量生成
- **用户手动触发**: 通过UI界面手动生成

### 默认尺寸
- 150px (小缩略图)
- 300px (中等缩略图)  
- 600px (大缩略图)

### 用途
- 提高图片浏览性能
- 减少网络传输时间
- 改善用户体验

## 4. EXTRACT_METADATA (提取元数据任务)

### 触发时机
- **图片导入时**: 在扫描库过程中可以同时触发
- **批量元数据提取**: 为整个库的图片批量提取元数据
- **用户手动触发**: 通过UI界面手动提取

### 提取的元数据
- EXIF数据 (相机信息、拍摄参数)
- 拍摄时间和日期
- GPS地理位置信息
- 图片尺寸和分辨率
- 文件创建/修改时间

### 用途
- 图片搜索和过滤
- 按时间/地点分类
- 地理位置展示
- 相机设备统计

## 5. DETECT_DUPLICATES (检测重复任务)

### 触发时机
- **扫描完成后**: 在库扫描完成后自动触发
- **定期清理**: 可设置定期执行的清理任务
- **用户手动触发**: `LibraryService.startDuplicateDetectionTask()` 手动触发

### 检测方法
- 基于文件哈希值 (MD5)
- 基于图片相似度算法
- 可配置相似度阈值 (默认: 0.95)

### 用途
- 节省存储空间
- 清理重复文件
- 优化库管理

## 任务执行机制

### 并发控制
- 最多同时执行 3 个任务
- 任务按优先级排序执行
- 支持任务取消和重试

### 重试机制
- 失败任务最多重试 3 次
- 重试间隔递增
- 超过重试次数标记为失败

### 状态管理
任务状态包括：
- `PENDING`: 等待执行
- `PROCESSING`: 正在执行
- `COMPLETED`: 执行完成
- `FAILED`: 执行失败
- `CANCELLED`: 已取消

### 进度跟踪
每个任务都有进度信息：
```typescript
progress: {
  current: number    // 当前进度
  total: number      // 总数
  message?: string   // 进度消息
}
```

## 任务间关系

### 依赖关系
1. **扫描库** → **图片分析**: 扫描发现新图片时自动添加分析任务
2. **扫描库** → **重复检测**: 扫描完成后可以触发重复检测
3. **图片分析** 与 **缩略图生成** 可以并行执行

### 优先级设计
- 扫描库优先级最高，确保快速发现文件
- 分析任务优先级较低，避免阻塞扫描
- 缩略图生成优先级最低，可以后台慢慢处理

## 性能优化

### 智能去重
- `shouldAddAnalysisTask()` 避免重复分析
- 检查现有标签和描述
- 检查待处理和正在处理的任务

### 资源管理
- 限制并发任务数量
- 任务优先级调度
- 失败重试机制

### 用户体验
- 实时进度更新
- 任务状态通知
- 支持任务取消

## 使用示例

### 创建新库并自动扫描
```typescript
// 创建库时自动触发扫描
const result = await libraryService.createLibrary({
  name: "我的图片库",
  rootPath: "/path/to/images"
});
// 自动调用 addScanLibraryTask()
```

### 手动触发图片分析
```typescript
// 为单张图片添加分析任务
await libraryService.startImageAnalysisTask(
  libraryId,
  imageId, 
  imagePath,
  ['tags', 'description']
);
```

### 批量分析整个库
```typescript
// 为库中所有图片添加分析任务
const taskIds = await taskService.addBatchAnalyzeLibraryTasks(
  libraryId,
  ['tags', 'description', 'objects']
);
```

这个任务系统设计确保了图片库管理的自动化和高效性，同时提供了灵活的手动控制选项。