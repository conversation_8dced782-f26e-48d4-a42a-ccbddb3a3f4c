# tRPC Integration Guide

本项目已成功集成 electron-trpc，提供了类型安全的 IPC 通信方案。

## 🚀 快速开始

### 1. tRPC 客户端使用

```typescript
import { trpcClient } from '../lib/trpcClient'

// AI 服务测试连接
const testConnection = async () => {
  const result = await trpcClient.ai.testConnection.query()
  console.log('AI 连接状态:', result)
}

// 分析图片
const analyzeImage = async (imagePath: string) => {
  const result = await trpcClient.ai.analyzeImageByPath.mutate(imagePath)
  console.log('图片分析结果:', result)
}

// 查询图片数据
const queryImages = async () => {
  const result = await trpcClient.database.queryImages.query({ limit: 10 })
  console.log('图片数据:', result)
}

// 获取图片库列表  
const getLibraries = async () => {
  const result = await trpcClient.imageLibrary.getLibraries.query()
  console.log('图片库列表:', result)
}
```

### 2. 在 React 组件中使用

```tsx
import { trpcClient } from '../lib/trpcClient'

const MyComponent: React.FC = () => {
  const [libraries, setLibraries] = useState<any[]>([])
  
  const handleGetLibraries = async () => {
    try {
      const result = await trpcClient.imageLibrary.getLibraries.query()
      if (result.success) {
        setLibraries(result.libraries || [])
      }
    } catch (error) {
      console.error('获取图片库失败:', error)
    }
  }

  return (
    <div>
      <button onClick={handleGetLibraries}>获取图片库</button>
      {libraries.map(lib => (
        <div key={lib.id}>{lib.name}</div>
      ))}
    </div>
  )
}
```

## 📋 可用的 API

### AI 服务
- `trpcClient.ai.testConnection.query()` - 测试 AI 连接
- `trpcClient.ai.analyzeImageByPath.mutate(imagePath)` - 分析图片
- `trpcClient.ai.processImageByPath.mutate({ imagePath, filename? })` - 处理图片
- `trpcClient.ai.parseSearchQuery.mutate(query)` - 解析搜索查询

### 数据库服务  
- `trpcClient.database.queryImages.query(params)` - 查询图片
- `trpcClient.database.insertImages.mutate(images)` - 插入图片
- `trpcClient.database.checkImageExists.query({ filePath, md5? })` - 检查图片是否存在
- `trpcClient.database.enhancedHybridSearch.query(params)` - 增强混合搜索

### 图片库服务
- `trpcClient.imageLibrary.getLibraries.query()` - 获取图片库列表
- `trpcClient.imageLibrary.createLibrary.mutate(params)` - 创建图片库
- `trpcClient.imageLibrary.scanLibrary.mutate({ libraryId, options? })` - 扫描图片库

### 文件系统服务
- `trpcClient.fileSystem.selectFolderAndGetImages.query()` - 选择文件夹并获取图片
- `trpcClient.fileSystem.validateImagePath.query(imagePath)` - 验证图片路径

## 🔧 类型安全

tRPC 提供完整的 TypeScript 类型支持：

```typescript
import type { AppRouter } from '../../electron/trpc/router'

// 客户端会自动推断所有输入和输出类型
const result = await trpcClient.ai.analyzeImageByPath.mutate('/path/to/image.jpg')
// result 的类型会被正确推断为 ImageAnalysisResult & { error?: string }
```

## 🎯 测试功能

在开发者模式下，访问"系统测试"页面的"tRPC 测试"选项卡来测试集成功能。

## 🚨 注意事项

1. **服务依赖**: tRPC 调用依赖后端服务的初始化状态
2. **错误处理**: 所有 API 调用都应该包含适当的错误处理
3. **类型检查**: 利用 TypeScript 的类型检查来确保 API 调用的正确性
4. **性能考虑**: 对于大量数据的操作，考虑使用分页或流式处理

## 🔄 与现有 IPC 的关系

tRPC 集成并不替代现有的 IPC 系统，而是提供了一个额外的类型安全选项：

- **现有 IPC**: 继续可用，适合简单的请求/响应
- **tRPC**: 提供类型安全和更好的开发体验，适合复杂的 API 调用

你可以根据具体需求选择使用哪种方式。