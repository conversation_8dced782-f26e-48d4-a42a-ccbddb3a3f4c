
const { app } = require('electron');
const { spawn } = require('child_process');

// 禁用 GPU 加速，避免在无头环境中出现问题
app.disableHardwareAcceleration();

app.whenReady().then(() => {
  console.log('🔧 Electron 环境已准备就绪');
  console.log('📊 Node.js 版本:', process.version);
  console.log('🔧 Electron 版本:', process.versions.electron);
  console.log('📦 模块版本:', process.versions.modules);
  
  // 在 Electron 环境中运行 vitest
  const testProcess = spawn('npx', [
    'vitest', 
    'run', 
    'test/services/task-service-simple.test.ts',
    '--reporter=verbose',
    '--no-coverage'
  ], {
    stdio: 'inherit',
    shell: true,
    cwd: process.cwd(),
    env: { ...process.env, NODE_ENV: 'test' }
  });
  
  testProcess.on('close', (code) => {
    console.log('\n📊 测试完成，退出码:', code);
    app.quit();
    process.exit(code);
  });
  
  testProcess.on('error', (error) => {
    console.error('❌ 测试进程错误:', error.message);
    app.quit();
    process.exit(1);
  });
});

app.on('window-all-closed', () => {
  app.quit();
});

// 处理未捕获的异常
process.on('uncaughtException', (error) => {
  console.error('❌ 未捕获的异常:', error);
  app.quit();
  process.exit(1);
});
