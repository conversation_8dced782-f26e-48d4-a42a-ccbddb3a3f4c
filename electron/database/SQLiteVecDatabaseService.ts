// import Database from 'better-sqlite3';
// import * as sqliteVec from 'sqlite-vec';
// import * as path from 'path';
// import * as fs from 'fs';
// import { PathBasedDatabaseService, FilePathUtils } from './PathBasedDatabaseService';
// import {
//   ImageRecord,
//   PathBasedImageRecord,
//   InsertResult,
//   QueryParams,
//   QueryResult,
//   PathBasedQueryResult,
//   BaseResult,
//   ExistsResult,
//   LibraryConfig,
//   LibraryConfigInsertResult,
//   LibraryConfigQueryParams,
//   LibraryConfigQueryResult,
//   FilePathMapping,
//   PathValidationResult
// } from '../../src/types/database';
//
// // 关键词扩展接口
// interface KeywordExpansion {
//   original: string;
//   similar: Array<{tag: string; tagId?: string; similarityScore: number}>;
//   all: string[];
// }
//
// // 增强查询结果接口
// interface EnhancedQueryResult {
//   query: string;
//   originalKeywords: string[];
//   expandedKeywords: string[];
//   results: ImageRecord[];
//   totalResults: number;
//   error?: string;
// }
// import {AIService} from "../ai/AIService.ts";
//
// /**
//  * SQLite with sqlite-vec extension database service
//  * Replaces MilvusDatabaseService with local SQLite + vector capabilities
//  */
// export class SQLiteVecDatabaseService {
//   private db: Database.Database | null = null;
//   private dbPath: string;
//   private aiService: AIService | null = null;
//
//   // Tag similarity configuration
//   private tagSimilarityConfig = {
//     defaultThreshold: 0.7,
//     categoryThresholds: new Map<string, number>([
//       ['person', 0.8],
//       ['animal', 0.75],
//       ['nature', 0.7],
//       ['activity', 0.75],
//       ['object', 0.7],
//       ['color', 0.9],
//       ['general', 0.7]
//     ])
//   };
//
//   constructor(dbPath: string = 'data/sqlite-vec.db') {
//     this.dbPath = dbPath;
//   }
//
//   /**
//    * Set AI service for embedding generation
//    */
//   setAIService(aiService: AIService) {
//     this.aiService = aiService;
//   }
//
//   // ===== Database Lifecycle Methods =====
//
//   /**
//    * Test database connection and sqlite-vec extension availability
//    */
//   testConnection(): boolean {
//     return true;
//   }
//
//   /**
//    * 连接数据库并加载sqlite-vec扩展（不创建表）
//    */
//   private connectDatabase(): void {
//     if (this.db) return;
//
//     this.connect();
//
//     // Load sqlite-vec extension
//     try {
//       sqliteVec.load(this.db!);
//     } catch (error) {
//       console.warn('Failed to load sqlite-vec with direct method, trying loadable path:', error);
//       try {
//         const loadablePath = sqliteVec.getLoadablePath();
//         this.db!.loadExtension(loadablePath);
//       } catch (error2) {
//         console.error('Failed to load sqlite-vec extension with both methods:', error2);
//         throw new Error(`sqlite-vec extension failed to load: ${error2 instanceof Error ? error2.message : String(error2)}`);
//       }
//     }
//   }
//
//   /**
//    * 初始化数据库表结构（仅在需要时调用）
//    */
//   initializeTables(): void {
//     this.createSchema(this.getDb());
//   }
//
//   /**
//    * Get database singleton
//    */
//   getDb(): Database.Database {
//     if (!this.db) {
//       this.connectDatabase();
//     }
//     return this.db!;
//   }
//
//   /**
//    * Clear all data from database
//    */
//   async clearDatabase(): Promise<boolean> {
//     try {
//       if (!this.db) {
//         return false;
//       }
//
//       // Drop all tables
//       const tables = ['image_vectors', 'tag_vectors', 'images', 'tags'];
//       for (const table of tables) {
//         try {
//           this.db.prepare(`DROP TABLE IF EXISTS ${table}`).run();
//         } catch (error) {
//           console.warn(`Failed to drop table ${table}:`, error);
//         }
//       }
//
//       return true;
//     } catch (error) {
//       console.error('Database clearing failed:', error);
//       return false;
//     }
//   }
//
//   /**
//    * Close database connection
//    */
//   async close(): Promise<void> {
//     if (this.db) {
//       this.db.close();
//       this.db = null;
//     }
//   }
//
//   // ===== Core Database Operations (Stubs) =====
//
//   async insertImages(images: ImageRecord[]): Promise<InsertResult> {
//     try {
//       const db = this.getDb();
//
//       const insertedIds: string[] = [];
//
//       // Prepare statements for batch insertion
//       const insertImageStmt = db.prepare(`
//         INSERT OR REPLACE INTO images (
//           id, file_path, filename, file_checksum, description, tags, tags_flat,
//           structured_metadata, metadata, latitude, longitude, altitude,
//           location_address, location_city, location_country, location_source,
//           captured_at, camera_info, shooting_params, updated_at
//         ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, unixepoch())
//       `);
//
//       const insertVectorStmt = db.prepare(`
//         INSERT OR REPLACE INTO image_vectors (image_id, description_vector)
//         VALUES (?, ?)
//       `);
//
//       // Begin transaction for batch insert
//       const transaction = db.transaction((images: ImageRecord[]) => {
//         for (const image of images) {
//           try {
//             // Convert ImageRecord to database format
//             const metadata = JSON.stringify(image.metadata || {});
//             const tags = JSON.stringify(image.tags || []);
//             const tagsFlat = JSON.stringify(image.tags_flat || image.tags || []);
//             const structuredMetadata = JSON.stringify(image.structured_metadata || {});
//
//             // Extract location data
//             const latitude = image.latitude || null;
//             const longitude = image.longitude || null;
//             const altitude = image.altitude || null;
//             const locationAddress = image.locationAddress || null;
//             const locationCity = image.locationCity || null;
//             const locationCountry = image.locationCountry || null;
//             const locationSource = image.locationSource || null;
//
//             // Extract time data
//             const capturedAt = image.capturedAt ? new Date(image.capturedAt).getTime() / 1000 : null;
//             const cameraInfo = image.cameraInfo || null;
//             const shootingParams = image.shootingParams || null;
//
//             // Extract file checksum from metadata
//             const fileChecksum = image.metadata?.fileChecksum || null;
//             const filename = image.metadata?.filename || '';
//
//             // Insert image record
//             insertImageStmt.run(
//               image.id,
//               image.imagePath, // Use imagePath as file_path for compatibility
//               filename,
//               fileChecksum,
//               image.description,
//               tags,
//               tagsFlat,
//               structuredMetadata,
//               metadata,
//               latitude,
//               longitude,
//               altitude,
//               locationAddress,
//               locationCity,
//               locationCountry,
//               locationSource,
//               capturedAt,
//               cameraInfo,
//               shootingParams
//             );
//
//             // Insert vector if available and has correct dimensions
//             if (image.embedding && image.embedding.length > 0) {
//               // Check if vector has correct dimensions (1024)
//               if (image.embedding.length === 1024) {
//                 // Convert embedding array to Float32Array for sqlite-vec
//                 const vectorData = new Float32Array(image.embedding);
//                 insertVectorStmt.run(image.id, vectorData);
//               } else {
//                 // For testing or compatibility, pad or truncate to 1024 dimensions
//                 const paddedEmbedding = new Array(1024).fill(0);
//                 for (let i = 0; i < Math.min(image.embedding.length, 1024); i++) {
//                   paddedEmbedding[i] = image.embedding[i];
//                 }
//                 const vectorData = new Float32Array(paddedEmbedding);
//                 insertVectorStmt.run(image.id, vectorData);
//               }
//             }
//
//             insertedIds.push(image.id);
//           } catch (error) {
//             console.error(`Failed to insert image ${image.id}:`, error);
//             throw error;
//           }
//         }
//       });
//
//       // Execute transaction
//       transaction(images);
//
//       return {
//         success: true,
//         insertedIds
//       };
//     } catch (error) {
//       console.error('Failed to insert images:', error);
//       return {
//         success: false,
//         error: error instanceof Error ? error.message : 'Unknown error',
//         insertedIds: []
//       };
//     }
//   }
//
//   async queryImages(params: QueryParams): Promise<QueryResult> {
//     try {
//       const db = this.getDb();
//
//       let query = 'SELECT * FROM images';
//       const queryParams: any[] = [];
//       const conditions: string[] = [];
//
//       // Handle tag filtering
//       if (params.tags && params.tags.length > 0) {
//         const tagConditions = params.tags.map(() => 'json_extract(tags_flat, \'$\') LIKE ?');
//         conditions.push(`(${tagConditions.join(' OR ')})`);
//         params.tags.forEach(tag => {
//           queryParams.push(`%"${tag}"%`);
//         });
//       }
//
//       // Handle expression filtering (basic support)
//       if (params.expr) {
//         // Simple expression support - can be enhanced later
//         conditions.push(params.expr);
//       }
//
//       // Add WHERE clause if conditions exist
//       if (conditions.length > 0) {
//         query += ' WHERE ' + conditions.join(' AND ');
//       }
//
//       // Add ordering
//       query += ' ORDER BY created_at DESC';
//
//       // Add limit
//       if (params.limit) {
//         query += ' LIMIT ?';
//         queryParams.push(params.limit);
//       }
//
//       // Execute query
//       const stmt = db.prepare(query);
//       const rows = stmt.all(...queryParams) as any[];
//
//       // Convert database rows to ImageRecord format
//       const results: ImageRecord[] = rows.map(row => this.convertRowToImageRecord(row));
//
//       return {
//         results,
//         total: results.length
//       };
//     } catch (error) {
//       console.error('Failed to query images:', error);
//       return {
//         results: [],
//         total: 0,
//         error: error instanceof Error ? error.message : 'Unknown error'
//       };
//     }
//   }
//
//   async deleteImage(id: string): Promise<BaseResult> {
//     try {
//       const db = this.getDb();
//
//       // Begin transaction to delete from both tables
//       const transaction = db.transaction(() => {
//         // Delete from image_vectors table
//         const deleteVectorStmt = db.prepare('DELETE FROM image_vectors WHERE image_id = ?');
//         deleteVectorStmt.run(id);
//
//         // Delete from images table
//         const deleteImageStmt = db.prepare('DELETE FROM images WHERE id = ?');
//         const result = deleteImageStmt.run(id);
//
//         if (result.changes === 0) {
//           throw new Error(`Image with id ${id} not found`);
//         }
//       });
//
//       transaction();
//
//       return {
//         success: true
//       };
//     } catch (error) {
//       console.error(`Failed to delete image ${id}:`, error);
//       return {
//         success: false,
//         error: error instanceof Error ? error.message : 'Unknown error'
//       };
//     }
//   }
//
//   async queryAllTags(): Promise<{ tags: string[]; error?: string }> {
//     try {
//       const db = this.getDb();
//
//       // Get all unique tags from images
//       const stmt = db.prepare(`
//         SELECT DISTINCT json_each.value as tag
//         FROM images, json_each(images.tags_flat)
//         WHERE json_each.value IS NOT NULL AND json_each.value != ''
//         ORDER BY tag
//       `);
//
//       const rows = stmt.all() as { tag: string }[];
//       const tags = rows.map(row => row.tag);
//
//       return {
//         tags
//       };
//     } catch (error) {
//       console.error('Failed to query all tags:', error);
//       return {
//         tags: [],
//         error: error instanceof Error ? error.message : 'Unknown error'
//       };
//     }
//   }
//
//   async hybridSearch(params: {
//     query: string;
//     keywords: string[];
//     embedding: number[];
//     limit?: number;
//     tags?: string[];
//     location?: {
//       latitude?: number;
//       longitude?: number;
//       radius?: number; // in kilometers
//       city?: string;
//       country?: string;
//     };
//     timeRange?: {
//       startTime?: Date;
//       endTime?: Date;
//     };
//     threshold?: number;
//   }): Promise<QueryResult> {
//     try {
//       const db = this.getDb();
//
//       const limit = params.limit || 10;
//       const threshold = params.threshold || 0.7;
//
//       // Validate and prepare embedding
//       let embedding = params.embedding;
//       if (embedding.length !== 1024) {
//         const paddedEmbedding = new Array(1024).fill(0);
//         for (let i = 0; i < Math.min(embedding.length, 1024); i++) {
//           paddedEmbedding[i] = embedding[i];
//         }
//         embedding = paddedEmbedding;
//       }
//       const queryVector = new Float32Array(embedding);
//
//       // Build the hybrid search query
//       let query = `
//         SELECT
//           i.*,
//           (1 - vec_distance_cosine(iv.description_vector, ?)) as similarity_score
//         FROM images i
//         JOIN image_vectors iv ON i.id = iv.image_id
//         WHERE (1 - vec_distance_cosine(iv.description_vector, ?)) >= ?
//       `;
//       const queryParams: any[] = [queryVector, queryVector, threshold];
//
//       // Add tag filtering
//       if (params.tags && params.tags.length > 0) {
//         const tagConditions = params.tags.map(() => 'json_extract(i.tags_flat, \'$\') LIKE ?');
//         query += ` AND (${tagConditions.join(' OR ')})`;
//         params.tags.forEach(tag => {
//           queryParams.push(`%"${tag}"%`);
//         });
//       }
//
//       // Add keyword filtering (search in description)
//       if (params.keywords && params.keywords.length > 0) {
//         const keywordConditions = params.keywords.map(() => 'i.description LIKE ?');
//         query += ` AND (${keywordConditions.join(' OR ')})`;
//         params.keywords.forEach(keyword => {
//           queryParams.push(`%${keyword}%`);
//         });
//       }
//
//       // Add location-based filtering
//       if (params.location) {
//         if (params.location.latitude && params.location.longitude && params.location.radius) {
//           // Haversine formula for distance calculation (approximate)
//           const lat = params.location.latitude;
//           const lon = params.location.longitude;
//           const radius = params.location.radius;
//
//           query += ` AND (
//             6371 * acos(
//               cos(radians(?)) * cos(radians(i.latitude)) *
//               cos(radians(i.longitude) - radians(?)) +
//               sin(radians(?)) * sin(radians(i.latitude))
//             ) <= ?
//           )`;
//           queryParams.push(lat, lon, lat, radius);
//         }
//
//         if (params.location.city) {
//           query += ` AND i.location_city LIKE ?`;
//           queryParams.push(`%${params.location.city}%`);
//         }
//
//         if (params.location.country) {
//           query += ` AND i.location_country LIKE ?`;
//           queryParams.push(`%${params.location.country}%`);
//         }
//       }
//
//       // Add time-range filtering
//       if (params.timeRange) {
//         if (params.timeRange.startTime) {
//           query += ` AND i.captured_at >= ?`;
//           queryParams.push(Math.floor(params.timeRange.startTime.getTime() / 1000));
//         }
//
//         if (params.timeRange.endTime) {
//           query += ` AND i.captured_at <= ?`;
//           queryParams.push(Math.floor(params.timeRange.endTime.getTime() / 1000));
//         }
//       }
//
//       // Add ordering and limit
//       query += ` ORDER BY similarity_score DESC LIMIT ?`;
//       queryParams.push(limit);
//
//       // Execute the hybrid search query
//       const stmt = db.prepare(query);
//       const rows = stmt.all(...queryParams) as any[];
//
//       // Convert database rows to ImageRecord format
//       const results: ImageRecord[] = rows.map(row => {
//         const imageRecord = this.convertRowToImageRecord(row);
//         // Add similarity score to metadata
//         if (imageRecord.metadata) {
//           imageRecord.metadata.similarityScore = row.similarity_score;
//         } else {
//           // Create minimal metadata with required fields and similarity score
//           imageRecord.metadata = {
//             filename: imageRecord.imagePath.split('/').pop() || 'unknown',
//             filesize: 0,
//             uploadTime: new Date().toISOString(),
//             dimensions: 'unknown',
//             format: 'unknown',
//             similarityScore: row.similarity_score
//           };
//         }
//         return imageRecord;
//       });
//
//       return {
//         results,
//         total: results.length
//       };
//     } catch (error) {
//       console.error('Failed to perform hybrid search:', error);
//       return {
//         results: [],
//         total: 0,
//         error: error instanceof Error ? error.message : 'Unknown error'
//       };
//     }
//   }
//
//   async checkImageExists(filePath: string, md5?: string): Promise<ExistsResult> {
//     try {
//       const db = this.getDb();
//
//       let query = 'SELECT id FROM images WHERE file_path = ?';
//       const params = [filePath];
//
//       // If MD5 is provided, also check checksum
//       if (md5) {
//         query += ' AND file_checksum = ?';
//         params.push(md5);
//       }
//
//       const stmt = db.prepare(query);
//       const result = stmt.get(...params);
//
//       return {
//         success: true,
//         exists: !!result
//       };
//     } catch (error) {
//       console.error('Failed to check image existence:', error);
//       return {
//         success: false,
//         exists: false,
//         error: error instanceof Error ? error.message : 'Unknown error'
//       };
//     }
//   }
//
//   /**
//    * Vector similarity search using a provided embedding vector
//    * @param embedding The query vector (should be 1024 dimensions)
//    * @param limit Maximum number of results to return
//    * @param threshold Minimum similarity threshold (0-1, where 1 is identical)
//    */
//   async vectorSimilaritySearch(embedding: number[], limit: number = 10, threshold: number = 0.7): Promise<QueryResult> {
//     try {
//       const db = this.getDb();
//
//       // Validate embedding dimensions
//       if (embedding.length !== 1024) {
//         // Pad or truncate to 1024 dimensions for compatibility
//         const paddedEmbedding = new Array(1024).fill(0);
//         for (let i = 0; i < Math.min(embedding.length, 1024); i++) {
//           paddedEmbedding[i] = embedding[i];
//         }
//         embedding = paddedEmbedding;
//       }
//
//       // Convert to Float32Array for sqlite-vec
//       const queryVector = new Float32Array(embedding);
//
//       // Perform vector similarity search using sqlite-vec cosine similarity
//       const searchStmt = db.prepare(`
//         SELECT
//           i.*,
//           (1 - vec_distance_cosine(iv.description_vector, ?)) as similarity_score
//         FROM images i
//         JOIN image_vectors iv ON i.id = iv.image_id
//         WHERE (1 - vec_distance_cosine(iv.description_vector, ?)) >= ?
//         ORDER BY similarity_score DESC
//         LIMIT ?
//       `);
//
//       const rows = searchStmt.all(queryVector, queryVector, threshold, limit) as any[];
//
//       // Convert database rows to ImageRecord format
//       const results: ImageRecord[] = rows.map(row => {
//         const imageRecord = this.convertRowToImageRecord(row);
//         // Add similarity score to metadata
//         if (imageRecord.metadata) {
//           imageRecord.metadata.similarityScore = row.similarity_score;
//         } else {
//           // Create minimal metadata with required fields and similarity score
//           imageRecord.metadata = {
//             filename: imageRecord.imagePath.split('/').pop() || 'unknown',
//             filesize: 0,
//             uploadTime: new Date().toISOString(),
//             dimensions: 'unknown',
//             format: 'unknown',
//             similarityScore: row.similarity_score
//           };
//         }
//         return imageRecord;
//       });
//
//       return {
//         results,
//         total: results.length
//       };
//     } catch (error) {
//       console.error('Failed to perform vector similarity search:', error);
//       return {
//         results: [],
//         total: 0,
//         error: error instanceof Error ? error.message : 'Unknown error'
//       };
//     }
//   }
//
//   async searchSimilarImages(imageId: string, options?: { threshold?: number; limit?: number }): Promise<QueryResult> {
//     try {
//       const db = this.getDb();
//
//       const threshold = options?.threshold || 0.8; // Default cosine similarity threshold
//       const limit = options?.limit || 10;
//
//       // First, get the vector for the given image ID
//       const vectorStmt = db.prepare('SELECT description_vector FROM image_vectors WHERE image_id = ?');
//       const vectorRow = vectorStmt.get(imageId) as { description_vector: Float32Array } | undefined;
//
//       if (!vectorRow || !vectorRow.description_vector) {
//         return {
//           results: [],
//           total: 0,
//           error: `No vector found for image ${imageId}`
//         };
//       }
//
//       const queryVector = vectorRow.description_vector;
//
//       // Perform vector similarity search using sqlite-vec
//       const searchStmt = db.prepare(`
//         SELECT
//           i.*,
//           (1 - vec_distance_cosine(iv.description_vector, ?)) as similarity_score
//         FROM images i
//         JOIN image_vectors iv ON i.id = iv.image_id
//         WHERE
//           i.id != ?
//           AND (1 - vec_distance_cosine(iv.description_vector, ?)) >= ?
//         ORDER BY similarity_score DESC
//         LIMIT ?
//       `);
//
//       const rows = searchStmt.all(queryVector, imageId, queryVector, threshold, limit) as any[];
//
//       // Convert database rows to ImageRecord format
//       const results: ImageRecord[] = rows.map(row => {
//         const imageRecord = this.convertRowToImageRecord(row);
//         // Add similarity score to metadata
//         if (imageRecord.metadata) {
//           imageRecord.metadata.similarityScore = row.similarity_score;
//         } else {
//           // Create minimal metadata with required fields and similarity score
//           imageRecord.metadata = {
//             filename: imageRecord.imagePath.split('/').pop() || 'unknown',
//             filesize: 0,
//             uploadTime: new Date().toISOString(),
//             dimensions: 'unknown',
//             format: 'unknown',
//             similarityScore: row.similarity_score
//           };
//         }
//         return imageRecord;
//       });
//
//       return {
//         results,
//         total: results.length
//       };
//     } catch (error) {
//       console.error(`Failed to search similar images for ${imageId}:`, error);
//       return {
//         results: [],
//         total: 0,
//         error: error instanceof Error ? error.message : 'Unknown error'
//       };
//     }
//   }
//
//   // ===== Library Config Operations (Stubs) =====
//   // 移动到SQLiteLibraryService.ts
//
//   // ===== Location-based Operations (Stubs) =====
//
//   async queryImagesByLocation(latitude: number, longitude: number, radius?: number, limit?: number): Promise<any[]> {
//     try {
//       const db = this.getDb();
//
//       const searchRadius = radius || 10; // Default 10km radius
//       const searchLimit = limit || 50;
//
//       // Use Haversine formula for distance calculation
//       const query = `
//         SELECT
//           *,
//           (6371 * acos(
//             cos(radians(?)) * cos(radians(latitude)) *
//             cos(radians(longitude) - radians(?)) +
//             sin(radians(?)) * sin(radians(latitude))
//           )) as distance_km
//         FROM images
//         WHERE
//           latitude IS NOT NULL
//           AND longitude IS NOT NULL
//           AND (6371 * acos(
//             cos(radians(?)) * cos(radians(latitude)) *
//             cos(radians(longitude) - radians(?)) +
//             sin(radians(?)) * sin(radians(latitude))
//           )) <= ?
//         ORDER BY distance_km ASC
//         LIMIT ?
//       `;
//
//       const stmt = db.prepare(query);
//       const rows = stmt.all(
//         latitude, longitude, latitude,
//         latitude, longitude, latitude,
//         searchRadius, searchLimit
//       ) as any[];
//
//       return rows.map(row => this.convertRowToImageRecord(row));
//     } catch (error) {
//       console.error('Failed to query images by location:', error);
//       return [];
//     }
//   }
//
//   async queryImagesByLocationName(location: string, limit?: number): Promise<any[]> {
//     try {
//       const db = this.getDb();
//
//       const searchLimit = limit || 50;
//
//       const query = `
//         SELECT * FROM images
//         WHERE
//           location_address LIKE ? OR
//           location_city LIKE ? OR
//           location_country LIKE ?
//         ORDER BY created_at DESC
//         LIMIT ?
//       `;
//
//       const searchPattern = `%${location}%`;
//       const stmt = db.prepare(query);
//       const rows = stmt.all(searchPattern, searchPattern, searchPattern, searchLimit) as any[];
//
//       return rows.map(row => this.convertRowToImageRecord(row));
//     } catch (error) {
//       console.error('Failed to query images by location name:', error);
//       return [];
//     }
//   }
//
//   async getImageExifData(imageId: string): Promise<any> {
//     // TODO: Implement in later tasks
//     throw new Error('getImageExifData not yet implemented');
//   }
//
//   async updateImageLocation(imageId: string, locationData: {
//     latitude?: number;
//     longitude?: number;
//     address?: string;
//     city?: string;
//     country?: string;
//   }): Promise<boolean> {
//     // TODO: Implement in later tasks
//     throw new Error('updateImageLocation not yet implemented');
//   }
//
//   // ===== Time-based Operations (Stubs) =====
//
//   async queryImagesByTimeRange(startTime: Date, endTime: Date, limit?: number): Promise<any[]> {
//     try {
//       const db = this.getDb();
//
//       const searchLimit = limit || 50;
//       const startTimestamp = Math.floor(startTime.getTime() / 1000);
//       const endTimestamp = Math.floor(endTime.getTime() / 1000);
//
//       const query = `
//         SELECT * FROM images
//         WHERE
//           captured_at IS NOT NULL
//           AND captured_at >= ?
//           AND captured_at <= ?
//         ORDER BY captured_at DESC
//         LIMIT ?
//       `;
//
//       const stmt = db.prepare(query);
//       const rows = stmt.all(startTimestamp, endTimestamp, searchLimit) as any[];
//
//       return rows.map(row => this.convertRowToImageRecord(row));
//     } catch (error) {
//       console.error('Failed to query images by time range:', error);
//       return [];
//     }
//   }
//
//   async queryImagesByDate(date: Date, limit?: number): Promise<any[]> {
//     try {
//       const db = this.getDb();
//
//       const searchLimit = limit || 50;
//
//       // Get start and end of the day
//       const startOfDay = new Date(date);
//       startOfDay.setHours(0, 0, 0, 0);
//       const endOfDay = new Date(date);
//       endOfDay.setHours(23, 59, 59, 999);
//
//       return this.queryImagesByTimeRange(startOfDay, endOfDay, searchLimit);
//     } catch (error) {
//       console.error('Failed to query images by date:', error);
//       return [];
//     }
//   }
//
//   // ===== Advanced Search Operations (Stubs) =====
//
//   /**
//    * 集成相似tag检索的增强混合搜索（优化版）
//    * 优化点：1. 批量标签搜索 2. 标签去重后精准搜索
//    */
//   async enhancedHybridSearch(params: {
//     query: string;
//     limit?: number;
//     expandKeywords?: boolean;
//     similarityThreshold?: number;
//   }): Promise<EnhancedQueryResult> {
//     try {
//       const {
//         query,
//         limit = 20,
//         expandKeywords = true,
//         similarityThreshold = 0.7
//       } = params;
//
//       console.log('开始增强混合搜索（优化版）...', params);
//
//       // 1. 解析查询关键词
//       const aiService = this.getAIService();
//       const parsedQuery = await aiService.parseSearchQuery(query);
//       console.log('解析出的关键词:', parsedQuery.keywords);
//
//       // 2. 批量扩展关键词（如果启用）
//       let keywordExpansions: KeywordExpansion[] = [];
//
//       if (expandKeywords && parsedQuery.keywords.length > 0) {
//         // 使用批量搜索优化：一次性搜索所有关键词
//         const batchSimilarTags = await this.findSimilarTagsBatch(
//           parsedQuery.keywords,
//           {
//             limit: 5,
//             threshold: similarityThreshold
//           },
//           aiService
//         );
//
//         // 构建关键词扩展结果
//         keywordExpansions = parsedQuery.keywords.map((keyword: string) => {
//           const similarTags = batchSimilarTags.get(keyword) || [];
//           return {
//             original: keyword,
//             similar: similarTags,
//             all: [keyword, ...similarTags.map((t: any) => t.tagText)]
//           };
//         });
//
//         console.log('批量扩展完成，关键词数量:', keywordExpansions.length);
//       }
//
//       // 3. 标签去重和映射生成
//       const { uniqueTags } = this.deduplicateExpandedTags(keywordExpansions);
//       console.log(`标签去重完成：原始 ${keywordExpansions.flatMap(exp => exp.all).length} 个 -> 去重后 ${uniqueTags.length} 个`);
//
//       // 4. 基于去重标签的精准搜索（无需生成向量）
//       const rawResults = await this.preciseTagSearch({
//         uniqueTags,
//         originalKeywords: parsedQuery.keywords,
//         limit: limit * 2,
//         keywordExpansions
//       });
//
//       console.log(`精准标签搜索返回 ${rawResults.length} 个结果`);
//
//       // 6. 结果重排序（使用原始关键词扩展进行重排序）
//       const reRankedResults = this.reRankWithTagMatching(
//         rawResults,
//         parsedQuery.keywords,
//         keywordExpansions
//       );
//
//       // 7. 限制返回结果数量
//       const finalResults = reRankedResults.slice(0, limit);
//
//       return {
//         query,
//         originalKeywords: parsedQuery.keywords,
//         expandedKeywords: uniqueTags,
//         results: finalResults,
//         totalResults: finalResults.length
//       };
//     } catch (error) {
//       console.error('增强混合搜索失败:', error);
//       // 回退到关键词搜索
//       const fallbackResult = await this.performKeywordOnlySearch(params.query, params.limit || 20);
//       return {
//         query: params.query,
//         originalKeywords: [],
//         expandedKeywords: [],
//         results: fallbackResult.results,
//         totalResults: fallbackResult.totalResults,
//         error: error instanceof Error ? error.message : '未知错误'
//       };
//     }
//   }
//
//   /**
//    * Get AI service instance
//    */
//   private getAIService(): AIService {
//     return this.aiService!;
//   }
//
//   /**
//    * Extract keywords from query text
//    */
//   private extractKeywords(query: string): string[] {
//     // Simple keyword extraction - split by spaces and filter out common words
//     const stopWords = new Set(['the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'is', 'are', 'was', 'were', 'be', 'been', 'being', 'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could', 'should']);
//
//     return query
//       .toLowerCase()
//       .split(/\s+/)
//       .filter(word => word.length > 2 && !stopWords.has(word))
//       .filter(word => /^[a-zA-Z\u4e00-\u9fff]+$/.test(word)); // Only letters and Chinese characters
//   }
//
//   /**
//    * Expand keywords for better search results
//    */
//   private async expandKeywordsForSearch(keywords: string[]): Promise<string[]> {
//     // For now, just return the original keywords
//     // TODO: Implement keyword expansion using synonyms or related terms
//     return keywords;
//   }
//
//   /**
//    * 执行纯关键词搜索（作为回退方案）
//    */
//   private async performKeywordOnlySearch(query: string, limit: number): Promise<EnhancedQueryResult> {
//     const db = this.getDb();
//
//     try {
//       const keywords = this.extractKeywords(query);
//
//       if (keywords.length === 0) {
//         return {
//           query,
//           originalKeywords: [],
//           expandedKeywords: [],
//           results: [],
//           totalResults: 0
//         };
//       }
//
//       // 构建关键词搜索查询
//       let searchQuery = `
//         SELECT i.*, 0.5 as similarity_score
//         FROM images i
//         WHERE 1=1
//       `;
//       const queryParams: any[] = [];
//
//       // 添加关键词过滤（在描述和标签中搜索）
//       if (keywords.length > 0) {
//         const keywordConditions = keywords.map(() =>
//           '(i.description LIKE ? OR json_extract(i.tags_flat, \'$\') LIKE ?)'
//         );
//         searchQuery += ` AND (${keywordConditions.join(' OR ')})`;
//         keywords.forEach(keyword => {
//           queryParams.push(`%${keyword}%`, `%"${keyword}"%`);
//         });
//       }
//
//       // 添加排序和限制
//       searchQuery += ` ORDER BY i.captured_at DESC LIMIT ?`;
//       queryParams.push(limit);
//
//       // 执行搜索查询
//       if (!db) {
//         throw new Error('数据库未初始化');
//       }
//       const stmt = db.prepare(searchQuery);
//       const rows = stmt.all(...queryParams) as any[];
//
//       // 将数据库行转换为ImageRecord格式
//       const results: ImageRecord[] = rows.map(row => {
//         const imageRecord = this.convertRowToImageRecord(row);
//         // 添加相似度分数到元数据
//         if (imageRecord.metadata) {
//           imageRecord.metadata.similarityScore = row.similarity_score;
//         } else {
//           imageRecord.metadata = {
//             filename: imageRecord.imagePath.split('/').pop() || 'unknown',
//             filesize: 0,
//             uploadTime: new Date().toISOString(),
//             dimensions: 'unknown',
//             format: 'unknown',
//             similarityScore: row.similarity_score
//           };
//         }
//         return imageRecord;
//       });
//
//       return {
//         query,
//         originalKeywords: keywords,
//         expandedKeywords: keywords,
//         results,
//         totalResults: results.length
//       };
//
//     } catch (error) {
//       console.error('关键词搜索失败:', error);
//       return {
//         query,
//         originalKeywords: [],
//         expandedKeywords: [],
//         results: [],
//         totalResults: 0,
//         error: error instanceof Error ? error.message : '未知错误'
//       };
//     }
//   }
//
//   async buildTagEmbeddings(aiService?: any): Promise<boolean> {
//     try {
//       const db = this.getDb();
//
//       if (!aiService) {
//         console.warn('AI service not provided, cannot generate tag embeddings');
//         return false;
//       }
//
//       // Get all unique tags from images
//       const tagsResult = await this.queryAllTags();
//       if (!tagsResult.tags || tagsResult.tags.length === 0) {
//         console.log('No tags found to build embeddings for');
//         return true;
//       }
//
//       console.log(`Building embeddings for ${tagsResult.tags.length} tags...`);
//
//       // Prepare statements for batch operations
//       const insertTagStmt = db.prepare(`
//         INSERT OR REPLACE INTO tags (tag_id, tag_text, frequency, category, updated_at)
//         VALUES (?, ?, ?, ?, unixepoch())
//       `);
//
//       const insertTagVectorStmt = db.prepare(`
//         INSERT OR REPLACE INTO tag_vectors (tag_id, tag_embedding)
//         VALUES (?, ?)
//       `);
//
//       const getTagFrequencyStmt = db.prepare(`
//         SELECT COUNT(*) as frequency
//         FROM images, json_each(images.tags_flat)
//         WHERE json_each.value = ?
//       `);
//
//       // Process tags sequentially (async operations cannot be in transactions)
//       for (const tag of tagsResult.tags) {
//         try {
//           // Generate unique tag ID
//           const tagId = `tag_${Buffer.from(tag).toString('base64').replace(/[^a-zA-Z0-9]/g, '_')}`;
//
//           // Get tag frequency
//           const frequencyResult = getTagFrequencyStmt.get(tag) as { frequency: number };
//           const frequency = frequencyResult?.frequency || 1;
//
//           // Categorize tag (basic categorization)
//           const category = this.categorizeTag(tag);
//
//           // Generate embedding for the tag first (async operation)
//           const embeddingResult = await aiService.generateEmbedding(tag);
//
//           // Now perform database operations in a transaction
//           const transaction = db.transaction(() => {
//             // Insert or update tag record
//             insertTagStmt.run(tagId, tag, frequency, category);
//
//             // Insert vector if embedding was generated successfully
//             if (embeddingResult && embeddingResult.embedding) {
//               // Ensure embedding has correct dimensions (1024)
//               let embedding = embeddingResult.embedding;
//               if (embedding.length !== 1024) {
//                 const paddedEmbedding = new Array(1024).fill(0);
//                 for (let i = 0; i < Math.min(embedding.length, 1024); i++) {
//                   paddedEmbedding[i] = embedding[i];
//                 }
//                 embedding = paddedEmbedding;
//               }
//
//               // Convert to Float32Array for sqlite-vec
//               const vectorData = new Float32Array(embedding);
//               insertTagVectorStmt.run(tagId, vectorData);
//             }
//           });
//
//           // Execute transaction for this tag
//           transaction();
//         } catch (error) {
//           console.error(`Failed to process tag "${tag}":`, error);
//           // Continue with other tags
//         }
//       }
//
//       console.log(`Successfully built embeddings for ${tagsResult.tags.length} tags`);
//       return true;
//     } catch (error) {
//       console.error('Failed to build tag embeddings:', error);
//       return false;
//     }
//   }
//
//   /**
//    * Basic tag categorization logic
//    */
//   private categorizeTag(tag: string): string {
//     const lowerTag = tag.toLowerCase();
//
//     // Object categories
//     if (['person', 'people', 'man', 'woman', 'child', 'baby', 'face'].some(keyword => lowerTag.includes(keyword))) {
//       return 'person';
//     }
//
//     // Animal categories
//     if (['dog', 'cat', 'bird', 'animal', 'pet', 'horse', 'cow'].some(keyword => lowerTag.includes(keyword))) {
//       return 'animal';
//     }
//
//     // Nature categories
//     if (['tree', 'flower', 'plant', 'nature', 'landscape', 'mountain', 'water', 'sky', 'cloud'].some(keyword => lowerTag.includes(keyword))) {
//       return 'nature';
//     }
//
//     // Activity categories
//     if (['sport', 'game', 'play', 'run', 'walk', 'dance', 'music', 'read'].some(keyword => lowerTag.includes(keyword))) {
//       return 'activity';
//     }
//
//     // Object categories
//     if (['car', 'vehicle', 'building', 'house', 'food', 'drink', 'tool', 'furniture'].some(keyword => lowerTag.includes(keyword))) {
//       return 'object';
//     }
//
//     // Color categories
//     if (['red', 'blue', 'green', 'yellow', 'black', 'white', 'color'].some(keyword => lowerTag.includes(keyword))) {
//       return 'color';
//     }
//
//     // Default category
//     return 'general';
//   }
//
//   async findSimilarTags(query: string, options?: {
//     limit?: number;
//     threshold?: number;
//     category?: string;
//   }, aiService?: any): Promise<any[]> {
//     try {
//       const db = this.getDb();
//
//       if (!aiService) {
//         console.warn('AI service not provided, cannot perform semantic tag search');
//         return [];
//       }
//
//       const limit = options?.limit || 10;
//       const threshold = options?.threshold || this.getTagSimilarityThreshold(options?.category);
//       const category = options?.category;
//
//       // Generate embedding for the query
//       const embeddingResult = await aiService.generateEmbedding(query);
//       if (!embeddingResult || !embeddingResult.embedding) {
//         throw new Error('Failed to generate embedding for query');
//       }
//
//       // Ensure embedding has correct dimensions (1024)
//       let queryEmbedding = embeddingResult.embedding;
//       if (queryEmbedding.length !== 1024) {
//         const paddedEmbedding = new Array(1024).fill(0);
//         for (let i = 0; i < Math.min(queryEmbedding.length, 1024); i++) {
//           paddedEmbedding[i] = queryEmbedding[i];
//         }
//         queryEmbedding = paddedEmbedding;
//       }
//
//       // Convert to Float32Array for sqlite-vec
//       const queryVector = new Float32Array(queryEmbedding);
//
//       // Build query with optional category filtering
//       let searchQuery = `
//         SELECT
//           t.tag_id,
//           t.tag_text,
//           t.frequency,
//           t.category,
//           (1 - vec_distance_cosine(tv.tag_embedding, ?)) as similarity_score
//         FROM tags t
//         JOIN tag_vectors tv ON t.tag_id = tv.tag_id
//         WHERE (1 - vec_distance_cosine(tv.tag_embedding, ?)) >= ?
//       `;
//
//       const params: any[] = [queryVector, queryVector, threshold];
//
//       // Add category filtering if specified
//       if (category) {
//         searchQuery += ' AND t.category = ?';
//         params.push(category);
//       }
//
//       searchQuery += ' ORDER BY similarity_score DESC LIMIT ?';
//       params.push(limit);
//
//       // Execute search
//       const searchStmt = db.prepare(searchQuery);
//       const rows = searchStmt.all(...params) as any[];
//
//       // Convert to result format
//       const results = rows.map(row => ({
//         tagId: row.tag_id,
//         tagText: row.tag_text,
//         frequency: row.frequency,
//         category: row.category,
//         similarityScore: row.similarity_score
//       }));
//
//       return results;
//     } catch (error) {
//       console.error('Failed to find similar tags:', error);
//       return [];
//     }
//   }
//
//   async findSimilarTagsBatch(queries: string[], options?: {
//     limit?: number;
//     threshold?: number;
//     category?: string;
//   }, aiService?: any): Promise<Map<string, any[]>> {
//     try {
//       if (!queries || queries.length === 0) {
//         return new Map();
//       }
//
//       const results = new Map<string, any[]>();
//       for (const query of queries) {
//         try {
//           const similarTags = await this.findSimilarTags(query, options, aiService);
//           results.set(query, similarTags);
//         } catch (error) {
//           console.error(`Failed to find similar tags for query "${query}":`, error);
//           results.set(query, []);
//         }
//       }
//
//       return results;
//     } catch (error) {
//       console.error('Failed to perform batch tag similarity search:', error);
//       return new Map();
//     }
//   }
//
//   /**
//    * Expand search query with similar tags for enhanced search results
//    * @param originalTags Original search tags
//    * @param options Expansion options
//    * @param aiService AI service for generating embeddings
//    * @returns Expanded tag list including similar tags
//    */
//   async expandTagsForSearch(originalTags: string[], options?: {
//     maxExpansionPerTag?: number;
//     similarityThreshold?: number;
//     includeOriginal?: boolean;
//   }, aiService?: any): Promise<string[]> {
//     try {
//       const db = this.getDb();
//
//       if (!aiService) {
//         console.warn('AI service not provided, returning original tags');
//         return originalTags;
//       }
//
//       const maxExpansionPerTag = options?.maxExpansionPerTag || 3;
//       const similarityThreshold = options?.similarityThreshold || 0.8;
//       const includeOriginal = options?.includeOriginal !== false;
//
//       const expandedTags = new Set<string>();
//
//       // Add original tags if requested
//       if (includeOriginal) {
//         originalTags.forEach(tag => expandedTags.add(tag));
//       }
//
//       // Find similar tags for each original tag
//       for (const tag of originalTags) {
//         try {
//           const similarTags = await this.findSimilarTags(tag, {
//             limit: maxExpansionPerTag,
//             threshold: similarityThreshold
//           }, aiService);
//
//           // Add similar tags to the expanded set
//           similarTags.forEach(similarTag => {
//             expandedTags.add(similarTag.tagText);
//           });
//         } catch (error) {
//           console.error(`Failed to expand tag "${tag}":`, error);
//           // Continue with other tags
//         }
//       }
//
//       return Array.from(expandedTags);
//     } catch (error) {
//       console.error('Failed to expand tags for search:', error);
//       return originalTags; // Fallback to original tags
//     }
//   }
//
//   /**
//    * Configure tag similarity threshold for a specific category
//    * @param category Tag category
//    * @param threshold Similarity threshold (0-1)
//    */
//   setTagSimilarityThreshold(category: string, threshold: number): void {
//     if (threshold < 0 || threshold > 1) {
//       throw new Error('Threshold must be between 0 and 1');
//     }
//     this.tagSimilarityConfig.categoryThresholds.set(category, threshold);
//   }
//
//   /**
//    * Get tag similarity threshold for a category
//    * @param category Tag category
//    * @returns Similarity threshold
//    */
//   getTagSimilarityThreshold(category?: string): number {
//     if (category && this.tagSimilarityConfig.categoryThresholds.has(category)) {
//       return this.tagSimilarityConfig.categoryThresholds.get(category)!;
//     }
//     return this.tagSimilarityConfig.defaultThreshold;
//   }
//
//   /**
//    * Set default tag similarity threshold
//    * @param threshold Default threshold (0-1)
//    */
//   setDefaultTagSimilarityThreshold(threshold: number): void {
//     if (threshold < 0 || threshold > 1) {
//       throw new Error('Threshold must be between 0 and 1');
//     }
//     this.tagSimilarityConfig.defaultThreshold = threshold;
//   }
//
//   /**
//    * 去重和映射扩展标签
//    * @param keywordExpansions 关键词扩展结果
//    * @returns 去重后的标签和映射关系
//    */
//   deduplicateExpandedTags(keywordExpansions: KeywordExpansion[]): {
//     uniqueTags: string[];
//     tagToOriginalMap: Map<string, string[]>;
//   } {
//     // 创建标签到原始关键词的映射
//     const tagToOriginalMap = new Map<string, string[]>();
//     const uniqueTagsSet = new Set<string>();
//
//     // 处理每个关键词扩展
//     for (const expansion of keywordExpansions) {
//       // 处理所有标签（原始+相似）
//       for (const tag of expansion.all) {
//         // 添加到唯一标签集合
//         uniqueTagsSet.add(tag);
//
//         // 更新映射关系
//         if (!tagToOriginalMap.has(tag)) {
//           tagToOriginalMap.set(tag, []);
//         }
//         // 将原始关键词添加到映射中
//         if (!tagToOriginalMap.get(tag)!.includes(expansion.original)) {
//           tagToOriginalMap.get(tag)!.push(expansion.original);
//         }
//       }
//     }
//
//     return {
//       uniqueTags: Array.from(uniqueTagsSet),
//       tagToOriginalMap
//     };
//   }
//
//   /**
//    * 基于去重标签的精准搜索
//    * @param params 搜索参数
//    * @returns 搜索结果
//    */
//   async preciseTagSearch(params: {
//     uniqueTags: string[];
//     originalKeywords: string[];
//     limit: number;
//     keywordExpansions: KeywordExpansion[];
//   }): Promise<ImageRecord[]> {
//     try {
//       const db = this.getDb();
//
//       const { uniqueTags, originalKeywords, limit } = params;
//
//       if (uniqueTags.length === 0) {
//         return [];
//       }
//
//       // 构建精准标签匹配查询（无需向量计算）
//       let sql = `
//         SELECT
//           i.*,
//           0.8 as similarity_score
//         FROM images i
//         WHERE 1=1
//       `;
//
//       const queryParams: any[] = [];
//
//       // 添加标签过滤条件
//       if (uniqueTags.length > 0) {
//         // 使用JSON_EACH来搜索标签数组中的元素
//         sql += ` AND EXISTS (
//           SELECT 1 FROM json_each(i.tags_flat)
//           WHERE json_each.value IN (${uniqueTags.map(() => '?').join(',')})
//         )`;
//         queryParams.push(...uniqueTags);
//       }
//
//       // 添加排序和限制
//       sql += ` ORDER BY similarity_score DESC LIMIT ?`;
//       queryParams.push(limit);
//
//       // 执行查询
//       const stmt = db.prepare(sql);
//       const rows = stmt.all(...queryParams) as any[];
//
//       // 转换结果
//       return rows.map(row => this.convertRowToImageRecord(row));
//     } catch (error) {
//       console.error('Failed to perform precise tag search:', error);
//       return [];
//     }
//   }
//
//   /**
//    * 使用标签匹配进行结果重排序
//    * @param results 原始搜索结果
//    * @param originalKeywords 原始关键词
//    * @param keywordExpansions 关键词扩展
//    * @returns 重排序后的结果
//    */
//   reRankWithTagMatching(
//     results: ImageRecord[],
//     originalKeywords: string[],
//     keywordExpansions: KeywordExpansion[]
//   ): ImageRecord[] {
//     // 创建关键词到扩展的映射，方便查找
//     const keywordToExpansionMap = new Map<string, KeywordExpansion>();
//     for (const expansion of keywordExpansions) {
//       keywordToExpansionMap.set(expansion.original, expansion);
//     }
//
//     // 计算每个结果的匹配分数
//     const scoredResults = results.map(result => {
//       // 基础分数是向量相似度
//       let score = result.metadata.similarityScore || 0;
//
//       // 标签匹配加分
//       const resultTags = new Set(result.tags.map(tag => tag.toLowerCase()));
//
//       // 原始关键词匹配加分（权重更高）
//       for (const keyword of originalKeywords) {
//         if (resultTags.has(keyword.toLowerCase())) {
//           score += 0.2; // 原始关键词匹配加分
//         }
//       }
//
//       // 扩展关键词匹配加分（权重较低）
//       for (const expansion of keywordExpansions) {
//         for (const similarTag of expansion.similar) {
//           if (resultTags.has(similarTag.tag.toLowerCase())) {
//             score += 0.1 * similarTag.similarityScore; // 根据相似度加权
//           }
//         }
//       }
//
//       return {
//         result,
//         score
//       };
//     });
//
//     // 按分数排序
//     scoredResults.sort((a, b) => b.score - a.score);
//
//     // 返回排序后的结果
//     return scoredResults.map(item => item.result);
//   }
//
//   async updateTagEmbeddings(newTags: string[], aiService?: any): Promise<void> {
//     try {
//       const db = this.getDb();
//
//       if (!aiService) {
//         console.warn('AI service not provided, cannot update tag embeddings');
//         return;
//       }
//
//       if (!newTags || newTags.length === 0) {
//         return;
//       }
//
//       console.log(`Updating embeddings for ${newTags.length} new tags...`);
//
//       // Prepare statements
//       const insertTagStmt = db.prepare(`
//         INSERT OR REPLACE INTO tags (tag_id, tag_text, frequency, category, updated_at)
//         VALUES (?, ?, ?, ?, unixepoch())
//       `);
//
//       const insertTagVectorStmt = db.prepare(`
//         INSERT OR REPLACE INTO tag_vectors (tag_id, tag_embedding)
//         VALUES (?, ?)
//       `);
//
//       const getTagFrequencyStmt = db.prepare(`
//         SELECT COUNT(*) as frequency
//         FROM images, json_each(images.tags_flat)
//         WHERE json_each.value = ?
//       `);
//
//       const checkExistingTagStmt = db.prepare(`
//         SELECT tag_id FROM tags WHERE tag_text = ?
//       `);
//
//       // Process each new tag
//       for (const tag of newTags) {
//         try {
//           // Check if tag already exists
//           const existingTag = checkExistingTagStmt.get(tag);
//           if (existingTag) {
//             continue; // Skip existing tags
//           }
//
//           // Generate unique tag ID
//           const tagId = `tag_${Buffer.from(tag).toString('base64').replace(/[^a-zA-Z0-9]/g, '_')}`;
//
//           // Get tag frequency
//           const frequencyResult = getTagFrequencyStmt.get(tag) as { frequency: number };
//           const frequency = frequencyResult?.frequency || 1;
//
//           // Categorize tag
//           const category = this.categorizeTag(tag);
//
//           // Insert tag record
//           insertTagStmt.run(tagId, tag, frequency, category);
//
//           // Generate and store embedding
//           const embeddingResult = await aiService.generateEmbedding(tag);
//           if (embeddingResult && embeddingResult.embedding) {
//             // Ensure embedding has correct dimensions (1024)
//             let embedding = embeddingResult.embedding;
//             if (embedding.length !== 1024) {
//               const paddedEmbedding = new Array(1024).fill(0);
//               for (let i = 0; i < Math.min(embedding.length, 1024); i++) {
//                 paddedEmbedding[i] = embedding[i];
//               }
//               embedding = paddedEmbedding;
//             }
//
//             // Convert to Float32Array for sqlite-vec
//             const vectorData = new Float32Array(embedding);
//             insertTagVectorStmt.run(tagId, vectorData);
//           }
//         } catch (error) {
//           console.error(`Failed to update embedding for tag "${tag}":`, error);
//           // Continue with other tags
//         }
//       }
//
//       console.log(`Successfully updated embeddings for new tags`);
//     } catch (error) {
//       console.error('Failed to update tag embeddings:', error);
//       throw error;
//     }
//   }
//
//   async queryTagEmbeddings(limit?: number): Promise<{ tags: any[]; error?: string }> {
//     try {
//       const db = this.getDb();
//
//       let query = `
//         SELECT
//           t.tag_id,
//           t.tag_text,
//           t.frequency,
//           t.category,
//           t.created_at,
//           t.updated_at,
//           CASE WHEN tv.tag_embedding IS NOT NULL THEN 1 ELSE 0 END as has_embedding
//         FROM tags t
//         LEFT JOIN tag_vectors tv ON t.tag_id = tv.tag_id
//         ORDER BY t.frequency DESC, t.tag_text ASC
//       `;
//
//       const params: any[] = [];
//       if (limit) {
//         query += ' LIMIT ?';
//         params.push(limit);
//       }
//
//       const stmt = db.prepare(query);
//       const rows = stmt.all(...params) as any[];
//
//       const tags = rows.map(row => ({
//         tagId: row.tag_id,
//         tagText: row.tag_text,
//         frequency: row.frequency,
//         category: row.category,
//         hasEmbedding: row.has_embedding === 1,
//         createdAt: new Date(row.created_at * 1000),
//         updatedAt: new Date(row.updated_at * 1000)
//       }));
//
//       return {
//         tags
//       };
//     } catch (error) {
//       console.error('Failed to query tag embeddings:', error);
//       return {
//         tags: [],
//         error: error instanceof Error ? error.message : 'Unknown error'
//       };
//     }
//   }
//
//   // ===== PathBasedDatabaseService Implementation (Stubs) =====
//
//   async insertPathBasedImages(images: PathBasedImageRecord[]): Promise<InsertResult> {
//     // TODO: Implement in subtask 2.3
//     throw new Error('insertPathBasedImages not yet implemented');
//   }
//
//   async queryPathBasedImages(params: QueryParams): Promise<PathBasedQueryResult> {
//     // TODO: Implement in subtask 2.3
//     throw new Error('queryPathBasedImages not yet implemented');
//   }
//
//   async queryImageByPath(filePath: string): Promise<PathBasedImageRecord | null> {
//     // TODO: Implement in subtask 2.3
//     throw new Error('queryImageByPath not yet implemented');
//   }
//
//   async queryImagesByPaths(filePaths: string[]): Promise<PathBasedImageRecord[]> {
//     // TODO: Implement in subtask 2.3
//     throw new Error('queryImagesByPaths not yet implemented');
//   }
//
//   async validateFilePath(filePath: string): Promise<PathValidationResult> {
//     // TODO: Implement in later tasks
//     throw new Error('validateFilePath not yet implemented');
//   }
//
//   async getFilePathMapping(relativePath: string): Promise<FilePathMapping> {
//     // TODO: Implement in later tasks
//     throw new Error('getFilePathMapping not yet implemented');
//   }
//
//   async getFilePathMappings(relativePaths: string[]): Promise<FilePathMapping[]> {
//     // TODO: Implement in later tasks
//     throw new Error('getFilePathMappings not yet implemented');
//   }
//
//   async syncFileSystemChanges(basePath: string): Promise<{
//     added: string[];
//     updated: string[];
//     deleted: string[];
//     errors: string[];
//   }> {
//     // TODO: Implement in later tasks
//     throw new Error('syncFileSystemChanges not yet implemented');
//   }
//
//   async cleanupInvalidPaths(): Promise<{
//     deletedCount: number;
//     errors: string[];
//   }> {
//     // TODO: Implement in later tasks
//     throw new Error('cleanupInvalidPaths not yet implemented');
//   }
//
//   async updateFileChecksums(filePaths: string[]): Promise<{
//     updated: string[];
//     errors: string[];
//   }> {
//     // TODO: Implement in later tasks
//     throw new Error('updateFileChecksums not yet implemented');
//   }
//
//   async migrateToPathBased(): Promise<{
//     migrated: number;
//     errors: string[];
//   }> {
//     // TODO: Implement in later tasks
//     throw new Error('migrateToPathBased not yet implemented');
//   }
//
//   async needsMigration(): Promise<boolean> {
//     // TODO: Implement in later tasks
//     throw new Error('needsMigration not yet implemented');
//   }
//
//   // ===== Private Helper Methods =====
//
//   /**
//    * Create database schema with all required tables and indexes
//    */
//   private createSchema(db: Database.Database): void {
//
//     // drop
//     db.prepare('DROP TABLE IF EXISTS images;').run();
//     db.prepare('DROP TABLE IF EXISTS image_vectors;').run();
//     db.prepare('DROP TABLE IF EXISTS tags;').run();
//     db.prepare('DROP TABLE IF EXISTS tag_vectors;').run();
//
//     // Create images table
//     db!.prepare(`
//       CREATE TABLE IF NOT EXISTS images (
//         id TEXT PRIMARY KEY,
//         file_path TEXT NOT NULL,
//         filename TEXT NOT NULL,
//         file_checksum TEXT,
//         description TEXT,
//         tags TEXT, -- JSON array
//         tags_flat TEXT, -- JSON array for efficient filtering
//         structured_metadata TEXT, -- JSON
//         metadata TEXT, -- JSON
//         -- Location fields
//         latitude REAL,
//         longitude REAL,
//         altitude REAL,
//         location_address TEXT,
//         location_city TEXT,
//         location_country TEXT,
//         location_source TEXT,
//         -- Time fields
//         captured_at INTEGER,
//         camera_info TEXT, -- JSON
//         shooting_params TEXT, -- JSON
//         created_at INTEGER DEFAULT (unixepoch()),
//         updated_at INTEGER DEFAULT (unixepoch())
//       )
//     `).run();
//
//     // Create image_vectors virtual table using vec0
//     db.prepare(`
//       CREATE VIRTUAL TABLE IF NOT EXISTS image_vectors USING vec0(
//         image_id TEXT PRIMARY KEY,
//         description_vector FLOAT[1024]
//       )
//     `).run();
//
//     // Create tags table
//     db.prepare(`
//       CREATE TABLE IF NOT EXISTS tags (
//         tag_id TEXT PRIMARY KEY,
//         tag_text TEXT UNIQUE NOT NULL,
//         frequency INTEGER DEFAULT 1,
//         category TEXT,
//         created_at INTEGER DEFAULT (unixepoch()),
//         updated_at INTEGER DEFAULT (unixepoch())
//       )
//     `).run();
//
//     // Create tag_vectors virtual table using vec0
//     db.prepare(`
//       CREATE VIRTUAL TABLE IF NOT EXISTS tag_vectors USING vec0(
//         tag_id TEXT PRIMARY KEY,
//         tag_embedding FLOAT[1024]
//       )
//     `).run();
//
//     // Create indexes for performance
//     this.createIndexes(db);
//   }
//
//   /**
//    * Create database indexes for optimal query performance
//    */
//   private createIndexes(db: Database.Database): void {
//     // Images table indexes
//     db.prepare('CREATE INDEX IF NOT EXISTS idx_images_file_path ON images(file_path)').run();
//     db.prepare('CREATE INDEX IF NOT EXISTS idx_images_file_checksum ON images(file_checksum)').run();
//     db.prepare('CREATE INDEX IF NOT EXISTS idx_images_created_at ON images(created_at)').run();
//     db.prepare('CREATE INDEX IF NOT EXISTS idx_images_captured_at ON images(captured_at)').run();
//     db.prepare('CREATE INDEX IF NOT EXISTS idx_images_location ON images(latitude, longitude)').run();
//     db.prepare('CREATE INDEX IF NOT EXISTS idx_images_location_city ON images(location_city)').run();
//     db.prepare('CREATE INDEX IF NOT EXISTS idx_images_location_country ON images(location_country)').run();
//
//     // Tags table indexes
//     db.prepare('CREATE INDEX IF NOT EXISTS idx_tags_text ON tags(tag_text)').run();
//     db.prepare('CREATE INDEX IF NOT EXISTS idx_tags_frequency ON tags(frequency DESC)').run();
//     db.prepare('CREATE INDEX IF NOT EXISTS idx_tags_category ON tags(category)').run();
//   }
//
//   /**
//    * Convert database row to ImageRecord format
//    */
//   private convertRowToImageRecord(row: any): ImageRecord {
//     try {
//       // Parse JSON fields
//       const metadata = row.metadata ? JSON.parse(row.metadata) : {};
//       const tags = row.tags ? JSON.parse(row.tags) : [];
//       const tagsFlat = row.tags_flat ? JSON.parse(row.tags_flat) : tags;
//       const structuredMetadata = row.structured_metadata ? JSON.parse(row.structured_metadata) : {};
//
//       // Convert timestamps
//       const capturedAt = row.captured_at ? new Date(row.captured_at * 1000).toISOString() : undefined;
//
//       return {
//         id: row.id,
//         imagePath: row.file_path,
//         description: row.description || '',
//         tags,
//         tags_flat: tagsFlat,
//         embedding: [], // Will be loaded separately if needed
//         structured_metadata: structuredMetadata,
//         metadata: {
//           ...metadata,
//           filename: row.filename,
//           fileChecksum: row.file_checksum
//         },
//         // Location fields
//         latitude: row.latitude,
//         longitude: row.longitude,
//         altitude: row.altitude,
//         locationAddress: row.location_address,
//         locationCity: row.location_city,
//         locationCountry: row.location_country,
//         locationSource: row.location_source,
//         // Time fields
//         capturedAt,
//         cameraInfo: row.camera_info,
//         shootingParams: row.shooting_params
//       };
//     } catch (error) {
//       console.error('Failed to convert row to ImageRecord:', error);
//       // Return minimal valid record
//       return {
//         id: row.id,
//         imagePath: row.file_path || '',
//         description: row.description || '',
//         tags: [],
//         embedding: [],
//         metadata: {
//           filename: row.filename || '',
//           filesize: 0,
//           uploadTime: new Date().toISOString(),
//           dimensions: '0x0',
//           format: 'unknown'
//         }
//       };
//     }
//   }
//
//   /**
//    * Convert database row to LibraryConfig format
//    */
//   private convertRowToLibraryConfig(row: any): LibraryConfig {
//     try {
//       // Parse JSON fields
//       const settings = row.settings ? JSON.parse(row.settings) : {};
//       const scanProgress = row.scan_progress ? JSON.parse(row.scan_progress) : {};
//       const statistics = row.statistics ? JSON.parse(row.statistics) : {};
//       const tags = row.tags ? JSON.parse(row.tags) : [];
//       const metadata = row.metadata ? JSON.parse(row.metadata) : {};
//
//       // Convert timestamps
//       const createdAt = new Date(row.created_at * 1000).toISOString();
//       const lastScanAt = row.last_scan_at ? new Date(row.last_scan_at * 1000).toISOString() : createdAt;
//       const updatedAt = row.updated_at ? new Date(row.updated_at * 1000).toISOString() : undefined;
//
//       return {
//         id: row.id,
//         name: row.name,
//         rootPath: row.root_path,
//         type: row.type || 'local',
//         status: row.status,
//         settings: {
//           recursive: true,
//           includeHidden: false,
//           maxDepth: 10,
//           supportedFormats: ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp'],
//           enableAIAnalysis: true,
//           autoScanOnStartup: false,
//           ...settings
//         },
//         scanProgress: {
//           total: 0,
//           processed: 0,
//           failed: 0,
//           skipped: 0,
//           duplicates: 0,
//           ...scanProgress
//         },
//         statistics: {
//           totalImages: 0,
//           totalSize: 0,
//           ...statistics
//         },
//         description: row.description,
//         tags,
//         metadata,
//         createdAt,
//         lastScanAt,
//         updatedAt
//       };
//     } catch (error) {
//       console.error('Failed to convert row to LibraryConfig:', error);
//       // Return minimal valid config
//       return {
//         id: row.id,
//         name: row.name || 'Unknown',
//         rootPath: row.root_path || '',
//         status: row.status || 'offline',
//         settings: {
//           recursive: true,
//           includeHidden: false,
//           maxDepth: 10,
//           supportedFormats: ['.jpg', '.jpeg', '.png'],
//           enableAIAnalysis: true,
//           autoScanOnStartup: false
//         },
//         scanProgress: {
//           total: 0,
//           processed: 0,
//           failed: 0,
//           skipped: 0,
//           duplicates: 0
//         },
//         statistics: {
//           totalImages: 0,
//           totalSize: 0
//         },
//         createdAt: new Date().toISOString(),
//         lastScanAt: new Date().toISOString()
//       };
//     }
//   }
//
//   /**
//    * Establish database connection
//    */
//   private connect(): void {
//     if (this.db) {
//       return;
//     }
//
//     // Ensure directory exists
//     const dbDir = path.dirname(this.dbPath);
//     if (!fs.existsSync(dbDir)) {
//       fs.mkdirSync(dbDir, { recursive: true });
//     }
//     if (!fs.existsSync(this.dbPath)) {
//       fs.writeFileSync(this.dbPath, '');
//     }
//
//     // Create database connection
//     this.db = new Database(this.dbPath);
//
//     // Enable WAL mode for better concurrency
//     this.db.pragma('journal_mode = WAL');
//     this.db.pragma('synchronous = NORMAL');
//     this.db.pragma('cache_size = 1000000');
//     this.db.pragma('temp_store = memory');
//   }
//
//   /**
//    * 获取所有表名
//    */
//   async getAllTables(): Promise<string[]> {
//     try {
//       this.connect();
//       const stmt = this.db!.prepare("SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'");
//       const rows = stmt.all() as { name: string }[];
//       return rows.map(row => row.name);
//     } catch (error) {
//       console.error('Failed to get all tables:', error);
//       return [];
//     }
//   }
//
//   /**
//    * 分页查询表数据
//    */
//   async getTableData(tableName: string, page: number, pageSize: number): Promise<{
//     data: any[];
//     total: number;
//   }> {
//     try {
//       this.connect();
//
//       // 验证表名以防止SQL注入
//       const validTableNames = await this.getAllTables();
//       if (!validTableNames.includes(tableName)) {
//         throw new Error(`Table '${tableName}' does not exist`);
//       }
//
//       // 获取总数
//       const countStmt = this.db!.prepare(`SELECT COUNT(*) as count FROM "${tableName}"`);
//       const countResult = countStmt.get() as { count: number };
//       const total = countResult.count;
//
//       // 分页查询数据
//       const offset = (page - 1) * pageSize;
//       const dataStmt = this.db!.prepare(`SELECT * FROM "${tableName}" LIMIT ? OFFSET ?`);
//       const data = dataStmt.all(pageSize, offset);
//
//       return {
//         data,
//         total
//       };
//     } catch (error) {
//       console.error(`Failed to get table data for '${tableName}':`, error);
//       return {
//         data: [],
//         total: 0
//       };
//     }
//   }
// }