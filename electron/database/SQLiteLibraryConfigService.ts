// import Database from 'better-sqlite3';
// import * as path from 'path';
// import * as fs from 'fs';
// import { fileURLToPath } from 'url';
//
// // ES模块兼容性定义
// const __filename = fileURLToPath(import.meta.url);
// const __dirname = path.dirname(__filename);
//
// import {
//   LibraryConfig,
//   LibraryConfigInsertResult,
//   LibraryConfigQueryParams,
//   LibraryConfigQueryResult,
//   BaseResult
// } from '../../src/types/database';
//
// /**
//  * SQLite数据库服务，专门用于管理图片库配置
//  * 与Milvus分离，Milvus专门处理向量数据，SQLite处理配置数据
//  */
// export class SQLiteLibraryConfigService {
//   private db: Database.Database | null = null;
//   private dbPath: string;
//
//   constructor(dbPath?: string) {
//     // 默认数据库路径为用户数据目录下的library_config.db
//     this.dbPath = dbPath || path.join(process.cwd(), 'data', 'library_config.db');
//   }
//
//   /**
//    * 连接数据库（不创建表）
//    */
//   private connectDatabase(): void {
//     if (this.db) return;
//
//     // 确保数据库目录存在
//     const dbDir = path.dirname(this.dbPath);
//     if (!fs.existsSync(dbDir)) {
//       fs.mkdirSync(dbDir, { recursive: true });
//     }
//     if (!fs.existsSync(this.dbPath)) {
//       fs.writeFileSync(this.dbPath, '');
//     }
//
//     // 创建或打开SQLite数据库
//     this.db = new Database(this.dbPath);
//
//     // 开启WAL模式以提高并发性能
//     this.db.pragma('journal_mode = WAL');
//   }
//
//   /**
//    * 创建图片库配置表
//    */
//   private createLibraryConfigTable(): void {
//     if (!this.db) {
//       throw new Error('数据库未连接');
//     }
//
//     // drop
//     this.db.exec('DROP TABLE IF EXISTS library_configs');
//
//     // 创建图片库配置表
//     this.db.exec(`
//       CREATE TABLE IF NOT EXISTS library_configs (
//         id TEXT PRIMARY KEY,
//         name TEXT NOT NULL,
//         root_path TEXT NOT NULL,
//         type TEXT DEFAULT 'local' CHECK (type IN ('local', 'cloud', 'network')),
//         status TEXT NOT NULL CHECK (status IN ('active', 'offline', 'removed')),
//         settings TEXT NOT NULL, -- JSON字符串
//         scan_progress TEXT NOT NULL, -- JSON字符串
//         statistics TEXT NOT NULL, -- JSON字符串
//         description TEXT,
//         tags TEXT, -- JSON数组字符串
//         metadata TEXT, -- JSON字符串
//         created_at TEXT NOT NULL,
//         last_scan_at TEXT NOT NULL,
//         updated_at TEXT,
//
//         -- 添加索引字段便于查询
//         UNIQUE(root_path),
//         CHECK(json_valid(settings)),
//         CHECK(json_valid(scan_progress)),
//         CHECK(json_valid(statistics)),
//         CHECK(tags IS NULL OR json_valid(tags)),
//         CHECK(metadata IS NULL OR json_valid(metadata))
//       )
//     `);
//
//     // 创建索引以提高查询性能
//     const indexes = [
//       'CREATE INDEX IF NOT EXISTS idx_library_configs_name ON library_configs(name)',
//       'CREATE INDEX IF NOT EXISTS idx_library_configs_status ON library_configs(status)',
//       'CREATE INDEX IF NOT EXISTS idx_library_configs_type ON library_configs(type)',
//       'CREATE INDEX IF NOT EXISTS idx_library_configs_created_at ON library_configs(created_at)',
//       'CREATE INDEX IF NOT EXISTS idx_library_configs_updated_at ON library_configs(updated_at)'
//     ];
//
//     indexes.forEach(indexSQL => {
//       this.db!.exec(indexSQL);
//     });
//   }
//
//   /**
//    * 初始化数据库表结构（仅在需要时调用）
//    */
//   initializeTables(): void {
//     this.getDb();
//     this.createLibraryConfigTable();
//   }
//
//   /**
//    * 获取数据库单例
//    */
//   getDb(): Database.Database {
//     if (!this.db) {
//       this.connectDatabase();
//     }
//     return this.db!;
//   }
//
//   /**
//    * 插入图片库配置
//    */
//   async insertLibraryConfigs(configs: LibraryConfig[]): Promise<LibraryConfigInsertResult> {
//     try {
//       const db = this.getDb();
//
//       const insertSQL = `
//         INSERT INTO library_configs (
//           id, name, root_path, type, status, settings, scan_progress,
//           statistics, description, tags, metadata, created_at, last_scan_at, updated_at
//         ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
//       `;
//
//       const insertStmt = db.prepare(insertSQL);
//       const insertMany = db.transaction((configs: LibraryConfig[]) => {
//         const insertedIds: string[] = [];
//
//         for (const config of configs) {
//           try {
//             insertStmt.run(
//               config.id,
//               config.name,
//               config.rootPath,
//               config.type || 'local',
//               config.status,
//               JSON.stringify(config.settings),
//               JSON.stringify(config.scanProgress),
//               JSON.stringify(config.statistics),
//               config.description || null,
//               config.tags ? JSON.stringify(config.tags) : null,
//               config.metadata ? JSON.stringify(config.metadata) : null,
//               config.createdAt,
//               config.lastScanAt,
//               config.updatedAt || new Date().toISOString()
//             );
//             insertedIds.push(config.id);
//           } catch (error) {
//             console.error(`插入配置失败 ${config.id}:`, error);
//             // 继续处理其他配置
//           }
//         }
//
//         return insertedIds;
//       });
//
//       const insertedIds = insertMany(configs);
//
//       return {
//         success: true,
//         ids: insertedIds
//       };
//     } catch (error) {
//       console.error('批量插入图片库配置失败:', error);
//       return {
//         success: false,
//         error: error instanceof Error ? error.message : String(error)
//       };
//     }
//   }
//
//   /**
//    * 查询图片库配置
//    */
//   async queryLibraryConfigs(params: LibraryConfigQueryParams = {}): Promise<LibraryConfigQueryResult> {
//     try {
//       const db = this.getDb();
//
//       const { limit = 100, offset = 0 } = params;
//
//       let whereClause = '';
//       const queryParams: any[] = [];
//
//       // 构建WHERE子句
//       if (params.expr) {
//         // 简单的表达式解析（支持基本的等式和AND/OR）
//         const expr = this.parseExpression(params.expr);
//         if (expr.sql) {
//           whereClause = `WHERE ${expr.sql}`;
//           queryParams.push(...expr.params);
//         }
//       }
//
//       const querySQL = `
//         SELECT * FROM library_configs
//         ${whereClause}
//         ORDER BY created_at DESC
//         LIMIT ? OFFSET ?
//       `;
//
//       queryParams.push(limit, offset);
//
//       const rows = db.prepare(querySQL).all(...queryParams);
//
//       const configs: LibraryConfig[] = rows.map((row: any) => ({
//         id: row.id,
//         name: row.name,
//         rootPath: row.root_path,
//         type: row.type,
//         status: row.status,
//         settings: JSON.parse(row.settings),
//         scanProgress: JSON.parse(row.scan_progress),
//         statistics: JSON.parse(row.statistics),
//         description: row.description,
//         tags: row.tags ? JSON.parse(row.tags) : [],
//         metadata: row.metadata ? JSON.parse(row.metadata) : {},
//         createdAt: row.created_at,
//         lastScanAt: row.last_scan_at,
//         updatedAt: row.updated_at
//       }));
//
//       return {
//         success: true,
//         results: configs,
//         count: configs.length
//       };
//     } catch (error) {
//       console.error('查询图片库配置失败:', error);
//       return {
//         success: false,
//         results: [],
//         count: 0,
//         error: error instanceof Error ? error.message : String(error)
//       };
//     }
//   }
//
//   /**
//    * 根据ID查询单个图片库配置
//    */
//   async getLibraryConfigById(id: string): Promise<LibraryConfig | null> {
//     try {
//       const db = this.getDb();
//
//       const querySQL = 'SELECT * FROM library_configs WHERE id = ?';
//       const row = db.prepare(querySQL).get(id) as any;
//
//       if (!row) {
//         return null;
//       }
//
//       return {
//         id: row.id,
//         name: row.name,
//         rootPath: row.root_path,
//         type: row.type,
//         status: row.status,
//         settings: JSON.parse(row.settings),
//         scanProgress: JSON.parse(row.scan_progress),
//         statistics: JSON.parse(row.statistics),
//         description: row.description,
//         tags: row.tags ? JSON.parse(row.tags) : [],
//         metadata: row.metadata ? JSON.parse(row.metadata) : {},
//         createdAt: row.created_at,
//         lastScanAt: row.last_scan_at,
//         updatedAt: row.updated_at
//       };
//     } catch (error) {
//       console.error('根据ID查询图片库配置失败:', error);
//       return null;
//     }
//   }
//
//   /**
//    * 根据根路径查询图片库配置
//    */
//   async getLibraryConfigByRootPath(rootPath: string): Promise<LibraryConfig | null> {
//     try {
//       const db = this.getDb();
//
//       const querySQL = 'SELECT * FROM library_configs WHERE root_path = ?';
//       const row = db.prepare(querySQL).get(rootPath) as any;
//
//       if (!row) {
//         return null;
//       }
//
//       return {
//         id: row.id,
//         name: row.name,
//         rootPath: row.root_path,
//         type: row.type,
//         status: row.status,
//         settings: JSON.parse(row.settings),
//         scanProgress: JSON.parse(row.scan_progress),
//         statistics: JSON.parse(row.statistics),
//         description: row.description,
//         tags: row.tags ? JSON.parse(row.tags) : [],
//         metadata: row.metadata ? JSON.parse(row.metadata) : {},
//         createdAt: row.created_at,
//         lastScanAt: row.last_scan_at,
//         updatedAt: row.updated_at
//       };
//     } catch (error) {
//       console.error('根据根路径查询图片库配置失败:', error);
//       return null;
//     }
//   }
//
//   /**
//    * 更新图片库配置
//    */
//   async updateLibraryConfig(id: string, updates: Partial<LibraryConfig>): Promise<BaseResult> {
//     try {
//       const db = this.getDb();
//
//       // 构建更新字段和值
//       const updateFields: string[] = [];
//       const updateValues: any[] = [];
//
//       if (updates.name !== undefined) {
//         updateFields.push('name = ?');
//         updateValues.push(updates.name);
//       }
//       if (updates.rootPath !== undefined) {
//         updateFields.push('root_path = ?');
//         updateValues.push(updates.rootPath);
//       }
//       if (updates.type !== undefined) {
//         updateFields.push('type = ?');
//         updateValues.push(updates.type);
//       }
//       if (updates.status !== undefined) {
//         updateFields.push('status = ?');
//         updateValues.push(updates.status);
//       }
//       if (updates.settings !== undefined) {
//         updateFields.push('settings = ?');
//         updateValues.push(JSON.stringify(updates.settings));
//       }
//       if (updates.scanProgress !== undefined) {
//         updateFields.push('scan_progress = ?');
//         updateValues.push(JSON.stringify(updates.scanProgress));
//       }
//       if (updates.statistics !== undefined) {
//         updateFields.push('statistics = ?');
//         updateValues.push(JSON.stringify(updates.statistics));
//       }
//       if (updates.description !== undefined) {
//         updateFields.push('description = ?');
//         updateValues.push(updates.description);
//       }
//       if (updates.tags !== undefined) {
//         updateFields.push('tags = ?');
//         updateValues.push(updates.tags ? JSON.stringify(updates.tags) : null);
//       }
//       if (updates.metadata !== undefined) {
//         updateFields.push('metadata = ?');
//         updateValues.push(updates.metadata ? JSON.stringify(updates.metadata) : null);
//       }
//       if (updates.lastScanAt !== undefined) {
//         updateFields.push('last_scan_at = ?');
//         updateValues.push(updates.lastScanAt);
//       }
//
//       // 总是更新updated_at字段
//       updateFields.push('updated_at = ?');
//       updateValues.push(new Date().toISOString());
//
//       if (updateFields.length === 1) { // 只有updated_at字段
//         return { success: true };
//       }
//
//       updateValues.push(id); // WHERE子句的参数
//
//       const updateSQL = `
//         UPDATE library_configs
//         SET ${updateFields.join(', ')}
//         WHERE id = ?
//       `;
//
//       const result = db.prepare(updateSQL).run(...updateValues);
//
//       return {
//         success: result.changes > 0,
//         error: result.changes === 0 ? '未找到要更新的配置' : undefined
//       };
//     } catch (error) {
//       console.error('更新图片库配置失败:', error);
//       return {
//         success: false,
//         error: error instanceof Error ? error.message : String(error)
//       };
//     }
//   }
//
//   /**
//    * 删除图片库配置
//    */
//   async deleteLibraryConfig(id: string): Promise<BaseResult> {
//     try {
//       const db = this.getDb();
//
//       const deleteSQL = 'DELETE FROM library_configs WHERE id = ?';
//       const result = db.prepare(deleteSQL).run(id);
//
//       return {
//         success: result.changes > 0,
//         error: result.changes === 0 ? '未找到要删除的配置' : undefined
//       };
//     } catch (error) {
//       console.error('删除图片库配置失败:', error);
//       return {
//         success: false,
//         error: error instanceof Error ? error.message : String(error)
//       };
//     }
//   }
//
//   /**
//    * 获取活跃的图片库配置
//    */
//   async getActiveLibraryConfigs(): Promise<LibraryConfigQueryResult> {
//     return this.queryLibraryConfigs({
//       expr: 'status == "active"'
//     });
//   }
//
//   /**
//    * 获取所有图片库配置的统计信息
//    */
//   async getLibraryConfigStats(): Promise<{
//     total: number;
//     active: number;
//     offline: number;
//     removed: number;
//     totalImages: number;
//     totalSize: number;
//   }> {
//     try {
//       const db = this.getDb();
//
//       const statsSQL = `
//         SELECT
//           COUNT(*) as total,
//           COUNT(CASE WHEN status = 'active' THEN 1 END) as active,
//           COUNT(CASE WHEN status = 'offline' THEN 1 END) as offline,
//           COUNT(CASE WHEN status = 'removed' THEN 1 END) as removed,
//           SUM(json_extract(statistics, '$.totalImages')) as total_images,
//           SUM(json_extract(statistics, '$.totalSize')) as total_size
//         FROM library_configs
//       `;
//
//       const result = db.prepare(statsSQL).get() as any;
//
//       return {
//         total: result.total || 0,
//         active: result.active || 0,
//         offline: result.offline || 0,
//         removed: result.removed || 0,
//         totalImages: result.total_images || 0,
//         totalSize: result.total_size || 0
//       };
//     } catch (error) {
//       console.error('获取图片库配置统计信息失败:', error);
//       return {
//         total: 0,
//         active: 0,
//         offline: 0,
//         removed: 0,
//         totalImages: 0,
//         totalSize: 0
//       };
//     }
//   }
//
//   /**
//    * 关闭数据库连接
//    */
//   async close(): Promise<void> {
//     try {
//       if (this.db) {
//         this.db.close();
//         this.db = null;
//       }
//
//       // 重置初始化状态
//       this.db = null;
//
//       console.log('✅ SQLite数据库连接已关闭');
//     } catch (error) {
//       console.error('❌ 关闭SQLite数据库连接失败:', error);
//     }
//   }
//
//   /**
//    * 简单的表达式解析器（支持基本的等式查询）
//    * 示例: status == "active" AND type == "local"
//    */
//   private parseExpression(expr: string): { sql: string; params: any[] } {
//     try {
//       // 这是一个简化的表达式解析器
//       // 在生产环境中，应该使用更严格的解析器
//
//       const params: any[] = [];
//       let sql = expr;
//
//       // 替换字段名映射
//       const fieldMappings: Record<string, string> = {
//         'status': 'status',
//         'type': 'type',
//         'name': 'name',
//         'id': 'id'
//       };
//
//       // 处理等式操作符
//       sql = sql.replace(/(\w+)\s*==\s*"([^"]+)"/g, (match, field, value) => {
//         const dbField = fieldMappings[field] || field;
//         params.push(value);
//         return `${dbField} = ?`;
//       });
//
//       // 处理AND和OR
//       sql = sql.replace(/\s+AND\s+/gi, ' AND ');
//       sql = sql.replace(/\s+OR\s+/gi, ' OR ');
//
//       return { sql, params };
//     } catch (error) {
//       console.error('表达式解析失败:', error);
//       return { sql: '1=1', params: [] };
//     }
//   }
//
//   /**
//    * 测试数据库连接
//    */
//   testConnection(): boolean {
//     return true;
//   }
//
//   /**
//    * 获取所有表名
//    */
//   async getAllTables(): Promise<string[]> {
//     try {
//       const db = this.getDb();
//       const tables = db.prepare(
//         "SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'"
//       ).all();
//       return tables.map((t: any) => t.name);
//     } catch (error) {
//       console.error('获取所有表名失败:', error);
//       return [];
//     }
//   }
//
//   /**
//    * 获取指定表的数据
//    * @param tableName 表名
//    * @param page 页码（从1开始）
//    * @param pageSize 每页大小
//    */
//   async getTableData(tableName: string, page: number = 1, pageSize: number = 10): Promise<{ data: any[]; total: number }> {
//     try {
//       const db = this.getDb();
//
//       // 验证表名是否存在
//       const tableExists = db.prepare(
//         "SELECT name FROM sqlite_master WHERE type='table' AND name = ?"
//       ).get(tableName);
//
//       if (!tableExists) {
//         throw new Error(`表 ${tableName} 不存在`);
//       }
//
//       // 获取总记录数
//       const totalStmt = db.prepare(`SELECT COUNT(*) as count FROM "${tableName}"`);
//       const { count } = totalStmt.get() as { count: number };
//
//       // 计算分页参数
//       const offset = (page - 1) * pageSize;
//
//       // 安全地查询表数据（带分页）
//       const stmt = db.prepare(`SELECT * FROM "${tableName}" LIMIT ? OFFSET ?`);
//       const data = stmt.all(pageSize, offset);
//
//       return {
//         data,
//         total: count
//       };
//     } catch (error) {
//       console.error(`获取表 ${tableName} 数据失败:`, error);
//       return {
//         data: [],
//         total: 0
//       };
//     }
//   }
// }