// import { PathBasedDatabaseService, FilePathUtils } from './PathBasedDatabaseService';
// import { SQLiteVecDatabaseService } from './SQLiteVecDatabaseService';
// import { SQLiteLibraryConfigService } from './SQLiteLibraryConfigService';
// import {
//   InsertResult,
//   QueryParams,
//   QueryResult,
//   PathBasedImageRecord,
//   PathBasedQueryResult,
//   FilePathMapping,
//   PathValidationResult,
//   ImageRecord,
//   BaseResult,
//   ExistsResult,
//   LibraryConfig,
//   LibraryConfigInsertResult,
//   LibraryConfigQueryParams,
//   LibraryConfigQueryResult
// } from '@shared/types/database';
// import {AIService} from "../ai/AIService.ts";
//
// /**
//  * 混合数据库服务
//  * - 使用SQLiteVec处理图片数据和向量搜索
//  * - 使用SQLite处理图片库配置数据
//  */
// export class HybridDatabaseService implements PathBasedDatabaseService {
//   private sqliteVecService: SQLiteVecDatabaseService;
//   private sqliteService: SQLiteLibraryConfigService;
//
//   constructor(sqliteVecDbPath?: string, sqliteConfigDbPath?: string) {
//     this.sqliteVecService = new SQLiteVecDatabaseService(sqliteVecDbPath);
//     this.sqliteService = new SQLiteLibraryConfigService(sqliteConfigDbPath);
//   }
//
//   /**
//    * Set AI service for embedding generation
//    */
//   setAIService(aiService: AIService) {
//     this.sqliteVecService.setAIService(aiService);
//   }
//
//   /**
//    * 初始化混合数据库服务
//    */
//   async initDatabase(): Promise<boolean> {
//     try {
//       console.log('🔄 初始化混合数据库服务...');
//
//       // 初始化SQLite（图片库配置）
//       await this.sqliteService.initializeTables();
//       console.log('✅ SQLite配置数据库初始化成功');
//
//       // 初始化SQLiteVec（图片数据和向量搜索）
//       await this.sqliteVecService.initializeTables();
//       console.log('✅ SQLiteVec数据库初始化成功');
//
//       console.log('🎉 混合数据库服务初始化完成');
//       return true;
//     } catch (error) {
//       console.error('❌ 混合数据库服务初始化失败:', error);
//       return false;
//     }
//   }
//
//   /**
//    * 测试连接
//    */
//   async testConnection(checkTables: boolean = true): Promise<boolean> {
//     try {
//       const sqliteOk = await this.sqliteService.testConnection();
//       const sqliteVecOk = await this.sqliteVecService.testConnection();
//
//       return sqliteOk && sqliteVecOk;
//     } catch (error) {
//       console.error('测试混合数据库连接失败:', error);
//       return false;
//     }
//   }
//
//   /**
//    * 清空数据库
//    */
//   async clearDatabase(): Promise<boolean> {
//     try {
//       const sqliteVecCleared = await this.sqliteVecService.clearDatabase();
//       // 注意：这里不清空SQLite配置数据，因为配置是持久的
//       return sqliteVecCleared;
//     } catch (error) {
//       console.error('清空混合数据库失败:', error);
//       return false;
//     }
//   }
//
//   /**
//    * 关闭数据库连接
//    */
//   async close(): Promise<void> {
//     try {
//       await Promise.all([
//         this.sqliteVecService.close(),
//         this.sqliteService.close()
//       ]);
//       console.log('✅ 混合数据库服务已关闭');
//     } catch (error) {
//       console.error('❌ 关闭混合数据库服务失败:', error);
//     }
//   }
//
//   // ==================== 图片数据相关方法（委托给SQLiteVec） ====================
//
//   async insertImages(images: ImageRecord[]): Promise<InsertResult> {
//     return this.sqliteVecService.insertImages(images);
//   }
//
//   async deleteImage(id: string): Promise<BaseResult> {
//     return this.sqliteVecService.deleteImage(id);
//   }
//
//   async queryImages(params: QueryParams): Promise<QueryResult> {
//     return this.sqliteVecService.queryImages(params);
//   }
//
//   async insertPathBasedImages(images: PathBasedImageRecord[]): Promise<InsertResult> {
//     return this.sqliteVecService.insertPathBasedImages(images);
//   }
//
//   async queryPathBasedImages(params: QueryParams): Promise<PathBasedQueryResult> {
//     return this.sqliteVecService.queryPathBasedImages(params);
//   }
//
//   async queryImageByPath(filePath: string): Promise<PathBasedImageRecord | null> {
//     return this.sqliteVecService.queryImageByPath(filePath);
//   }
//
//   async queryImagesByPaths(filePaths: string[]): Promise<PathBasedImageRecord[]> {
//     return this.sqliteVecService.queryImagesByPaths(filePaths);
//   }
//
//   async validateFilePath(filePath: string): Promise<PathValidationResult> {
//     return this.sqliteVecService.validateFilePath(filePath);
//   }
//
//   async getFilePathMapping(relativePath: string): Promise<FilePathMapping> {
//     return this.sqliteVecService.getFilePathMapping(relativePath);
//   }
//
//   async getFilePathMappings(relativePaths: string[]): Promise<FilePathMapping[]> {
//     return this.sqliteVecService.getFilePathMappings(relativePaths);
//   }
//
//   async syncFileSystemChanges(basePath: string): Promise<{
//     added: string[];
//     updated: string[];
//     deleted: string[];
//     errors: string[];
//   }> {
//     return this.sqliteVecService.syncFileSystemChanges(basePath);
//   }
//
//   async cleanupInvalidPaths(): Promise<{
//     deletedCount: number;
//     errors: string[];
//   }> {
//     return this.sqliteVecService.cleanupInvalidPaths();
//   }
//
//   async updateFileChecksums(filePaths: string[]): Promise<{
//     updated: string[];
//     errors: string[];
//   }> {
//     return this.sqliteVecService.updateFileChecksums(filePaths);
//   }
//
//   async migrateToPathBased(): Promise<{
//     migrated: number;
//     errors: string[];
//   }> {
//     return this.sqliteVecService.migrateToPathBased();
//   }
//
//   async needsMigration(): Promise<boolean> {
//     return this.sqliteVecService.needsMigration();
//   }
//
//   async queryAllTags(): Promise<{ tags: string[], error?: string }> {
//     return this.sqliteVecService.queryAllTags();
//   }
//
//   async hybridSearch(params: {
//     query: string;
//     keywords: string[];
//     embedding: number[];
//     limit?: number;
//     threshold?: number;
//   }): Promise<QueryResult> {
//     return this.sqliteVecService.hybridSearch(params);
//   }
//
//   async checkImageExists(filePath: string, md5?: string): Promise<ExistsResult> {
//     return this.sqliteVecService.checkImageExists(filePath, md5);
//   }
//
//   // ==================== 图片库配置相关方法（委托给SQLite） ====================
//
//   /**
//    * 插入图片库配置
//    */
//   async insertLibraryConfigs(configs: LibraryConfig[]): Promise<LibraryConfigInsertResult> {
//     return this.sqliteService.insertLibraryConfigs(configs);
//   }
//
//   /**
//    * 查询图片库配置
//    */
//   async queryLibraryConfigs(params: LibraryConfigQueryParams): Promise<LibraryConfigQueryResult> {
//     return this.sqliteService.queryLibraryConfigs(params);
//   }
//
//   /**
//    * 根据ID获取图片库配置
//    */
//   async getLibraryConfigById(id: string): Promise<LibraryConfig | null> {
//     return this.sqliteService.getLibraryConfigById(id);
//   }
//
//   /**
//    * 根据根路径获取图片库配置
//    */
//   async getLibraryConfigByRootPath(rootPath: string): Promise<LibraryConfig | null> {
//     return this.sqliteService.getLibraryConfigByRootPath(rootPath);
//   }
//
//   /**
//    * 更新图片库配置
//    */
//   async updateLibraryConfig(id: string, updates: Partial<LibraryConfig>): Promise<BaseResult> {
//     return this.sqliteService.updateLibraryConfig(id, updates);
//   }
//
//   /**
//    * 删除图片库配置
//    */
//   async deleteLibraryConfig(id: string): Promise<BaseResult> {
//     return this.sqliteService.deleteLibraryConfig(id);
//   }
//
//   /**
//    * 获取活跃的图片库配置
//    */
//   async getActiveLibraryConfigs(): Promise<LibraryConfigQueryResult> {
//     return this.sqliteService.getActiveLibraryConfigs();
//   }
//
//   /**
//    * 获取图片库配置统计信息
//    */
//   async getLibraryConfigStats(): Promise<{
//     total: number;
//     active: number;
//     offline: number;
//     removed: number;
//     totalImages: number;
//     totalSize: number;
//   }> {
//     return this.sqliteService.getLibraryConfigStats();
//   }
//
//   // ==================== 高级搜索方法（委托给SQLiteVec） ====================
//
//   async enhancedHybridSearch(params: {
//     query: string;
//     limit?: number;
//     expandKeywords?: boolean;
//     similarityThreshold?: number;
//   }): Promise<any> {
//     return this.sqliteVecService.enhancedHybridSearch(params);
//   }
//
//   async buildTagEmbeddings(): Promise<boolean> {
//     return this.sqliteVecService.buildTagEmbeddings();
//   }
//
//   async findSimilarTags(
//     query: string,
//     options?: {
//       limit?: number;
//       threshold?: number;
//       category?: string;
//     }
//   ): Promise<any[]> {
//     return this.sqliteVecService.findSimilarTags(query, options);
//   }
//
//   async updateTagEmbeddings(newTags: string[]): Promise<void> {
//     return this.sqliteVecService.updateTagEmbeddings(newTags);
//   }
//
//   async findSimilarTagsBatch(queries: string[], options?: any): Promise<Map<string, any>> {
//     // 如果 SQLiteVecService 有这个方法，则委托给它
//     if (typeof this.sqliteVecService.findSimilarTagsBatch === 'function') {
//       return this.sqliteVecService.findSimilarTagsBatch(queries, options);
//     }
//
//     // 否则通过批量调用 findSimilarTags 来实现
//     const resultMap = new Map<string, any>();
//     for (const query of queries) {
//       try {
//         const result = await this.findSimilarTags(query, options);
//         resultMap.set(query, result);
//       } catch (error) {
//         console.error(`查找相似标签失败 for query "${query}":`, error);
//         resultMap.set(query, []);
//       }
//     }
//     return resultMap;
//   }
//
//   async queryTagEmbeddings(limit?: number): Promise<{ tags: any[]; error?: string }> {
//     // 如果 SQLiteVecService 有这个方法，则委托给它
//     if (typeof this.sqliteVecService.queryTagEmbeddings === 'function') {
//       return this.sqliteVecService.queryTagEmbeddings(limit);
//     }
//
//     // 否则返回空结果
//     return { tags: [], error: '标签向量查询功能未实现' };
//   }
//
//   // ==================== 位置和时间相关搜索（委托给SQLiteVec） ====================
//
//   async queryImagesByLocation(
//     latitude: number,
//     longitude: number,
//     radius?: number,
//     limit?: number
//   ): Promise<any[]> {
//     return this.sqliteVecService.queryImagesByLocation(latitude, longitude, radius, limit);
//   }
//
//   async queryImagesByLocationName(location: string, limit?: number): Promise<any[]> {
//     return this.sqliteVecService.queryImagesByLocationName(location, limit);
//   }
//
//   async queryImagesByTimeRange(
//     startTime: Date,
//     endTime: Date,
//     limit?: number
//   ): Promise<any[]> {
//     return this.sqliteVecService.queryImagesByTimeRange(startTime, endTime, limit);
//   }
//
//   async queryImagesByDate(date: Date, limit?: number): Promise<any[]> {
//     return this.sqliteVecService.queryImagesByDate(date, limit);
//   }
//
//   async getImageExifData(imageId: string): Promise<any> {
//     return this.sqliteVecService.getImageExifData(imageId);
//   }
//
//   async updateImageLocation(imageId: string, locationData: {
//     latitude?: number;
//     longitude?: number;
//     address?: string;
//     city?: string;
//     country?: string;
//   }): Promise<boolean> {
//     return this.sqliteVecService.updateImageLocation(imageId, locationData);
//   }
//
//   async vectorSimilaritySearch(embedding: number[], limit?: number, threshold?: number): Promise<QueryResult> {
//     return this.sqliteVecService.vectorSimilaritySearch(embedding, limit, threshold);
//   }
//
//   async searchSimilarImages(imageId: string, options?: { threshold?: number; limit?: number }): Promise<QueryResult> {
//     return this.sqliteVecService.searchSimilarImages(imageId, options);
//   }
//
//   // ==================== 便利方法 ====================
//
//   /**
//    * 获取SQLiteVec服务实例（用于需要直接访问SQLiteVec功能的情况）
//    */
//   getSQLiteVecService(): SQLiteVecDatabaseService {
//     return this.sqliteVecService;
//   }
//
//   /**
//    * 获取SQLite服务实例（用于需要直接访问SQLite功能的情况）
//    */
//   getSQLiteService(): SQLiteLibraryConfigService {
//     return this.sqliteService;
//   }
//
//   /**
//    * 获取所有表名
//    */
//   async getAllTables(): Promise<string[]> {
//     try {
//       // 合并两个数据库服务的表名
//       const sqliteVecTables = await this.sqliteVecService.getAllTables();
//       const sqliteTables = await this.sqliteService.getAllTables();
//
//       // 去重合并
//       return [...new Set([...sqliteVecTables, ...sqliteTables])];
//     } catch (error) {
//       console.error('获取所有表名失败:', error);
//       return [];
//     }
//   }
//
//   /**
//    * 获取指定表的数据
//    */
//   async getTableData(tableName: string, page: number = 1, pageSize: number = 10): Promise<{ data: any[]; total: number }> {
//     try {
//       // 检查表是否存在于 SQLiteVec 数据库
//       const vecTables = await this.sqliteVecService.getAllTables();
//       if (vecTables.includes(tableName)) {
//         return this.sqliteVecService.getTableData(tableName, page, pageSize);
//       }
//
//       // 检查表是否存在于 SQLite 配置数据库
//       const sqliteTables = await this.sqliteService.getAllTables();
//       if (sqliteTables.includes(tableName)) {
//         return this.sqliteService.getTableData(tableName, page, pageSize);
//       }
//
//       throw new Error(`表 ${tableName} 不存在`);
//     } catch (error) {
//       console.error(`获取表 ${tableName} 数据失败:`, error);
//       return {
//         data: [],
//         total: 0
//       };
//     }
//   }
// }