// import { ImageRecord, InsertResult, QueryParams, QueryResult, BaseResult, ExistsResult, LibraryConfig, LibraryConfigInsertResult, LibraryConfigQueryParams, LibraryConfigQueryResult } from '@shared/types/database';
// import {AIService} from "../ai/AIService.ts";
//
// export interface DatabaseService {
//   /** 测试数据库连接 */
//   testConnection(checkTables?: boolean): Promise<boolean>;
//   /** 初始化数据库（建表、建索引等） */
//   initDatabase(): Promise<boolean>;
//   /** 清空数据库 */
//   clearDatabase(): Promise<boolean>;
//   /** 批量插入图片数据 */
//   insertImages(images: ImageRecord[]): Promise<InsertResult>;
//   /** 查询图片（支持向量检索和条件过滤） */
//   queryImages(params: QueryParams): Promise<QueryResult>;
//   /** 删除图片 */
//   deleteImage(id: string): Promise<BaseResult>;
//   /** 查询所有唯一标签 */
//   queryAllTags(): Promise<{ tags: string[]; error?: string }>;
//   /** 混合搜索：标量过滤 + 语义搜索 */
//   hybridSearch(params: {
//     query: string;
//     keywords: string[];
//     embedding: number[];
//     limit?: number;
//   }): Promise<QueryResult>;
//   /** 检查图片是否已存在（通过文件路径和MD5） */
//   checkImageExists(filePath: string, md5?: string): Promise<ExistsResult>;
//   /** 向量相似度搜索 */
//   vectorSimilaritySearch(embedding: number[], limit?: number, threshold?: number): Promise<QueryResult>;
//   /** 按图片ID搜索相似图片 */
//   searchSimilarImages(imageId: string, options?: { threshold?: number; limit?: number }): Promise<QueryResult>;
//
//   // 图片库配置相关方法
//   /** 插入图片库配置 */
//   insertLibraryConfigs(configs: LibraryConfig[]): Promise<LibraryConfigInsertResult>;
//   /** 查询图片库配置 */
//   queryLibraryConfigs(params: LibraryConfigQueryParams): Promise<LibraryConfigQueryResult>;
//   /** 更新图片库配置 */
//   updateLibraryConfig(id: string, updates: Partial<LibraryConfig>): Promise<BaseResult>;
//   /** 删除图片库配置 */
//   deleteLibraryConfig(id: string): Promise<BaseResult>;
//   /** 获取活跃的图片库配置 */
//   getActiveLibraryConfigs(): Promise<LibraryConfigQueryResult>;
//
//   // 地理位置相关方法
//   /** 按地理位置查询图片 */
//   queryImagesByLocation(latitude: number, longitude: number, radius?: number, limit?: number): Promise<any[]>;
//   /** 按城市或国家查询图片 */
//   queryImagesByLocationName(location: string, limit?: number): Promise<any[]>;
//   /** 获取图片EXIF数据 */
//   getImageExifData(imageId: string): Promise<any>;
//   /** 更新图片位置信息 */
//   updateImageLocation(imageId: string, locationData: {
//     latitude?: number;
//     longitude?: number;
//     address?: string;
//     city?: string;
//     country?: string;
//   }): Promise<boolean>;
//
//   // 时间相关方法
//   /** 按拍摄时间范围查询图片 */
//   queryImagesByTimeRange(startTime: Date, endTime: Date, limit?: number): Promise<any[]>;
//   /** 按拍摄日期查询图片 */
//   queryImagesByDate(date: Date, limit?: number): Promise<any[]>;
//
//   // 高级搜索方法
//   /** 增强混合搜索：集成相似tag检索 */
//   enhancedHybridSearch(params: {
//     query: string;
//     limit?: number;
//     expandKeywords?: boolean;
//     similarityThreshold?: number;
//   }): Promise<any>;
//   /** 构建标签向量库 */
//   buildTagEmbeddings(): Promise<boolean>;
//   /** 查找相似标签 */
//   findSimilarTags(query: string, options?: {
//     limit?: number;
//     threshold?: number;
//     category?: string;
//   }): Promise<any[]>;
//   /** 批量查找相似标签 */
//   findSimilarTagsBatch(queries: string[], options?: any): Promise<Map<string, any>>;
//   /** 更新标签向量库 */
//   updateTagEmbeddings(newTags: string[]): Promise<void>;
//   /** 查询标签向量库 */
//   queryTagEmbeddings(limit?: number): Promise<{ tags: any[]; error?: string }>;
//
//   /** 设置AI服务 */
//   setAIService?(aiService: AIService): void;
//
//   /** 获取所有表名 */
//   getAllTables(): Promise<string[]>;
//
//   /** 分页查询表数据 */
//   getTableData(tableName: string, page: number, pageSize: number): Promise<{
//     data: any[];
//     total: number;
//   }>;
//
//   /** 关闭数据库连接 */
//   close(): Promise<void>;
// }