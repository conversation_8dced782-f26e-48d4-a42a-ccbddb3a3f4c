import { AIService, ImageAnalysisResult, EmbeddingResult } from '../ai/AIService'
import * as fs from 'node:fs/promises'
import { existsSync } from 'node:fs'

/**
 * AI分析业务服务
 * 封装AI相关业务逻辑，直接调用AI API
 */
export class AIAnalysisService {
  constructor(private aiService: AIService) {}

  // ============= 图片分析 =============

  /**
   * 分析图片（通过base64数据）
   */
  async analyzeImage(imageBase64: string): Promise<ImageAnalysisResult> {
    if (!this.aiService) {
      throw new Error('AI service not initialized')
    }

    return await this.aiService.analyzeImage(imageBase64)
  }

  /**
   * 分析图片（通过文件路径）
   */
  async analyzeImageByPath(imagePath: string): Promise<ImageAnalysisResult> {
    if (!this.aiService) {
      throw new Error('AI service not initialized')
    }

    // 验证文件是否存在
    if (!existsSync(imagePath)) {
      throw new Error(`Image file does not exist: ${imagePath}`)
    }

    try {
      // 读取文件并转换为base64
      const buffer = await fs.readFile(imagePath)
      const base64Data = buffer.toString('base64')
      
      return await this.aiService.analyzeImage(base64Data)
    } catch (error) {
      throw new Error(`Failed to analyze image: ${error instanceof Error ? error.message : String(error)}`)
    }
  }

  /**
   * 批量分析图片
   */
  async analyzeImagesBatch(imagePaths: string[], batchSize: number = 3): Promise<Array<{
    imagePath: string
    result?: ImageAnalysisResult
    error?: string
  }>> {
    const results = []
    
    // 分批处理以控制并发
    for (let i = 0; i < imagePaths.length; i += batchSize) {
      const batch = imagePaths.slice(i, i + batchSize)
      
      const batchPromises = batch.map(async (imagePath) => {
        try {
          const result = await this.analyzeImageByPath(imagePath)
          return { imagePath, result }
        } catch (error) {
          return {
            imagePath,
            error: error instanceof Error ? error.message : String(error)
          }
        }
      })

      const batchResults = await Promise.all(batchPromises)
      results.push(...batchResults)
    }

    return results
  }

  // ============= 向量生成 =============

  /**
   * 生成文本向量
   */
  async generateEmbedding(text: string): Promise<number[]> {
    if (!this.aiService) {
      throw new Error('AI service not initialized')
    }

    if (!text || text.trim().length === 0) {
      throw new Error('Text cannot be empty')
    }

    try {
      const result = await this.aiService.generateEmbedding(text)
      return result.embedding
    } catch (error) {
      throw new Error(`Failed to generate embedding: ${error instanceof Error ? error.message : String(error)}`)
    }
  }

  /**
   * 批量生成文本向量
   */
  async generateEmbeddingsBatch(texts: string[], batchSize: number = 5): Promise<Array<{
    text: string
    embedding?: number[]
    error?: string
  }>> {
    const results = []
    
    // 分批处理以控制并发
    for (let i = 0; i < texts.length; i += batchSize) {
      const batch = texts.slice(i, i + batchSize)
      
      const batchPromises = batch.map(async (text) => {
        try {
          const embedding = await this.generateEmbedding(text)
          return { text, embedding }
        } catch (error) {
          return {
            text,
            error: error instanceof Error ? error.message : String(error)
          }
        }
      })

      const batchResults = await Promise.all(batchPromises)
      results.push(...batchResults)
    }

    return results
  }

  // ============= 搜索查询解析 =============

  /**
   * 解析搜索查询
   */
  async parseSearchQuery(query: string): Promise<{
    keywords: string[]
    error?: string
  }> {
    if (!this.aiService) {
      return {
        keywords: this.fallbackKeywordExtraction(query),
        error: 'AI service not available, using fallback extraction'
      }
    }

    try {
      return await this.aiService.parseSearchQuery(query)
    } catch (error) {
      return {
        keywords: this.fallbackKeywordExtraction(query),
        error: error instanceof Error ? error.message : String(error)
      }
    }
  }

  /**
   * 智能搜索关键词提取和扩展
   */
  async extractAndExpandKeywords(query: string): Promise<{
    originalKeywords: string[]
    expandedKeywords: string[]
    searchEmbedding: number[]
  }> {
    // 1. 解析原始查询
    const parseResult = await this.parseSearchQuery(query)
    const originalKeywords = parseResult.keywords

    // 2. 生成查询向量
    const searchEmbedding = await this.generateEmbedding(query)

    // 3. 关键词扩展（基于同义词和相关词）
    const expandedKeywords = await this.expandKeywords(originalKeywords)

    return {
      originalKeywords,
      expandedKeywords,
      searchEmbedding
    }
  }

  // ============= 图片内容理解 =============

  /**
   * 提取图片中的文本标签（扁平化）
   */
  extractFlatTags(analysisResult: ImageAnalysisResult): string[] {
    const tags = new Set<string>()

    // 添加基础标签
    if (analysisResult.tags) {
      analysisResult.tags.forEach(tag => tags.add(tag.toLowerCase()))
    }

    // 添加结构化标签
    if (analysisResult.structured_data?.tags) {
      const structuredTags = analysisResult.structured_data.tags
      
      Object.values(structuredTags).forEach(tagArray => {
        if (Array.isArray(tagArray)) {
          tagArray.forEach(tag => tags.add(tag.toLowerCase()))
        }
      })
    }

    // 添加对象名称
    if (analysisResult.structured_data?.objects) {
      analysisResult.structured_data.objects.forEach(obj => {
        tags.add(obj.name.toLowerCase())
        obj.attributes.forEach(attr => tags.add(attr.toLowerCase()))
      })
    }

    // 添加主题相关标签
    if (analysisResult.structured_data?.theme) {
      const theme = analysisResult.structured_data.theme
      tags.add(theme.scene.toLowerCase())
      tags.add(theme.mood.toLowerCase())
      tags.add(theme.time.toLowerCase())
      tags.add(theme.style.toLowerCase())
    }

    return Array.from(tags).filter(tag => tag.length > 1)
  }

  /**
   * 生成图片搜索关键词
   */
  generateSearchKeywords(analysisResult: ImageAnalysisResult): string[] {
    const keywords = new Set<string>()

    // 从描述中提取关键词
    if (analysisResult.description) {
      const descKeywords = this.fallbackKeywordExtraction(analysisResult.description)
      descKeywords.forEach(keyword => keywords.add(keyword))
    }

    // 添加扁平化标签
    const flatTags = this.extractFlatTags(analysisResult)
    flatTags.forEach(tag => keywords.add(tag))

    return Array.from(keywords)
  }

  // ============= 辅助方法 =============

  /**
   * 备用关键词提取（当AI服务不可用时）
   */
  private fallbackKeywordExtraction(text: string): string[] {
    if (!text) return []

    return text
      .toLowerCase()
      .replace(/[^\w\s]/g, ' ')
      .split(/\s+/)
      .filter(word => word.length > 2)
      .filter(word => !this.isStopWord(word))
      .slice(0, 10)
  }

  /**
   * 检查是否为停用词
   */
  private isStopWord(word: string): boolean {
    const stopWords = new Set([
      'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for',
      'of', 'with', 'by', 'is', 'are', 'was', 'were', 'be', 'been', 'being',
      'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could',
      'should', 'this', 'that', 'these', 'those', 'i', 'you', 'he', 'she',
      'it', 'we', 'they', 'me', 'him', 'her', 'us', 'them', 'my', 'your',
      'his', 'her', 'its', 'our', 'their'
    ])

    return stopWords.has(word.toLowerCase())
  }

  /**
   * 关键词扩展（基于同义词）
   */
  private async expandKeywords(keywords: string[]): Promise<string[]> {
    const expanded = new Set(keywords)

    // 简单的同义词映射
    const synonyms: Record<string, string[]> = {
      'car': ['vehicle', 'automobile', 'auto'],
      'dog': ['canine', 'puppy', 'pet'],
      'cat': ['feline', 'kitten', 'pet'],
      'house': ['home', 'building', 'residence'],
      'person': ['people', 'human', 'individual'],
      'food': ['meal', 'dish', 'cuisine'],
      'water': ['ocean', 'sea', 'lake', 'river'],
      'tree': ['forest', 'woods', 'nature'],
      'sky': ['clouds', 'heaven', 'atmosphere']
    }

    keywords.forEach(keyword => {
      const lowerKeyword = keyword.toLowerCase()
      if (synonyms[lowerKeyword]) {
        synonyms[lowerKeyword].forEach(synonym => expanded.add(synonym))
      }
    })

    return Array.from(expanded)
  }

  /**
   * 计算向量相似度
   */
  calculateCosineSimilarity(vector1: number[], vector2: number[]): number {
    if (vector1.length !== vector2.length) {
      throw new Error('Vectors must have the same length')
    }

    let dotProduct = 0
    let norm1 = 0
    let norm2 = 0

    for (let i = 0; i < vector1.length; i++) {
      dotProduct += vector1[i] * vector2[i]
      norm1 += vector1[i] * vector1[i]
      norm2 += vector2[i] * vector2[i]
    }

    if (norm1 === 0 || norm2 === 0) {
      return 0
    }

    return dotProduct / (Math.sqrt(norm1) * Math.sqrt(norm2))
  }

  // ============= 服务状态检查 =============

  /**
   * 测试AI服务连接
   */
  async testConnection(): Promise<boolean> {
    if (!this.aiService) {
      return false
    }

    try {
      return await this.aiService.testConnection()
    } catch (error) {
      console.error('AI service connection test failed:', error)
      return false
    }
  }

  /**
   * 检查AI服务是否可用
   */
  isAvailable(): boolean {
    return this.aiService !== null && this.aiService !== undefined
  }

  /**
   * 获取AI服务状态
   */
  async getServiceStatus(): Promise<{
    available: boolean
    connected: boolean
    error?: string
  }> {
    const available = this.isAvailable()
    
    if (!available) {
      return {
        available: false,
        connected: false,
        error: 'AI service not initialized'
      }
    }

    try {
      const connected = await this.testConnection()
      return {
        available: true,
        connected
      }
    } catch (error) {
      return {
        available: true,
        connected: false,
        error: error instanceof Error ? error.message : String(error)
      }
    }
  }
}