import * as fs from 'fs';
import * as path from 'path';
import { ExifMetadata } from '../../src/types/database';

/**
 * EXIF数据提取服务
 * 用于从图片文件中提取位置、时间和相机信息
 */
export class ExifService {
  /**
   * 从图片文件中提取EXIF元数据
   */
  async extractExifData(filePath: string): Promise<ExifMetadata | null> {
    try {
      // 检查文件是否存在
      if (!fs.existsSync(filePath)) {
        console.warn(`文件不存在: ${filePath}`);
        return null;
      }

      // 动态导入exifr库（需要安装：npm install exifr）
      const exifr = await this.loadExifrLibrary();
      if (!exifr) {
        console.warn('EXIF库未安装，跳过EXIF数据提取');
        return null;
      }

      // 提取EXIF数据
      const exifData = await exifr.parse(filePath, {
        gps: true,
        tiff: true,
        exif: true,
        ifd0: true,
        ifd1: true,
        interop: true,
        makerNote: false, // 跳过厂商特定数据以提高性能
      });

      if (!exifData) {
        return null;
      }

      // 转换为标准化的EXIF元数据格式
      return this.normalizeExifData(exifData);
    } catch (error) {
      console.error(`提取EXIF数据失败 (${filePath}):`, error);
      return null;
    }
  }

  /**
   * 从EXIF数据中提取GPS位置信息
   */
  extractLocationFromExif(exifData: ExifMetadata): {
    latitude?: number;
    longitude?: number;
    altitude?: number;
  } | null {
    if (!exifData.gps) {
      return null;
    }

    const { gps } = exifData;
    
    // 检查是否有有效的GPS坐标
    if (typeof gps.latitude !== 'number' || typeof gps.longitude !== 'number') {
      return null;
    }

    return {
      latitude: gps.latitude,
      longitude: gps.longitude,
      altitude: gps.altitude,
    };
  }

  /**
   * 从EXIF数据中提取拍摄时间
   */
  extractCaptureTimeFromExif(exifData: ExifMetadata): Date | null {
    // 优先使用原始拍摄时间
    if (exifData.dateTimeOriginal) {
      const date = this.parseExifDateTime(exifData.dateTimeOriginal);
      if (date) return date;
    }

    // 其次使用数字化时间
    if (exifData.dateTimeDigitized) {
      const date = this.parseExifDateTime(exifData.dateTimeDigitized);
      if (date) return date;
    }

    // 最后使用文件修改时间
    if (exifData.dateTime) {
      const date = this.parseExifDateTime(exifData.dateTime);
      if (date) return date;
    }

    return null;
  }

  /**
   * 批量提取多个图片的EXIF数据
   */
  async batchExtractExifData(filePaths: string[]): Promise<Map<string, ExifMetadata | null>> {
    const results = new Map<string, ExifMetadata | null>();
    
    // 并发处理，但限制并发数量以避免内存问题
    const concurrency = 5;
    const chunks = this.chunkArray(filePaths, concurrency);
    
    for (const chunk of chunks) {
      const promises = chunk.map(async (filePath) => {
        const exifData = await this.extractExifData(filePath);
        results.set(filePath, exifData);
      });
      
      await Promise.all(promises);
    }
    
    return results;
  }

  /**
   * 检查图片是否包含GPS信息
   */
  async hasGpsData(filePath: string): Promise<boolean> {
    try {
      const exifData = await this.extractExifData(filePath);
      return !!(exifData?.gps?.latitude && exifData?.gps?.longitude);
    } catch (error) {
      return false;
    }
  }

  /**
   * 动态加载exifr库
   */
  private async loadExifrLibrary(): Promise<any> {
    try {
      // 尝试动态导入exifr
      const exifr = await import('exifr');
      return exifr.default || exifr;
    } catch (error) {
      console.warn('exifr库未安装，请运行: npm install exifr');
      return null;
    }
  }

  /**
   * 标准化EXIF数据格式
   */
  private normalizeExifData(rawExifData: any): ExifMetadata {
    const exifData: ExifMetadata = {};

    // 时间信息
    if (rawExifData.DateTimeOriginal) {
      exifData.dateTimeOriginal = this.formatExifDateTime(rawExifData.DateTimeOriginal);
    }
    if (rawExifData.DateTimeDigitized) {
      exifData.dateTimeDigitized = this.formatExifDateTime(rawExifData.DateTimeDigitized);
    }
    if (rawExifData.DateTime) {
      exifData.dateTime = this.formatExifDateTime(rawExifData.DateTime);
    }

    // GPS信息
    if (rawExifData.latitude && rawExifData.longitude) {
      exifData.gps = {
        latitude: rawExifData.latitude,
        longitude: rawExifData.longitude,
        altitude: rawExifData.altitude,
        latitudeRef: rawExifData.GPSLatitudeRef,
        longitudeRef: rawExifData.GPSLongitudeRef,
        altitudeRef: rawExifData.GPSAltitudeRef,
        timestamp: rawExifData.GPSTimeStamp,
        datestamp: rawExifData.GPSDateStamp,
        processingMethod: rawExifData.GPSProcessingMethod,
        areaInformation: rawExifData.GPSAreaInformation,
      };
    }

    // 相机信息
    exifData.camera = {
      make: rawExifData.Make,
      model: rawExifData.Model,
      software: rawExifData.Software,
      artist: rawExifData.Artist,
      copyright: rawExifData.Copyright,
    };

    // 拍摄参数
    exifData.settings = {
      exposureTime: rawExifData.ExposureTime,
      fNumber: rawExifData.FNumber,
      iso: rawExifData.ISO,
      focalLength: rawExifData.FocalLength,
      flash: rawExifData.Flash,
      whiteBalance: rawExifData.WhiteBalance,
      exposureMode: rawExifData.ExposureMode,
      meteringMode: rawExifData.MeteringMode,
      orientation: rawExifData.Orientation,
    };

    // 技术信息
    exifData.technical = {
      colorSpace: rawExifData.ColorSpace,
      pixelXDimension: rawExifData.PixelXDimension,
      pixelYDimension: rawExifData.PixelYDimension,
      compression: rawExifData.Compression,
      photometricInterpretation: rawExifData.PhotometricInterpretation,
      samplesPerPixel: rawExifData.SamplesPerPixel,
      bitsPerSample: rawExifData.BitsPerSample,
    };

    return exifData;
  }

  /**
   * 格式化EXIF日期时间
   */
  private formatExifDateTime(dateTime: any): string {
    if (dateTime instanceof Date) {
      return dateTime.toISOString().replace('T', ' ').substring(0, 19);
    }
    if (typeof dateTime === 'string') {
      return dateTime;
    }
    return '';
  }

  /**
   * 解析EXIF日期时间字符串
   */
  private parseExifDateTime(dateTimeStr: string): Date | null {
    try {
      // EXIF日期格式通常是 "YYYY:MM:DD HH:MM:SS"
      // 转换为 "YYYY-MM-DD HH:MM:SS" 格式以便 Date 构造函数解析
      const normalizedStr = dateTimeStr.replace(/^(\d{4}):(\d{2}):(\d{2})/, '$1-$2-$3');
      const date = new Date(normalizedStr);
      
      if (isNaN(date.getTime())) {
        return null;
      }
      
      return date;
    } catch (error) {
      return null;
    }
  }

  /**
   * 将数组分块
   */
  private chunkArray<T>(array: T[], chunkSize: number): T[][] {
    const chunks: T[][] = [];
    for (let i = 0; i < array.length; i += chunkSize) {
      chunks.push(array.slice(i, i + chunkSize));
    }
    return chunks;
  }
}

export default ExifService;