/**
 * 服务状态枚举
 */
export enum ServiceStatus {
  NOT_STARTED = 'not_started',
  INITIALIZING = 'initializing',
  READY = 'ready',
  ERROR = 'error'
}

/**
 * 重试状态信息
 */
export interface RetryInfo {
  attempts: number;
  maxAttempts: number;
  lastAttemptTime?: number;
  isRetrying: boolean;
  retryDelay: number;
}

/**
 * 单个服务的状态信息
 */
export interface ServiceInfo {
  status: ServiceStatus;
  error?: string;
  initTime?: number;
  startTime?: number;
  retry?: RetryInfo;
}

/**
 * 工具系统特定的状态信息
 */
export interface ToolSystemInfo extends ServiceInfo {
  availableTools?: number;
  loadedTools?: string[];
  failedTools?: string[];
}

/**
 * 所有服务的状态
 */
export interface ServiceState {
  database: ServiceInfo;
  aiService: ServiceInfo;
  toolSystem: ToolSystemInfo;
  imageLibrary: ServiceInfo;
}

/**
 * 服务状态变化事件
 */
export interface ServiceStateChangeEvent {
  service: keyof ServiceState;
  previousStatus: ServiceStatus;
  currentStatus: ServiceStatus;
  error?: string;
  timestamp: number;
}

/**
 * 启动性能指标
 */
export interface StartupMetrics {
  uiRenderTime?: number;
  databaseReadyTime?: number;
  aiServiceReadyTime?: number;
  toolSystemReadyTime?: number;
  imageLibraryReadyTime?: number;
  allServicesReadyTime?: number;
}