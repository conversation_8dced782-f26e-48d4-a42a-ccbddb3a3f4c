/**
 * Generic Task Management Types
 * Following KISS principle for simple and maintainable task management
 */

/**
 * Task status enumeration
 */
export type TaskStatus = 'pending' | 'running' | 'completed' | 'failed' | 'cancelled'

/**
 * Generic Task interface
 * Represents a unit of work that can be executed asynchronously
 */
export interface Task<T = any> {
  /** Unique task identifier */
  id: string
  
  /** Human-readable task name */
  name: string
  
  /** Task execution function */
  execute: () => Promise<T>
  
  /** Current task status */
  status: TaskStatus
  
  /** Task creation timestamp */
  createdAt: Date
  
  /** Task start timestamp */
  startedAt?: Date
  
  /** Task completion timestamp */
  completedAt?: Date
  
  /** Error information if task failed */
  error?: Error
  
  /** Task result if completed successfully */
  result?: T
  
  /** Optional progress callback */
  onProgress?: (progress: TaskProgress) => void
  
  /** Optional completion callback */
  onComplete?: (result: T) => void
  
  /** Optional error callback */
  onError?: (error: Error) => void
}

/**
 * Task progress information
 */
export interface TaskProgress {
  /** Current progress percentage (0-100) */
  percentage: number
  
  /** Current step description */
  currentStep?: string
  
  /** Total items to process */
  total?: number
  
  /** Items processed so far */
  processed?: number
  
  /** Items that failed processing */
  failed?: number
  
  /** Additional progress data */
  data?: any
}

/**
 * Task creation parameters
 * Used when adding a new task to the queue
 */
export interface TaskParams<T = any> {
  /** Human-readable task name */
  name: string
  
  /** Task execution function */
  execute: () => Promise<T>
  
  /** Optional progress callback */
  onProgress?: (progress: TaskProgress) => void
  
  /** Optional completion callback */
  onComplete?: (result: T) => void
  
  /** Optional error callback */
  onError?: (error: Error) => void
}

/**
 * Task queue status information
 */
export interface TaskQueueStatus {
  /** Number of pending tasks */
  pending: number
  
  /** Number of currently running tasks */
  running: number
  
  /** Number of completed tasks */
  completed: number
  
  /** Number of failed tasks */
  failed: number
  
  /** Number of cancelled tasks */
  cancelled: number
  
  /** Total number of tasks */
  total: number
}

/**
 * Task manager configuration
 */
export interface TaskManagerConfig {
  /** Processing interval in milliseconds (default: 1000) */
  intervalMs?: number
  
  /** Maximum number of concurrent tasks (default: 1) */
  maxConcurrent?: number
  
  /** Whether to automatically start processing (default: true) */
  autoStart?: boolean
  
  /** Task cleanup interval in milliseconds (default: 300000 = 5 minutes) */
  cleanupIntervalMs?: number
  
  /** How long to keep completed tasks in memory (default: 300000 = 5 minutes) */
  keepCompletedMs?: number
}
