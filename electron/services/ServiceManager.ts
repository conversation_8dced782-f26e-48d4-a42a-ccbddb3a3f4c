// import { config } from 'dotenv'
// import { OpenAIService, OpenAIConfig } from '../ai/OpenAIService'
// import { ImageLibraryScanService } from './ImageLibraryScanService'
// import { ErrorLogger } from './ErrorLogger'
//
// // 加载环境变量
// config()
//
// /**
//  * 服务管理器 - 统一管理所有服务的初始化和生命周期
//  */
// export class ServiceManager {
//   private databaseService: any = null
//   private libraryConfigService: any = null
//   private aiService: any = null
//   private scanService: ImageLibraryScanService | null = null
//   private logger: ErrorLogger
//
//   constructor() {
//     console.log('🏗️ 服务管理器初始化')
//     this.logger = new ErrorLogger()
//   }
//
//   /**
//    * 初始化AI服务
//    */
//   private async initAIService(testConnection: boolean = false) {
//     try {
//       console.log('🔧 开始初始化AI服务...')
//
//       // AI服务配置，可以通过环境变量覆盖
//       const aiConfig: OpenAIConfig = {
//         vlBaseURL: process.env.AI_VL_BASE_URL || "",
//         vlApiKey: process.env.AI_VL_API_KEY || "",
//         vlModel: process.env.AI_VL_MODEL || "",
//         chatBaseURL: process.env.AI_CHAT_BASE_URL || "",
//         chatApiKey: process.env.AI_CHAT_API_KEY || "",
//         chatModel: process.env.AI_CHAT_MODEL || "",
//         embeddingModel: process.env.AI_EMBEDDING_MODEL || "",
//         embeddingBaseURL: process.env.AI_EMBEDDING_BASE_URL,
//         embeddingApiKey: process.env.AI_EMBEDDING_KEY,
//         timeout: 90000,
//         maxRetries: 3
//       }
//
//       this.aiService = new OpenAIService(aiConfig)
//
//       // 如果数据库服务已经初始化，设置AI服务
//       if (this.databaseService) {
//         this.databaseService.setAIService(this.aiService);
//       }
//
//       // 只有在明确要求时才测试连接
//       if (testConnection) {
//         console.log('🔗 测试AI服务连接...')
//         const connected = await this.aiService.testConnection()
//         if (connected) {
//           console.log('✅ AI服务连接成功')
//         } else {
//           console.warn('⚠️ AI服务连接失败，某些功能可能不可用')
//         }
//       }
//
//       console.log('✅ AI服务初始化完成')
//     } catch (error) {
//       console.error('❌ 初始化AI服务失败:', error)
//     }
//   }
//
//   /**
//    * 初始化数据库服务
//    */
//   private async initDatabase(testConnection: boolean = false) {
//     try {
//       console.log('🔄 初始化混合数据库服务...')
//
//       // SQLite数据库路径配置
//       const sqliteVecDbPath = process.env.SQLITE_VEC_DB_PATH || './data/sqlite-vec.db'
//       const sqliteConfigDbPath = process.env.SQLITE_CONFIG_DB_PATH || './data/library_config.db'
//
//       // 创建混合数据库服务实例
//       this.databaseService = new HybridDatabaseService(sqliteVecDbPath, sqliteConfigDbPath)
//       this.libraryConfigService = this.databaseService.sqliteService
//
//       // 如果AI服务已经初始化，设置到数据库服务中
//       if (this.aiService) {
//         this.databaseService.setAIService(this.aiService);
//       }
//
//       console.log('✅ 数据库服务初始化完成')
//     } catch (error) {
//       console.error('❌ 初始化数据库服务失败:', error)
//     }
//   }
//
//   /**
//    * 初始化扫描服务
//    */
//   private async initScanService() {
//     try {
//       console.log('🔧 开始初始化扫描服务...')
//
//       if (!this.libraryConfigService) {
//         throw new Error('图书馆配置服务未初始化')
//       }
//
//       if (!this.databaseService) {
//         throw new Error('数据库服务未初始化')
//       }
//
//       if (!this.aiService) {
//         throw new Error('AI服务未初始化')
//       }
//
//       this.scanService = new ImageLibraryScanService(
//         this.libraryConfigService,
//         this.databaseService,
//         this.aiService,
//         this.logger
//       )
//
//       console.log('✅ 扫描服务初始化完成')
//     } catch (error) {
//       console.error('❌ 初始化扫描服务失败:', error)
//     }
//   }
//
//   /**
//    * 初始化所有服务
//    */
//   async initializeAll(options: { testConnections?: boolean } = {}) {
//     const { testConnections = false } = options
//
//     console.log('🚀 开始初始化所有服务...')
//
//     // 按依赖顺序初始化服务
//     await this.initDatabase(testConnections)
//     await this.initAIService(testConnections)
//     await this.initScanService()
//
//     console.log('✅ 所有服务初始化完成')
//   }
//
//   /**
//    * 获取所有服务实例
//    */
//   getServices() {
//     return {
//       databaseService: this.databaseService,
//       aiService: this.aiService,
//       libraryConfigService: this.libraryConfigService,
//       scanService: this.scanService
//     }
//   }
//
//   /**
//    * 关闭所有服务
//    */
//   async closeAll() {
//     console.log('🔄 关闭所有服务...')
//
//     // 关闭扫描服务
//     if (this.scanService) {
//       try {
//         this.scanService.destroy()
//         console.log('✅ 扫描服务已关闭')
//       } catch (error) {
//         console.error('❌ 关闭扫描服务失败:', error)
//       }
//     }
//
//     // 关闭数据库服务
//     if (this.databaseService) {
//       try {
//         await this.databaseService.close()
//         console.log('✅ 数据库服务已关闭')
//       } catch (error) {
//         console.error('❌ 关闭数据库服务失败:', error)
//       }
//     }
//
//     console.log('✅ 所有服务已关闭')
//   }
// }