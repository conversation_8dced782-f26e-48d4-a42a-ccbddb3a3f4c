import { BrowserWindow, <PERSON><PERSON>, screen } from 'electron'
import path from 'node:path'
import fs from 'node:fs'
import { fileURLToPath } from 'node:url'

const __dirname = path.dirname(fileURLToPath(import.meta.url))

// 从环境变量获取配置
const VITE_DEV_SERVER_URL = process.env['VITE_DEV_SERVER_URL']
// 修正APP_ROOT路径计算 - 在开发模式下，__dirname是dist-electron，需要回到项目根目录
const APP_ROOT = process.env.APP_ROOT || path.join(__dirname, '..')
const RENDERER_DIST = path.join(APP_ROOT, 'dist')

console.log('🔧 调试路径信息:')
console.log('  __dirname:', __dirname)
console.log('  APP_ROOT:', APP_ROOT)
console.log('  RENDERER_DIST:', RENDERER_DIST)

// 获取正确的 preload 脚本路径
function getPreloadPath(): string {
  const preloadPath = path.join(APP_ROOT, 'dist-electron', 'preload.mjs')
  console.log('📄 计算的 preload 路径:', preloadPath)
  
  // 检查文件是否存在
  try {
    const exists = fs.existsSync(preloadPath)
    console.log('📄 preload 文件存在:', exists)
    if (!exists) {
      console.error('❌ preload 文件不存在:', preloadPath)
      // 尝试列出 dist-electron 目录内容
      const distElectronDir = path.join(APP_ROOT, 'dist-electron')
      if (fs.existsSync(distElectronDir)) {
        const files = fs.readdirSync(distElectronDir)
        console.log('📁 dist-electron 目录内容:', files)
      } else {
        console.error('❌ dist-electron 目录不存在:', distElectronDir)
      }
    }
  } catch (error) {
    console.error('❌ 检查 preload 文件时出错:', error)
  }
  
  return preloadPath
}

/**
 * 窗口管理器 - 负责创建和管理应用窗口
 */
export class WindowManager {
  private mainWindow: BrowserWindow | null = null

  constructor() {
    console.log('🪟 窗口管理器初始化')
  }

  /**
   * 创建主应用窗口
   */
  createMainWindow(): BrowserWindow {
    console.log('🪟 创建主应用窗口...')

    const preloadPath = getPreloadPath()
    console.log('📄 使用 preload 脚本:', preloadPath)

    // 获取主显示器尺寸
    const primaryDisplay = screen.getPrimaryDisplay()
    const { width: screenWidth, height: screenHeight } = primaryDisplay.workAreaSize
    
    // 计算窗口尺寸为屏幕的4/5
    const windowWidth = Math.floor(screenWidth * 4 / 5)
    const windowHeight = Math.floor(screenHeight * 4 / 5)
    
    // 计算居中位置
    const x = Math.floor((screenWidth - windowWidth) / 2)
    const y = Math.floor((screenHeight - windowHeight) / 2)

    this.mainWindow = new BrowserWindow({
      width: windowWidth,
      height: windowHeight,
      x: x,
      y: y,
      icon: path.join(process.env.VITE_PUBLIC || '', 'electron-vite.svg'),
      webPreferences: {
        preload: preloadPath,
        nodeIntegration: false,
        contextIsolation: true,
        sandbox: false, // 确保 contextBridge 可以正常工作
      },
    })

    // 移除默认菜单栏
    Menu.setApplicationMenu(null)

    // 设置窗口事件处理
    this.setupWindowEvents()

    // 加载应用内容
    this.loadContent()

    console.log('✅ 主应用窗口创建完成')
    return this.mainWindow
  }

  /**
   * 设置窗口事件处理
   */
  private setupWindowEvents() {
    if (!this.mainWindow) return

    // 页面加载完成事件
    this.mainWindow.webContents.on('did-finish-load', () => {
      if (!this.mainWindow || this.mainWindow.isDestroyed()) return

      console.log('🪟 页面加载完成')

      // 发送启动消息
      this.mainWindow.webContents.send('main-process-message', new Date().toLocaleString())

      // 开发模式下打开开发者工具
      if (VITE_DEV_SERVER_URL) {
        this.mainWindow.webContents.openDevTools()
      }
    })

    // preload 脚本加载事件
    this.mainWindow.webContents.on('preload-error', (event, preloadPath, error) => {
      console.error('❌ Preload 脚本加载失败:', preloadPath, error)
    })

    // 控制台消息事件（捕获 preload 脚本的 console.log）
    this.mainWindow.webContents.on('console-message', (event, level, message, line, sourceId) => {
      if (message.includes('Preload script') || message.includes('electronAPI')) {
        try {
          console.log(`🔧 [Renderer Console] ${message}`)
        } catch (error: unknown) {
          // 忽略 EPIPE 错误，防止应用崩溃
          if (error && typeof error === 'object' && 'code' in error && error.code !== 'EPIPE') {
            console.error('Console output error:', error)
          }
        }
      }
    })

    // 窗口关闭事件
    this.mainWindow.on('closed', () => {
      this.mainWindow = null
      console.log('🪟 主窗口已关闭')
    })
  }

  /**
   * 加载应用内容
   */
  private loadContent() {
    if (!this.mainWindow) return

    if (VITE_DEV_SERVER_URL) {
      console.log('🔗 加载开发服务器内容:', VITE_DEV_SERVER_URL)
      this.mainWindow.loadURL(VITE_DEV_SERVER_URL)
    } else {
      const indexPath = path.join(RENDERER_DIST, 'index.html')
      console.log('📄 加载生产版本内容:', indexPath)
      this.mainWindow.loadFile(indexPath)
    }
  }

  /**
   * 获取主窗口实例
   */
  getMainWindow(): BrowserWindow | null {
    return this.mainWindow
  }

  /**
   * 发送消息到主窗口
   */
  sendToMainWindow(channel: string, data: any) {
    if (this.mainWindow && !this.mainWindow.isDestroyed()) {
      this.mainWindow.webContents.send(channel, data)
    }
  }

  /**
   * 关闭所有窗口
   */
  closeAll() {
    if (this.mainWindow && !this.mainWindow.isDestroyed()) {
      this.mainWindow.close()
    }
  }
}