import { Task, TaskParams, TaskQueueStatus, TaskManagerConfig, TaskStatus } from './types/Task'
import { randomUUID } from 'crypto'

/**
 * Generic Task Manager
 * Simple task queue system following KISS principle
 * 
 * Features:
 * - FIFO task queue
 * - Timer-based processing
 * - Progress tracking
 * - Error handling
 * - Automatic cleanup
 */
export class TaskManager {
  private tasks = new Map<string, Task>()
  private queue: string[] = []
  private processingTimer: NodeJS.Timeout | null = null
  private cleanupTimer: NodeJS.Timeout | null = null
  private isProcessing = false
  
  private readonly config: Required<TaskManagerConfig>
  
  constructor(config: TaskManagerConfig = {}) {
    this.config = {
      intervalMs: config.intervalMs ?? 1000,
      maxConcurrent: config.maxConcurrent ?? 1,
      autoStart: config.autoStart ?? true,
      cleanupIntervalMs: config.cleanupIntervalMs ?? 300000, // 5 minutes
      keepCompletedMs: config.keepCompletedMs ?? 300000 // 5 minutes
    }
    
    if (this.config.autoStart) {
      this.start()
    }
  }
  
  /**
   * Add a new task to the queue
   */
  addTask<T = any>(params: TaskParams<T>): string {
    const taskId = randomUUID()
    
    const task: Task<T> = {
      id: taskId,
      name: params.name,
      execute: params.execute,
      status: 'pending',
      createdAt: new Date(),
      onProgress: params.onProgress,
      onComplete: params.onComplete,
      onError: params.onError
    }
    
    this.tasks.set(taskId, task)
    this.queue.push(taskId)
    
    console.log(`[TaskManager] Task added: ${task.name} (${taskId})`)
    
    return taskId
  }
  
  /**
   * Get task by ID
   */
  getTask(taskId: string): Task | undefined {
    return this.tasks.get(taskId)
  }
  
  /**
   * Remove task from queue and memory
   */
  removeTask(taskId: string): boolean {
    const task = this.tasks.get(taskId)
    if (!task) return false
    
    // Remove from queue if pending
    const queueIndex = this.queue.indexOf(taskId)
    if (queueIndex !== -1) {
      this.queue.splice(queueIndex, 1)
    }
    
    // Remove from memory
    this.tasks.delete(taskId)
    
    console.log(`[TaskManager] Task removed: ${task.name} (${taskId})`)
    return true
  }
  
  /**
   * Cancel a pending or running task
   */
  cancelTask(taskId: string): boolean {
    const task = this.tasks.get(taskId)
    if (!task) return false
    
    if (task.status === 'pending' || task.status === 'running') {
      task.status = 'cancelled'
      task.completedAt = new Date()
      
      // Remove from queue if pending
      const queueIndex = this.queue.indexOf(taskId)
      if (queueIndex !== -1) {
        this.queue.splice(queueIndex, 1)
      }
      
      console.log(`[TaskManager] Task cancelled: ${task.name} (${taskId})`)
      return true
    }
    
    return false
  }
  
  /**
   * Get queue status
   */
  getQueueStatus(): TaskQueueStatus {
    const tasks = Array.from(this.tasks.values())
    
    return {
      pending: tasks.filter(t => t.status === 'pending').length,
      running: tasks.filter(t => t.status === 'running').length,
      completed: tasks.filter(t => t.status === 'completed').length,
      failed: tasks.filter(t => t.status === 'failed').length,
      cancelled: tasks.filter(t => t.status === 'cancelled').length,
      total: tasks.length
    }
  }
  
  /**
   * Get all tasks with optional status filter
   */
  getTasks(status?: TaskStatus): Task[] {
    const tasks = Array.from(this.tasks.values())
    return status ? tasks.filter(t => t.status === status) : tasks
  }
  
  /**
   * Start task processing
   */
  start(): void {
    if (this.processingTimer) return
    
    console.log('[TaskManager] Starting task processing')
    
    this.processingTimer = setInterval(() => {
      this.processNextTask()
    }, this.config.intervalMs)
    
    this.cleanupTimer = setInterval(() => {
      this.cleanupCompletedTasks()
    }, this.config.cleanupIntervalMs)
  }
  
  /**
   * Stop task processing
   */
  stop(): void {
    if (this.processingTimer) {
      clearInterval(this.processingTimer)
      this.processingTimer = null
    }
    
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer)
      this.cleanupTimer = null
    }
    
    console.log('[TaskManager] Task processing stopped')
  }
  
  /**
   * Process the next task in queue
   */
  private async processNextTask(): Promise<void> {
    if (this.isProcessing || this.queue.length === 0) {
      return
    }
    
    // Check if we've reached max concurrent tasks
    const runningTasks = this.getTasks('running').length
    if (runningTasks >= this.config.maxConcurrent) {
      return
    }
    
    this.isProcessing = true
    
    try {
      const taskId = this.queue.shift()
      if (!taskId) return
      
      const task = this.tasks.get(taskId)
      if (!task || task.status !== 'pending') return
      
      await this.executeTask(task)
    } catch (error) {
      console.error('[TaskManager] Error in processNextTask:', error)
    } finally {
      this.isProcessing = false
    }
  }
  
  /**
   * Execute a single task
   */
  private async executeTask(task: Task): Promise<void> {
    console.log(`[TaskManager] Executing task: ${task.name} (${task.id})`)
    
    task.status = 'running'
    task.startedAt = new Date()
    
    try {
      const result = await task.execute()
      
      task.status = 'completed'
      task.completedAt = new Date()
      task.result = result
      
      console.log(`[TaskManager] Task completed: ${task.name} (${task.id})`)
      
      if (task.onComplete) {
        task.onComplete(result)
      }
    } catch (error) {
      task.status = 'failed'
      task.completedAt = new Date()
      task.error = error instanceof Error ? error : new Error(String(error))
      
      console.error(`[TaskManager] Task failed: ${task.name} (${task.id})`, error)
      
      if (task.onError) {
        task.onError(task.error)
      }
    }
  }
  
  /**
   * Clean up old completed tasks
   */
  private cleanupCompletedTasks(): void {
    const now = Date.now()
    const tasksToRemove: string[] = []
    
    for (const [taskId, task] of this.tasks) {
      if (task.status === 'completed' || task.status === 'failed' || task.status === 'cancelled') {
        if (task.completedAt && (now - task.completedAt.getTime()) > this.config.keepCompletedMs) {
          tasksToRemove.push(taskId)
        }
      }
    }
    
    for (const taskId of tasksToRemove) {
      this.tasks.delete(taskId)
    }
    
    if (tasksToRemove.length > 0) {
      console.log(`[TaskManager] Cleaned up ${tasksToRemove.length} old tasks`)
    }
  }
  
  /**
   * Destroy the task manager
   */
  destroy(): void {
    this.stop()
    this.tasks.clear()
    this.queue.length = 0
    console.log('[TaskManager] Task manager destroyed')
  }
}
