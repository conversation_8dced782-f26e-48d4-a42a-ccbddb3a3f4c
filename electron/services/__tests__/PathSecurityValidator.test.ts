// // Tes   t file for PathSecurityValidator
// // Note: This requires vitest to be installed and configured
// import path from 'node:path'
// import { PathSecurityValidator } from '../PathSecurityValidator'
//
// describe('PathSecurityValidator', () => {
//     let validator: PathSecurityValidator
//     let allowedDir: string
//
//     beforeEach(() => {
//         allowedDir = path.join(__dirname, 'allowed')
//         validator = new PathSecurityValidator([allowedDir])
//     })
//
//     describe('validatePath', () => {
//         it('should validate safe paths', () => {
//             const result = validator.validatePath('safe-file.jpg')
//             expect(result.isValid).toBe(true)
//             expect(result.errors).toHaveLength(0)
//             expect(result.sanitizedPath).toBeDefined()
//         })
//
//         it('should reject empty or null paths', () => {
//             const result1 = validator.validatePath('')
//             const result2 = validator.validatePath(null as any)
//
//             expect(result1.isValid).toBe(false)
//             expect(result2.isValid).toBe(false)
//             expect(result1.errors.length).toBeGreaterThan(0)
//             expect(result2.errors.length).toBeGreaterThan(0)
//         })
//
//         it('should reject paths that are too long', () => {
//             const longPath = 'a'.repeat(300) + '.jpg'
//             const result = validator.validatePath(longPath)
//             expect(result.isValid).toBe(false)
//             expect(result.errors.some(error => error.includes('长度超过限制'))).toBe(true)
//         })
//
//         it('should reject path traversal attempts', () => {
//             const dangerousPaths = [
//                 '../../../etc/passwd',
//                 '..\\..\\..\\windows\\system32',
//                 './../../sensitive-file.txt',
//                 'normal/../../../etc/passwd'
//             ]
//
//             dangerousPaths.forEach(dangerousPath => {
//                 const result = validator.validatePath(dangerousPath)
//                 expect(result.isValid).toBe(false)
//                 expect(result.errors.length).toBeGreaterThan(0)
//             })
//         })
//
//         it('should reject paths with dangerous characters', () => {
//             const dangerousPaths = [
//                 'file<script>.jpg',
//                 'file>redirect.jpg',
//                 'file|pipe.jpg',
//                 'file?.jpg',
//                 'file*.jpg',
//                 'file\x00null.jpg',
//                 'CON.jpg',
//                 'PRN.jpg',
//                 'AUX.jpg'
//             ]
//
//             dangerousPaths.forEach(dangerousPath => {
//                 const result = validator.validatePath(dangerousPath)
//                 expect(result.isValid).toBe(false)
//                 expect(result.errors.length).toBeGreaterThan(0)
//             })
//         })
//
//         it('should sanitize paths correctly', () => {
//             const result = validator.validatePath('  /path//to///file.jpg  ')
//             expect(result.sanitizedPath).toBeDefined()
//             expect(result.sanitizedPath).not.toContain('  ')
//             expect(result.sanitizedPath).not.toContain('//')
//         })
//     })
//
//     describe('isPathAllowed', () => {
//         it('should allow paths within allowed directories', () => {
//             const allowedPath = path.join(allowedDir, 'subfolder', 'file.jpg')
//             expect(validator.isPathAllowed(allowedPath)).toBe(true)
//         })
//
//         it('should reject paths outside allowed directories', () => {
//             const disallowedPath = path.join(__dirname, 'not-allowed', 'file.jpg')
//             expect(validator.isPathAllowed(disallowedPath)).toBe(false)
//         })
//
//         it('should allow any path when no allowed directories are set', () => {
//             const openValidator = new PathSecurityValidator([])
//             const anyPath = '/any/path/file.jpg'
//             expect(openValidator.isPathAllowed(anyPath)).toBe(true)
//         })
//     })
//
//     describe('sanitizePath', () => {
//         it('should normalize path separators', () => {
//             const mixedPath = 'folder\\subfolder/file.jpg'
//             const sanitized = validator.sanitizePath(mixedPath)
//             expect(sanitized).not.toContain('\\/')
//             expect(sanitized).not.toContain('/\\')
//         })
//
//         it('should remove trailing separators', () => {
//             const pathWithTrailing = 'folder/subfolder/'
//             const sanitized = validator.sanitizePath(pathWithTrailing)
//             expect(sanitized).not.toEndWith('/')
//             expect(sanitized).not.toEndWith('\\')
//         })
//
//         it('should remove extra whitespace', () => {
//             const pathWithSpaces = '  folder/file.jpg  '
//             const sanitized = validator.sanitizePath(pathWithSpaces)
//             expect(sanitized).toBe('folder/file.jpg')
//         })
//
//         it('should collapse multiple separators', () => {
//             const pathWithMultiple = 'folder///subfolder\\\\\\file.jpg'
//             const sanitized = validator.sanitizePath(pathWithMultiple)
//             expect(sanitized).not.toContain('///')
//             expect(sanitized).not.toContain('\\\\\\')
//         })
//     })
//
//     describe('directory management', () => {
//         it('should get allowed directories', () => {
//             const dirs = validator.getAllowedDirectories()
//             expect(dirs).toContain(allowedDir)
//         })
//
//         it('should set allowed directories', () => {
//             const newDirs = ['/new/dir1', '/new/dir2']
//             validator.setAllowedDirectories(newDirs)
//             const dirs = validator.getAllowedDirectories()
//             expect(dirs).toHaveLength(2)
//         })
//
//         it('should add allowed directory', () => {
//             const newDir = '/new/directory'
//             validator.addAllowedDirectory(newDir)
//             const dirs = validator.getAllowedDirectories()
//             expect(dirs).toContain(path.resolve(newDir))
//         })
//
//         it('should remove allowed directory', () => {
//             validator.removeAllowedDirectory(allowedDir)
//             const dirs = validator.getAllowedDirectories()
//             expect(dirs).not.toContain(allowedDir)
//         })
//
//         it('should not add duplicate directories', () => {
//             const initialLength = validator.getAllowedDirectories().length
//             validator.addAllowedDirectory(allowedDir)
//             const finalLength = validator.getAllowedDirectories().length
//             expect(finalLength).toBe(initialLength)
//         })
//     })
//
//     describe('containsPathTraversal', () => {
//         it('should detect various path traversal patterns', () => {
//             const traversalPaths = [
//                 '../sensitive-file',
//                 '..\\sensitive-file',
//                 '/etc/passwd',
//                 'C:\\Windows\\System32',
//                 '/root/.ssh/id_rsa'
//             ]
//
//             traversalPaths.forEach(traversalPath => {
//                 const result = validator.validatePath(traversalPath)
//                 expect(result.isValid).toBe(false)
//             })
//         })
//
//         it('should allow safe relative paths', () => {
//             const safePaths = [
//                 'subfolder/file.jpg',
//                 'images/photo.png',
//                 'documents/report.pdf'
//             ]
//
//             safePaths.forEach(safePath => {
//                 const result = validator.validatePath(safePath)
//                 // Note: These might still fail due to allowed directory restrictions,
//                 // but they shouldn't fail due to path traversal detection
//                 const hasTraversalError = result.errors.some(error =>
//                     error.includes('路径遍历') || error.includes('危险字符')
//                 )
//                 expect(hasTraversalError).toBe(false)
//             })
//         })
//     })
// })