// // Test file for BackendImagePathService
// // Note: This requires vitest to be installed and configured
// import fs from 'node:fs'
// import path from 'node:path'
// import { BackendImagePathService } from '../BackendImagePathService'
// import { PathSecurityValidator } from '../PathSecurityValidator'
//
// // Mock electron app module
// const mockApp = {
//   getPath: (name: string) => {
//     if (name === 'userData') {
//       return path.join(__dirname, 'test-data')
//     }
//     return ''
//   }
// }
//
// // Mock electron module
// vi.mock('electron', () => ({
//   app: mockApp
// }))
//
// describe('BackendImagePathService', () => {
//   let service: BackendImagePathService
//   let testDir: string
//   let testImagePath: string
//
//   beforeEach(() => {
//     testDir = path.join(__dirname, 'test-data', 'images')
//     testImagePath = path.join(testDir, 'test-image.jpg')
//
//     // 创建测试目录和文件
//     fs.mkdirSync(testDir, { recursive: true })
//
//     // 创建一个简单的测试图片文件（1x1 JPEG）
//     const jpegHeader = Buffer.from([
//       0xFF, 0xD8, 0xFF, 0xE0, 0x00, 0x10, 0x4A, 0x46, 0x49, 0x46, 0x00, 0x01,
//       0x01, 0x01, 0x00, 0x48, 0x00, 0x48, 0x00, 0x00, 0xFF, 0xDB, 0x00, 0x43,
//       0x00, 0x08, 0x06, 0x06, 0x07, 0x06, 0x05, 0x08, 0x07, 0x07, 0x07, 0x09,
//       0x09, 0x08, 0x0A, 0x0C, 0x14, 0x0D, 0x0C, 0x0B, 0x0B, 0x0C, 0x19, 0x12,
//       0x13, 0x0F, 0x14, 0x1D, 0x1A, 0x1F, 0x1E, 0x1D, 0x1A, 0x1C, 0x1C, 0x20,
//       0x24, 0x2E, 0x27, 0x20, 0x22, 0x2C, 0x23, 0x1C, 0x1C, 0x28, 0x37, 0x29,
//       0x2C, 0x30, 0x31, 0x34, 0x34, 0x34, 0x1F, 0x27, 0x39, 0x3D, 0x38, 0x32,
//       0x3C, 0x2E, 0x33, 0x34, 0x32, 0xFF, 0xD9
//     ])
//     fs.writeFileSync(testImagePath, jpegHeader)
//
//     service = new BackendImagePathService()
//   })
//
//   afterEach(() => {
//     // 清理测试文件
//     if (fs.existsSync(testDir)) {
//       fs.rmSync(testDir, { recursive: true, force: true })
//     }
//   })
//
//   describe('validateImagePath', () => {
//     it('should validate safe paths', async () => {
//       const result = await service.validateImagePath('test-image.jpg')
//       expect(result.isValid).toBe(true)
//       expect(result.errors).toHaveLength(0)
//     })
//
//     it('should reject path traversal attempts', async () => {
//       const result = await service.validateImagePath('../../../etc/passwd')
//       expect(result.isValid).toBe(false)
//       expect(result.errors.length).toBeGreaterThan(0)
//     })
//
//     it('should reject paths with dangerous characters', async () => {
//       const result = await service.validateImagePath('test<script>.jpg')
//       expect(result.isValid).toBe(false)
//       expect(result.errors.length).toBeGreaterThan(0)
//     })
//   })
//
//   describe('getImageBuffer', () => {
//     it('should read existing image file', async () => {
//       const result = await service.getImageBuffer('test-image.jpg')
//       expect(result.success).toBe(true)
//       expect(result.data).toBeInstanceOf(Buffer)
//       expect(result.data!.length).toBeGreaterThan(0)
//     })
//
//     it('should fail for non-existent file', async () => {
//       const result = await service.getImageBuffer('non-existent.jpg')
//       expect(result.success).toBe(false)
//       expect(result.error).toContain('文件不存在')
//     })
//
//     it('should fail for invalid path', async () => {
//       const result = await service.getImageBuffer('../../../etc/passwd')
//       expect(result.success).toBe(false)
//       expect(result.error).toContain('路径验证失败')
//     })
//   })
//
//   describe('getImageBlob', () => {
//     it('should create blob from existing image', async () => {
//       const result = await service.getImageBlob('test-image.jpg')
//       expect(result.success).toBe(true)
//       expect(result.data).toBeInstanceOf(Blob)
//       expect(result.data!.type).toBe('image/jpeg')
//       expect(result.data!.size).toBeGreaterThan(0)
//     })
//
//     it('should handle different image formats', async () => {
//       // 创建PNG测试文件
//       const pngPath = path.join(testDir, 'test.png')
//       fs.writeFileSync(pngPath, Buffer.from([0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A]))
//
//       const result = await service.getImageBlob('test.png')
//       expect(result.success).toBe(true)
//       expect(result.data!.type).toBe('image/png')
//     })
//   })
//
//   describe('getImageMetadata', () => {
//     it('should return metadata for existing image', async () => {
//       const result = await service.getImageMetadata('test-image.jpg')
//       expect(result.success).toBe(true)
//       expect(result.data).toBeDefined()
//       expect(result.data!.format).toBe('jpg')
//       expect(result.data!.size).toBeGreaterThan(0)
//       expect(result.data!.checksum).toBeDefined()
//       expect(result.data!.createdAt).toBeInstanceOf(Date)
//       expect(result.data!.modifiedAt).toBeInstanceOf(Date)
//     })
//   })
//
//   describe('utility methods', () => {
//     it('should resolve relative paths correctly', () => {
//       const resolved = service.resolveImagePath('test.jpg')
//       expect(resolved).toContain('images')
//       expect(resolved).toContain('test.jpg')
//     })
//
//     it('should return absolute paths unchanged', () => {
//       const absolutePath = '/absolute/path/test.jpg'
//       const resolved = service.resolveImagePath(absolutePath)
//       expect(resolved).toBe(absolutePath)
//     })
//
//     it('should identify supported image formats', () => {
//       expect(service.isSupportedImageFormat('test.jpg')).toBe(true)
//       expect(service.isSupportedImageFormat('test.png')).toBe(true)
//       expect(service.isSupportedImageFormat('test.webp')).toBe(true)
//       expect(service.isSupportedImageFormat('test.txt')).toBe(false)
//     })
//
//     it('should return images directory path', () => {
//       const dir = service.getImagesDirectory()
//       expect(dir).toContain('images')
//     })
//   })
// })