// /**
//  * ImageLibraryService 测试
//  */
// import { ImageLibraryService } from '../ImageLibraryService'
// import { DatabaseService } from '../../database/DatabaseService'
// import { ImageRecord, InsertResult, QueryResult, BaseResult, ExistsResult } from '../../../src/types/database'
//
// // Mock DatabaseService
// class MockDatabaseService implements DatabaseService {
//   async testConnection(): Promise<boolean> {
//     return true
//   }
//
//   async initDatabase(): Promise<boolean> {
//     return true
//   }
//
//   async clearDatabase(): Promise<boolean> {
//     return true
//   }
//
//   async insertImages(images: ImageRecord[]): Promise<InsertResult> {
//     return {
//       success: true,
//       insertedIds: images.map(img => img.id)
//     }
//   }
//
//   async queryImages(): Promise<QueryResult> {
//     return {
//       results: [],
//       total: 0
//     }
//   }
//
//   async deleteImage(): Promise<BaseResult> {
//     return { success: true }
//   }
//
//   async queryAllTags(): Promise<{ tags: string[]; error?: string }> {
//     return { tags: [] }
//   }
//
//   async hybridSearch(): Promise<QueryResult> {
//     return {
//       results: [],
//       total: 0
//     }
//   }
//
//   async checkImageExists(): Promise<ExistsResult> {
//     return { success: true, exists: false }
//   }
//
//   async searchSimilarImages(): Promise<QueryResult> {
//     return {
//       results: [],
//       total: 0
//     }
//   }
//
//   async close(): Promise<void> {
//     // Mock implementation
//   }
// }
//
// describe('ImageLibraryService', () => {
//   let service: ImageLibraryService
//   let mockDatabaseService: MockDatabaseService
//
//   beforeEach(() => {
//     mockDatabaseService = new MockDatabaseService()
//     service = new ImageLibraryService(mockDatabaseService)
//   })
//
//   test('应该能够创建服务实例', () => {
//     expect(service).toBeInstanceOf(ImageLibraryService)
//   })
//
//   test('应该能够获取空的图片库列表', async () => {
//     const libraries = await service.getLibraries()
//     expect(libraries).toEqual([])
//   })
//
//   test('应该能够添加新的图片库', async () => {
//     const library = await service.addLibrary('测试库', '/test/path')
//     expect(library.name).toBe('测试库')
//     expect(library.rootPath).toBe('/test/path')
//     expect(library.status).toBe('active')
//   })
//
//   test('应该能够获取添加的图片库', async () => {
//     const addedLibrary = await service.addLibrary('测试库', '/test/path')
//     const library = await service.getLibrary(addedLibrary.id)
//     expect(library).not.toBeNull()
//     expect(library?.name).toBe('测试库')
//   })
//
//   test('应该能够获取图片库列表', async () => {
//     await service.addLibrary('库1', '/path1')
//     await service.addLibrary('库2', '/path2')
//
//     const libraries = await service.getLibraries()
//     expect(libraries).toHaveLength(2)
//     expect(libraries[0].name).toBe('库2') // 按创建时间倒序
//     expect(libraries[1].name).toBe('库1')
//   })
// })