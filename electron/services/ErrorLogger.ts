/**
 * 错误日志记录器
 * 用于后端服务的错误日志记录和性能监控
 */

import * as fs from 'node:fs'
import * as path from 'node:path'
import { app } from 'electron'
import { performance } from 'perf_hooks'

// 日志级别
export enum LogLevel {
  DEBUG = 'debug',
  INFO = 'info',
  WARN = 'warn',
  ERROR = 'error'
}

// 日志条目
export interface LogEntry {
  level: LogLevel
  message: string
  timestamp: string
  context?: string
  data?: Record<string, any>
}

// 性能指标
export interface PerformanceMetric {
  operation: string
  duration: number
  timestamp: string
  metadata?: Record<string, any>
}

// 日志配置
export interface LoggerConfig {
  minLevel: LogLevel
  enableConsole: boolean
  enableFileLogging: boolean
  logFilePath?: string
  maxLogFileSize: number // 字节
  maxLogFiles: number
  enablePerformanceMonitoring: boolean
}

/**
 * 错误日志记录器类
 */
export class ErrorLogger {
  private config: LoggerConfig
  private performanceMarks: Map<string, number> = new Map()
  private logFilePath: string
  private performanceFilePath: string

  constructor(config?: Partial<LoggerConfig>) {
    // 默认配置
    this.config = {
      minLevel: LogLevel.INFO,
      enableConsole: true,
      enableFileLogging: true,
      maxLogFileSize: 5 * 1024 * 1024, // 5MB
      maxLogFiles: 5,
      enablePerformanceMonitoring: true,
      ...config
    }

    // 设置日志文件路径
    const logsDir = path.join(app.getPath('userData'), 'logs')
    this.ensureDirectoryExists(logsDir)
    
    this.logFilePath = this.config.logFilePath || path.join(logsDir, 'error.log')
    this.performanceFilePath = path.join(logsDir, 'performance.log')
  }

  /**
   * 记录调试日志
   */
  debug(message: string, context?: string, data?: Record<string, any>): void {
    this.log(LogLevel.DEBUG, message, context, data)
  }

  /**
   * 记录信息日志
   */
  info(message: string, context?: string, data?: Record<string, any>): void {
    this.log(LogLevel.INFO, message, context, data)
  }

  /**
   * 记录警告日志
   */
  warn(message: string, context?: string, data?: Record<string, any>): void {
    this.log(LogLevel.WARN, message, context, data)
  }

  /**
   * 记录错误日志
   */
  error(message: string, context?: string, data?: Record<string, any>): void {
    this.log(LogLevel.ERROR, message, context, data)
  }

  /**
   * 记录异常
   */
  logException(error: Error, context?: string, additionalData?: Record<string, any>): void {
    const data = {
      name: error.name,
      message: error.message,
      stack: error.stack,
      ...additionalData
    }
    this.error(`Exception: ${error.message}`, context, data)
  }

  /**
   * 开始性能计时
   */
  startPerformanceTimer(markId: string): void {
    if (!this.config.enablePerformanceMonitoring) return
    this.performanceMarks.set(markId, performance.now())
  }

  /**
   * 结束性能计时并记录
   */
  endPerformanceTimer(markId: string, operation: string, metadata?: Record<string, any>): number | null {
    if (!this.config.enablePerformanceMonitoring) return null
    
    const startTime = this.performanceMarks.get(markId)
    if (startTime === undefined) {
      this.warn(`Performance mark '${markId}' not found`, 'Performance')
      return null
    }
    
    const endTime = performance.now()
    const duration = endTime - startTime
    
    this.recordPerformanceMetric({
      operation,
      duration,
      timestamp: new Date().toISOString(),
      metadata
    })
    
    this.performanceMarks.delete(markId)
    return duration
  }

  // 私有方法

  /**
   * 记录日志
   */
  private log(level: LogLevel, message: string, context?: string, data?: Record<string, any>): void {
    // 检查日志级别
    if (!this.shouldLogLevel(level)) return

    const timestamp = new Date().toISOString()
    const entry: LogEntry = {
      level,
      message,
      timestamp,
      context,
      data
    }
    
    // 输出到控制台
    if (this.config.enableConsole) {
      this.logToConsole(entry)
    }
    
    // 写入日志文件
    if (this.config.enableFileLogging) {
      this.writeLogToFile(entry)
    }
  }

  /**
   * 记录性能指标
   */
  private recordPerformanceMetric(metric: PerformanceMetric): void {
    // 输出到控制台
    if (this.config.enableConsole) {
      console.debug(
        `⏱️ Performance: ${metric.operation} - ${metric.duration.toFixed(2)}ms`,
        metric.metadata || ''
      )
    }
    
    // 写入性能日志文件
    if (this.config.enableFileLogging) {
      this.writePerformanceToFile(metric)
    }
  }

  /**
   * 输出到控制台
   */
  private logToConsole(entry: LogEntry): void {
    const contextStr = entry.context ? `[${entry.context}]` : ''
    const message = `${entry.timestamp} ${contextStr} ${entry.message}`
    
    switch (entry.level) {
      case LogLevel.DEBUG:
        console.debug(message, entry.data || '')
        break
      case LogLevel.INFO:
        console.info(message, entry.data || '')
        break
      case LogLevel.WARN:
        console.warn(message, entry.data || '')
        break
      case LogLevel.ERROR:
        console.error(message, entry.data || '')
        break
    }
  }

  /**
   * 写入日志到文件
   */
  private writeLogToFile(entry: LogEntry): void {
    try {
      // 检查日志文件大小
      this.rotateLogFileIfNeeded(this.logFilePath)
      
      const contextStr = entry.context ? `[${entry.context}]` : ''
      const dataStr = entry.data ? ` ${JSON.stringify(entry.data)}` : ''
      const logLine = `${entry.timestamp} [${entry.level.toUpperCase()}] ${contextStr} ${entry.message}${dataStr}\n`
      
      fs.appendFileSync(this.logFilePath, logLine)
    } catch (error) {
      console.error('写入日志文件失败:', error)
    }
  }

  /**
   * 写入性能指标到文件
   */
  private writePerformanceToFile(metric: PerformanceMetric): void {
    try {
      // 检查性能日志文件大小
      this.rotateLogFileIfNeeded(this.performanceFilePath)
      
      const metadataStr = metric.metadata ? ` ${JSON.stringify(metric.metadata)}` : ''
      const logLine = `${metric.timestamp} [PERF] ${metric.operation} - ${metric.duration.toFixed(2)}ms${metadataStr}\n`
      
      fs.appendFileSync(this.performanceFilePath, logLine)
    } catch (error) {
      console.error('写入性能日志文件失败:', error)
    }
  }

  /**
   * 如果需要，轮换日志文件
   */
  private rotateLogFileIfNeeded(filePath: string): void {
    try {
      // 检查文件是否存在
      if (!fs.existsSync(filePath)) {
        return
      }
      
      // 检查文件大小
      const stats = fs.statSync(filePath)
      if (stats.size < this.config.maxLogFileSize) {
        return
      }
      
      // 轮换日志文件
      for (let i = this.config.maxLogFiles - 1; i > 0; i--) {
        const oldPath = `${filePath}.${i}`
        const newPath = `${filePath}.${i + 1}`
        
        if (fs.existsSync(oldPath)) {
          if (i === this.config.maxLogFiles - 1) {
            // 删除最旧的日志文件
            fs.unlinkSync(oldPath)
          } else {
            // 重命名日志文件
            fs.renameSync(oldPath, newPath)
          }
        }
      }
      
      // 重命名当前日志文件
      fs.renameSync(filePath, `${filePath}.1`)
    } catch (error) {
      console.error('轮换日志文件失败:', error)
    }
  }

  /**
   * 确保目录存在
   */
  private ensureDirectoryExists(dirPath: string): void {
    if (!fs.existsSync(dirPath)) {
      fs.mkdirSync(dirPath, { recursive: true })
    }
  }

  /**
   * 检查是否应该记录该级别的日志
   */
  private shouldLogLevel(level: LogLevel): boolean {
    const levels = [LogLevel.DEBUG, LogLevel.INFO, LogLevel.WARN, LogLevel.ERROR]
    const configLevelIndex = levels.indexOf(this.config.minLevel)
    const currentLevelIndex = levels.indexOf(level)
    
    return currentLevelIndex >= configLevelIndex
  }
}

// 导出单例实例
export const errorLogger = new ErrorLogger()