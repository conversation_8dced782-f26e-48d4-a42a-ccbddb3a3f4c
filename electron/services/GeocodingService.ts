/**
 * 地理编码服务
 * 用于将GPS坐标转换为地址信息，支持多个地理编码提供商
 */
export class GeocodingService {
  private readonly providers: GeocodingProvider[] = [];
  private readonly cache = new Map<string, LocationInfo>();
  private readonly maxCacheSize = 1000;

  constructor() {
    // 初始化地理编码提供商
    this.initializeProviders();
  }

  /**
   * 将GPS坐标转换为地址信息
   */
  async reverseGeocode(latitude: number, longitude: number): Promise<LocationInfo | null> {
    // 生成缓存键
    const cacheKey = `${latitude.toFixed(6)},${longitude.toFixed(6)}`;
    
    // 检查缓存
    if (this.cache.has(cacheKey)) {
      return this.cache.get(cacheKey)!;
    }

    // 尝试使用各个提供商进行地理编码
    for (const provider of this.providers) {
      try {
        const result = await provider.reverseGeocode(latitude, longitude);
        if (result) {
          // 缓存结果
          this.cacheResult(cacheKey, result);
          return result;
        }
      } catch (error) {
        console.warn(`地理编码提供商 ${provider.name} 失败:`, error);
        continue;
      }
    }

    console.warn(`所有地理编码提供商都失败了: ${latitude}, ${longitude}`);
    return null;
  }

  /**
   * 批量地理编码
   */
  async batchReverseGeocode(coordinates: Array<{ latitude: number; longitude: number }>): Promise<Array<LocationInfo | null>> {
    const results: Array<LocationInfo | null> = [];
    
    // 限制并发数量以避免API限制
    const concurrency = 3;
    const chunks = this.chunkArray(coordinates, concurrency);
    
    for (const chunk of chunks) {
      const promises = chunk.map(coord => 
        this.reverseGeocode(coord.latitude, coord.longitude)
      );
      
      const chunkResults = await Promise.all(promises);
      results.push(...chunkResults);
      
      // 添加延迟以避免API限制
      if (chunks.indexOf(chunk) < chunks.length - 1) {
        await this.delay(1000); // 1秒延迟
      }
    }
    
    return results;
  }

  /**
   * 清除缓存
   */
  clearCache(): void {
    this.cache.clear();
  }

  /**
   * 获取缓存统计信息
   */
  getCacheStats(): { size: number; maxSize: number } {
    return {
      size: this.cache.size,
      maxSize: this.maxCacheSize,
    };
  }

  /**
   * 初始化地理编码提供商
   */
  private initializeProviders(): void {
    // 添加OpenStreetMap Nominatim提供商（免费）
    this.providers.push(new NominatimProvider());
    
    // 可以添加更多提供商
    // this.providers.push(new GoogleMapsProvider(apiKey));
    // this.providers.push(new BingMapsProvider(apiKey));
  }

  /**
   * 缓存结果
   */
  private cacheResult(key: string, result: LocationInfo): void {
    // 如果缓存已满，删除最旧的条目
    if (this.cache.size >= this.maxCacheSize) {
      const firstKey = this.cache.keys().next().value ?? '';
      this.cache.delete(firstKey);
    }
    
    this.cache.set(key, result);
  }

  /**
   * 延迟函数
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * 将数组分块
   */
  private chunkArray<T>(array: T[], chunkSize: number): T[][] {
    const chunks: T[][] = [];
    for (let i = 0; i < array.length; i += chunkSize) {
      chunks.push(array.slice(i, i + chunkSize));
    }
    return chunks;
  }
}

/**
 * 位置信息接口
 */
export interface LocationInfo {
  address: string;
  city: string;
  country: string;
  state?: string;
  postalCode?: string;
  countryCode?: string;
  confidence?: number;
}

/**
 * 地理编码提供商接口
 */
interface GeocodingProvider {
  name: string;
  reverseGeocode(latitude: number, longitude: number): Promise<LocationInfo | null>;
}

/**
 * OpenStreetMap Nominatim提供商（免费）
 */
class NominatimProvider implements GeocodingProvider {
  name = 'Nominatim';
  private readonly baseUrl = 'https://nominatim.openstreetmap.org/reverse';
  private readonly userAgent = 'PicMind/1.0';

  async reverseGeocode(latitude: number, longitude: number): Promise<LocationInfo | null> {
    try {
      const url = new URL(this.baseUrl);
      url.searchParams.set('lat', latitude.toString());
      url.searchParams.set('lon', longitude.toString());
      url.searchParams.set('format', 'json');
      url.searchParams.set('addressdetails', '1');
      url.searchParams.set('zoom', '18');

      const response = await fetch(url.toString(), {
        headers: {
          'User-Agent': this.userAgent,
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      
      if (!data || !data.address) {
        return null;
      }

      return this.parseNominatimResponse(data);
    } catch (error) {
      console.error('Nominatim地理编码失败:', error);
      return null;
    }
  }

  private parseNominatimResponse(data: any): LocationInfo {
    const address = data.address || {};
    
    // 构建完整地址
    const addressParts = [
      address.house_number,
      address.road,
      address.suburb || address.neighbourhood,
      address.city || address.town || address.village,
      address.state,
      address.country,
    ].filter(Boolean);

    return {
      address: data.display_name || addressParts.join(', '),
      city: address.city || address.town || address.village || '',
      country: address.country || '',
      state: address.state,
      postalCode: address.postcode,
      countryCode: address.country_code,
      confidence: this.calculateConfidence(data),
    };
  }

  private calculateConfidence(data: any): number {
    // 基于返回数据的完整性计算置信度
    const address = data.address || {};
    let score = 0;
    
    if (address.house_number) score += 0.2;
    if (address.road) score += 0.2;
    if (address.city || address.town) score += 0.3;
    if (address.state) score += 0.15;
    if (address.country) score += 0.15;
    
    return Math.min(score, 1.0);
  }
}

/**
 * Google Maps地理编码提供商（需要API密钥）
 */
class GoogleMapsProvider implements GeocodingProvider {
  name = 'Google Maps';
  private readonly baseUrl = 'https://maps.googleapis.com/maps/api/geocode/json';

  constructor(private readonly apiKey: string) {}

  async reverseGeocode(latitude: number, longitude: number): Promise<LocationInfo | null> {
    try {
      const url = new URL(this.baseUrl);
      url.searchParams.set('latlng', `${latitude},${longitude}`);
      url.searchParams.set('key', this.apiKey);
      url.searchParams.set('language', 'zh-CN');

      const response = await fetch(url.toString());
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      
      if (data.status !== 'OK' || !data.results || data.results.length === 0) {
        return null;
      }

      return this.parseGoogleResponse(data.results[0]);
    } catch (error) {
      console.error('Google Maps地理编码失败:', error);
      return null;
    }
  }

  private parseGoogleResponse(result: any): LocationInfo {
    const components = result.address_components || [];
    const location: Partial<LocationInfo> = {};

    // 解析地址组件
    for (const component of components) {
      const types = component.types || [];
      
      if (types.includes('locality')) {
        location.city = component.long_name;
      } else if (types.includes('administrative_area_level_1')) {
        location.state = component.long_name;
      } else if (types.includes('country')) {
        location.country = component.long_name;
        location.countryCode = component.short_name;
      } else if (types.includes('postal_code')) {
        location.postalCode = component.long_name;
      }
    }

    return {
      address: result.formatted_address || '',
      city: location.city || '',
      country: location.country || '',
      state: location.state,
      postalCode: location.postalCode,
      countryCode: location.countryCode,
      confidence: 0.9, // Google Maps通常有很高的准确性
    };
  }
}

export default GeocodingService;