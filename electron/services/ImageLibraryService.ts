// import * as fs from 'node:fs/promises'
// import { createReadStream, existsSync } from 'node:fs'
// import * as path from 'node:path'
// import * as crypto from 'node:crypto'
// // import chokidar from 'chokidar' // 可选依赖，需要时安装
// import { dialog } from 'electron'
// import { PathSecurityValidator, ValidationResult } from './PathSecurityValidator'
// import { DatabaseService } from '../database/DatabaseService'
// import { ImageRecord } from '../../src/types/database'
// import { ErrorLogger } from './ErrorLogger'
// import { AIService } from '../ai/AIService'
// import { ImageAnalysisResult } from '../../src/types/ai'
// import { ProgressEventManager } from './ProgressEventManager'
// import { BaseProgress } from '../../src/types/progress'
// import { LibraryPersistenceService, PersistedLibraryConfig } from './LibraryPersistenceService'
//
// // 接口定义
// export interface ImageLibrary {
//   id: string
//   name: string
//   rootPath: string
//   createdAt: Date
//   lastScanAt: Date
//   status: 'active' | 'offline' | 'removed'
//   scanProgress: {
//     total: number
//     processed: number
//     failed: number
//   }
// }
//
// // 使用从 ../../src/types/database 导入的 ImageRecord 类型
//
// export interface ImageDuplicates {
//   compositeKey: string
//   originalPath: string
//   fileSize: number
//   md5Hash: string
//   primaryImageId: string
//   duplicateImageIds: string[]
// }
//
// export interface ImageLibraryConfig {
//   supportedFormats: string[]
//   scanSettings: {
//     recursive: boolean
//     includeHidden: boolean
//     maxDepth: number
//     batchSize: number
//   }
//   deduplicationStrategy: 'composite' | 'md5_only' | 'content_aware' | 'disabled'
//   vectorDatabase: {
//     enabled: boolean
//     provider: 'milvus' | 'pinecone' | 'weaviate'
//     collection: string
//     dimension: number
//   }
//   permissions: {
//     requireUserConsent: boolean
//     allowedPaths: string[]
//     restrictedPaths: string[]
//   }
//   fileWatching: {
//     enabled: boolean
//     debounceMs: number
//   }
// }
//
// export interface FolderAnalysisOptions {
//   recursive?: boolean
//   includeHidden?: boolean
//   maxDepth?: number
//   supportedFormats?: string[]
// }
//
// export interface FolderAnalysisResult {
//   estimatedFiles: number
//   totalSize: number
//   deepestLevel: number
//   formatBreakdown: Record<string, number>
//   permissionIssues: string[]
// }
//
// export interface ScanOptions {
//   recursive?: boolean
//   includeHidden?: boolean
//   maxDepth?: number
//   batchSize?: number
//   supportedFormats?: string[]
//   enableAIAnalysis?: boolean
//   autoScanOnStartup?: boolean
// }
//
// export interface PermissionResult {
//   granted: boolean
//   path?: string
//   error?: string
// }
//
// export interface ScanProgress {
//   libraryId: string
//   total: number
//   processed: number
//   failed: number
//   currentFile?: string
//   isScanning: boolean
// }
//
// // 默认配置
// const DEFAULT_CONFIG: ImageLibraryConfig = {
//   supportedFormats: ['.jpg', '.jpeg', '.png', '.webp', '.gif', '.bmp', '.tiff'],
//   scanSettings: {
//     recursive: true,
//     includeHidden: false,
//     maxDepth: 20,
//     batchSize: 50
//   },
//   deduplicationStrategy: 'composite',
//   vectorDatabase: {
//     enabled: false,
//     provider: 'milvus',
//     collection: 'image_embeddings',
//     dimension: 512
//   },
//   permissions: {
//     requireUserConsent: true,
//     allowedPaths: [],
//     restrictedPaths: ['/System', '/usr', '/bin', '/sbin', '/etc', '/var', '/tmp']
//   },
//   fileWatching: {
//     enabled: true,
//     debounceMs: 1000
//   }
// }
//
// /**
//  * Electron权限管理服务
//  */
// class ElectronPermissionService {
//   private allowedPaths: Set<string> = new Set()
//   private userConsentCache: Map<string, boolean> = new Map()
//
//   /**
//    * 请求文件夹访问权限
//    */
//   async requestFolderAccess(folderPath: string = ''): Promise<PermissionResult> {
//     // 如果已经提供了有效的文件夹路径，直接使用它
//     if (folderPath && folderPath.trim().length > 0) {
//       // 验证路径安全性
//       const securityCheck = this.validatePathSecurity(folderPath)
//       if (!securityCheck.isValid) {
//         return { granted: false, error: securityCheck.errors.join(', ') }
//       }
//
//       // 记录权限
//       this.allowedPaths.add(folderPath)
//       this.userConsentCache.set(folderPath, true)
//
//       return { granted: true, path: folderPath }
//     }
//
//     // 只有在没有提供路径时才显示选择对话框
//     try {
//       console.log('ImageLibraryService 开始选择文件夹对话框')
//
//       // 使用Electron的文件夹选择对话框
//       const result = await dialog.showOpenDialog({
//         title: '选择图片库文件夹',
//         defaultPath: folderPath,
//         properties: ['openDirectory', 'createDirectory'],
//         message: '请选择要添加为图片库的文件夹'
//       })
//
//       console.log('ImageLibraryService 文件夹选择结果:', result)
//
//       if (result.canceled) {
//         console.log('ImageLibraryService 用户取消了文件夹选择')
//         return { granted: false, error: '用户取消选择' }
//       }
//
//       if (!result.filePaths || !result.filePaths.length) {
//         console.error('ImageLibraryService filePaths为空或未定义:', result.filePaths)
//         return { granted: false, error: '未选择任何文件夹' }
//       }
//
//       const selectedPath = result.filePaths[0]
//       console.log('ImageLibraryService 选择的文件夹路径:', selectedPath)
//
//       if (!selectedPath || typeof selectedPath !== 'string') {
//         console.error('ImageLibraryService 选择的路径无效:', selectedPath)
//         return { granted: false, error: '选择的路径无效' }
//       }
//
//       // 验证路径安全性
//       const securityCheck = this.validatePathSecurity(selectedPath)
//       if (!securityCheck.isValid) {
//         return { granted: false, error: securityCheck.errors.join(', ') }
//       }
//
//       // 记录权限
//       this.allowedPaths.add(selectedPath)
//       this.userConsentCache.set(selectedPath, true)
//
//       return { granted: true, path: selectedPath }
//     } catch (error: any) {
//       return { granted: false, error: `权限请求失败: ${error.message}` }
//     }
//   }
//
//   /**
//    * 检查是否有访问权限
//    */
//   hasPermission(folderPath: string): boolean {
//     const normalizedPath = path.resolve(folderPath)
//
//     // 检查直接权限
//     if (this.allowedPaths.has(normalizedPath)) {
//       return true
//     }
//
//     // 检查父目录权限
//     for (const allowedPath of this.allowedPaths) {
//       if (normalizedPath.startsWith(allowedPath)) {
//         return true
//       }
//     }
//
//     return false
//   }
//
//   /**
//    * 撤销文件夹权限
//    */
//   revokePermission(folderPath: string): void {
//     const normalizedPath = path.resolve(folderPath)
//     this.allowedPaths.delete(normalizedPath)
//     this.userConsentCache.delete(normalizedPath)
//   }
//
//   private validatePathSecurity(folderPath: string): ValidationResult {
//     // 系统目录保护
//     const systemDirs = [
//       '/System', '/usr', '/bin', '/sbin', '/etc', '/var', '/tmp',
//       'C:\\Windows', 'C:\\System32', 'C:\\Program Files'
//     ]
//
//     const normalizedPath = path.resolve(folderPath)
//
//     for (const sysDir of systemDirs) {
//       if (normalizedPath.startsWith(sysDir)) {
//         return {
//           isValid: false,
//           errors: ['不允许访问系统目录'],
//           warnings: []
//         }
//       }
//     }
//
//     return { isValid: true, errors: [], warnings: [] }
//   }
// }
//
// /**
//  * 图片库路径验证器
//  */
// class ImageLibraryPathValidator {
//   private allowedLibraries: Map<string, string> = new Map() // libraryId -> rootPath
//   private pathValidator: PathSecurityValidator
//
//   constructor() {
//     this.pathValidator = new PathSecurityValidator()
//   }
//
//   /**
//    * 注册图片库
//    */
//   registerLibrary(libraryId: string, rootPath: string): void {
//     const normalizedPath = path.resolve(rootPath)
//     this.allowedLibraries.set(libraryId, normalizedPath)
//   }
//
//   /**
//    * 移除图片库
//    */
//   unregisterLibrary(libraryId: string): void {
//     this.allowedLibraries.delete(libraryId)
//   }
//
//   /**
//    * 验证路径是否属于已注册的库
//    */
//   validatePath(imagePath: string): ValidationResult & { libraryId?: string; relativePath?: string } {
//     const normalizedPath = path.resolve(imagePath)
//
//     // 基础安全检查
//     const securityCheck = this.pathValidator.validatePath(imagePath)
//     if (!securityCheck.isValid) {
//       return securityCheck
//     }
//
//     // 检查是否在任何已注册库内
//     for (const [libraryId, rootPath] of this.allowedLibraries) {
//       if (normalizedPath.startsWith(rootPath)) {
//         return {
//           isValid: true,
//           libraryId,
//           relativePath: path.relative(rootPath, normalizedPath),
//           errors: [],
//           warnings: []
//         }
//       }
//     }
//
//     return {
//       isValid: false,
//       errors: ['文件不在任何已注册的图片库内'],
//       warnings: []
//     }
//   }
//
//   /**
//    * 验证新库路径
//    */
//   validateLibraryPath(proposedPath: string): ValidationResult {
//     const normalizedPath = path.resolve(proposedPath)
//
//     // 安全检查
//     const securityCheck = this.pathValidator.validatePath(proposedPath)
//     if (!securityCheck.isValid) {
//       return securityCheck
//     }
//
//     // 检查是否与现有库冲突
//     for (const [existingId, existingPath] of this.allowedLibraries) {
//       if (normalizedPath.startsWith(existingPath) || existingPath.startsWith(normalizedPath)) {
//         return {
//           isValid: false,
//           errors: [`路径与现有库冲突: ${existingPath}`],
//           warnings: []
//         }
//       }
//     }
//
//     return { isValid: true, errors: [], warnings: [] }
//   }
// }
//
// /**
//  * 图片库管理服务
//  */
// export class ImageLibraryService {
//   private databaseService: DatabaseService
//   private pathValidator: ImageLibraryPathValidator
//   private permissionService: ElectronPermissionService
//   private fileWatcher: any | null = null // chokidar.FSWatcher when available
//   private scanQueue: Map<string, Promise<void>> = new Map()
//   private config: ImageLibraryConfig
//   private logger: ErrorLogger
//   private scanProgressCallbacks: Map<string, (progress: ScanProgress) => void> = new Map()
//   private aiService: AIService | null = null
//   private aiAnalysisQueue: Map<string, Promise<void>> = new Map()
//   private progressManager: ProgressEventManager
//   private persistenceService: LibraryPersistenceService
//   private retryAttempts = new Map<string, number>()
//   private readonly operationLocks = new Map<string, Promise<any>>()
//   private readonly concurrentOperations = new Map<string, number>()
//   private readonly MAX_CONCURRENT_AI_OPERATIONS = 3
//   private cleanupTimers: Map<string, NodeJS.Timeout> = new Map()
//   private readonly CLEANUP_INTERVAL = 5 * 60 * 1000 // 5分钟
//   private isInitialized = false // 添加初始化状态标志
//
//   /**
//    * 路径解析辅助类
//    */
//   private PathResolver = class {
//     constructor(private libraries: Map<string, ImageLibrary>) {}
//
//     /**
//      * 解析图片路径为绝对路径
//      */
//     resolveImagePath(imagePath: string): string | null {
//       // 如果已经是绝对路径
//       if (path.isAbsolute(imagePath)) {
//         return existsSync(imagePath) ? imagePath : null
//       }
//
//       // 在所有库中搜索相对路径
//       for (const library of this.libraries.values()) {
//         const fullPath = path.join(library.rootPath, imagePath)
//         if (existsSync(fullPath)) {
//           return fullPath
//         }
//       }
//
//       return null
//     }
//
//     /**
//      * 获取相对路径
//      */
//     getRelativePath(fullPath: string, libraryRootPath: string): string {
//       return path.relative(libraryRootPath, fullPath)
//     }
//
//     /**
//      * 标准化路径
//      */
//     normalizePath(inputPath: string): string {
//       return path.resolve(inputPath)
//     }
//   }
//
//   /**
//    * 并发控制装饰器
//    */
//   private async withConcurrencyControl<T>(
//     operationKey: string,
//     maxConcurrent: number,
//     operation: () => Promise<T>
//   ): Promise<T> {
//     // 检查并发数量限制
//     const currentCount = this.concurrentOperations.get(operationKey) || 0
//     if (currentCount >= maxConcurrent) {
//       // 等待一个操作完成
//       await new Promise(resolve => setTimeout(resolve, 100))
//       return this.withConcurrencyControl(operationKey, maxConcurrent, operation)
//     }
//
//     // 增加并发计数
//     this.concurrentOperations.set(operationKey, currentCount + 1)
//
//     try {
//       return await operation()
//     } finally {
//       // 减少并发计数
//       const newCount = (this.concurrentOperations.get(operationKey) || 1) - 1
//       if (newCount <= 0) {
//         this.concurrentOperations.delete(operationKey)
//       } else {
//         this.concurrentOperations.set(operationKey, newCount)
//       }
//     }
//   }
//
//   /**
//    * 互斥锁装饰器
//    */
//   private async withLock<T>(
//     lockKey: string,
//     operation: () => Promise<T>
//   ): Promise<T> {
//     // 如果已经有锁，等待
//     if (this.operationLocks.has(lockKey)) {
//       await this.operationLocks.get(lockKey)
//     }
//
//     // 创建新锁
//     const lockPromise = operation()
//     this.operationLocks.set(lockKey, lockPromise)
//
//     try {
//       return await lockPromise
//     } finally {
//       this.operationLocks.delete(lockKey)
//     }
//   }
//
//   /**
//    * 启动资源清理
//    */
//   private startResourceCleanup(): void {
//     const cleanupTimer = setInterval(() => {
//       this.performResourceCleanup()
//     }, this.CLEANUP_INTERVAL)
//
//     this.cleanupTimers.set('main', cleanupTimer)
//   }
//
//   /**
//    * 执行资源清理
//    */
//   private performResourceCleanup(): void {
//     console.log('开始执行资源清理')
//
//     // 清理过期的重试记录
//     const now = Date.now()
//     const RETRY_EXPIRE_TIME = 10 * 60 * 1000 // 10分钟
//
//     for (const [key, value] of this.retryAttempts.entries()) {
//       // 这里可以添加时间戳检查逻辑
//       if (value > this.MAX_RETRY_ATTEMPTS) {
//         this.retryAttempts.delete(key)
//       }
//     }
//
//     // 清理已完成的操作锁
//     for (const [key, promise] of this.operationLocks.entries()) {
//       if (promise && typeof promise.then === 'function') {
//         promise.then(() => {
//           // 操作完成后清理
//         }).catch(() => {
//           // 操作失败后也清理
//         })
//       }
//     }
//
//     // 清理并发操作计数中的0值
//     for (const [key, count] of this.concurrentOperations.entries()) {
//       if (count <= 0) {
//         this.concurrentOperations.delete(key)
//       }
//     }
//
//     console.log('资源清理完成')
//   }
//
//   /**
//    * 停止资源清理
//    */
//   private stopResourceCleanup(): void {
//     for (const [key, timer] of this.cleanupTimers.entries()) {
//       clearInterval(timer)
//     }
//     this.cleanupTimers.clear()
//   }
//
//   /**
//    * 清理服务资源
//    */
//   public cleanup(): void {
//     console.log('开始清理ImageLibraryService资源')
//
//     // 停止文件监控
//     this.stopFileWatching()
//
//     // 停止资源清理定时器
//     this.stopResourceCleanup()
//
//     // 清理所有映射
//     this.libraries.clear()
//     this.retryAttempts.clear()
//     this.operationLocks.clear()
//     this.concurrentOperations.clear()
//     this.scanProgressCallbacks.clear()
//     this.aiAnalysisQueue.clear()
//     this.scanQueue.clear()
//
//     console.log('ImageLibraryService资源清理完成')
//   }
//
//   private pathResolver: InstanceType<typeof this.PathResolver>
//   private readonly MAX_RETRY_ATTEMPTS = 3
//
//   constructor(databaseService: DatabaseService, config: Partial<ImageLibraryConfig> = {}, aiService?: AIService) {
//     this.databaseService = databaseService
//     this.config = { ...DEFAULT_CONFIG, ...config }
//     this.pathValidator = new ImageLibraryPathValidator()
//     this.permissionService = new ElectronPermissionService()
//     this.logger = new ErrorLogger()
//     this.aiService = aiService || null
//     this.progressManager = new ProgressEventManager()
//     this.persistenceService = new LibraryPersistenceService(databaseService)
//     this.pathResolver = new this.PathResolver(this.libraries)
//
//     // 启动资源清理定时器
//     this.startResourceCleanup()
//
//     // 移除自动初始化，改为延迟初始化模式
//     console.log('ImageLibraryService 已创建，等待手动初始化')
//   }
//
//   /**
//    * 带重试的异步操作
//    */
//   private async retryOperation<T>(
//     operation: () => Promise<T>,
//     operationName: string,
//     maxRetries: number = this.MAX_RETRY_ATTEMPTS
//   ): Promise<T> {
//     let lastError: Error | null = null
//
//     for (let attempt = 1; attempt <= maxRetries; attempt++) {
//       try {
//         return await operation()
//       } catch (error) {
//         lastError = error as Error
//         console.warn(`${operationName} 失败，第 ${attempt}/${maxRetries} 次尝试:`, error)
//
//         if (attempt < maxRetries) {
//           // 指数退避
//           const delay = Math.min(1000 * Math.pow(2, attempt - 1), 5000)
//           await new Promise(resolve => setTimeout(resolve, delay))
//         }
//       }
//     }
//
//     throw new Error(`${operationName} 失败，已经尝试 ${maxRetries} 次: ${lastError?.message}`)
//   }
//
//   /**
//    * 安全的文件操作
//    */
//   private async safeFileOperation<T>(
//     operation: () => Promise<T>,
//     filePath: string,
//     operationName: string
//   ): Promise<T | null> {
//     try {
//       return await this.retryOperation(operation, `${operationName}: ${filePath}`)
//     } catch (error) {
//       this.logger.error(`文件操作失败: ${operationName}`, 'safeFileOperation', {
//         filePath,
//         error: error instanceof Error ? error.message : String(error)
//       })
//       return null
//     }
//   }
//
//   /**
//    * 安全的数据库操作
//    */
//   private async safeDatabaseOperation<T>(
//     operation: () => Promise<T>,
//     operationName: string
//   ): Promise<T | null> {
//     try {
//       return await this.retryOperation(operation, operationName)
//     } catch (error) {
//       this.logger.error(`数据库操作失败: ${operationName}`, 'safeDatabaseOperation', {
//         error: error instanceof Error ? error.message : String(error)
//       })
//       return null
//     }
//   }
//
//   /**
//    * 异步初始化服务（手动调用）
//    */
//   async initialize(options: { initializeDatabase?: boolean } = {}): Promise<void> {
//     if (this.isInitialized) {
//       console.log('ImageLibraryService 已初始化，跳过重复初始化')
//       return
//     }
//
//     try {
//       console.log('开始手动初始化 ImageLibraryService...')
//
//       // 只有在明确要求时才初始化数据库
//       if (options.initializeDatabase) {
//         console.log('执行数据库初始化...')
//         await this.initializeDatabase()
//       } else {
//         console.log('跳过数据库初始化（仅初始化服务状态）')
//       }
//
//       await this.loadPersistedLibraries()
//       this.isInitialized = true
//       console.log('ImageLibraryService 初始化完成')
//     } catch (error) {
//       console.error('ImageLibraryService 初始化过程中出错:', error)
//       throw error
//     }
//   }
//
//   /**
//    * 手动初始化数据库（ultrathink 调用）
//    */
//   async manualInitializeDatabase(): Promise<void> {
//     try {
//       console.log('🚀 开始手动初始化数据库集合...')
//
//       await this.initializeDatabase()
//
//       // 如果服务还没有初始化，现在进行完整初始化
//       if (!this.isInitialized) {
//         await this.initialize({ initializeDatabase: false }) // 数据库已经初始化了
//       }
//
//       console.log('✅ 手动数据库初始化完成')
//     } catch (error) {
//       console.error('❌ 手动数据库初始化失败:', error)
//       throw error
//     }
//   }
//
//   /**
//    * 初始化数据库表
//    */
//   private async initializeDatabase(): Promise<void> {
//     try {
//       // 使用现有的DatabaseService初始化
//       await this.databaseService.initDatabase()
//       console.log('Database initialized successfully')
//     } catch (error) {
//       console.error('Failed to initialize database:', error)
//       throw error
//     }
//   }
//
//   /**
//    * 加载持久化的图片库配置
//    */
//   private async loadPersistedLibraries(): Promise<void> {
//     try {
//       const persistedLibraries = await this.persistenceService.loadLibraries()
//
//       for (const config of persistedLibraries) {
//         // 验证路径是否仍然可访问
//         try {
//           await fs.access(config.rootPath)
//
//           // 重建库对象
//           const library: ImageLibrary = {
//             id: config.id,
//             name: config.name,
//             rootPath: config.rootPath,
//             createdAt: new Date(config.createdAt),
//             lastScanAt: new Date(config.lastScanAt),
//             status: config.status,
//             scanProgress: {
//               total: config.scanProgress.total,
//               processed: config.scanProgress.processed,
//               failed: config.scanProgress.failed
//             }
//           }
//
//           this.libraries.set(library.id, library)
//           this.pathValidator.registerLibrary(library.id, library.rootPath)
//
//           console.log(`加载图片库: ${library.name} (${library.rootPath})`)
//
//           // 禁用自动扫描，只有在手动调用时才扫描
//           if (config.settings.autoScanOnStartup && config.status === 'active') {
//             console.log(`跳过自动扫描（需手动触发）: ${library.name}`)
//             // this.startLibraryScanWithOptions(library.id, config.settings) // 已禁用
//           }
//
//         } catch (error) {
//           // 路径不可访问，标记为离线
//           console.warn(`图片库路径不可访问，标记为离线: ${config.name} (${config.rootPath})`)
//           await this.persistenceService.updateLibrary(config.id, { status: 'offline' })
//         }
//       }
//
//       console.log(`成功加载 ${this.libraries.size} 个图片库`)
//
//     } catch (error) {
//       console.error('加载持久化图片库失败:', error)
//     }
//   }
//
//   /**
//    * 添加新的图片库（需要用户权限确认）
//    */
//   async addLibrary(name: string, requestedPath?: string): Promise<ImageLibrary> {
//     // 请求用户选择文件夹并获得权限
//     const permissionResult = await this.permissionService.requestFolderAccess(requestedPath || '')
//     if (!permissionResult.granted) {
//       throw new Error(`权限被拒绝: ${permissionResult.error}`)
//     }
//
//     const rootPath = permissionResult.path!
//
//     // 验证路径
//     const validation = this.pathValidator.validateLibraryPath(rootPath)
//     if (!validation.isValid) {
//       throw new Error(`无效路径: ${validation.errors.join(', ')}`)
//     }
//
//     const library: ImageLibrary = {
//       id: this.generateId(),
//       name,
//       rootPath: path.resolve(rootPath),
//       createdAt: new Date(),
//       lastScanAt: new Date(0),
//       status: 'active',
//       scanProgress: { total: 0, processed: 0, failed: 0 }
//     }
//
//     // 保存到内存存储
//     this.libraries.set(library.id, library)
//
//     // 注册到路径验证器
//     this.pathValidator.registerLibrary(library.id, library.rootPath)
//
//     // 启动后台扫描
//     this.startLibraryScan(library.id)
//
//     return library
//   }
//
//   /**
//    * 添加新的图片库（带选项）
//    */
//   async addLibraryWithOptions(name: string, rootPath: string, options: ScanOptions): Promise<ImageLibrary> {
//     // 验证路径
//     const validation = this.pathValidator.validateLibraryPath(rootPath)
//     if (!validation.isValid) {
//       throw new Error(`无效路径: ${validation.errors.join(', ')}`)
//     }
//
//     const library: ImageLibrary = {
//       id: this.generateId(),
//       name,
//       rootPath: path.resolve(rootPath),
//       createdAt: new Date(),
//       lastScanAt: new Date(0),
//       status: 'active',
//       scanProgress: { total: 0, processed: 0, failed: 0 }
//     }
//
//     // 保存到内存存储
//     this.libraries.set(library.id, library)
//
//     // 注册到路径验证器
//     this.pathValidator.registerLibrary(library.id, library.rootPath)
//
//     // 保存到持久化存储
//     await this.persistenceService.saveLibrary(library, options)
//
//     // 使用选项启动扫描
//     this.startLibraryScanWithOptions(library.id, options)
//
//     return library
//   }
//
//   /**
//    * 分析文件夹
//    */
//   async analyzeFolder(folderPath: string, options: FolderAnalysisOptions = {}): Promise<FolderAnalysisResult> {
//     try {
//       console.log('ImageLibraryService.analyzeFolder 开始:', { folderPath, options })
//       this.logger.info('开始分析文件夹', 'analyzeFolder', { folderPath, options })
//
//       const analysis: FolderAnalysisResult = {
//         estimatedFiles: 0,
//         totalSize: 0,
//         deepestLevel: 0,
//         formatBreakdown: {},
//         permissionIssues: []
//       }
//
//       // 检查文件夹是否存在
//       if (!existsSync(folderPath)) {
//         const error = new Error(`文件夹不存在: ${folderPath}`)
//         console.error('文件夹不存在:', folderPath)
//         this.logger.error('文件夹不存在', 'analyzeFolder', { folderPath })
//         throw error
//       }
//
//       console.log('文件夹存在，开始递归分析')
//
//       // 递归分析文件夹
//       await this.analyzeFolderRecursive(folderPath, analysis, 0, options)
//
//       console.log('文件夹分析完成:', analysis)
//       this.logger.info('文件夹分析完成', 'analyzeFolder', { folderPath, analysis })
//
//       return analysis
//     } catch (error) {
//       console.error('ImageLibraryService.analyzeFolder 失败:', error)
//       this.logger.error('文件夹分析失败', 'analyzeFolder', { folderPath, error: error instanceof Error ? error.message : String(error) })
//       throw error
//     }
//   }
//
//   /**
//    * 递归分析文件夹
//    */
//   private async analyzeFolderRecursive(
//     dirPath: string,
//     analysis: FolderAnalysisResult,
//     currentDepth: number,
//     options: FolderAnalysisOptions
//   ): Promise<void> {
//     try {
//       console.log('analyzeFolderRecursive 开始:', { dirPath, currentDepth, options })
//
//       const maxDepth = options.maxDepth || 20
//       const includeHidden = options.includeHidden || false
//       const supportedFormats = options.supportedFormats || this.config.supportedFormats
//
//       if (currentDepth > maxDepth) {
//         console.log('达到最大深度，跳过:', currentDepth)
//         return
//       }
//
//       analysis.deepestLevel = Math.max(analysis.deepestLevel, currentDepth)
//
//       console.log('准备读取目录:', dirPath)
//       if (!dirPath || typeof dirPath !== 'string') {
//         throw new Error(`无效的目录路径: ${dirPath}`)
//       }
//
//       const entries = await fs.readdir(dirPath, { withFileTypes: true })
//       console.log(`读取到 ${entries.length} 个条目`)
//
//       for (const entry of entries) {
//         const fullPath = path.join(dirPath, entry.name)
//
//         // 跳过隐藏文件（如果设置）
//         if (!includeHidden && entry.name.startsWith('.')) {
//           continue
//         }
//
//         try {
//           if (entry.isDirectory()) {
//             if (options.recursive !== false) {
//               await this.analyzeFolderRecursive(fullPath, analysis, currentDepth + 1, options)
//             }
//           } else if (entry.isFile()) {
//             const ext = path.extname(entry.name).toLowerCase()
//
//             if (supportedFormats.includes(ext)) {
//               analysis.estimatedFiles++
//
//               // 获取文件大小
//               try {
//                 const stats = await fs.stat(fullPath)
//                 analysis.totalSize += stats.size
//               } catch (error) {
//                 // 权限问题
//                 analysis.permissionIssues.push(fullPath)
//               }
//
//               // 格式统计
//               analysis.formatBreakdown[ext] = (analysis.formatBreakdown[ext] || 0) + 1
//             }
//           }
//         } catch (error) {
//           // 权限问题
//           console.log('处理文件时出错:', fullPath, error)
//           analysis.permissionIssues.push(fullPath)
//         }
//       }
//     } catch (error) {
//       console.error('analyzeFolderRecursive 处理目录时出错:', dirPath, error)
//       this.logger.error('递归分析文件夹失败', 'analyzeFolderRecursive', { dirPath, currentDepth, error: error instanceof Error ? error.message : String(error) })
//       analysis.permissionIssues.push(dirPath)
//     }
//   }
//
//   /**
//    * 使用选项启动库扫描
//    */
//   private async startLibraryScanWithOptions(libraryId: string, options: ScanOptions): Promise<void> {
//     // 如果已经在扫描，跳过
//     if (this.scanQueue.has(libraryId)) {
//       return
//     }
//
//     const library = this.libraries.get(libraryId)
//     if (!library) {
//       throw new Error(`图片库不存在: ${libraryId}`)
//     }
//
//     // 创建进度追踪
//     const scanProgress = this.progressManager.createScanProgress(libraryId, library.name)
//
//     // 创建扫描任务
//     const scanPromise = this.performLibraryScanWithOptions(library, options, scanProgress.id)
//     this.scanQueue.set(libraryId, scanPromise)
//
//     try {
//       await scanPromise
//     } finally {
//       this.scanQueue.delete(libraryId)
//     }
//   }
//
//   /**
//    * 执行带选项的库扫描
//    */
//   private async performLibraryScanWithOptions(library: ImageLibrary, options: ScanOptions, progressId?: string): Promise<void> {
//     try {
//       console.log(`开始扫描图片库: ${library.name} (${library.rootPath})，使用选项:`, options)
//
//       // 更新进度状态为运行中
//       if (progressId) {
//         this.progressManager.updateProgress(progressId, { status: 'running' })
//         this.progressManager.updateScanPhase(progressId, 'discovery', { status: 'running', progress: 0 })
//       }
//
//       // 重置扫描进度
//       library.scanProgress = { total: 0, processed: 0, failed: 0 }
//
//       // 使用选项扫描文件
//       const files = await this.scanDirectoryWithOptions(library.rootPath, options)
//       library.scanProgress.total = files.length
//
//       console.log(`发现 ${files.length} 个图片文件`)
//
//       // 更新进度
//       if (progressId) {
//         this.progressManager.setTotalFiles(progressId, files.length)
//         this.progressManager.updateScanPhase(progressId, 'discovery', { status: 'completed', progress: 100 })
//         this.progressManager.updateScanPhase(progressId, 'processing', { status: 'running', progress: 0 })
//       }
//
//       // 批量处理文件
//       const batchSize = options.batchSize || this.config.scanSettings.batchSize
//       for (let i = 0; i < files.length; i += batchSize) {
//         const batch = files.slice(i, i + batchSize)
//         await this.processBatchWithOptions(library, batch, options, progressId)
//
//         // 更新处理阶段进度
//         if (progressId && files.length > 0) {
//           const processedCount = Math.min(i + batchSize, files.length)
//           const progress = Math.round((processedCount / files.length) * 100)
//           this.progressManager.updateScanPhase(progressId, 'processing', { progress })
//         }
//       }
//
//       // 标记处理阶段完成
//       if (progressId) {
//         this.progressManager.updateScanPhase(progressId, 'processing', { status: 'completed', progress: 100 })
//
//         // 如果启用了AI服务，开始AI分析阶段
//         if (this.aiService && files.length > 0) {
//           this.progressManager.updateScanPhase(progressId, 'analysis', { status: 'running', progress: 0 })
//           // AI分析逻辑将在后续实现
//           this.progressManager.updateScanPhase(progressId, 'analysis', { status: 'completed', progress: 100 })
//         } else {
//           this.progressManager.updateScanPhase(progressId, 'analysis', { status: 'completed', progress: 100 })
//         }
//
//         // 完成整个扫描
//         this.progressManager.completeProgress(progressId)
//       }
//
//       library.lastScanAt = new Date()
//       console.log(`图片库扫描完成: ${library.name}`)
//
//     } catch (error) {
//       this.logger.error('图片库扫描失败', 'performLibraryScanWithOptions', { libraryId: library.id, error: error instanceof Error ? error.message : String(error) })
//
//       if (progressId) {
//         this.progressManager.failProgress(progressId, error instanceof Error ? error.message : String(error))
//       }
//
//       throw error
//     }
//   }
//
//   /**
//    * 使用选项扫描目录
//    */
//   private async scanDirectoryWithOptions(dirPath: string, options: ScanOptions): Promise<string[]> {
//     const files: string[] = []
//     const maxDepth = options.maxDepth || 20
//     const includeHidden = options.includeHidden || false
//     const supportedFormats = options.supportedFormats || this.config.supportedFormats
//
//     await this.scanDirectoryRecursiveWithOptions(
//       dirPath,
//       files,
//       0,
//       maxDepth,
//       includeHidden,
//       supportedFormats,
//       options.recursive !== false
//     )
//
//     return files
//   }
//
//   /**
//    * 递归扫描目录（带选项）
//    */
//   private async scanDirectoryRecursiveWithOptions(
//     dirPath: string,
//     files: string[],
//     currentDepth: number,
//     maxDepth: number,
//     includeHidden: boolean,
//     supportedFormats: string[],
//     recursive: boolean
//   ): Promise<void> {
//     if (currentDepth > maxDepth) {
//       return
//     }
//
//     try {
//       const entries = await fs.readdir(dirPath, { withFileTypes: true })
//
//       for (const entry of entries) {
//         const fullPath = path.join(dirPath, entry.name)
//
//         // 跳过隐藏文件（如果设置）
//         if (!includeHidden && entry.name.startsWith('.')) {
//           continue
//         }
//
//         try {
//           if (entry.isDirectory() && recursive) {
//             await this.scanDirectoryRecursiveWithOptions(
//               fullPath,
//               files,
//               currentDepth + 1,
//               maxDepth,
//               includeHidden,
//               supportedFormats,
//               recursive
//             )
//           } else if (entry.isFile()) {
//             const ext = path.extname(entry.name).toLowerCase()
//             if (supportedFormats.includes(ext)) {
//               files.push(fullPath)
//             }
//           }
//         } catch (error) {
//           // 跳过无法访问的文件/目录
//           console.warn(`跳过无法访问的路径: ${fullPath}`, error)
//         }
//       }
//     } catch (error) {
//       console.warn(`无法读取目录: ${dirPath}`, error)
//     }
//   }
//
//   /**
//    * 使用选项处理文件批次
//    */
//   private async processBatchWithOptions(library: ImageLibrary, files: string[], options: ScanOptions, progressId?: string): Promise<void> {
//     for (const filePath of files) {
//       try {
//         // 更新当前处理的文件
//         if (progressId) {
//           this.progressManager.updateCurrentFile(progressId, filePath)
//         }
//
//         await this.processImageFileWithOptions(library, filePath, options)
//         library.scanProgress.processed++
//
//         // 更新进度计数
//         if (progressId) {
//           this.progressManager.incrementProcessedFiles(progressId, 1)
//         }
//       } catch (error) {
//         library.scanProgress.failed++
//
//         // 更新失败计数
//         if (progressId) {
//           this.progressManager.incrementFailedFiles(progressId, 1)
//         }
//
//         this.logger.error('处理图片文件失败', 'processBatchWithOptions', { filePath, error: error instanceof Error ? error.message : String(error) })
//       }
//
//       // 通知进度更新（兼容性）
//       const callback = this.scanProgressCallbacks.get(library.id)
//       if (callback) {
//         callback({
//           libraryId: library.id,
//           ...library.scanProgress,
//           currentFile: filePath,
//           isScanning: true
//         })
//       }
//     }
//   }
//
//   /**
//    * 使用选项处理单个图片文件
//    */
//   private async processImageFileWithOptions(library: ImageLibrary, filePath: string, options: ScanOptions): Promise<void> {
//     // 基本的文件处理逻辑
//     const stats = await fs.stat(filePath)
//     const relativePath = path.relative(library.rootPath, filePath)
//
//     // 计算MD5哈希
//     const md5Hash = await this.calculateMD5(filePath)
//
//     const imageRecord: ImageRecord = {
//       id: this.generateId(),
//       imagePath: relativePath,
//       description: '',
//       tags: [],
//       embedding: [],
//       metadata: {
//         filename: path.basename(filePath),
//         filesize: stats.size,
//         uploadTime: new Date().toISOString(),
//         dimensions: '0x0',
//         format: path.extname(filePath).substring(1),
//         fileChecksum: md5Hash,
//         createdAt: stats.birthtime,
//         modifiedAt: stats.mtime
//       }
//     }
//
//     // 保存到数据库（简化实现）
//     console.log(`处理图片: ${filePath}`)
//
//     // 如果启用AI分析，添加到AI分析队列
//     if (options.enableAIAnalysis && this.aiService) {
//       this.scheduleAIAnalysisWithRecord(imageRecord)
//     }
//   }
//
//   /**
//    * 获取所有图片库
//    */
//   async getAllLibraries(): Promise<ImageLibrary[]> {
//     return Array.from(this.libraries.values())
//       .filter(library => library.status !== 'removed')
//       .sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime())
//   }
//
//   /**
//    * 启动库扫描
//    */
//   async startLibraryScan(libraryId: string): Promise<void> {
//     // 使用互斥锁防止重复扫描
//     return this.withLock(`scan-${libraryId}`, async () => {
//       if (this.scanQueue.has(libraryId)) {
//         return
//       }
//
//       const library = await this.getLibrary(libraryId)
//       if (!library) throw new Error('库不存在')
//
//       // 更新扫描状态
//       await this.updateLibraryStatus(libraryId, 'active')
//
//       // 异步扫描
//       const scanPromise = this.scanLibraryFiles(library).catch(error => {
//         this.logger.error('扫描库失败', 'ImageLibraryService', { libraryId, error: error instanceof Error ? error.message : String(error) })
//       }).finally(() => {
//         this.scanQueue.delete(libraryId)
//       })
//
//       this.scanQueue.set(libraryId, scanPromise)
//     })
//   }
//
//   /**
//    * 扫描库文件
//    */
//   private async scanLibraryFiles(library: ImageLibrary): Promise<void> {
//     const { rootPath, id: libraryId } = library
//
//     try {
//       // 检查目录是否存在
//       if (!existsSync(rootPath)) {
//         await this.updateLibraryStatus(libraryId, 'offline')
//         return
//       }
//
//       // 获取所有图片文件
//       const imageFiles = await this.findImageFiles(rootPath)
//
//       // 更新总数
//       await this.updateScanProgress(libraryId, { total: imageFiles.length })
//
//       // 批量处理文件
//       const batchSize = this.config.scanSettings.batchSize
//       for (let i = 0; i < imageFiles.length; i += batchSize) {
//         const batch = imageFiles.slice(i, i + batchSize)
//         await this.processBatch(libraryId, batch, rootPath)
//       }
//
//       // 更新最后扫描时间
//       await this.updateLastScanTime(libraryId)
//
//     } catch (error) {
//       this.logger.error('扫描文件失败', 'ImageLibraryService', { libraryId, rootPath, error: error instanceof Error ? error.message : String(error) })
//       throw error
//     }
//   }
//
//   /**
//    * 查找图片文件
//    */
//   private async findImageFiles(rootPath: string): Promise<string[]> {
//     const imageFiles: string[] = []
//     const { supportedFormats } = this.config
//     const { recursive, includeHidden, maxDepth } = this.config.scanSettings
//
//     const scanDirectory = async (dirPath: string, depth: number = 0): Promise<void> => {
//       if (depth > maxDepth) return
//
//       try {
//         const entries = await fs.readdir(dirPath, { withFileTypes: true })
//
//         for (const entry of entries) {
//           // 跳过隐藏文件（如果配置不包含）
//           if (!includeHidden && entry.name.startsWith('.')) {
//             continue
//           }
//
//           const fullPath = path.join(dirPath, entry.name)
//
//           if (entry.isDirectory() && recursive) {
//             await scanDirectory(fullPath, depth + 1)
//           } else if (entry.isFile()) {
//             const ext = path.extname(entry.name).toLowerCase()
//             if (supportedFormats.includes(ext)) {
//               imageFiles.push(fullPath)
//             }
//           }
//         }
//       } catch (error) {
//         this.logger.error('读取目录失败', 'ImageLibraryService', { dirPath, error: error instanceof Error ? error.message : String(error) })
//       }
//     }
//
//     await scanDirectory(rootPath)
//     return imageFiles
//   }
//
//   /**
//    * 处理文件批次
//    */
//   private async processBatch(libraryId: string, filePaths: string[], rootPath: string): Promise<void> {
//     for (const filePath of filePaths) {
//       try {
//         await this.processImageFile(libraryId, filePath, rootPath)
//         await this.updateScanProgress(libraryId, { processed: 1 })
//       } catch (error) {
//         this.logger.error('处理文件失败', 'ImageLibraryService', { libraryId, filePath, error: error instanceof Error ? error.message : String(error) })
//         await this.updateScanProgress(libraryId, { failed: 1 })
//       }
//     }
//   }
//
//   /**
//    * 处理单个图片文件（优化的去重策略）
//    */
//   private async processImageFile(libraryId: string, filePath: string, rootPath: string): Promise<void> {
//     try {
//       // 第一步：快速去重检查（先检查路径和MD5）
//       const stats = await fs.stat(filePath)
//       const md5Hash = await this.calculateMD5(filePath)
//
//       // 优先检查是否重复（避免不必要的AI处理）
//       const duplicateRecord = await this.getDuplicateRecord(md5Hash, filePath, stats.size)
//       if (duplicateRecord) {
//         console.log(`🔄 发现重复文件，跳过处理: ${filePath}`)
//         return
//       }
//
//       // 检查文件是否已存在于数据库
//       const existingRecord = await this.getImageRecord(filePath)
//       if (existingRecord) {
//         console.log(`📄 文件已存在于数据库，更新验证时间: ${filePath}`)
//         await this.updateImageVerification(existingRecord.id)
//         return
//       }
//
//       console.log(`🆕 处理新图片: ${filePath}`)
//
//       // 第二步：创建基础图片记录（不包含AI数据）
//       const imageRecord: ImageRecord = {
//         id: this.generateId(),
//         imagePath: path.resolve(filePath), // 使用绝对路径
//         description: '',
//         tags: [],
//         embedding: [],
//         metadata: {
//           filename: path.basename(filePath),
//           filesize: stats.size,
//           uploadTime: new Date().toISOString(),
//           dimensions: '0x0', // 将在AI分析时获取
//           format: path.extname(filePath).slice(1),
//           fileChecksum: md5Hash,
//           createdAt: stats.birthtime,
//           modifiedAt: stats.mtime
//         }
//       }
//
//       // 第三步：保存记录到数据库（数据库自动处理重复检测）
//       await this.saveImageRecord(imageRecord)
//       console.log(`💾 记录已保存: ${filePath}`)
//
//       // 第四步：如果有AI服务，异步执行AI分析（不阻塞主流程）
//       if (this.aiService) {
//         console.log(`🤖 开始AI分析: ${filePath}`)
//         // 使用异步队列，不阻塞文件处理流程
//         this.scheduleAIAnalysisWithRecord(imageRecord)
//       }
//
//       console.log(`✅ 图片处理完成: ${filePath}`)
//
//     } catch (error) {
//       console.error(`❌ 处理图片失败: ${filePath}`, error)
//       throw error
//     }
//   }
//
//   /**
//    * 生成复合键
//    */
//   private generateCompositeKey(filePath: string, fileSize: number, md5Hash: string): string {
//     const normalizedPath = path.resolve(filePath)
//     return `${normalizedPath}|${fileSize}|${md5Hash}`
//   }
//
//   /**
//    * 计算文件MD5
//    */
//   private async calculateMD5(filePath: string): Promise<string> {
//     return this.safeFileOperation(
//       () => new Promise<string>((resolve, reject) => {
//         const hash = crypto.createHash('md5')
//         const stream = createReadStream(filePath)
//
//         stream.on('data', data => hash.update(data))
//         stream.on('end', () => resolve(hash.digest('hex')))
//         stream.on('error', reject)
//       }),
//       filePath,
//       '计算MD5哈希'
//     ).then(result => result || '')
//   }
//
//   /**
//    * 获取图片记录
//    */
//   private async getImageRecord(filePath: string): Promise<ImageRecord | null> {
//     const result = await this.safeDatabaseOperation(
//       () => this.databaseService.checkImageExists(filePath),
//       '检查图片是否存在'
//     )
//
//     if (result?.exists) {
//       return {
//         id: '',
//         imagePath: filePath,
//         description: '',
//         tags: [],
//         embedding: [],
//         metadata: {
//           filename: path.basename(filePath),
//           filesize: 0,
//           uploadTime: new Date().toISOString(),
//           dimensions: '0x0',
//           format: path.extname(filePath).slice(1)
//         }
//       }
//     }
//     return null
//   }
//
//   /**
//    * 获取重复记录 - 实时查询数据库
//    */
//   private async getDuplicateRecord(md5Hash: string, filePath: string, fileSize: number): Promise<ImageDuplicates | null> {
//     try {
//       // 查询数据库中是否已存在相同 md5Hash 的图片
//       const result = await this.safeDatabaseOperation(
//         () => this.databaseService.queryImages({
//           expr: `file_checksum = '${md5Hash}'`
//         }),
//         '查询重复图片记录'
//       )
//
//       if (!result?.results || result.results.length === 0) {
//         return null
//       }
//
//       // 如果找到记录，构造 ImageDuplicates 对象
//       const primaryRecord = result.results[0]
//       const compositeKey = this.generateCompositeKey(filePath, fileSize, md5Hash)
//       const duplicateRecord: ImageDuplicates = {
//         compositeKey,
//         originalPath: filePath,
//         fileSize,
//         md5Hash,
//         primaryImageId: primaryRecord.id,
//         duplicateImageIds: result.results.slice(1).map(r => r.id) // 除了第一个，其他都是重复的
//       }
//
//       return duplicateRecord
//     } catch (error) {
//       console.error('查询重复记录失败:', error)
//       return null
//     }
//   }
//
//   /**
//    * 保存图片记录
//    */
//   private async saveImageRecord(record: ImageRecord): Promise<void> {
//     const result = await this.safeDatabaseOperation(
//       () => this.databaseService.insertImages([record]),
//       '保存图片记录'
//     )
//
//     if (!result || !result.success) {
//       throw new Error(`保存图片记录失败: ${result?.error || '未知错误'}`)
//     }
//   }
//
//   /**
//    * 更新扫描进度（简化实现）
//    */
//   private async updateScanProgress(libraryId: string, progress: Partial<{ total: number; processed: number; failed: number }>): Promise<void> {
//     const library = this.libraries.get(libraryId)
//     if (!library) return
//
//     if (progress.total !== undefined) {
//       library.scanProgress.total = progress.total
//     }
//     if (progress.processed !== undefined) {
//       library.scanProgress.processed += progress.processed
//     }
//     if (progress.failed !== undefined) {
//       library.scanProgress.failed += progress.failed
//     }
//
//     // 触发进度回调
//     const callback = this.scanProgressCallbacks.get(libraryId)
//     if (callback) {
//       callback({
//         libraryId,
//         ...library.scanProgress,
//         isScanning: this.scanQueue.has(libraryId)
//       })
//     }
//   }
//
//   /**
//    * 更新库状态（简化实现）
//    */
//   private async updateLibraryStatus(libraryId: string, status: ImageLibrary['status']): Promise<void> {
//     const library = this.libraries.get(libraryId)
//     if (library) {
//       library.status = status
//     }
//   }
//
//   /**
//    * 更新最后扫描时间（简化实现）
//    */
//   private async updateLastScanTime(libraryId: string): Promise<void> {
//     const library = this.libraries.get(libraryId)
//     if (library) {
//       library.lastScanAt = new Date()
//     }
//   }
//
//   /**
//    * 更新图片验证时间（简化实现）
//    */
//   private async updateImageVerification(imageId: string): Promise<void> {
//     // 简化实现，暂时不更新验证时间
//     console.log(`Image verification updated: ${imageId}`)
//   }
//
//   /**
//    * 调度AI分析任务 - 使用完整记录
//    */
//   private async scheduleAIAnalysisWithRecord(imageRecord: ImageRecord): Promise<void> {
//     if (!this.aiService) {
//       console.log('AI service not available, skipping AI analysis')
//       return
//     }
//
//     const imageId = imageRecord.id
//
//     // 使用并发控制限制AI分析数量
//     await this.withConcurrencyControl(
//       'ai-analysis',
//       this.MAX_CONCURRENT_AI_OPERATIONS,
//       async () => {
//         // 避免重复分析
//         if (this.aiAnalysisQueue.has(imageId)) {
//           return
//         }
//
//         const analysisPromise = this.performAIAnalysisWithRecord(imageRecord)
//         this.aiAnalysisQueue.set(imageId, analysisPromise)
//
//         try {
//           await analysisPromise
//         } catch (error) {
//           console.error(`AI analysis failed for image ${imageId}:`, error)
//         } finally {
//           this.aiAnalysisQueue.delete(imageId)
//         }
//       }
//     )
//   }
//
//   /**
//    * 执行AI分析 - 使用完整记录
//    */
//   private async performAIAnalysisWithRecord(imageRecord: ImageRecord): Promise<void> {
//     try {
//       console.log(`🔍 开始AI分析图片: ${imageRecord.id}`);
//
//       // 使用路径解析器获取完整路径
//       const imagePath = this.pathResolver.resolveImagePath(imageRecord.imagePath)
//       if (!imagePath) {
//         throw new Error(`无法解析图片路径: ${imageRecord.imagePath}`)
//       }
//
//       // 验证文件是否存在
//       if (!existsSync(imagePath)) {
//         throw new Error(`图片文件不存在: ${imagePath}`)
//       }
//
//       // 读取文件数据
//       const buffer = await this.safeFileOperation(
//         () => fs.readFile(imagePath),
//         imagePath,
//         '读取图片文件'
//       )
//
//       if (!buffer) {
//         throw new Error(`读取图片文件失败: ${imagePath}`)
//       }
//
//       // 执行AI分析
//       const base64Data = buffer.toString('base64')
//       const analysisResult = await this.aiService!.analyzeImage(base64Data)
//
//       // 生成embedding
//       let embedding: number[] = []
//       try {
//         if (!this.aiService) {
//           throw new Error('AI服务未初始化');
//         }
//
//         const embeddingResult = await this.aiService.generateEmbedding(analysisResult.description)
//         embedding = embeddingResult.embedding
//
//       } catch (embeddingError) {
//         console.error(`❌ 向量生成失败 for image ${imageRecord.id}:`, embeddingError);
//       }
//
//       // 直接更新传入的imageRecord对象
//       imageRecord.description = analysisResult.description || '';
//       imageRecord.tags = analysisResult.tags || [];
//       imageRecord.tags_flat = analysisResult.tags_flat || analysisResult.tags || [];
//       imageRecord.embedding = embedding;
//       imageRecord.structured_metadata = analysisResult.structured_data;
//
//       // 保存完整的记录到数据库
//       await this.saveImageRecord(imageRecord);
//
//       console.log(`✅ AI分析完成: ${imageRecord.id} (embedding: ${embedding.length}维)`);
//     } catch (error) {
//       console.error(`❌ AI analysis error for image ${imageRecord.id}:`, error);
//       throw error
//     }
//   }
//
//   /**
//    * 调度AI分析任务
//    */
//   private async scheduleAIAnalysis(imageId: string): Promise<void> {
//     if (!this.aiService) {
//       console.log('AI service not available, skipping AI analysis')
//       return
//     }
//
//     // 避免重复分析
//     if (this.aiAnalysisQueue.has(imageId)) {
//       return
//     }
//
//     const analysisPromise = this.performAIAnalysis(imageId)
//     this.aiAnalysisQueue.set(imageId, analysisPromise)
//
//     try {
//       await analysisPromise
//     } catch (error) {
//       console.error(`AI analysis failed for image ${imageId}:`, error)
//     } finally {
//       this.aiAnalysisQueue.delete(imageId)
//     }
//   }
//
//   /**
//    * 执行AI分析
//    */
//   private async performAIAnalysis(imageId: string): Promise<void> {
//     try {
//       // 获取图片数据
//       const imageData = await this.getImageData(imageId)
//       if (!imageData) {
//         throw new Error(`Image not found: ${imageId}`)
//       }
//
//       // 执行AI分析
//       const base64Data = imageData.buffer.toString('base64')
//       const analysisResult = await this.aiService!.analyzeImage(base64Data)
//
//       // 生成embedding
//       let embedding: number[] = []
//       try {
//         const embeddingResult = await this.aiService!.generateEmbedding(analysisResult.description)
//         embedding = embeddingResult.embedding
//       } catch (embeddingError) {
//         console.warn(`Failed to generate embedding for image ${imageId}:`, embeddingError)
//       }
//
//       // 更新数据库记录
//       await this.updateAIAnalysis(imageId, analysisResult, embedding)
//
//       console.log(`AI analysis completed for image ${imageId}`)
//     } catch (error) {
//       console.error(`AI analysis error for image ${imageId}:`, error)
//       throw error
//     }
//   }
//
//   /**
//    * 更新AI分析结果
//    */
//   private async updateAIAnalysis(imageId: string, analysisResult: ImageAnalysisResult, embedding: number[] = []): Promise<void> {
//     try {
//       // 更新图片记录的描述和标签
//       const updateData = {
//         description: analysisResult.description || '',
//         tags: analysisResult.tags || [],
//         embedding: embedding
//       }
//
//       // 首先查询现有记录
//       const queryResult = await this.databaseService.queryImages({
//         expr: `id == "${imageId}"`
//       });
//
//       if (!queryResult.results || queryResult.results.length === 0) {
//         console.warn(`未找到图片记录: ${imageId}, 尝试重新插入`);
//         return;
//       }
//
//       const existingRecord = queryResult.results[0];
//
//       // 构建更新后的完整记录
//       const updatedRecord = {
//         ...existingRecord,
//         description: updateData.description,
//         tags: updateData.tags,
//         tags_flat: analysisResult.tags_flat || updateData.tags, // 使用结构化标签或传统标签
//         embedding: updateData.embedding,
//         structured_metadata: analysisResult.structured_data || existingRecord.structured_metadata
//       };
//
//       // 删除旧记录
//       await this.databaseService.deleteImage(imageId);
//
//       // 插入更新后的记录
//       const insertResult = await this.databaseService.insertImages([updatedRecord]);
//
//       if (!insertResult.success) {
//         throw new Error(`插入更新记录失败: ${insertResult.error}`);
//       }
//
//     } catch (error) {
//       console.error(`更新AI分析结果失败 for image ${imageId}:`, error);
//       throw error;
//     }
//   }
//
//   /**
//    * 设置AI服务
//    */
//   public setAIService(aiService: AIService): void {
//     this.aiService = aiService
//   }
//
//   /**
//    * 获取AI分析队列状态
//    */
//   public getAIAnalysisQueueStatus(): { pending: number; imageIds: string[] } {
//     return {
//       pending: this.aiAnalysisQueue.size,
//       imageIds: Array.from(this.aiAnalysisQueue.keys())
//     }
//   }
//
//
//
//   /**
//    * 生成唯一ID
//    */
//   private generateId(): string {
//     return crypto.randomBytes(16).toString('hex')
//   }
//
//
//   /**
//    * 获取库中的图片
//    */
//   async getLibraryImages(libraryId: string, limit: number = 50, offset: number = 0): Promise<ImageRecord[]> {
//     // 使用现有的queryImages方法，通过表达式过滤
//     const result = await this.databaseService.queryImages({
//       expr: `library_id == "${libraryId}"`,
//       limit
//     })
//
//     return result.results || []
//   }
//
//   /**
//    * 注册扫描进度回调
//    */
//   onScanProgress(libraryId: string, callback: (progress: ScanProgress) => void): void {
//     this.scanProgressCallbacks.set(libraryId, callback)
//   }
//
//   /**
//    * 移除扫描进度回调
//    */
//   removeScanProgressCallback(libraryId: string): void {
//     this.scanProgressCallbacks.delete(libraryId)
//   }
//
//   /**
//    * 启动文件监控
//    */
//   startFileWatching(): void {
//     if (!this.config.fileWatching.enabled || this.fileWatcher) {
//       return
//     }
//
//     // 实现文件监控逻辑
//     // 这里可以监控已注册库的文件变化
//   }
//
//   /**
//    * 停止文件监控
//    */
//   stopFileWatching(): void {
//     if (this.fileWatcher) {
//       this.fileWatcher.close()
//       this.fileWatcher = null
//     }
//   }
//
//
//   /**
//    * 获取图片库扫描进度
//    */
//   async getLibraryScanProgress(libraryId: string): Promise<ScanProgress | null> {
//     const library = this.libraries.get(libraryId)
//     if (!library) return null
//
//     return {
//       libraryId,
//       ...library.scanProgress,
//       isScanning: this.scanQueue.has(libraryId)
//     }
//   }
//
//
//
//
//   /**
//    * 获取图片数据
//    */
//   async getImageData(imageId: string): Promise<{ buffer: Buffer; mimeType: string } | null> {
//     try {
//       // 使用数据库操作重试机制
//       let result = await this.safeDatabaseOperation(
//         () => this.databaseService.queryImages({ expr: `id == "${imageId}"` }),
//         '查询图片记录通过ID'
//       )
//
//       // 如果通过ID没有找到，尝试通过imagePath查找（兼容性处理）
//       if (!result?.results || result.results.length === 0) {
//         result = await this.safeDatabaseOperation(
//           () => this.databaseService.queryImages({ expr: `imagePath == "${imageId}"` }),
//           '查询图片记录通过路径'
//         )
//       }
//
//       if (!result?.results || result.results.length === 0) {
//         throw new Error(`图片记录不存在: ${imageId}`)
//       }
//
//       const imageRecord = result.results[0]
//
//       // 使用路径解析器获取完整路径
//       const imagePath = this.pathResolver.resolveImagePath(imageRecord.imagePath)
//       if (!imagePath) {
//         throw new Error(`无法解析图片路径: ${imageRecord.imagePath}`)
//       }
//
//       // 验证文件是否存在
//       if (!existsSync(imagePath)) {
//         throw new Error(`图片文件不存在: ${imagePath}`)
//       }
//
//       // 读取文件数据
//       const buffer = await this.safeFileOperation(
//         () => fs.readFile(imagePath),
//         imagePath,
//         '读取图片文件'
//       )
//
//       if (!buffer) {
//         throw new Error(`读取图片文件失败: ${imagePath}`)
//       }
//
//       // 根据文件扩展名确定MIME类型
//       const ext = path.extname(imagePath).toLowerCase()
//       let mimeType = 'image/jpeg' // 默认值
//
//       switch (ext) {
//         case '.png':
//           mimeType = 'image/png'
//           break
//         case '.jpg':
//         case '.jpeg':
//           mimeType = 'image/jpeg'
//           break
//         case '.gif':
//           mimeType = 'image/gif'
//           break
//         case '.webp':
//           mimeType = 'image/webp'
//           break
//         case '.bmp':
//           mimeType = 'image/bmp'
//           break
//         default:
//           mimeType = 'image/jpeg'
//       }
//
//       return { buffer, mimeType }
//
//     } catch (error) {
//       this.logger.error('获取图片数据失败', 'getImageData', { imageId, error: error instanceof Error ? error.message : String(error) })
//       throw error
//     }
//   }
//
//   // ===== 进度管理相关方法 =====
//
//   /**
//    * 获取所有活动进度
//    */
//   getActiveProgresses(): BaseProgress[] {
//     return this.progressManager.getActiveProgresses()
//   }
//
//   /**
//    * 获取特定进度详情
//    */
//   getProgressById(progressId: string): BaseProgress | undefined {
//     return this.progressManager.getProgress(progressId)
//   }
//
//   /**
//    * 暂停进度
//    */
//   pauseProgress(progressId: string): { success: boolean; error?: string } {
//     try {
//       this.progressManager.pauseProgress(progressId)
//       return { success: true }
//     } catch (error) {
//       return { success: false, error: error instanceof Error ? error.message : String(error) }
//     }
//   }
//
//   /**
//    * 恢复进度
//    */
//   resumeProgress(progressId: string): { success: boolean; error?: string } {
//     try {
//       this.progressManager.resumeProgress(progressId)
//       return { success: true }
//     } catch (error) {
//       return { success: false, error: error instanceof Error ? error.message : String(error) }
//     }
//   }
//
//   /**
//    * 取消进度
//    */
//   cancelProgress(progressId: string): { success: boolean; error?: string } {
//     try {
//       this.progressManager.cancelProgress(progressId)
//       return { success: true }
//     } catch (error) {
//       return { success: false, error: error instanceof Error ? error.message : String(error) }
//     }
//   }
//
//   // ===== 缺失的图片库管理方法 =====
//
//   /**
//    * 获取单个图片库
//    */
//   async getLibrary(libraryId: string): Promise<ImageLibrary | null> {
//     console.log('📚 ImageLibraryService.getLibrary 调用:', libraryId)
//     const library = this.libraries.get(libraryId) || null
//     console.log('📚 找到的库:', library ? { id: library.id, name: library.name } : 'null')
//     return library
//   }
//
//   /**
//    * 移除图片库
//    */
//   async removeLibrary(libraryId: string): Promise<void> {
//     console.log('📚 ImageLibraryService.removeLibrary 调用:', libraryId)
//
//     const library = this.libraries.get(libraryId)
//     if (!library) {
//       throw new Error(`图片库不存在: ${libraryId}`)
//     }
//
//     // 取消正在进行的扫描
//     if (this.scanQueue.has(libraryId)) {
//       console.log('📚 取消正在进行的扫描:', libraryId)
//       // 这里可以添加取消扫描的逻辑
//     }
//
//     // 从内存中移除
//     this.libraries.delete(libraryId)
//
//     // 从持久化存储中移除
//     await this.persistenceService.removeLibrary(libraryId)
//
//     // 从路径验证器中移除
//     this.pathValidator.unregisterLibrary(libraryId)
//
//     console.log('📚 图片库移除成功:', libraryId)
//   }
//
//   /**
//    * 更新图片库名称
//    */
//   async updateLibraryName(libraryId: string, newName: string): Promise<void> {
//     console.log('📚 ImageLibraryService.updateLibraryName 调用:', { libraryId, newName })
//
//     const library = this.libraries.get(libraryId)
//     if (!library) {
//       throw new Error(`图片库不存在: ${libraryId}`)
//     }
//
//     library.name = newName
//
//     // 更新持久化存储
//     await this.persistenceService.updateLibrary(libraryId, { name: newName })
//
//     console.log('📚 图片库名称更新成功:', { libraryId, newName })
//   }
//
//   /**
//    * 获取图片库扫描进度 - 使用现有正确实现
//    */
//
//   /**
//    * 获取图片库中的图片 - 使用现有实现
//    */
//
//   /**
//    * 搜索相似图片
//    */
//   async searchSimilarImages(imageId: string, threshold?: number): Promise<any[]> {
//     console.log('📚 ImageLibraryService.searchSimilarImages 调用:', { imageId, threshold })
//     // 暂时返回空数组
//     return []
//   }
//
//   /**
//    * 验证图片路径
//    */
//   async validateImagePath(imagePath: string): Promise<any> {
//     console.log('📚 ImageLibraryService.validateImagePath 调用:', imagePath)
//     return this.pathValidator.validatePath(imagePath)
//   }
//
//   /**
//    * 获取重复图片 - 实时查询数据库
//    */
//   async getDuplicateImages(libraryId?: string): Promise<ImageDuplicates[]> {
//     console.log('📚 ImageLibraryService.getDuplicateImages 调用:', libraryId)
//
//     try {
//       // 查询所有图片记录，按 fileChecksum 分组
//       const result = await this.safeDatabaseOperation(
//         () => this.databaseService.queryImages({}),
//         '查询所有图片记录'
//       )
//
//       if (!result?.results || result.results.length === 0) {
//         return []
//       }
//
//       // 按 md5Hash 分组
//       const hashGroups = new Map<string, ImageRecord[]>()
//
//       for (const record of result.results) {
//         const md5Hash = record.metadata?.fileChecksum
//         if (!md5Hash) continue
//
//         // 如果指定了库ID，只处理该库的图片
//         if (libraryId) {
//           const library = this.libraries.get(libraryId)
//           if (library) {
//             const fullPath = this.pathResolver.resolveImagePath(record.imagePath)
//             if (!fullPath || !fullPath.startsWith(library.rootPath)) {
//               continue
//             }
//           }
//         }
//
//         if (!hashGroups.has(md5Hash)) {
//           hashGroups.set(md5Hash, [])
//         }
//         hashGroups.get(md5Hash)!.push(record)
//       }
//
//       // 找出有重复的组（超过1个文件的组）
//       const duplicates: ImageDuplicates[] = []
//
//       for (const [md5Hash, records] of hashGroups.entries()) {
//         if (records.length > 1) {
//           const primaryRecord = records[0]
//           const duplicateRecord: ImageDuplicates = {
//             compositeKey: `${primaryRecord.imagePath}|${primaryRecord.metadata.filesize}|${md5Hash}`,
//             originalPath: primaryRecord.imagePath,
//             fileSize: primaryRecord.metadata.filesize,
//             md5Hash,
//             primaryImageId: primaryRecord.id,
//             duplicateImageIds: records.slice(1).map(r => r.id)
//           }
//           duplicates.push(duplicateRecord)
//         }
//       }
//
//       console.log(`📚 找到 ${duplicates.length} 组重复图片`)
//       return duplicates
//
//     } catch (error) {
//       console.error('查询重复图片失败:', error)
//       return []
//     }
//   }
//
// }