// /**
//  * 图像库扫描服务 (简化版)
//  * 单线程处理，一个一个文件处理，KISS原则
//  */
//
// import * as fs from 'node:fs/promises'
// import * as path from 'node:path'
// import * as crypto from 'node:crypto'
// import { existsSync } from 'node:fs'
// import { SQLiteLibraryConfigService } from '../database/SQLiteLibraryConfigService'
// import { DatabaseService } from '../database/DatabaseService'
// import { AIService } from '../ai/AIService'
// import { ImageRecord } from '../../src/types/database'
// import { ErrorLogger } from './ErrorLogger'
//
// // 扫描任务接口
// interface ScanTask {
//   libraryId: string
//   scanId: string
//   options: ScanOptions
//   status: 'pending' | 'scanning' | 'completed' | 'error'
//   createdAt: Date
// }
//
// // 扫描选项接口
// interface ScanOptions {
//   forceRescan?: boolean
//   maxDepth?: number
//   includeSubfolders?: boolean
//   fileTypes?: string[]
// }
//
// // 扫描进度接口
// interface ScanProgress {
//   libraryId: string
//   scanId: string
//   status: 'pending' | 'scanning' | 'completed' | 'error'
//   totalFiles: number
//   processedFiles: number
//   failedFiles: number
//   skippedFiles: number
//   currentFile?: string
//   startTime: string
//   elapsedTime: number
//   processingSpeed: number
//   errors: string[]
// }
//
// // 文件信息接口
// interface FileInfo {
//   path: string
//   name: string
//   size: number
//   lastModified: Date
//   extension: string
// }
//
// /**
//  * 图像库扫描服务 (简化版)
//  */
// export class ImageLibraryScanService {
//   private scanQueue: ScanTask[] = []
//   private activeScans: Map<string, ScanProgress> = new Map()
//   private isProcessing: boolean = false
//   private processingInterval: NodeJS.Timeout | null = null
//
//   // 配置参数
//   private readonly SUPPORTED_FORMATS = ['jpg', 'jpeg', 'png', 'gif', 'webp', 'svg', 'bmp', 'tiff']
//
//   constructor(
//     private dbConfigService: SQLiteLibraryConfigService,
//     private imageDbService: DatabaseService,
//     private aiService: AIService,
//     private logger: ErrorLogger
//   ) {
//     this.startProcessingLoop()
//   }
//
//   /**
//    * 开始扫描
//    */
//   async startScan(libraryId: string, options: ScanOptions = {}): Promise<string> {
//     // 检查是否已经在扫描
//     if (this.activeScans.has(libraryId)) {
//       const existingProgress = this.activeScans.get(libraryId)!
//       if (['pending', 'scanning'].includes(existingProgress.status)) {
//         throw new Error(`library ${libraryId} 正在扫描中`)
//       }
//     }
//
//     // 生成扫描ID
//     const scanId = this.generateScanId()
//
//     // 获取library配置
//     const libraryConfig = await this.dbConfigService.getLibraryConfigById(libraryId)
//     if (!libraryConfig) {
//       throw new Error(`library不存在: ${libraryId}`)
//     }
//
//     // 创建扫描任务
//     const task: ScanTask = {
//       libraryId,
//       scanId,
//       options: {
//         forceRescan: options.forceRescan ?? false,
//         maxDepth: options.maxDepth ?? 0,
//         includeSubfolders: options.includeSubfolders ?? true,
//         fileTypes: options.fileTypes ?? this.SUPPORTED_FORMATS
//       },
//       status: 'pending',
//       createdAt: new Date()
//     }
//
//     // 创建初始进度
//     const progress: ScanProgress = {
//       libraryId,
//       scanId,
//       status: 'pending',
//       totalFiles: 0,
//       processedFiles: 0,
//       failedFiles: 0,
//       skippedFiles: 0,
//       startTime: new Date().toISOString(),
//       elapsedTime: 0,
//       processingSpeed: 0,
//       errors: []
//     }
//
//     // 添加到队列和活跃扫描
//     this.scanQueue.push(task)
//     this.activeScans.set(libraryId, progress)
//
//     // 保存到数据库
//     await this.saveScanProgressToDb(progress)
//
//     this.logger.info('扫描任务已加入队列', 'ImageLibraryScanService', { libraryId, scanId })
//
//     return scanId
//   }
//
//   /**
//    * 获取扫描进度
//    */
//   async getScanProgress(libraryId: string): Promise<ScanProgress | null> {
//     // 优先返回内存中的进度
//     const memoryProgress = this.activeScans.get(libraryId)
//     if (memoryProgress) {
//       // 更新已用时间
//       const startTime = new Date(memoryProgress.startTime)
//       memoryProgress.elapsedTime = Math.floor((Date.now() - startTime.getTime()) / 1000)
//
//       // 计算处理速度
//       if (memoryProgress.elapsedTime > 0) {
//         memoryProgress.processingSpeed = memoryProgress.processedFiles / memoryProgress.elapsedTime
//       }
//
//       return memoryProgress
//     }
//
//     // 从数据库加载进度
//     return await this.loadScanProgressFromDb(libraryId)
//   }
//
//   /**
//    * 启动处理循环
//    */
//   private startProcessingLoop(): void {
//     this.processingInterval = setInterval(async () => {
//       if (!this.isProcessing && this.scanQueue.length > 0) {
//         await this.processNextTask()
//       }
//     }, 1000) // 每秒检查一次
//   }
//
//   /**
//    * 处理下一个任务
//    */
//   private async processNextTask(): Promise<void> {
//     if (this.isProcessing || this.scanQueue.length === 0) {
//       return
//     }
//
//     this.isProcessing = true
//
//     try {
//       // 获取队列中的第一个任务（FIFO）
//       const task = this.scanQueue.shift()
//       if (task) {
//         await this.processScanTask(task)
//       }
//     } catch (error) {
//       this.logger.error('处理扫描任务失败', 'ImageLibraryScanService', { error })
//     } finally {
//       this.isProcessing = false
//     }
//   }
//
//   /**
//    * 处理扫描任务
//    */
//   private async processScanTask(task: ScanTask): Promise<void> {
//     const progress = this.activeScans.get(task.libraryId)
//     if (!progress) {
//       this.logger.error('未找到扫描进度', 'ImageLibraryScanService', { libraryId: task.libraryId })
//       return
//     }
//
//     try {
//       // 更新状态为扫描中
//       progress.status = 'scanning'
//       await this.saveScanProgressToDb(progress)
//
//       // 获取library配置
//       const libraryConfig = await this.dbConfigService.getLibraryConfigById(task.libraryId)
//       if (!libraryConfig) {
//         throw new Error(`library不存在: ${task.libraryId}`)
//       }
//
//       // 发现文件
//       this.logger.info('开始发现文件', 'ImageLibraryScanService', { libraryId: task.libraryId, path: libraryConfig.rootPath })
//       const files = await this.discoverFiles(libraryConfig.rootPath, task.options)
//
//       progress.totalFiles = files.length
//       await this.saveScanProgressToDb(progress)
//
//       this.logger.info('文件发现完成', 'ImageLibraryScanService', {
//         libraryId: task.libraryId,
//         totalFiles: files.length
//       })
//
//       // 处理文件（一个一个处理）
//       await this.processFiles(task.libraryId, files, task.options, progress)
//
//       // 完成扫描
//       progress.status = 'completed'
//       await this.saveScanProgressToDb(progress)
//
//       // 更新library统计信息
//       await this.updateLibraryStatistics(task.libraryId)
//
//       this.logger.info('扫描任务完成', 'ImageLibraryScanService', {
//         libraryId: task.libraryId,
//         totalFiles: progress.totalFiles,
//         processedFiles: progress.processedFiles,
//         failedFiles: progress.failedFiles
//       })
//
//     } catch (error) {
//       progress.status = 'error'
//       progress.errors.push(error instanceof Error ? error.message : String(error))
//       await this.saveScanProgressToDb(progress)
//
//       this.logger.error('扫描任务失败', 'ImageLibraryScanService', {
//         libraryId: task.libraryId,
//         error: error instanceof Error ? error.message : String(error)
//       })
//     } finally {
//       // 延迟清理已完成的进度（保留1小时）
//       setTimeout(() => {
//         if (progress.status === 'completed' || progress.status === 'error') {
//           this.activeScans.delete(task.libraryId)
//         }
//       }, 60 * 60 * 1000)
//     }
//   }
//
//   /**
//    * 发现文件
//    */
//   private async discoverFiles(rootPath: string, options: ScanOptions): Promise<FileInfo[]> {
//     const files: FileInfo[] = []
//     const maxDepth = options.maxDepth || 0
//     const includeSubfolders = options.includeSubfolders !== false
//     const supportedFormats = options.fileTypes || this.SUPPORTED_FORMATS
//
//     await this.discoverFilesRecursive(rootPath, files, 0, maxDepth, includeSubfolders, supportedFormats)
//
//     return files
//   }
//
//   /**
//    * 递归发现文件
//    */
//   private async discoverFilesRecursive(
//     dirPath: string,
//     files: FileInfo[],
//     currentDepth: number,
//     maxDepth: number,
//     includeSubfolders: boolean,
//     supportedFormats: string[]
//   ): Promise<void> {
//     try {
//       if (!existsSync(dirPath)) {
//         return
//       }
//
//       const entries = await fs.readdir(dirPath, { withFileTypes: true })
//
//       for (const entry of entries) {
//         const fullPath = path.join(dirPath, entry.name)
//
//         if (entry.isFile()) {
//           const ext = path.extname(entry.name).toLowerCase().slice(1)
//           if (supportedFormats.includes(ext)) {
//             try {
//               const stats = await fs.stat(fullPath)
//               files.push({
//                 path: fullPath,
//                 name: entry.name,
//                 size: stats.size,
//                 lastModified: stats.mtime,
//                 extension: ext
//               })
//             } catch (error) {
//               this.logger.warn('获取文件信息失败', 'ImageLibraryScanService', {
//                 path: fullPath,
//                 error: error instanceof Error ? error.message : String(error)
//               })
//             }
//           }
//         } else if (entry.isDirectory() && includeSubfolders) {
//           // 检查深度限制
//           if (maxDepth === 0 || currentDepth < maxDepth) {
//             await this.discoverFilesRecursive(
//               fullPath,
//               files,
//               currentDepth + 1,
//               maxDepth,
//               includeSubfolders,
//               supportedFormats
//             )
//           }
//         }
//       }
//     } catch (error) {
//       this.logger.error('递归发现文件失败', 'ImageLibraryScanService', {
//         dirPath,
//         error: error instanceof Error ? error.message : String(error)
//       })
//     }
//   }
//
//   /**
//    * 处理文件（一个一个处理）
//    */
//   private async processFiles(
//     libraryId: string,
//     files: FileInfo[],
//     options: ScanOptions,
//     progress: ScanProgress
//   ): Promise<void> {
//     for (let i = 0; i < files.length; i++) {
//       const file = files[i]
//
//       try {
//         // 更新当前处理的文件
//         progress.currentFile = file.path
//
//         // 检查文件是否已存在（如果不是强制重新扫描）
//         if (!options.forceRescan) {
//           const existing = await this.imageDbService.checkImageExists(file.path)
//           if (existing.exists) {
//             progress.skippedFiles++
//             progress.processedFiles++
//             continue
//           }
//         }
//
//         // 处理图片文件
//         this.logger.info('处理图片文件', 'ImageLibraryScanService', {libraryId, filePath: file.path})
//         const imageRecord = await this.processImageFile(file)
//         if (imageRecord) {
//           // 插入到数据库
//           await this.imageDbService.insertImages([imageRecord])
//           this.logger.debug('插入图片记录完成', 'ImageLibraryScanService', {libraryId, filePath: file.path})
//         }
//
//         progress.processedFiles++
//
//       } catch (error) {
//         progress.failedFiles++
//         progress.processedFiles++
//         progress.errors.push(`处理文件失败 ${file.path}: ${error instanceof Error ? error.message : String(error)}`)
//
//         this.logger.error('处理图片文件失败', 'ImageLibraryScanService', {
//           libraryId,
//           filePath: file.path,
//           error: error instanceof Error ? error.message : String(error)
//         })
//       }
//
//       // 每处理10个文件更新一次进度到数据库
//       if (i % 10 === 0 || i === files.length - 1) {
//         await this.saveScanProgressToDb(progress)
//       }
//     }
//
//     // 清除当前处理的文件
//     progress.currentFile = undefined
//   }
//
//   /**
//    * 处理单个图片文件
//    */
//   private async processImageFile(file: FileInfo): Promise<ImageRecord | null> {
//     try {
//       // 计算文件MD5
//       const md5 = await this.calculateMD5(file.path)
//
//       // 生成图片ID
//       const imageId = crypto.randomUUID()
//
//       // 构建基础记录
//       const imageRecord: ImageRecord = {
//         id: imageId,
//         imagePath: file.path,
//         description: '',
//         tags: [],
//         embedding: [],
//         metadata: {
//           filename: file.name,
//           filesize: file.size,
//           uploadTime: new Date().toISOString(),
//           dimensions: '0x0',
//           format: file.extension,
//           fileChecksum: md5
//         },
//         capturedAt: file.lastModified.toISOString()
//       }
//
//       // AI分析功能
//       try {
//         this.logger.debug('开始AI分析', 'ImageLibraryScanService', { filePath: file.path })
//         const analysis = await this.aiService.analyzeImageByPath(file.path)
//
//         if (analysis) {
//           imageRecord.description = analysis.description || ''
//           imageRecord.tags = analysis.tags || []
//
//           // 如果有描述，生成embedding用于向量搜索
//           if (analysis.description) {
//             const embeddingResult = await this.aiService.generateEmbedding(analysis.description)
//             if (embeddingResult && embeddingResult.embedding) {
//               imageRecord.embedding = embeddingResult.embedding
//             }
//           }
//
//           this.logger.debug('AI分析完成', 'ImageLibraryScanService', {
//             filePath: file.path,
//             description: analysis.description,
//             tagsCount: analysis.tags?.length || 0
//           })
//         }
//       } catch (error) {
//         // AI分析失败不影响图片入库，只记录错误
//         this.logger.warn('AI分析失败，继续处理图片', 'ImageLibraryScanService', {
//           filePath: file.path,
//           error: error instanceof Error ? error.message : String(error)
//         })
//       }
//
//       return imageRecord
//
//     } catch (error) {
//       this.logger.error('处理图片文件失败', 'ImageLibraryScanService', {
//         filePath: file.path,
//         error: error instanceof Error ? error.message : String(error)
//       })
//       return null
//     }
//   }
//
//   /**
//    * 计算文件MD5
//    */
//   private async calculateMD5(filePath: string): Promise<string> {
//     const hash = crypto.createHash('md5')
//     const data = await fs.readFile(filePath)
//     hash.update(data)
//     return hash.digest('hex')
//   }
//
//   /**
//    * 获取MIME类型
//    */
//   private getMimeType(extension: string): string {
//     const mimeTypes: Record<string, string> = {
//       'jpg': 'image/jpeg',
//       'jpeg': 'image/jpeg',
//       'png': 'image/png',
//       'gif': 'image/gif',
//       'webp': 'image/webp',
//       'svg': 'image/svg+xml',
//       'bmp': 'image/bmp',
//       'tiff': 'image/tiff'
//     }
//     return mimeTypes[extension.toLowerCase()] || 'image/jpeg'
//   }
//
//   /**
//    * 更新library统计信息
//    */
//   private async updateLibraryStatistics(libraryId: string): Promise<void> {
//     try {
//       // TODO: 实现统计信息更新逻辑
//       // 统计图片数量、总大小等
//
//       this.logger.info('library统计信息更新完成', 'ImageLibraryScanService', { libraryId })
//     } catch (error) {
//       this.logger.error('更新library统计信息失败', 'ImageLibraryScanService', {
//         libraryId,
//         error: error instanceof Error ? error.message : String(error)
//       })
//     }
//   }
//
//   /**
//    * 保存扫描进度到数据库
//    */
//   private async saveScanProgressToDb(progress: ScanProgress): Promise<void> {
//     try {
//       // 更新library表中的scanProgress字段
//       const updateData = {
//         scanProgress: {
//           total: progress.totalFiles,
//           processed: progress.processedFiles,
//           failed: progress.failedFiles,
//           skipped: progress.skippedFiles,
//           duplicates: 0, // 暂时设为0，后续实现重复检测
//
//           // 扫描控制信息
//           scanId: progress.scanId,
//           status: progress.status,
//           currentFile: progress.currentFile,
//           startTime: progress.startTime,
//           elapsedTime: progress.elapsedTime,
//           processingSpeed: progress.processingSpeed,
//           errors: progress.errors
//         },
//         // 数据库status约束只允许: 'active', 'offline', 'removed'
//         // 扫描状态通过scanProgress.status字段记录
//         status: (progress.status === 'error' ? 'offline' : 'active') as 'active' | 'offline' | 'removed',
//         lastScanAt: new Date().toISOString()
//       }
//
//       const result = await this.dbConfigService.updateLibraryConfig(progress.libraryId, updateData)
//       if (!result.success) {
//         throw new Error(result.error)
//       }
//
//       this.logger.debug('扫描进度已保存到数据库', 'ImageLibraryScanService', {
//         libraryId: progress.libraryId,
//         status: progress.status,
//         processedFiles: progress.processedFiles,
//         totalFiles: progress.totalFiles
//       })
//     } catch (error) {
//       this.logger.error('保存扫描进度失败', 'ImageLibraryScanService', {
//         libraryId: progress.libraryId,
//         error: error instanceof Error ? error.message : String(error)
//       })
//     }
//   }
//
//   /**
//    * 从数据库加载扫描进度
//    */
//   private async loadScanProgressFromDb(libraryId: string): Promise<ScanProgress | null> {
//     try {
//       const config = await this.dbConfigService.getLibraryConfigById(libraryId)
//       if (!config || !config.scanProgress) {
//         return null
//       }
//
//       const sp = config.scanProgress
//
//       // 如果扫描已完成或没有详细进度信息，返回null
//       if (!sp.scanId || !sp.status || ['completed', 'cancelled'].includes(sp.status)) {
//         return null
//       }
//
//       // 构建进度对象
//       const progress: ScanProgress = {
//         libraryId,
//         scanId: sp.scanId,
//         status: sp.status as any,
//         totalFiles: sp.total,
//         processedFiles: sp.processed,
//         failedFiles: sp.failed,
//         skippedFiles: sp.skipped,
//         currentFile: sp.currentFile,
//         startTime: sp.startTime || new Date().toISOString(),
//         elapsedTime: sp.elapsedTime || 0,
//         processingSpeed: sp.processingSpeed || 0,
//         errors: sp.errors || []
//       }
//
//       this.logger.debug('从数据库加载扫描进度', 'ImageLibraryScanService', {
//         libraryId,
//         status: progress.status,
//         processedFiles: progress.processedFiles,
//         totalFiles: progress.totalFiles
//       })
//
//       return progress
//     } catch (error) {
//       this.logger.error('加载扫描进度失败', 'ImageLibraryScanService', {
//         libraryId,
//         error: error instanceof Error ? error.message : String(error)
//       })
//       return null
//     }
//   }
//
//   /**
//    * 生成扫描ID
//    */
//   private generateScanId(): string {
//     return `scan_${Date.now()}_${crypto.randomBytes(8).toString('hex')}`
//   }
//
//   /**
//    * 销毁服务
//    */
//   destroy(): void {
//     if (this.processingInterval) {
//       clearInterval(this.processingInterval)
//       this.processingInterval = null
//     }
//
//     this.scanQueue.length = 0
//     this.activeScans.clear()
//   }
// }