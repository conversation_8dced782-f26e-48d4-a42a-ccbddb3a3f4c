import * as path from 'node:path'
import * as fs from 'node:fs'

export interface ValidationResult {
  isValid: boolean
  sanitizedPath?: string
  errors: string[]
  warnings: string[]
}

export class PathSecurityValidator {
  private allowedDirectories: string[]
  private maxPathLength: number = 260 // Windows path limit
  private dangerousPatterns: RegExp[]

  constructor(allowedDirectories: string[] = []) {
    this.allowedDirectories = allowedDirectories.map(dir => path.resolve(dir))
    this.dangerousPatterns = [
      /\.\./,                    // 路径遍历
      /[<>:"|?*]/,              // Windows 非法字符
      /[\x00-\x1f]/,            // 控制字符
      /^(CON|PRN|AUX|NUL|COM[1-9]|LPT[1-9])$/i, // Windows 保留名称
      /\/$|\\$/,                // 以路径分隔符结尾
    ]
  }

  /**
   * 验证路径安全性
   */
  validatePath(inputPath: string): ValidationResult {
    const result: ValidationResult = {
      isValid: true,
      errors: [],
      warnings: []
    }

    // 基本验证
    if (!inputPath || typeof inputPath !== 'string') {
      result.isValid = false
      result.errors.push('路径不能为空')
      return result
    }

    // 检查是否为base64数据URL
    if (this.isBase64DataUrl(inputPath)) {
      // base64数据URL被认为是有效的，不需要路径验证
      result.sanitizedPath = inputPath
      result.warnings.push('检测到base64数据URL，跳过路径验证')
      return result
    }

    // 长度验证
    if (inputPath.length > this.maxPathLength) {
      result.isValid = false
      result.errors.push(`路径长度超过限制 (${this.maxPathLength} 字符)`)
    }

    // 危险模式检测
    for (const pattern of this.dangerousPatterns) {
      if (pattern.test(inputPath)) {
        // 特殊处理：允许 Windows 驱动器路径中的冒号 (如 C:)
        if (pattern.source === '[<>:"|?*]' && /^[A-Za-z]:[\\\/]/.test(inputPath)) {
          // 这是一个有效的 Windows 驱动器路径，跳过冒号检查
          continue
        }
        result.isValid = false
        result.errors.push(`路径包含危险字符或模式: ${pattern.source}`)
      }
    }

    // 路径遍历检测
    if (this.containsPathTraversal(inputPath)) {
      result.isValid = false
      result.errors.push('检测到路径遍历攻击尝试')
    }

    // 清理和标准化路径
    try {
      const sanitizedPath = this.sanitizePath(inputPath)
      result.sanitizedPath = sanitizedPath

      // 检查是否在允许的目录范围内
      if (this.allowedDirectories.length > 0) {
        const resolvedPath = path.resolve(sanitizedPath)
        if (!this.isPathAllowed(resolvedPath)) {
          result.isValid = false
          result.errors.push('路径不在允许的目录范围内')
        }
      }
    } catch (error) {
      result.isValid = false
      result.errors.push(`路径处理失败: ${error instanceof Error ? error.message : String(error)}`)
    }

    return result
  }

  /**
   * 检查是否为base64数据URL
   */
  private isBase64DataUrl(input: string): boolean {
    // 检查是否以data:开头的数据URL
    const dataUrlPattern = /^data:([a-zA-Z0-9][a-zA-Z0-9\/+]*);base64,([A-Za-z0-9+/=]+)$/
    return dataUrlPattern.test(input)
  }

  /**
   * 检查路径是否在允许的目录范围内
   */
  isPathAllowed(resolvedPath: string): boolean {
    if (this.allowedDirectories.length === 0) {
      return true
    }

    const normalizedPath = path.normalize(resolvedPath)
    
    return this.allowedDirectories.some(allowedDir => {
      const normalizedAllowedDir = path.normalize(allowedDir)
      return normalizedPath.startsWith(normalizedAllowedDir + path.sep) || 
             normalizedPath === normalizedAllowedDir
    })
  }

  /**
   * 清理和标准化路径
   */
  sanitizePath(inputPath: string): string {
    // 移除多余的空白字符
    let sanitized = inputPath.trim()
    
    // 标准化路径分隔符
    sanitized = path.normalize(sanitized)
    
    // 移除连续的路径分隔符
    sanitized = sanitized.replace(/[/\\]+/g, path.sep)
    
    // 移除尾部的路径分隔符
    if (sanitized.endsWith(path.sep) && sanitized.length > 1) {
      sanitized = sanitized.slice(0, -1)
    }
    
    return sanitized
  }

  /**
   * 检测路径遍历攻击
   */
  private containsPathTraversal(inputPath: string): boolean {
    const normalizedPath = path.normalize(inputPath)
    
    // 检查是否包含 ../ 或 ..\
    if (normalizedPath.includes('..')) {
      return true
    }
    
    // 检查是否尝试访问根目录或系统目录
    const dangerousPaths = [
      '/etc',
      '/bin',
      '/usr',
      '/var',
      '/root',
      'C:\\Windows',
      'C:\\System32',
      'C:\\Program Files'
    ]
    
    const lowerPath = normalizedPath.toLowerCase()
    return dangerousPaths.some(dangerous => 
      lowerPath.startsWith(dangerous.toLowerCase())
    )
  }

  /**
   * 获取允许的目录列表
   */
  getAllowedDirectories(): string[] {
    return [...this.allowedDirectories]
  }

  /**
   * 设置允许的目录列表
   */
  setAllowedDirectories(directories: string[]): void {
    this.allowedDirectories = directories.map(dir => path.resolve(dir))
  }

  /**
   * 添加允许的目录
   */
  addAllowedDirectory(directory: string): void {
    const resolvedDir = path.resolve(directory)
    if (!this.allowedDirectories.includes(resolvedDir)) {
      this.allowedDirectories.push(resolvedDir)
    }
  }

  /**
   * 移除允许的目录
   */
  removeAllowedDirectory(directory: string): void {
    const resolvedDir = path.resolve(directory)
    const index = this.allowedDirectories.indexOf(resolvedDir)
    if (index > -1) {
      this.allowedDirectories.splice(index, 1)
    }
  }
}