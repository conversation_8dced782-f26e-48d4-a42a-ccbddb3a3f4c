import { ImageDAO } from '../dao/interfaces/ImageDAO'
import { LibraryDAO } from '../dao/interfaces/LibraryDAO'
import { TagDAO } from '../dao/interfaces/TagDAO'

/**
 * 数据库查询结果接口
 */
export interface TableDataResult {
  success: boolean
  data?: any[]
  total?: number
  page?: number
  pageSize?: number
  totalPages?: number
  error?: string
}

export interface TablesResult {
  success: boolean
  tables?: string[]
  error?: string
}

/**
 * 数据库服务
 * 使用现有的DAO提供数据库查询功能
 */
export class DatabaseService {
  constructor(
    private imageDAO: ImageDAO,
    private libraryDAO: LibraryDAO,
    private tagDAO: TagDAO
  ) {}

  /**
   * 获取所有可查询的表名
   */
  async getAllTables(): Promise<TablesResult> {
    try {
      // 返回我们支持查询的表
      const tables = [
        'images',           // 图片表
        'library_configs',  // 图片库配置表
        'tags'             // 标签表
      ]

      return {
        success: true,
        tables
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : '获取表列表失败'
      }
    }
  }

  /**
   * 获取表数据（支持分页）
   */
  async getTableData(tableName: string, page: number = 1, pageSize: number = 20): Promise<TableDataResult> {
    try {
      switch (tableName) {
        case 'images':
          return await this.getImagesData(page, pageSize)
        
        case 'library_configs':
          return await this.getLibraryConfigsData(page, pageSize)
        
        case 'tags':
          return await this.getTagsData(page, pageSize)
        
        default:
          return {
            success: false,
            error: `不支持的表名: ${tableName}`
          }
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : '查询表数据失败'
      }
    }
  }

  /**
   * 获取图片数据
   */
  private async getImagesData(page: number, pageSize: number): Promise<TableDataResult> {
    try {
      const offset = (page - 1) * pageSize
      
      // 使用ImageDAO查询图片数据
      const result = await this.imageDAO.queryImages({
        limit: pageSize,
        offset: offset
      })

      if (!result.results) {
        return {
          success: false,
          error: '查询图片数据失败'
        }
      }

      const total = result.total || result.results.length
      const totalPages = Math.ceil(total / pageSize)

      return {
        success: true,
        data: result.results,
        total,
        page,
        pageSize,
        totalPages
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : '查询图片数据失败'
      }
    }
  }

  /**
   * 获取图片库配置数据
   */
  private async getLibraryConfigsData(page: number, pageSize: number): Promise<TableDataResult> {
    try {
      const offset = (page - 1) * pageSize
      
      // 使用LibraryDAO查询配置数据
      const result = await this.libraryDAO.query({
        limit: pageSize,
        offset: offset
      })

      if (!result.results) {
        return {
          success: false,
          error: '查询图片库配置失败'
        }
      }

      const total = result.total || result.results.length
      const totalPages = Math.ceil(total / pageSize)

      return {
        success: true,
        data: result.results,
        total,
        page,
        pageSize,
        totalPages
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : '查询图片库配置失败'
      }
    }
  }

  /**
   * 获取标签数据
   */
  private async getTagsData(page: number, pageSize: number): Promise<TableDataResult> {
    try {
      const offset = (page - 1) * pageSize

      // 使用 getAllTags 方法获取分页数据
      const tags = await this.tagDAO.getAllTags({
        limit: pageSize,
        offset: offset
      })

      // 获取标签总数
      const total = await this.tagDAO.getTotalTagCount()
      const totalPages = Math.ceil(total / pageSize)

      return {
        success: true,
        data: tags,
        total,
        page,
        pageSize,
        totalPages
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : '查询标签数据失败'
      }
    }
  }
}
