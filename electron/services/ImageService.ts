import { ImageDAO } from '../dao/interfaces/ImageDAO'
import { TagDAO } from '../dao/interfaces/TagDAO'
import { ImageRecord, QueryParams, QueryResult } from '@shared/types/database'
import { AIAnalysisService } from './AIAnalysisService'

/**
 * 图片业务服务
 * 负责图片相关的业务逻辑，依赖 ImageDAO 和 TagDAO
 */
export class ImageService {
  constructor(
    private imageDAO: ImageDAO,
    private tagDAO: TagDAO,
    private aiAnalysisService: AIAnalysisService
  ) {}

  // ============= 图片CRUD操作 =============

  /**
   * 创建图片记录
   */
  async createImage(image: ImageRecord): Promise<string> {
    return await this.imageDAO.create(image)
  }

  /**
   * 根据ID获取图片
   */
  async getImageById(id: string): Promise<ImageRecord | null> {
    return await this.imageDAO.findById(id)
  }

  /**
   * 根据路径获取图片
   */
  async getImageByPath(filePath: string): Promise<ImageRecord | null> {
    return await this.imageDAO.findByPath(filePath)
  }

  /**
   * 更新图片记录
   */
  async updateImage(id: string, updates: Partial<ImageRecord>): Promise<boolean> {
    return await this.imageDAO.update(id, updates)
  }

  /**
   * 删除图片记录
   */
  async deleteImage(id: string): Promise<boolean> {
    return await this.imageDAO.delete(id)
  }

  /**
   * 批量插入图片
   */
  async insertImagesBatch(images: ImageRecord[]): Promise<{ success: boolean; insertedCount: number; error?: string }> {
    return await this.imageDAO.insertBatch(images)
  }

  // ============= 查询操作 =============

  /**
   * 查询图片
   */
  async queryImages(params: QueryParams): Promise<QueryResult> {
    return await this.imageDAO.queryImages(params)
  }

  /**
   * 检查图片是否存在
   */
  async checkImageExists(filePath: string, md5?: string) {
    return await this.imageDAO.checkExists(filePath, md5)
  }

  /**
   * 获取图片总数
   */
  async getTotalImageCount(): Promise<number> {
    return await this.imageDAO.getTotalCount()
  }

  // ============= 搜索操作 =============

  /**
   * 向量搜索图片
   */
  async vectorSearchImages(embedding: number[], threshold?: number, limit?: number): Promise<QueryResult> {
    return await this.imageDAO.vectorSearch(embedding, threshold, limit)
  }

  /**
   * 智能搜索（混合搜索）
   */
  async smartSearch(query: string): Promise<QueryResult> {
    // 1. 生成查询向量
    const embedding = await this.aiAnalysisService.generateEmbedding(query)
    
    // 2. 提取关键词
    const keywords = this.extractKeywords(query)
    
    // 3. 扩展相似标签
    const similarTags = await this.tagDAO.findSimilarTags(embedding, 0.8, 10)
    const expandedKeywords = [...keywords, ...similarTags.map(tag => tag.tagText)]
    
    // 4. 执行混合搜索
    return await this.imageDAO.hybridSearch({
      query,
      keywords: expandedKeywords,
      embedding,
      limit: 50,
      similarityThreshold: 0.7,
      expandKeywords: true
    })
  }

  /**
   * 查找相似图片
   */
  async findSimilarImages(imageId: string, threshold?: number, limit?: number): Promise<QueryResult> {
    return await this.imageDAO.findSimilarImages(imageId, { threshold, limit })
  }

  // ============= 图片处理业务逻辑 =============

  /**
   * 处理并存储图片（完整业务流程）
   */
  async processAndStoreImage(imagePath: string, metadata?: any): Promise<string> {
    // 1. 检查图片是否已存在
    const existingImage = await this.imageDAO.findByPath(imagePath)
    if (existingImage) {
      throw new Error(`图片已存在: ${imagePath}`)
    }

    // 2. 创建基础图片记录
    const imageRecord: ImageRecord = {
      id: this.generateId(),
      filePath: imagePath,
      fileName: this.getFilename(imagePath),
      description: '',
      tags: '[]', // JSON字符串格式
      tagsFlat: '', // 扁平化标签字符串
      metadata: JSON.stringify({
        filename: this.getFilename(imagePath),
        filesize: 0,
        uploadTime: new Date().toISOString(),
        dimensions: '0x0',
        format: this.getFileExtension(imagePath),
        ...metadata
      }),
      createdAt: Date.now(),
      updatedAt: Date.now()
    }

    // 3. 保存基础记录
    const imageId = await this.imageDAO.create(imageRecord)

    // 4. 异步执行AI分析和标签处理
    await this.performAsyncAnalysis(imageRecord)

    return imageId
  }

  /**
   * 异步执行AI分析和标签处理
   */
  private async performAsyncAnalysis(imageRecord: ImageRecord): Promise<void> {
    try {
      // 1. AI分析获取描述和标签
      const analysis = await this.aiAnalysisService.analyzeImageByPath(imageRecord.filePath)

      // 2. 准备扁平化标签
      const flatTags = this.aiAnalysisService.extractFlatTags(analysis)

      // 3. 处理标签（使用扁平化标签创建标签记录）
      // 优先使用扁平化标签，如果为空则使用传统标签
      const tagsToProcess = flatTags.length > 0 ? flatTags : (analysis.tags || [])
      const tagIds = await this.processTags(tagsToProcess)

      // 4. 生成向量
      const embedding = await this.aiAnalysisService.generateEmbedding(analysis.description)

      // 5. 更新图片记录（正确的数据格式）
      await this.imageDAO.update(imageRecord.id, {
        description: analysis.description,
        tags: JSON.stringify(tagIds), // 存储标签ID数组的JSON字符串
        tagsFlat: flatTags.join(' '), // 扁平化标签，空格分隔
        structuredMetadata: JSON.stringify(analysis.structured_data), // 结构化元数据
        descriptionVector: embedding, // 描述向量
        updatedAt: Date.now()
      })

      console.log(`图片AI分析完成: ${imageRecord.id}, 处理了 ${tagIds.length} 个标签`)
    } catch (error) {
      console.error(`图片AI分析失败: ${imageRecord.id}`, error)
    }
  }

  /**
   * 处理标签（创建新标签或更新使用频率）
   */
  private async processTags(tagTexts: string[]): Promise<string[]> {
    const processedTags: string[] = []

    for (const tagText of tagTexts) {
      try {
        // 查找现有标签
        const tag = await this.tagDAO.findTagByText(tagText)
        
        if (!tag) {
          // 创建新标签
          const tagId = await this.tagDAO.createTag({
            tagText: tagText,
            category: this.categorizeTag(tagText),
            frequency: 1
          })
          processedTags.push(tagId)
        } else {
          // 更新现有标签频率
          await this.tagDAO.incrementTagFrequency(tag.tagId)
          processedTags.push(tag.tagId)
        }
      } catch (error) {
        console.warn(`处理标签失败: ${tagText}`, error)
      }
    }

    return processedTags
  }

  // ============= 位置和时间查询 =============

  /**
   * 按地理位置查询图片
   */
  async queryImagesByLocation(latitude: number, longitude: number, radius?: number, limit?: number): Promise<ImageRecord[]> {
    return await this.imageDAO.queryByLocation(latitude, longitude, radius, limit)
  }

  /**
   * 按地点名称查询图片
   */
  async queryImagesByLocationName(location: string, limit?: number): Promise<ImageRecord[]> {
    return await this.imageDAO.queryByLocationName(location, limit)
  }

  /**
   * 按时间范围查询图片
   */
  async queryImagesByTimeRange(startTime: number, endTime: number, limit?: number): Promise<ImageRecord[]> {
    return await this.imageDAO.queryByTimeRange(startTime, endTime, limit)
  }

  // ============= 辅助方法 =============

  /**
   * 生成唯一ID
   */
  private generateId(): string {
    return Date.now().toString(36) + Math.random().toString(36).substring(2, 11)
  }

  /**
   * 获取文件名
   */
  private getFilename(filePath: string): string {
    return filePath.split('/').pop() || filePath.split('\\').pop() || ''
  }

  /**
   * 获取文件扩展名
   */
  private getFileExtension(filePath: string): string {
    const filename = this.getFilename(filePath)
    const lastDot = filename.lastIndexOf('.')
    return lastDot > 0 ? filename.substring(lastDot + 1) : ''
  }

  /**
   * 提取关键词
   */
  private extractKeywords(query: string): string[] {
    return query
      .toLowerCase()
      .split(/\s+/)
      .filter(word => word.length > 2)
      .slice(0, 10) // 限制关键词数量
  }

  /**
   * 标签分类
   */
  private categorizeTag(tagText: string): string {
    // 简单的标签分类逻辑
    const lowerTag = tagText.toLowerCase()
    
    if (['person', 'people', 'man', 'woman', 'child', 'baby'].some(keyword => lowerTag.includes(keyword))) {
      return 'person'
    }
    if (['animal', 'dog', 'cat', 'bird', 'horse'].some(keyword => lowerTag.includes(keyword))) {
      return 'animal'
    }
    if (['car', 'vehicle', 'truck', 'bike', 'motorcycle'].some(keyword => lowerTag.includes(keyword))) {
      return 'vehicle'
    }
    if (['building', 'house', 'architecture', 'tower'].some(keyword => lowerTag.includes(keyword))) {
      return 'architecture'
    }
    if (['nature', 'tree', 'mountain', 'ocean', 'sky', 'flower'].some(keyword => lowerTag.includes(keyword))) {
      return 'nature'
    }
    
    return 'general'
  }

  /**
   * 生成标签颜色（移除未使用的方法）
   */
  /*
  private generateTagColor(tagText: string): string {
    // 根据标签文本生成一致的颜色
    let hash = 0
    for (let i = 0; i < tagText.length; i++) {
      hash = ((hash << 5) - hash + tagText.charCodeAt(i)) & 0xffffffff
    }
    
    const hue = Math.abs(hash) % 360
    return `hsl(${hue}, 70%, 50%)`
  }
  */

  /**
   * 清空所有图片数据
   */
  async clearAllImages() {
    return await this.imageDAO.clearAll()
  }

  /**
   * 测试连接
   */
  async testConnection(): Promise<boolean> {
    return await this.imageDAO.testConnection()
  }
}