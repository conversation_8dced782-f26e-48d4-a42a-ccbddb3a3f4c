import { TagDAO } from '../dao/interfaces/TagDAO'
import { TagRecord, TagSearchParams, ImageCategory } from '@shared/types/database'
import { AIAnalysisService } from './AIAnalysisService'

/**
 * 标签业务服务
 * 负责标签相关的业务逻辑，依赖 TagDAO
 */
export class TagService {
  constructor(
    private tagDAO: TagDAO,
    private aiAnalysisService?: AIAnalysisService
  ) {}

  // ============= 标签CRUD操作 =============

  /**
   * 创建标签
   */
  async createTag(tagData: {
    tagText: string
    category?: ImageCategory
    frequency?: number
    metadata?: any
  }): Promise<string> {
    // 检查标签是否已存在
    const existingTag = await this.tagDAO.findTagByText(tagData.tagText)
    if (existingTag) {
      // 如果标签已存在，更新频率
      await this.tagDAO.incrementTagFrequency(existingTag.tagId, tagData.frequency || 1)
      return existingTag.tagId
    }

    // 创建新标签
    const tagRecord = {
      tagText: tagData.tagText,
      category: tagData.category || this.categorizeTag(tagData.tagText),
      frequency: tagData.frequency || 1,
      metadata: JSON.stringify(tagData.metadata || {})
    }

    const tagId = await this.tagDAO.createTag(tagRecord)

    // 异步生成向量
    if (this.aiAnalysisService) {
      this.generateAndStoreTagVector(tagId, tagData.tagText).catch(error => {
        console.warn(`Failed to generate vector for tag ${tagId}:`, error)
      })
    }

    return tagId
  }

  /**
   * 获取标签详情
   */
  async getTag(tagId: string): Promise<TagRecord | null> {
    return await this.tagDAO.findTagById(tagId)
  }

  /**
   * 根据文本查找标签
   */
  async getTagByText(tagText: string): Promise<TagRecord | null> {
    return await this.tagDAO.findTagByText(tagText)
  }

  /**
   * 更新标签
   */
  async updateTag(tagId: string, updates: Partial<TagRecord>): Promise<boolean> {
    return await this.tagDAO.updateTag(tagId, updates)
  }

  /**
   * 删除标签
   */
  async deleteTag(tagId: string): Promise<boolean> {
    return await this.tagDAO.deleteTag(tagId)
  }

  /**
   * 批量创建标签
   */
  async createTagsBatch(tagTexts: string[]): Promise<{
    success: boolean
    createdCount: number
    existingCount: number
    tagIds: string[]
  }> {
    const results = {
      success: true,
      createdCount: 0,
      existingCount: 0,
      tagIds: [] as string[]
    }

    for (const tagText of tagTexts) {
      try {
        const tagId = await this.createTag({ tagText })
        results.tagIds.push(tagId)
        
        // Check if it was newly created or existing
        const existing = await this.tagDAO.findTagByText(tagText)
        if (existing && existing.frequency > 1) {
          results.existingCount++
        } else {
          results.createdCount++
        }
      } catch (error) {
        console.warn(`Failed to create tag: ${tagText}`, error)
        results.success = false
      }
    }

    return results
  }

  // ============= 查询操作 =============

  /**
   * 获取所有标签
   */
  async getAllTags(params?: TagSearchParams): Promise<TagRecord[]> {
    return await this.tagDAO.getAllTags(params)
  }

  /**
   * 按频率获取热门标签
   */
  async getPopularTags(limit: number = 50, minFrequency: number = 1): Promise<TagRecord[]> {
    return await this.tagDAO.getTagsByFrequency(limit, minFrequency)
  }

  /**
   * 按分类获取标签
   */
  async getTagsByCategory(category: ImageCategory, limit?: number): Promise<TagRecord[]> {
    return await this.tagDAO.getTagsByCategory(category, limit)
  }

  /**
   * 搜索标签
   */
  async searchTags(query: string, limit: number = 20): Promise<TagRecord[]> {
    return await this.tagDAO.searchTags(query, limit)
  }

  // ============= 向量搜索操作 =============

  /**
   * 查找相似标签
   */
  async findSimilarTags(query: string, threshold: number = 0.8, limit: number = 10): Promise<TagRecord[]> {
    if (!this.aiAnalysisService) {
      // 如果没有AI服务，使用文本搜索作为备选
      return await this.searchTags(query, limit)
    }

    try {
      // 生成查询向量
      const embedding = await this.aiAnalysisService.generateEmbedding(query)
      
      // 向量搜索
      return await this.tagDAO.findSimilarTags(embedding, threshold, limit)
    } catch (error) {
      console.warn('Vector search failed, falling back to text search:', error)
      return await this.searchTags(query, limit)
    }
  }

  /**
   * 根据标签向量查找相似标签
   */
  async findSimilarTagsByVector(embedding: number[], threshold: number = 0.8, limit: number = 10): Promise<TagRecord[]> {
    return await this.tagDAO.findSimilarTags(embedding, threshold, limit)
  }

  /**
   * 扩展标签集合（添加相似标签）
   */
  async expandTagsWithSimilar(tagIds: string[], threshold: number = 0.7, limit: number = 5): Promise<TagRecord[]> {
    return await this.tagDAO.expandTagsWithSimilar(tagIds, threshold, limit)
  }

  // ============= 标签管理操作 =============

  /**
   * 增加标签使用频率
   */
  async incrementTagUsage(tagId: string, increment: number = 1): Promise<boolean> {
    return await this.tagDAO.incrementTagFrequency(tagId, increment)
  }

  /**
   * 批量增加标签使用频率
   */
  async incrementTagsUsage(tagIds: string[]): Promise<{ success: boolean; updatedCount: number }> {
    return await this.tagDAO.incrementTagsFrequency(tagIds)
  }

  /**
   * 处理图片标签（创建新标签或更新频率）
   */
  async processImageTags(tagTexts: string[]): Promise<string[]> {
    const tagIds: string[] = []

    for (const tagText of tagTexts) {
      try {
        const tagId = await this.createTag({ tagText })
        tagIds.push(tagId)
      } catch (error) {
        console.warn(`Failed to process tag: ${tagText}`, error)
      }
    }

    return tagIds
  }

  // ============= 标签统计 =============

  /**
   * 获取标签总数
   */
  async getTotalTagCount(): Promise<number> {
    return await this.tagDAO.getTotalTagCount()
  }

  /**
   * 获取标签分类统计
   */
  async getTagCategoryStats(): Promise<Array<{ category: ImageCategory; count: number }>> {
    return await this.tagDAO.getTagCategoryStats()
  }

  /**
   * 获取标签云数据
   */
  async getTagCloud(limit: number = 100): Promise<Array<{
    tagText: string
    frequency: number
    category: ImageCategory
    size: number
  }>> {
    const tags = await this.tagDAO.getTagsByFrequency(limit, 1)
    
    // 计算标签大小（基于频率）
    const maxFrequency = Math.max(...tags.map(tag => tag.frequency))
    const minFrequency = Math.min(...tags.map(tag => tag.frequency))
    const frequencyRange = maxFrequency - minFrequency || 1

    return tags.map(tag => ({
      tagText: tag.tagText,
      frequency: tag.frequency,
      category: tag.category || 'general',
      size: Math.round(((tag.frequency - minFrequency) / frequencyRange) * 100) + 20 // 20-120 range
    }))
  }

  // ============= 标签维护 =============

  /**
   * 清理未使用的标签
   */
  async cleanupUnusedTags(): Promise<{ success: boolean; deletedCount: number }> {
    return await this.tagDAO.cleanupUnusedTags()
  }

  /**
   * 重新计算标签频率
   */
  async recalculateTagFrequencies(): Promise<{ success: boolean; updatedCount: number }> {
    return await this.tagDAO.recalculateTagFrequencies()
  }

  /**
   * 合并重复标签
   */
  async mergeDuplicateTags(): Promise<{ success: boolean; mergedCount: number }> {
    const allTags = await this.tagDAO.getAllTags()
    const tagGroups = new Map<string, TagRecord[]>()
    let mergedCount = 0

    // 按标准化文本分组
    for (const tag of allTags) {
      const normalizedText = this.normalizeTagText(tag.tagText)
      if (!tagGroups.has(normalizedText)) {
        tagGroups.set(normalizedText, [])
      }
      tagGroups.get(normalizedText)!.push(tag)
    }

    // 处理重复项
    for (const [_, tags] of tagGroups) {
      if (tags.length > 1) {
        // 保留频率最高的标签
        const primaryTag = tags.reduce((prev, current) => 
          current.frequency > prev.frequency ? current : prev
        )

        // 合并其他标签的频率
        const totalFrequency = tags.reduce((sum, tag) => sum + tag.frequency, 0)
        
        // 更新主标签频率
        await this.tagDAO.updateTag(primaryTag.tagId, { 
          frequency: totalFrequency 
        })

        // 删除其他重复标签
        for (const tag of tags) {
          if (tag.tagId !== primaryTag.tagId) {
            await this.tagDAO.deleteTag(tag.tagId)
            mergedCount++
          }
        }
      }
    }

    return { success: true, mergedCount }
  }

  // ============= 辅助方法 =============

  /**
   * 标签分类
   */
  private categorizeTag(tagText: string): ImageCategory {
    const lowerTag = tagText.toLowerCase()
    
    // 颜色相关
    if (['red', 'blue', 'green', 'yellow', 'white', 'black', 'pink', 'purple', 'orange', 'brown', 'gray', 'grey', 'color', 'colour'].some(keyword => lowerTag.includes(keyword))) {
      return 'color'
    }
    
    // 场景相关
    if (['scene', 'landscape', 'city', 'street', 'room', 'office', 'kitchen', 'bedroom', 'outdoor', 'indoor', 'beach', 'mountain', 'forest'].some(keyword => lowerTag.includes(keyword))) {
      return 'scene'
    }
    
    // 氛围相关
    if (['happy', 'sad', 'mood', 'emotion', 'feeling', 'atmosphere', 'vibe', 'cheerful', 'melancholy', 'romantic', 'mysterious'].some(keyword => lowerTag.includes(keyword))) {
      return 'mood'
    }
    
    // 时间相关
    if (['morning', 'afternoon', 'evening', 'night', 'sunrise', 'sunset', 'day', 'time', 'weather', 'sunny', 'rainy', 'cloudy'].some(keyword => lowerTag.includes(keyword))) {
      return 'time'
    }
    
    // 风格相关
    if (['style', 'vintage', 'modern', 'classic', 'artistic', 'minimalist', 'retro', 'fashion', 'design'].some(keyword => lowerTag.includes(keyword))) {
      return 'style'
    }
    
    // 物体相关
    if (['object', 'thing', 'item', 'furniture', 'tool', 'device', 'equipment', 'machine'].some(keyword => lowerTag.includes(keyword))) {
      return 'object'
    }
    
    // 动作相关
    if (['action', 'running', 'walking', 'sitting', 'standing', 'dancing', 'singing', 'playing', 'working', 'cooking'].some(keyword => lowerTag.includes(keyword))) {
      return 'action'
    }
    
    // 衣着相关
    if (['clothing', 'shirt', 'dress', 'pants', 'shoes', 'hat', 'jacket', 'coat', 'suit', 'uniform'].some(keyword => lowerTag.includes(keyword))) {
      return 'clothing'
    }
    
    // 关系相关
    if (['family', 'couple', 'friends', 'group', 'team', 'relationship', 'together', 'alone'].some(keyword => lowerTag.includes(keyword))) {
      return 'relationship'
    }
    
    // 活动领域相关
    if (['business', 'sport', 'education', 'entertainment', 'travel', 'food', 'music', 'art', 'technology'].some(keyword => lowerTag.includes(keyword))) {
      return 'domain'
    }
    
    // 文字相关
    if (['text', 'word', 'letter', 'sign', 'label', 'title', 'caption', 'writing'].some(keyword => lowerTag.includes(keyword))) {
      return 'text'
    }
    
    // 位置相关
    if (['location', 'place', 'country', 'city', 'town', 'village', 'area', 'region'].some(keyword => lowerTag.includes(keyword))) {
      return 'location'
    }
    
    return 'general'
  }

  /**
   * 标准化标签文本
   */
  private normalizeTagText(tagText: string): string {
    return tagText.toLowerCase().trim().replace(/\s+/g, ' ')
  }

  /**
   * 异步生成并存储标签向量
   */
  private async generateAndStoreTagVector(tagId: string, tagText: string): Promise<void> {
    try {
      if (!this.aiAnalysisService) return

      const embedding = await this.aiAnalysisService.generateEmbedding(tagText)
      await this.tagDAO.createTagVector(tagId, embedding)
    } catch (error) {
      console.error(`Failed to generate vector for tag ${tagId}:`, error)
    }
  }

  /**
   * 清空所有标签
   */
  async clearAll(): Promise<{ success: boolean; error?: string }> {
    return await this.tagDAO.clearAll()
  }

  /**
   * 测试连接
   */
  async testConnection(): Promise<boolean> {
    return await this.tagDAO.testConnection()
  }
}