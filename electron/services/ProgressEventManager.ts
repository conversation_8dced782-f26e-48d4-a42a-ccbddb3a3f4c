import { BaseProgress, ScanProgress, ImportProgress, AIAnalysisProgress, PhaseProgress } from '../../src/types/progress'

/**
 * 进度事件管理器
 * 负责管理所有类型的进度追踪和事件发射
 * TODO use ipc to communicate with frontend
 */
export class ProgressEventManager {
  private activeProgresses = new Map<string, BaseProgress>()
  private progressIdCounter = 0

  /**
   * 生成唯一的进度ID
   */
  private generateProgressId(): string {
    return `progress_${Date.now()}_${++this.progressIdCounter}`
  }

  /**
   * 创建扫描进度
   */
  createScanProgress(libraryId: string, libraryName: string): ScanProgress {
    const progress: ScanProgress = {
      id: this.generateProgressId(),
      type: 'scan',
      status: 'pending',
      startTime: new Date(),
      libraryId,
      libraryName,
      phases: {
        discovery: { name: '发现文件', status: 'pending', progress: 0 },
        processing: { name: '处理文件', status: 'pending', progress: 0 },
        analysis: { name: 'AI分析', status: 'pending', progress: 0 }
      },
      totalFiles: 0,
      processedFiles: 0,
      failedFiles: 0,
      skippedFiles: 0,
      duplicateFiles: 0,
      currentFile: undefined,
      estimatedTimeRemaining: undefined
    }

    this.activeProgresses.set(progress.id, progress)
    this.emitProgress(progress)
    return progress
  }

  /**
   * 创建导入进度
   */
  createImportProgress(sourceFolder: string): ImportProgress {
    const progress: ImportProgress = {
      id: this.generateProgressId(),
      type: 'import',
      status: 'pending',
      startTime: new Date(),
      sourceFolder,
      totalFiles: 0,
      processedFiles: 0,
      successfulFiles: 0,
      failedFiles: 0,
      currentFile: undefined,
      fileResults: []
    }

    this.activeProgresses.set(progress.id, progress)
    this.emitProgress(progress)
    return progress
  }

  /**
   * 创建AI分析进度
   */
  createAIAnalysisProgress(): AIAnalysisProgress {
    const progress: AIAnalysisProgress = {
      id: this.generateProgressId(),
      type: 'analysis',
      status: 'pending',
      startTime: new Date(),
      totalImages: 0,
      analyzedImages: 0,
      pendingImages: 0,
      failedImages: 0,
      currentImage: undefined,
      queueStatus: {
        pending: 0,
        processing: 0,
        completed: 0
      }
    }

    this.activeProgresses.set(progress.id, progress)
    this.emitProgress(progress)
    return progress
  }

  /**
   * 更新进度状态
   */
  updateProgress(progressId: string, updates: Partial<BaseProgress>): void {
    const progress = this.activeProgresses.get(progressId)
    if (!progress) return

    Object.assign(progress, updates)
    this.emitProgress(progress)
  }

  /**
   * 更新扫描阶段进度
   */
  updateScanPhase(progressId: string, phase: 'discovery' | 'processing' | 'analysis', phaseProgress: Partial<PhaseProgress>): void {
    const progress = this.activeProgresses.get(progressId) as ScanProgress
    if (!progress || progress.type !== 'scan') return

    Object.assign(progress.phases[phase], phaseProgress)
    this.emitProgress(progress)
    this.emitPhaseChange(progressId, phase, progress.phases[phase])
  }

  /**
   * 设置扫描进度的总文件数
   */
  setTotalFiles(progressId: string, totalFiles: number): void {
    const progress = this.activeProgresses.get(progressId)
    if (!progress) return

    if (progress.type === 'scan') {
      (progress as ScanProgress).totalFiles = totalFiles
    } else if (progress.type === 'import') {
      (progress as ImportProgress).totalFiles = totalFiles
    }

    this.emitProgress(progress)
  }

  /**
   * 增加已处理文件数
   */
  incrementProcessedFiles(progressId: string, count: number = 1): void {
    const progress = this.activeProgresses.get(progressId)
    if (!progress) return

    if (progress.type === 'scan') {
      (progress as ScanProgress).processedFiles += count
    } else if (progress.type === 'import') {
      (progress as ImportProgress).processedFiles += count
      ;(progress as ImportProgress).successfulFiles += count
    }

    this.emitProgress(progress)
  }

  /**
   * 增加失败文件数
   */
  incrementFailedFiles(progressId: string, count: number = 1): void {
    const progress = this.activeProgresses.get(progressId)
    if (!progress) return

    if (progress.type === 'scan') {
      (progress as ScanProgress).failedFiles += count
    } else if (progress.type === 'import') {
      (progress as ImportProgress).failedFiles += count
    }

    this.emitProgress(progress)
  }

  /**
   * 增加跳过文件数
   */
  incrementSkippedFiles(progressId: string, count: number = 1): void {
    const progress = this.activeProgresses.get(progressId)
    if (!progress) return

    if (progress.type === 'scan') {
      (progress as ScanProgress).skippedFiles += count
    }

    this.emitProgress(progress)
  }

  /**
   * 增加重复文件数
   */
  incrementDuplicateFiles(progressId: string, count: number = 1): void {
    const progress = this.activeProgresses.get(progressId)
    if (!progress) return

    if (progress.type === 'scan') {
      (progress as ScanProgress).duplicateFiles += count
    }

    this.emitProgress(progress)
  }

  /**
   * 更新当前处理的文件
   */
  updateCurrentFile(progressId: string, currentFile: string): void {
    const progress = this.activeProgresses.get(progressId)
    if (!progress) return

    if (progress.type === 'scan') {
      (progress as ScanProgress).currentFile = currentFile
    } else if (progress.type === 'import') {
      (progress as ImportProgress).currentFile = currentFile
    } else if (progress.type === 'analysis') {
      (progress as AIAnalysisProgress).currentImage = currentFile
    }

    this.emitProgress(progress)
  }

  /**
   * 完成进度
   */
  completeProgress(progressId: string): void {
    const progress = this.activeProgresses.get(progressId)
    if (!progress) return

    progress.status = 'completed'
    progress.endTime = new Date()
    
    // 清除当前文件信息
    if (progress.type === 'scan') {
      (progress as ScanProgress).currentFile = undefined
    } else if (progress.type === 'import') {
      (progress as ImportProgress).currentFile = undefined
    } else if (progress.type === 'analysis') {
      (progress as AIAnalysisProgress).currentImage = undefined
    }

    this.emitProgress(progress)
    
    // 延迟清理已完成的进度（保留5分钟）
    setTimeout(() => {
      this.removeProgress(progressId)
    }, 5 * 60 * 1000)
  }

  /**
   * 标记进度失败
   */
  failProgress(progressId: string, error: string): void {
    const progress = this.activeProgresses.get(progressId)
    if (!progress) return

    progress.status = 'failed'
    progress.endTime = new Date()
    progress.error = error

    this.emitProgress(progress)
    this.emitError(progressId, new Error(error))
    
    // 延迟清理失败的进度（保留10分钟）
    setTimeout(() => {
      this.removeProgress(progressId)
    }, 10 * 60 * 1000)
  }

  /**
   * 暂停进度
   */
  pauseProgress(progressId: string): void {
    const progress = this.activeProgresses.get(progressId)
    if (!progress) return

    progress.status = 'paused'
    this.emitProgress(progress)
  }

  /**
   * 恢复进度
   */
  resumeProgress(progressId: string): void {
    const progress = this.activeProgresses.get(progressId)
    if (!progress) return

    progress.status = 'running'
    this.emitProgress(progress)
  }

  /**
   * 取消进度
   */
  cancelProgress(progressId: string): void {
    const progress = this.activeProgresses.get(progressId)
    if (!progress) return

    progress.status = 'cancelled'
    progress.endTime = new Date()
    this.emitProgress(progress)
    
    // 立即清理已取消的进度
    setTimeout(() => {
      this.removeProgress(progressId)
    }, 1000)
  }

  /**
   * 移除进度记录
   */
  removeProgress(progressId: string): void {
    this.activeProgresses.delete(progressId)
  }

  /**
   * 获取所有活动进度
   */
  getActiveProgresses(): BaseProgress[] {
    return Array.from(this.activeProgresses.values())
  }

  /**
   * 获取特定进度
   */
  getProgress(progressId: string): BaseProgress | undefined {
    return this.activeProgresses.get(progressId)
  }

  /**
   * 发射进度更新事件
   */
  private emitProgress(progress: BaseProgress): void {
    this.emit('progress', progress)
  }

  /**
   * 发射阶段变更事件
   */
  private emitPhaseChange(progressId: string, phase: string, phaseProgress: PhaseProgress): void {
    this.emit('phaseChange', { progressId, phase, phaseProgress })
  }

  /**
   * 发射错误事件
   */
  private emitError(progressId: string, error: Error): void {
    this.emit('error', { progressId, error })
  }

  /**
   * 清理所有进度
   */
  clear(): void {
    this.activeProgresses.clear()
  }

  private emit(progress1: string, progress: any) {
    // DO NOTHING
    // TODO use ipc to emit progress
  }
}