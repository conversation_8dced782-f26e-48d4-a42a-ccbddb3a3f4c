import { LibraryDAO } from '../dao/interfaces/LibraryDAO'
import {
  LibraryConfig,
  LibraryConfigQueryParams,
  LibraryConfigQueryResult,
  BaseResult,
  LibrarySettings
} from '@shared/types/database'
import { TaskManager } from './TaskManager'
import { TaskProgress } from './types/Task'
import { ImageService } from './ImageService'
import * as fs from 'node:fs/promises'
import { existsSync } from 'node:fs'
import * as path from 'node:path'

/**
 * 文件夹分析选项
 */
export interface FolderAnalysisOptions {
  maxDepth?: number
  includeHidden?: boolean
  recursive?: boolean
  supportedFormats?: string[]
}

/**
 * 文件夹分析结果
 */
export interface FolderAnalysisResult {
  estimatedFiles: number
  totalSize: number
  deepestLevel: number
  formatBreakdown: Record<string, number>
  permissionIssues: string[]
}

/**
 * 图片库业务服务
 * 负责图片库相关的业务逻辑，依赖 LibraryDAO
 * 使用 TaskManager 进行异步扫描操作
 */
export class LibraryService {
  constructor(
    private libraryDAO: LibraryDAO,
    private taskManager: TaskManager,
    private imageService: ImageService
  ) {}

  // ============= 图片库管理操作 =============

  /**
   * 创建新的图片库
   */
  async createLibrary(libraryData: {
    name: string
    rootPath: string
    type?: 'local' | 'cloud' | 'network'
    description?: string
    settings?: any
  }): Promise<string> {
    // 1. 验证路径
    const validationResult = await this.validateLibraryPath(libraryData.rootPath)
    if (!validationResult.valid) {
      throw new Error(`Invalid library path: ${validationResult.error}`)
    }

    // 2. 检查路径冲突
    const conflictCheck = await this.libraryDAO.checkPathConflict(libraryData.rootPath)
    if (conflictCheck.hasConflict) {
      throw new Error(`Path conflict detected: ${conflictCheck.conflictType} with existing library`)
    }

    // 3. 检查名称是否已存在
    const existingLibrary = await this.libraryDAO.findByName(libraryData.name)
    if (existingLibrary) {
      throw new Error(`Library name already exists: ${libraryData.name}`)
    }

    // 4. 创建图片库配置
    const config: Omit<LibraryConfig, 'id' | 'createdAt' | 'updatedAt'> = {
      name: libraryData.name,
      rootPath: path.resolve(libraryData.rootPath),
      type: libraryData.type || 'local',
      status: 'active',
      description: libraryData.description || '',
      settings: JSON.stringify({
        recursive: true,
        includeHidden: false,
        maxDepth: 20,
        supportedFormats: ['.jpg', '.jpeg', '.png', '.webp', '.gif', '.bmp', '.tiff'],
        excludePatterns: [],
        autoScanInterval: 0,
        ...libraryData.settings
      }),
      scanProgress: JSON.stringify({
        total: 0,
        processed: 0,
        failed: 0,
        status: 'idle',
        lastScannedPath: '',
        estimatedTimeRemaining: 0
      }),
      statistics: JSON.stringify({
        totalImages: 0,
        totalSize: 0,
        imagesByFormat: {},
        averageFileSize: 0,
        newestImageDate: '',
        oldestImageDate: ''
      }),
      lastScanAt: ''
    }

    return await this.libraryDAO.create(config)
  }

  /**
   * 获取图片库详情
   */
  async getLibrary(id: string): Promise<LibraryConfig | null> {
    return await this.libraryDAO.findById(id)
  }

  /**
   * 获取所有图片库
   */
  async getAllLibraries(): Promise<LibraryConfig[]> {
    return await this.libraryDAO.findAll()
  }

  /**
   * 获取活跃的图片库
   */
  async getActiveLibraries(): Promise<LibraryConfig[]> {
    return await this.libraryDAO.findActive()
  }

  /**
   * 更新图片库配置
   */
  async updateLibrary(id: string, updates: Partial<LibraryConfig>): Promise<BaseResult> {
    // 如果更新路径，需要验证
    if (updates.rootPath) {
      const validationResult = await this.validateLibraryPath(updates.rootPath)
      if (!validationResult.valid) {
        return { success: false, error: `Invalid library path: ${validationResult.error}` }
      }

      // 检查路径冲突（排除当前图片库）
      const conflictCheck = await this.libraryDAO.checkPathConflict(updates.rootPath, id)
      if (conflictCheck.hasConflict) {
        return { success: false, error: `Path conflict detected: ${conflictCheck.conflictType}` }
      }

      updates.rootPath = path.resolve(updates.rootPath)
    }

    // 如果更新名称，检查重复
    if (updates.name) {
      const existingLibrary = await this.libraryDAO.findByName(updates.name)
      if (existingLibrary && existingLibrary.id !== id) {
        return { success: false, error: `Library name already exists: ${updates.name}` }
      }
    }

    return await this.libraryDAO.update(id, updates)
  }

  /**
   * 删除图片库
   */
  async deleteLibrary(id: string): Promise<BaseResult> {
    const library = await this.libraryDAO.findById(id)
    if (!library) {
      return { success: false, error: 'Library not found' }
    }

    return await this.libraryDAO.delete(id)
  }

  /**
   * 更新图片库名称
   */
  async updateLibraryName(id: string, newName: string): Promise<BaseResult> {
    // 检查名称是否已存在
    const existingLibrary = await this.libraryDAO.findByName(newName)
    if (existingLibrary && existingLibrary.id !== id) {
      return { success: false, error: `Library name already exists: ${newName}` }
    }

    return await this.libraryDAO.update(id, { name: newName })
  }

  // ============= 查询操作 =============

  /**
   * 查询图片库
   */
  async queryLibraries(params: LibraryConfigQueryParams): Promise<LibraryConfigQueryResult> {
    return await this.libraryDAO.query(params)
  }

  /**
   * 按状态获取图片库
   */
  async getLibrariesByStatus(status: 'active' | 'offline' | 'removed'): Promise<LibraryConfig[]> {
    return await this.libraryDAO.findByStatus(status)
  }

  /**
   * 按类型获取图片库
   */
  async getLibrariesByType(type: 'local' | 'cloud' | 'network'): Promise<LibraryConfig[]> {
    return await this.libraryDAO.findByType(type)
  }

  /**
   * 根据路径查找图片库
   */
  async getLibraryByPath(rootPath: string): Promise<LibraryConfig | null> {
    return await this.libraryDAO.findByPath(path.resolve(rootPath))
  }

  // ============= 扫描相关操作 =============

  /**
   * 更新扫描进度
   */
  async updateScanProgress(id: string, progress: {
    total?: number
    processed?: number
    failed?: number
    status?: string
    lastScannedPath?: string
    estimatedTimeRemaining?: number
  }): Promise<BaseResult> {
    return await this.libraryDAO.updateScanProgress(id, progress)
  }

  /**
   * 开始异步扫描图片库
   * 使用 TaskManager 进行异步处理，立即返回
   */
  async startScan(id: string, options: {
    forceRescan?: boolean
    maxFiles?: number
  } = {}): Promise<BaseResult> {
    // 1. 验证图片库存在
    const library = await this.libraryDAO.findById(id)
    if (!library) {
      return { success: false, error: 'Library not found' }
    }

    // 2. 检查是否已在扫描中
    // 由于 DAO 已经解析了 scanProgress，这里直接使用对象
    const currentProgress = library.scanProgress as any || {}

    if (currentProgress.status === 'scanning') {
      return { success: false, error: 'Library is already being scanned' }
    }

    // 3. 初始化扫描进度
    await this.libraryDAO.updateScanProgress(id, {
      status: 'pending',
      total: 0,
      processed: 0,
      failed: 0,
      lastScannedPath: '',
      estimatedTimeRemaining: 0
    })

    // 4. 创建扫描任务并添加到队列
    const taskId = this.taskManager.addTask({
      name: `Scan Library: ${library.name}`,
      execute: () => this.executeScanTask(id, options),
      onProgress: (progress: TaskProgress) => {
        // 更新扫描进度到数据库
        this.updateScanProgressFromTask(id, progress)
      },
      onComplete: () => {
        console.log(`Library scan completed: ${library.name}`)
      },
      onError: (error: Error) => {
        console.error(`Library scan failed: ${library.name}`, error)
        this.libraryDAO.updateScanProgress(id, {
          status: 'error'
        })
      }
    })

    console.log(`Scan task queued for library: ${library.name} (task: ${taskId})`)

    return { success: true }
  }

  /**
   * 标记扫描完成
   */
  async completeScan(id: string, summary: { total: number; processed: number; failed: number }): Promise<BaseResult> {
    const updateResult = await this.libraryDAO.updateScanProgress(id, {
      ...summary,
      status: 'completed',
      estimatedTimeRemaining: 0
    })

    if (updateResult.success) {
      // 更新最后扫描时间
      await this.libraryDAO.updateLastScanTime(id)
    }

    return updateResult
  }

  /**
   * 获取需要扫描的图片库
   */
  async getLibrariesForScan(maxIdleHours: number = 24): Promise<LibraryConfig[]> {
    return await this.libraryDAO.findLibrariesForScan(maxIdleHours)
  }

  // ============= 统计相关操作 =============

  /**
   * 更新图片库统计信息
   */
  async updateStatistics(id: string, statistics: {
    totalImages?: number
    totalSize?: number
    imagesByFormat?: Record<string, number>
    averageFileSize?: number
    newestImageDate?: string
    oldestImageDate?: string
  }): Promise<BaseResult> {
    return await this.libraryDAO.updateStatistics(id, statistics)
  }

  /**
   * 获取统计摘要
   */
  async getStatisticsSummary() {
    return await this.libraryDAO.getStatisticsSummary()
  }

  /**
   * 计算图片库大小
   */
  async calculateLibrarySize(id: string): Promise<{ totalSize: number; fileCount: number }> {
    const library = await this.libraryDAO.findById(id)
    if (!library) {
      throw new Error('Library not found')
    }

    // 解析 settings JSON 字符串
    const settings = library.settings as LibrarySettings || {}
    return await this.calculateDirectorySize(library.rootPath, settings.supportedFormats)
  }

  // ============= 设置相关操作 =============

  /**
   * 更新图片库设置
   */
  async updateSettings(id: string, settings: {
    recursive?: boolean
    includeHidden?: boolean
    maxDepth?: number
    supportedFormats?: string[]
    excludePatterns?: string[]
    autoScanInterval?: number
  }): Promise<BaseResult> {
    return await this.libraryDAO.updateSettings(id, settings)
  }

  /**
   * 更新图片库状态
   */
  async updateStatus(id: string, status: 'active' | 'offline' | 'removed'): Promise<BaseResult> {
    return await this.libraryDAO.updateStatus(id, status)
  }

  // ============= 验证和维护操作 =============

  /**
   * 验证图片库路径
   */
  async validateLibraryPath(rootPath: string): Promise<{
    valid: boolean
    exists: boolean
    accessible: boolean
    error?: string
  }> {
    try {
      const resolvedPath = path.resolve(rootPath)

      // 检查路径是否存在
      const exists = existsSync(resolvedPath)
      if (!exists) {
        return {
          valid: false,
          exists: false,
          accessible: false,
          error: 'Path does not exist'
        }
      }

      // 检查是否可访问
      let accessible = true
      try {
        await fs.access(resolvedPath, fs.constants.R_OK)
      } catch {
        accessible = false
      }

      // 检查是否为目录
      const stats = await fs.stat(resolvedPath)
      if (!stats.isDirectory()) {
        return {
          valid: false,
          exists: true,
          accessible,
          error: 'Path is not a directory'
        }
      }

      // 检查是否为系统目录
      if (this.isSystemDirectory(resolvedPath)) {
        return {
          valid: false,
          exists: true,
          accessible,
          error: 'System directories are not allowed'
        }
      }

      return {
        valid: true,
        exists: true,
        accessible
      }
    } catch (error) {
      return {
        valid: false,
        exists: false,
        accessible: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * 验证图片库状态
   */
  async validateLibrary(id: string): Promise<{
    valid: boolean
    pathValid: boolean
    accessible: boolean
    error?: string
  }> {
    const library = await this.libraryDAO.findById(id)
    if (!library) {
      return {
        valid: false,
        pathValid: false,
        accessible: false,
        error: 'Library not found'
      }
    }

    const pathValidation = await this.validateLibraryPath(library.rootPath)
    
    return {
      valid: pathValidation.valid && library.status !== 'removed',
      pathValid: pathValidation.valid,
      accessible: pathValidation.accessible,
      error: pathValidation.error
    }
  }

  /**
   * 批量验证图片库
   */
  async validateAllLibraries(): Promise<Array<{
    id: string
    name: string
    valid: boolean
    error?: string
  }>> {
    const libraries = await this.libraryDAO.findAll()
    const results = []

    for (const library of libraries) {
      const validation = await this.validateLibrary(library.id)
      results.push({
        id: library.id,
        name: library.name,
        valid: validation.valid,
        error: validation.error
      })
    }

    return results
  }

  /**
   * 清理无效的图片库
   */
  async cleanupInvalidLibraries() {
    return await this.libraryDAO.cleanupInvalidLibraries()
  }

  // ============= 辅助方法 =============

  /**
   * 检查是否为系统目录
   */
  private isSystemDirectory(dirPath: string): boolean {
    const systemDirs = [
      '/System', '/usr', '/bin', '/sbin', '/etc', '/var', '/tmp',
      'C:\\Windows', 'C:\\System32', 'C:\\Program Files', 'C:\\Program Files (x86)'
    ]

    const normalizedPath = path.resolve(dirPath)
    return systemDirs.some(sysDir => normalizedPath.startsWith(sysDir))
  }

  /**
   * 计算目录大小
   */
  private async calculateDirectorySize(dirPath: string, supportedFormats: string[]): Promise<{
    totalSize: number
    fileCount: number
  }> {
    let totalSize = 0
    let fileCount = 0

    const calculateRecursive = async (currentPath: string): Promise<void> => {
      try {
        const entries = await fs.readdir(currentPath, { withFileTypes: true })

        for (const entry of entries) {
          const fullPath = path.join(currentPath, entry.name)

          if (entry.isDirectory()) {
            await calculateRecursive(fullPath)
          } else if (entry.isFile()) {
            const ext = path.extname(entry.name).toLowerCase()
            if (supportedFormats.includes(ext)) {
              try {
                const stats = await fs.stat(fullPath)
                totalSize += stats.size
                fileCount++
              } catch {
                // 忽略无法访问的文件
              }
            }
          }
        }
      } catch {
        // 忽略无法访问的目录
      }
    }

    await calculateRecursive(dirPath)
    return { totalSize, fileCount }
  }

  // ============= 异步扫描任务相关方法 =============

  /**
   * 执行扫描任务的核心逻辑
   */
  private async executeScanTask(libraryId: string, options: {
    forceRescan?: boolean
    maxFiles?: number
  }): Promise<void> {
    const library = await this.libraryDAO.findById(libraryId)
    if (!library) {
      throw new Error('Library not found')
    }

    console.log(`Starting scan for library: ${library.name} at ${library.rootPath}`)

    // 1. 更新状态为扫描中
    await this.libraryDAO.updateScanProgress(libraryId, {
      status: 'scanning'
    })

    try {
      // 2. 发现图片文件
      const imageFiles = await this.discoverImageFiles(library.rootPath, options.maxFiles)

      // 3. 更新总文件数
      await this.libraryDAO.updateScanProgress(libraryId, {
        total: imageFiles.length
      })

      console.log(`Found ${imageFiles.length} image files to process`)

      // 4. 处理每个图片文件
      let processed = 0
      let failed = 0

      for (const filePath of imageFiles) {
        try {
          // 检查是否已存在（如果不是强制重新扫描）
          if (!options.forceRescan) {
            const existing = await this.imageService.getImageByPath(filePath)
            if (existing) {
              processed++
              continue
            }
          }

          // 处理图片
          await this.imageService.processAndStoreImage(filePath, {
            filesize: await this.getFileSize(filePath)
          })

          processed++
          console.log(`Processed image ${processed}/${imageFiles.length}: ${path.basename(filePath)}`)

        } catch (error) {
          failed++
          console.error(`Failed to process image: ${filePath}`, error)
        }

        // 更新进度
        await this.libraryDAO.updateScanProgress(libraryId, {
          processed,
          failed,
          lastScannedPath: filePath
        })

        // 添加小延迟避免过度占用资源
        await new Promise(resolve => setTimeout(resolve, 100))
      }

      // 5. 完成扫描
      await this.libraryDAO.updateScanProgress(libraryId, {
        status: 'completed',
        lastScannedPath: ''
      })

      // 6. 更新最后扫描时间
      await this.libraryDAO.updateLastScanTime(libraryId)

      console.log(`Scan completed for library: ${library.name}. Processed: ${processed}, Failed: ${failed}`)

    } catch (error) {
      await this.libraryDAO.updateScanProgress(libraryId, {
        status: 'error'
      })
      throw error
    }
  }

  /**
   * 发现图片文件
   */
  private async discoverImageFiles(rootPath: string, maxFiles?: number): Promise<string[]> {
    const supportedFormats = ['.jpg', '.jpeg', '.png', '.webp', '.gif', '.bmp', '.tiff']
    const imageFiles: string[] = []

    const discoverRecursive = async (dirPath: string): Promise<void> => {
      try {
        const entries = await fs.readdir(dirPath, { withFileTypes: true })

        for (const entry of entries) {
          if (maxFiles && imageFiles.length >= maxFiles) {
            break
          }

          const fullPath = path.join(dirPath, entry.name)

          if (entry.isFile()) {
            const ext = path.extname(entry.name).toLowerCase()
            if (supportedFormats.includes(ext)) {
              imageFiles.push(fullPath)
            }
          } else if (entry.isDirectory() && !entry.name.startsWith('.')) {
            await discoverRecursive(fullPath)
          }
        }
      } catch (error) {
        console.warn(`Failed to read directory: ${dirPath}`, error)
      }
    }

    await discoverRecursive(rootPath)
    return imageFiles
  }

  /**
   * 获取文件大小
   */
  private async getFileSize(filePath: string): Promise<number> {
    try {
      const stats = await fs.stat(filePath)
      return stats.size
    } catch {
      return 0
    }
  }

  /**
   * 从任务进度更新扫描进度
   */
  private async updateScanProgressFromTask(libraryId: string, taskProgress: TaskProgress): Promise<void> {
    try {
      await this.libraryDAO.updateScanProgress(libraryId, {
        processed: taskProgress.processed || 0,
        failed: taskProgress.failed || 0,
        lastScannedPath: taskProgress.currentStep || ''
      })
    } catch (error) {
      console.error('Failed to update scan progress from task:', error)
    }
  }

  /**
   * 获取图片库总数
   */
  async getTotalCount(): Promise<number> {
    return await this.libraryDAO.getTotalCount()
  }

  /**
   * 清空所有图片库配置
   */
  async clearAll(): Promise<BaseResult> {
    return await this.libraryDAO.clearAll()
  }

  /**
   * 测试连接
   */
  async testConnection(): Promise<boolean> {
    return await this.libraryDAO.testConnection()
  }

  /**
   * 分析文件夹
   */
  async analyzeFolder(folderPath: string, options: FolderAnalysisOptions = {}): Promise<FolderAnalysisResult> {
    const resolvedPath = path.resolve(folderPath)

    // 验证路径
    const validation = await this.validateLibraryPath(resolvedPath)
    if (!validation.valid) {
      throw new Error(`Invalid folder path: ${validation.error}`)
    }

    const analysis: FolderAnalysisResult = {
      estimatedFiles: 0,
      totalSize: 0,
      deepestLevel: 0,
      formatBreakdown: {},
      permissionIssues: []
    }

    const {
      maxDepth = 10,
      includeHidden = false,
      recursive = true,
      supportedFormats = ['.jpg', '.jpeg', '.png', '.webp', '.gif', '.bmp', '.tiff']
    } = options

    await this.analyzeFolderRecursive(resolvedPath, analysis, 0, {
      maxDepth,
      includeHidden,
      recursive,
      supportedFormats
    })

    return analysis
  }

  /**
   * 递归分析文件夹
   */
  private async analyzeFolderRecursive(
    dirPath: string,
    analysis: FolderAnalysisResult,
    currentDepth: number,
    options: Required<FolderAnalysisOptions>
  ): Promise<void> {
    try {
      // 检查深度限制
      if (currentDepth >= options.maxDepth) {
        return
      }

      // 更新最深层级
      analysis.deepestLevel = Math.max(analysis.deepestLevel, currentDepth)

      const entries = await fs.readdir(dirPath, { withFileTypes: true })

      for (const entry of entries) {
        const fullPath = path.join(dirPath, entry.name)

        // 跳过隐藏文件（如果设置）
        if (!options.includeHidden && entry.name.startsWith('.')) {
          continue
        }

        try {
          if (entry.isDirectory()) {
            // 递归处理子目录
            if (options.recursive) {
              await this.analyzeFolderRecursive(fullPath, analysis, currentDepth + 1, options)
            }
          } else if (entry.isFile()) {
            // 处理文件
            const ext = path.extname(entry.name).toLowerCase()
            if (options.supportedFormats.includes(ext)) {
              analysis.estimatedFiles++
              
              // 统计格式分布
              analysis.formatBreakdown[ext] = (analysis.formatBreakdown[ext] || 0) + 1

              // 计算文件大小
              try {
                const stats = await fs.stat(fullPath)
                analysis.totalSize += stats.size
              } catch {
                // 忽略无法访问的文件
              }
            }
          }
        } catch (error) {
          // 记录权限问题
          analysis.permissionIssues.push(fullPath)
        }
      }
    } catch (error) {
      // 记录目录访问问题
      analysis.permissionIssues.push(dirPath)
    }
  }
}