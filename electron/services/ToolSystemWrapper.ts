// /**
//  * Main-process-only wrapper for tool system to avoid EventEmitter in frontend
//  */
// export class ToolSystemWrapper {
//   private toolInitializer: any = null;
//   private isInitialized = false;
//
//   async initialize() {
//     if (this.isInitialized) return;
//
//     try {
//       // Dynamic import only in main process
//       const { ToolInitializer } = await import('../../src/lib/toolInitializer');
//       this.toolInitializer = ToolInitializer.getInstance({
//         enableProgressTracking: true,
//         enableEventNotifications: true,
//         enableErrorRecovery: true,
//         retryAttempts: 3,
//         retryDelay: 1000
//       });
//       this.isInitialized = true;
//     } catch (error) {
//       console.error('Failed to initialize tool system wrapper:', error);
//       throw error;
//     }
//   }
//
//   async startAsyncInitialization() {
//     if (!this.isInitialized) {
//       await this.initialize();
//     }
//     return this.toolInitializer?.startAsyncInitialization();
//   }
//
//   getInitializationStatus() {
//     if (!this.toolInitializer) {
//       return {
//         successfulTools: 0,
//         loadedTools: [],
//         failedTools: [],
//         totalTools: 0
//       };
//     }
//     return this.toolInitializer.getInitializationStatus();
//   }
//
//   isReady(): boolean {
//     return this.toolInitializer?.isReady() || false;
//   }
//
//   isLoading(): boolean {
//     return this.toolInitializer?.isLoading() || false;
//   }
//
//   async reinitializeFailedTools() {
//     if (!this.toolInitializer) {
//       throw new Error('Tool system not initialized');
//     }
//     return this.toolInitializer.reinitializeFailedTools();
//   }
// }