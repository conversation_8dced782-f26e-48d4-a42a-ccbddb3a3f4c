import { LibraryConfig, LibraryConfigInsertResult, LibraryConfigQueryParams, LibraryConfigQueryResult, BaseResult } from '@shared/types/database'

/**
 * 图片库配置数据访问对象接口
 * 处理 library_configs 表的所有操作
 * 注意：此DAO不涉及向量操作，只使用普通SQLite
 */
export interface LibraryDAO {
  // ============= 基础 CRUD 操作 =============
  
  /**
   * 创建图片库配置
   * @param config 图片库配置
   * @returns 创建的配置ID
   */
  create(config: Omit<LibraryConfig, 'id' | 'createdAt' | 'updatedAt'>): Promise<string>
  
  /**
   * 根据ID查找图片库配置
   * @param id 配置ID
   * @returns 配置记录或null
   */
  findById(id: string): Promise<LibraryConfig | null>
  
  /**
   * 根据路径查找图片库配置
   * @param rootPath 根路径
   * @returns 配置记录或null
   */
  findByPath(rootPath: string): Promise<LibraryConfig | null>
  
  /**
   * 根据名称查找图片库配置
   * @param name 图片库名称
   * @returns 配置记录或null
   */
  findByName(name: string): Promise<LibraryConfig | null>
  
  /**
   * 更新图片库配置
   * @param id 配置ID
   * @param updates 更新数据
   * @returns 操作结果
   */
  update(id: string, updates: Partial<LibraryConfig>): Promise<BaseResult>
  
  /**
   * 删除图片库配置
   * @param id 配置ID
   * @returns 操作结果
   */
  delete(id: string): Promise<BaseResult>
  
  /**
   * 批量插入图片库配置
   * @param configs 配置数组
   * @returns 插入结果
   */
  insertBatch(configs: Array<Omit<LibraryConfig, 'id' | 'createdAt' | 'updatedAt'>>): Promise<LibraryConfigInsertResult>
  
  // ============= 查询操作 =============
  
  /**
   * 查询图片库配置（支持条件过滤）
   * @param params 查询参数
   * @returns 查询结果
   */
  query(params: LibraryConfigQueryParams): Promise<LibraryConfigQueryResult>
  
  /**
   * 获取所有图片库配置
   * @returns 所有配置列表
   */
  findAll(): Promise<LibraryConfig[]>
  
  /**
   * 获取活跃的图片库配置
   * @returns 活跃配置列表
   */
  findActive(): Promise<LibraryConfig[]>
  
  /**
   * 按状态查询图片库配置
   * @param status 状态值
   * @returns 匹配的配置列表
   */
  findByStatus(status: 'active' | 'offline' | 'removed'): Promise<LibraryConfig[]>
  
  /**
   * 按类型查询图片库配置
   * @param type 类型值
   * @returns 匹配的配置列表
   */
  findByType(type: 'local' | 'cloud' | 'network'): Promise<LibraryConfig[]>
  
  // ============= 扫描进度相关操作 =============
  
  /**
   * 更新扫描进度
   * @param id 配置ID
   * @param progress 扫描进度信息
   * @returns 操作结果
   */
  updateScanProgress(id: string, progress: {
    total?: number
    processed?: number
    failed?: number
    status?: string
    lastScannedPath?: string
    estimatedTimeRemaining?: number
  }): Promise<BaseResult>
  
  /**
   * 更新最后扫描时间
   * @param id 配置ID
   * @param scanTime 扫描时间（可选，默认当前时间）
   * @returns 操作结果
   */
  updateLastScanTime(id: string, scanTime?: string): Promise<BaseResult>
  
  /**
   * 获取需要扫描的图片库
   * @param maxIdleTime 最大空闲时间（小时）
   * @returns 需要扫描的图片库列表
   */
  findLibrariesForScan(maxIdleTime?: number): Promise<LibraryConfig[]>
  
  // ============= 统计相关操作 =============
  
  /**
   * 更新图片库统计信息
   * @param id 配置ID
   * @param statistics 统计信息
   * @returns 操作结果
   */
  updateStatistics(id: string, statistics: {
    totalImages?: number
    totalSize?: number
    imagesByFormat?: Record<string, number>
    averageFileSize?: number
    newestImageDate?: string
    oldestImageDate?: string
  }): Promise<BaseResult>
  
  /**
   * 获取图片库统计摘要
   * @returns 统计摘要信息
   */
  getStatisticsSummary(): Promise<{
    totalLibraries: number
    activeLibraries: number
    totalImages: number
    totalSize: number
    librariesByType: Record<string, number>
    librariesByStatus: Record<string, number>
  }>
  
  // ============= 设置相关操作 =============
  
  /**
   * 更新图片库设置
   * @param id 配置ID
   * @param settings 设置信息
   * @returns 操作结果
   */
  updateSettings(id: string, settings: {
    recursive?: boolean
    includeHidden?: boolean
    maxDepth?: number
    supportedFormats?: string[]
    excludePatterns?: string[]
    autoScanInterval?: number
  }): Promise<BaseResult>
  
  /**
   * 更新图片库状态
   * @param id 配置ID
   * @param status 新状态
   * @returns 操作结果
   */
  updateStatus(id: string, status: 'active' | 'offline' | 'removed'): Promise<BaseResult>
  
  // ============= 验证和维护操作 =============
  
  /**
   * 验证图片库路径是否存在且可访问
   * @param id 配置ID
   * @returns 验证结果
   */
  validateLibraryPath(id: string): Promise<{
    valid: boolean
    exists: boolean
    accessible: boolean
    error?: string
  }>
  
  /**
   * 检查路径冲突（是否已存在相同或父子路径的图片库）
   * @param rootPath 根路径
   * @param excludeId 排除的配置ID（用于更新时检查）
   * @returns 冲突检查结果
   */
  checkPathConflict(rootPath: string, excludeId?: string): Promise<{
    hasConflict: boolean
    conflictType?: 'duplicate' | 'parent' | 'child'
    conflictingLibrary?: LibraryConfig
  }>
  
  /**
   * 清理无效的图片库配置
   * @returns 清理结果
   */
  cleanupInvalidLibraries(): Promise<{
    success: boolean
    cleanedCount: number
    error?: string
  }>
  
  /**
   * 获取图片库配置总数
   * @returns 配置数量
   */
  getTotalCount(): Promise<number>
  
  /**
   * 清空所有图片库配置
   * @returns 操作结果
   */
  clearAll(): Promise<BaseResult>
  
  /**
   * 测试数据库连接
   * @returns 连接是否正常
   */
  testConnection(): Promise<boolean>
}