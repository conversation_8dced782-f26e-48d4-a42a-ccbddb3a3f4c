import { TagRecord, TagVectorRecord, TagSearchParams, ImageCategory } from '@shared/types/database'

/**
 * 标签数据访问对象接口
 * 处理 tags 表和 tag_vectors 虚拟表的所有操作
 */
export interface TagDAO {
  // ============= 基础 CRUD 操作 =============
  
  /**
   * 创建标签记录（包含向量数据）
   * @param tag 标签记录
   * @returns 创建的标签ID
   */
  createTag(tag: Omit<TagRecord, 'tagId' | 'createdAt' | 'updatedAt'>): Promise<string>
  
  /**
   * 根据ID查找标签
   * @param tagId 标签ID
   * @returns 标签记录或null
   */
  findTagById(tagId: string): Promise<TagRecord | null>
  
  /**
   * 根据标签文本查找标签
   * @param text 标签文本
   * @returns 标签记录或null
   */
  findTagByText(text: string): Promise<TagRecord | null>
  
  /**
   * 更新标签记录
   * @param tagId 标签ID
   * @param updates 更新数据
   * @returns 是否更新成功
   */
  updateTag(tagId: string, updates: Partial<TagRecord>): Promise<boolean>
  
  /**
   * 删除标签记录（包含向量数据）
   * @param tagId 标签ID
   * @returns 是否删除成功
   */
  deleteTag(tagId: string): Promise<boolean>
  
  /**
   * 批量创建标签
   * @param tags 标签数组
   * @returns 创建结果
   */
  createTagsBatch(tags: Array<Omit<TagRecord, 'tagId' | 'createdAt' | 'updatedAt'>>): Promise<{
    success: boolean
    createdCount: number
    error?: string
  }>
  
  // ============= 查询操作 =============
  
  /**
   * 获取所有标签
   * @param params 搜索参数（可选）
   * @returns 标签列表
   */
  getAllTags(params?: TagSearchParams): Promise<TagRecord[]>
  
  /**
   * 按频率排序获取标签
   * @param limit 返回数量限制
   * @param minFrequency 最小频率
   * @returns 标签列表
   */
  getTagsByFrequency(limit?: number, minFrequency?: number): Promise<TagRecord[]>
  
  /**
   * 按分类获取标签
   * @param category 标签分类
   * @param limit 返回数量限制
   * @returns 标签列表
   */
  getTagsByCategory(category: ImageCategory, limit?: number): Promise<TagRecord[]>
  
  /**
   * 搜索标签（模糊匹配）
   * @param query 搜索关键词
   * @param limit 返回数量限制
   * @returns 标签列表
   */
  searchTags(query: string, limit?: number): Promise<TagRecord[]>
  
  // ============= 向量搜索操作 =============
  
  /**
   * 创建标签向量
   * @param tagId 标签ID
   * @param embedding 标签向量
   * @returns 是否创建成功
   */
  createTagVector(tagId: string, embedding: number[]): Promise<boolean>
  
  /**
   * 更新标签向量
   * @param tagId 标签ID
   * @param embedding 新的标签向量
   * @returns 是否更新成功
   */
  updateTagVector(tagId: string, embedding: number[]): Promise<boolean>
  
  /**
   * 查找相似标签
   * @param embedding 查询向量
   * @param threshold 相似度阈值
   * @param limit 返回数量限制
   * @returns 相似标签列表
   */
  findSimilarTags(embedding: number[], threshold?: number, limit?: number): Promise<TagRecord[]>
  
  /**
   * 根据标签ID列表扩展相似标签
   * @param tagIds 标签ID列表
   * @param threshold 相似度阈值
   * @param limit 每个标签返回的相似标签数量
   * @returns 扩展后的标签列表
   */
  expandTagsWithSimilar(tagIds: string[], threshold?: number, limit?: number): Promise<TagRecord[]>
  
  /**
   * 获取标签的向量表示
   * @param tagId 标签ID
   * @returns 标签向量或null
   */
  getTagVector(tagId: string): Promise<number[] | null>
  
  // ============= 频率和统计操作 =============
  
  /**
   * 增加标签使用频率
   * @param tagId 标签ID
   * @param increment 增加数量（默认1）
   * @returns 是否更新成功
   */
  incrementTagFrequency(tagId: string, increment?: number): Promise<boolean>
  
  /**
   * 批量增加标签频率
   * @param tagIds 标签ID列表
   * @returns 更新结果
   */
  incrementTagsFrequency(tagIds: string[]): Promise<{ success: boolean; updatedCount: number }>
  
  /**
   * 获取标签总数
   * @returns 标签数量
   */
  getTotalTagCount(): Promise<number>
  
  /**
   * 获取标签分类统计
   * @returns 分类统计信息
   */
  getTagCategoryStats(): Promise<Array<{ category: ImageCategory; count: number }>>
  
  // ============= 维护操作 =============
  
  /**
   * 清理未使用的标签（频率为0的标签）
   * @returns 清理结果
   */
  cleanupUnusedTags(): Promise<{ success: boolean; deletedCount: number }>
  
  /**
   * 重新计算所有标签的频率
   * @returns 重新计算结果
   */
  recalculateTagFrequencies(): Promise<{ success: boolean; updatedCount: number }>
  
  /**
   * 清空所有标签数据
   * @returns 操作结果
   */
  clearAll(): Promise<{ success: boolean; error?: string }>
  
  /**
   * 测试数据库连接
   * @returns 连接是否正常
   */
  testConnection(): Promise<boolean>
}