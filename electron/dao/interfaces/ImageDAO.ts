import { ImageRecord, QueryParams, QueryResult, BaseResult, ExistsResult } from '@shared/types/database'

/**
 * 图片数据访问对象接口
 * 处理 images 表和 image_vectors 虚拟表的所有操作
 */
export interface ImageDAO {
  // ============= 基础 CRUD 操作 =============
  
  /**
   * 创建图片记录（包含向量数据）
   * @param image 图片记录
   * @returns 创建的图片ID
   */
  create(image: ImageRecord): Promise<string>
  
  /**
   * 根据ID查找图片
   * @param id 图片ID
   * @returns 图片记录或null
   */
  findById(id: string): Promise<ImageRecord | null>
  
  /**
   * 根据文件路径查找图片
   * @param filePath 文件路径
   * @returns 图片记录或null
   */
  findByPath(filePath: string): Promise<ImageRecord | null>
  
  /**
   * 更新图片记录
   * @param id 图片ID
   * @param updates 更新数据
   * @returns 是否更新成功
   */
  update(id: string, updates: Partial<ImageRecord>): Promise<boolean>
  
  /**
   * 删除图片记录（包含向量数据）
   * @param id 图片ID
   * @returns 是否删除成功
   */
  delete(id: string): Promise<boolean>
  
  /**
   * 批量插入图片记录
   * @param images 图片记录数组
   * @returns 插入结果
   */
  insertBatch(images: ImageRecord[]): Promise<{ success: boolean; insertedCount: number; error?: string }>
  
  // ============= 查询操作 =============
  
  /**
   * 查询图片（支持条件过滤）
   * @param params 查询参数
   * @returns 查询结果
   */
  queryImages(params: QueryParams): Promise<QueryResult>
  
  /**
   * 检查图片是否存在
   * @param filePath 文件路径
   * @param md5 可选的MD5校验和
   * @returns 存在性检查结果
   */
  checkExists(filePath: string, md5?: string): Promise<ExistsResult>
  
  // ============= 向量搜索操作 =============
  
  /**
   * 向量相似度搜索
   * @param embedding 查询向量
   * @param threshold 相似度阈值 (可选)
   * @param limit 返回数量限制 (可选)
   * @returns 搜索结果
   */
  vectorSearch(embedding: number[], threshold?: number, limit?: number): Promise<QueryResult>
  
  /**
   * 混合搜索：结合标量过滤和向量搜索
   * @param params 混合搜索参数
   * @returns 搜索结果
   */
  hybridSearch(params: {
    query: string
    keywords: string[]
    embedding: number[]
    limit?: number
    similarityThreshold?: number
    expandKeywords?: boolean
  }): Promise<QueryResult>
  
  /**
   * 按图片ID搜索相似图片
   * @param imageId 图片ID
   * @param options 搜索选项
   * @returns 相似图片结果
   */
  findSimilarImages(imageId: string, options?: { 
    threshold?: number
    limit?: number 
  }): Promise<QueryResult>
  
  // ============= 位置相关查询 =============
  
  /**
   * 按地理位置查询图片
   * @param latitude 纬度
   * @param longitude 经度
   * @param radius 搜索半径（米）
   * @param limit 返回数量限制
   * @returns 图片列表
   */
  queryByLocation(latitude: number, longitude: number, radius?: number, limit?: number): Promise<ImageRecord[]>
  
  /**
   * 按城市或国家查询图片
   * @param location 地点名称
   * @param limit 返回数量限制
   * @returns 图片列表
   */
  queryByLocationName(location: string, limit?: number): Promise<ImageRecord[]>
  
  // ============= 时间相关查询 =============
  
  /**
   * 按时间范围查询图片
   * @param startTime 开始时间（Unix时间戳）
   * @param endTime 结束时间（Unix时间戳）
   * @param limit 返回数量限制
   * @returns 图片列表
   */
  queryByTimeRange(startTime: number, endTime: number, limit?: number): Promise<ImageRecord[]>
  
  // ============= 统计和维护操作 =============
  
  /**
   * 获取图片总数
   * @returns 图片数量
   */
  getTotalCount(): Promise<number>
  
  /**
   * 清空所有图片数据
   * @returns 操作结果
   */
  clearAll(): Promise<BaseResult>
  
  /**
   * 测试数据库连接
   * @returns 连接是否正常
   */
  testConnection(): Promise<boolean>
}