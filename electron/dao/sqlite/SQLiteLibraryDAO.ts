import Database, { Database as DatabaseType } from 'better-sqlite3'
import * as path from 'path'
import * as fs from 'fs'
import { LibraryDAO } from '../interfaces/LibraryDAO'
import { LibraryConfig, LibraryConfigInsertResult, LibraryConfigQueryParams, LibraryConfigQueryResult, BaseResult } from '@shared/types/database'
import { dbToTs, tsToDb } from '@shared/utils/fieldMapper'

/**
 * SQLite 图片库配置 DAO 实现
 * 处理 library_configs 表的所有操作
 * 使用普通 SQLite，不涉及向量操作
 */
export class SQLiteLibraryDAO implements LibraryDAO {
  private db: DatabaseType | null = null
  private dbPath: string

  constructor(dbPath?: string) {
    this.dbPath = dbPath || path.join(process.cwd(), 'data', 'sqlite-vec.db')
    this.initDatabase()
  }

  /**
   * 初始化数据库连接和表结构
   */
  private initDatabase(): void {
    this.connectDatabase()
    this.createTables()
    this.createIndexes()
  }

  /**
   * 连接数据库并加载 sqlite-vec 扩展
   */
  private connectDatabase(): void {
    if (this.db) return

    // 确保数据库目录存在
    const dbDir = path.dirname(this.dbPath)
    if (!fs.existsSync(dbDir)) {
      fs.mkdirSync(dbDir, { recursive: true })
    }

    // 创建或打开SQLite数据库
    this.db = new Database(this.dbPath)

    // 开启WAL模式以提高并发性能
    this.db.pragma('journal_mode = WAL')
  }

  /**
   * 创建图片库配置表
   */
  private createTables(): void {
    if (!this.db) throw new Error('数据库未连接')

    this.db.exec(`
      CREATE TABLE IF NOT EXISTS library_configs (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        root_path TEXT NOT NULL,
        type TEXT DEFAULT 'local' CHECK (type IN ('local', 'cloud', 'network')),
        status TEXT NOT NULL CHECK (status IN ('active', 'offline', 'removed')),
        settings TEXT NOT NULL, -- JSON字符串
        scan_progress TEXT NOT NULL, -- JSON字符串
        statistics TEXT NOT NULL, -- JSON字符串
        description TEXT,
        tags TEXT, -- JSON数组字符串
        metadata TEXT, -- JSON字符串
        created_at TEXT NOT NULL,
        last_scan_at TEXT NOT NULL,
        updated_at TEXT,
        
        UNIQUE(root_path), -- 根路径唯一约束
        CHECK(json_valid(settings)), -- JSON格式验证
        CHECK(json_valid(scan_progress)), -- JSON格式验证
        CHECK(json_valid(statistics)), -- JSON格式验证
        CHECK(tags IS NULL OR json_valid(tags)), -- JSON格式验证
        CHECK(metadata IS NULL OR json_valid(metadata)) -- JSON格式验证
      )
    `)
  }

  /**
   * 创建索引
   */
  private createIndexes(): void {
    if (!this.db) throw new Error('数据库未连接')

    const indexes = [
      'CREATE INDEX IF NOT EXISTS idx_library_configs_name ON library_configs(name)',
      'CREATE INDEX IF NOT EXISTS idx_library_configs_status ON library_configs(status)',
      'CREATE INDEX IF NOT EXISTS idx_library_configs_type ON library_configs(type)',
      'CREATE INDEX IF NOT EXISTS idx_library_configs_created_at ON library_configs(created_at)',
      'CREATE INDEX IF NOT EXISTS idx_library_configs_updated_at ON library_configs(updated_at)'
    ]

    indexes.forEach(sql => this.db!.exec(sql))
  }

  // ============= 基础 CRUD 操作 =============

  async create(config: Omit<LibraryConfig, 'id' | 'created_at' | 'updated_at'>): Promise<string> {
    if (!this.db) throw new Error('数据库未连接')

    const id = `lib_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    const now = new Date().toISOString()

    const stmt = this.db.prepare(`
      INSERT INTO library_configs (
        id, name, root_path, type, status, settings, scan_progress, 
        statistics, description, tags, metadata, created_at, last_scan_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `)

    try {
      stmt.run(
        id,
        config.name,
        config.rootPath,
        config.type || 'local',
        config.status,
        JSON.stringify(config.settings),
        JSON.stringify(config.scanProgress),
        JSON.stringify(config.statistics),
        config.description || null,
        config.tags ? JSON.stringify(config.tags) : null,
        config.metadata ? JSON.stringify(config.metadata) : null,
        now,
        config.lastScanAt,
        now
      )
      return id
    } catch (error: any) {
      throw new Error(`创建图片库配置失败: ${error.message}`)
    }
  }

  async findById(id: string): Promise<LibraryConfig | null> {
    if (!this.db) throw new Error('数据库未连接')

    const stmt = this.db.prepare('SELECT * FROM library_configs WHERE id = ?')
    const row = stmt.get(id) as any

    return row ? this.parseLibraryConfig(row) : null
  }

  async findByPath(rootPath: string): Promise<LibraryConfig | null> {
    if (!this.db) throw new Error('数据库未连接')

    const stmt = this.db.prepare('SELECT * FROM library_configs WHERE root_path = ?')
    const row = stmt.get(rootPath) as any

    return row ? this.parseLibraryConfig(row) : null
  }

  async findByName(name: string): Promise<LibraryConfig | null> {
    if (!this.db) throw new Error('数据库未连接')

    const stmt = this.db.prepare('SELECT * FROM library_configs WHERE name = ?')
    const row = stmt.get(name) as any

    return row ? this.parseLibraryConfig(row) : null
  }

  async update(id: string, updates: Partial<LibraryConfig>): Promise<BaseResult> {
    if (!this.db) throw new Error('数据库未连接')

    const setClause: string[] = []
    const values: any[] = []

    // 构建动态更新语句
    Object.entries(updates).forEach(([key, value]) => {
      if (key === 'id' || key === 'created_at') return // 不允许更新这些字段

      // 处理需要JSON序列化的字段
      if (['settings', 'scanProgress', 'statistics', 'tags', 'metadata'].includes(key)) {
        // 将驼峰字段名转换为数据库下划线字段名
        const dbFieldName = key === 'scanProgress' ? 'scan_progress' : key
        setClause.push(`${dbFieldName} = ?`)
        values.push(value ? JSON.stringify(value) : null)
      } else {
        // 其他字段需要转换为下划线格式
        const dbFieldName = key.replace(/([A-Z])/g, '_$1').toLowerCase()
        setClause.push(`${dbFieldName} = ?`)
        values.push(value)
      }
    })

    if (setClause.length === 0) {
      return { success: true }
    }

    // 添加更新时间
    setClause.push('updated_at = ?')
    values.push(new Date().toISOString())
    values.push(id)

    const sql = `UPDATE library_configs SET ${setClause.join(', ')} WHERE id = ?`
    const stmt = this.db.prepare(sql)

    try {
      const result = stmt.run(...values)
      return {
        success: result.changes > 0,
        error: result.changes === 0 ? '未找到要更新的记录' : undefined
      }
    } catch (error: any) {
      return {
        success: false,
        error: `更新失败: ${error.message}`
      }
    }
  }

  async delete(id: string): Promise<BaseResult> {
    if (!this.db) throw new Error('数据库未连接')

    const stmt = this.db.prepare('DELETE FROM library_configs WHERE id = ?')

    try {
      const result = stmt.run(id)
      return {
        success: result.changes > 0,
        error: result.changes === 0 ? '未找到要删除的记录' : undefined
      }
    } catch (error: any) {
      return {
        success: false,
        error: `删除失败: ${error.message}`
      }
    }
  }

  async insertBatch(configs: Array<Omit<LibraryConfig, 'id' | 'created_at' | 'updated_at'>>): Promise<LibraryConfigInsertResult> {
    if (!this.db) throw new Error('数据库未连接')

    const stmt = this.db.prepare(`
      INSERT INTO library_configs (
        id, name, root_path, type, status, settings, scan_progress, 
        statistics, description, tags, metadata, created_at, last_scan_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `)

    const transaction = this.db.transaction((configs: any[]) => {
      const results: string[] = []
      const now = new Date().toISOString()

      for (const config of configs) {
        const id = `lib_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`

        stmt.run(
          id,
          config.name,
          config.rootPath,
          config.type || 'local',
          config.status,
          JSON.stringify(config.settings),
          JSON.stringify(config.scanProgress),
          JSON.stringify(config.statistics),
          config.description || null,
          config.tags ? JSON.stringify(config.tags) : null,
          config.metadata ? JSON.stringify(config.metadata) : null,
          now,
          config.lastScanAt,
          now
        )
        results.push(id)
      }
      return results
    })

    try {
      const insertedIds = transaction(configs)
      return {
        success: true,
        insertedIds,
        insertedCount: insertedIds.length,
        ids: insertedIds
      }
    } catch (error: any) {
      return {
        success: false,
        error: `批量插入失败: ${error.message}`,
        insertedIds: [],
        insertedCount: 0,
        ids: []
      }
    }
  }

  // ============= 查询操作 =============

  async query(params: LibraryConfigQueryParams): Promise<LibraryConfigQueryResult> {
    if (!this.db) throw new Error('数据库未连接')

    let sql = 'SELECT * FROM library_configs WHERE 1=1'
    const values: any[] = []

    // 构建查询条件
    if (params.status) {
      sql += ' AND status = ?'
      values.push(params.status)
    }
    if (params.type) {
      sql += ' AND type = ?'
      values.push(params.type)
    }
    if (params.name) {
      sql += ' AND name LIKE ?'
      values.push(`%${params.name}%`)
    }

    // 排序
    sql += ' ORDER BY created_at DESC'

    // 分页
    if (params.limit) {
      sql += ' LIMIT ?'
      values.push(params.limit)

      if (params.offset) {
        sql += ' OFFSET ?'
        values.push(params.offset)
      }
    }

    try {
      const stmt = this.db.prepare(sql)
      const rows = stmt.all(...values) as any[]

      const libraries = rows.map(row => this.parseLibraryConfig(row))

      // 获取总数（用于分页）
      const countSql = sql.replace(/SELECT \* FROM/, 'SELECT COUNT(*) as count FROM').replace(/ORDER BY.*$/, '')
      const countStmt = this.db.prepare(countSql)
      const countResult = countStmt.get(...values.slice(0, -2)) as any // 移除 limit 和 offset 参数

      return {
        success: true,
        results: libraries,
        total: countResult?.count || 0,
        count: countResult?.count || 0
      }
    } catch (error: any) {
      return {
        success: false,
        results: [],
        total: 0,
        count: 0,
        error: `查询失败: ${error.message}`
      }
    }
  }

  async findAll(): Promise<LibraryConfig[]> {
    if (!this.db) throw new Error('数据库未连接')

    const stmt = this.db.prepare('SELECT * FROM library_configs ORDER BY created_at DESC')
    const rows = stmt.all() as any[]

    return rows.map(row => this.parseLibraryConfig(row))
  }

  async findActive(): Promise<LibraryConfig[]> {
    return this.findByStatus('active')
  }

  async findByStatus(status: 'active' | 'offline' | 'removed'): Promise<LibraryConfig[]> {
    if (!this.db) throw new Error('数据库未连接')

    const stmt = this.db.prepare('SELECT * FROM library_configs WHERE status = ? ORDER BY created_at DESC')
    const rows = stmt.all(status) as any[]

    return rows.map(row => this.parseLibraryConfig(row))
  }

  async findByType(type: 'local' | 'cloud' | 'network'): Promise<LibraryConfig[]> {
    if (!this.db) throw new Error('数据库未连接')

    const stmt = this.db.prepare('SELECT * FROM library_configs WHERE type = ? ORDER BY created_at DESC')
    const rows = stmt.all(type) as any[]

    return rows.map(row => this.parseLibraryConfig(row))
  }

  // ============= 其他方法（简化实现，可根据需要扩展） =============

  async updateScanProgress(id: string, progress: any): Promise<BaseResult> {
    return this.update(id, { scanProgress: progress })
  }

  async updateLastScanTime(id: string, scanTime?: string): Promise<BaseResult> {
    return this.update(id, { lastScanAt: scanTime || new Date().toISOString() })
  }

  async findLibrariesForScan(maxIdleTime: number = 24): Promise<LibraryConfig[]> {
    if (!this.db) throw new Error('数据库未连接')

    const cutoffTime = new Date(Date.now() - maxIdleTime * 60 * 60 * 1000).toISOString()
    const stmt = this.db.prepare(`
      SELECT * FROM library_configs 
      WHERE status = 'active' AND last_scan_at < ? 
      ORDER BY last_scan_at ASC
    `)
    const rows = stmt.all(cutoffTime) as any[]

    return rows.map(row => this.parseLibraryConfig(row))
  }

  async updateStatistics(id: string, statistics: any): Promise<BaseResult> {
    return this.update(id, { statistics })
  }

  async getStatisticsSummary(): Promise<any> {
    if (!this.db) throw new Error('数据库未连接')

    const stmt = this.db.prepare(`
      SELECT 
        COUNT(*) as totalLibraries,
        COUNT(CASE WHEN status = 'active' THEN 1 END) as activeLibraries,
        type,
        status
      FROM library_configs 
      GROUP BY type, status
    `)
    const rows = stmt.all() as any[]

    // 处理统计结果
    const summary = {
      totalLibraries: 0,
      activeLibraries: 0,
      totalImages: 0,
      totalSize: 0,
      librariesByType: {} as Record<string, number>,
      librariesByStatus: {} as Record<string, number>
    }

    rows.forEach(row => {
      summary.totalLibraries += row.totalLibraries
      summary.activeLibraries += row.activeLibraries
      summary.librariesByType[row.type] = (summary.librariesByType[row.type] || 0) + row.totalLibraries
      summary.librariesByStatus[row.status] = (summary.librariesByStatus[row.status] || 0) + row.totalLibraries
    })

    return summary
  }

  async updateSettings(id: string, settings: any): Promise<BaseResult> {
    return this.update(id, { settings })
  }

  async updateStatus(id: string, status: 'active' | 'offline' | 'removed'): Promise<BaseResult> {
    return this.update(id, { status })
  }

  async validateLibraryPath(id: string): Promise<any> {
    const config = await this.findById(id)
    if (!config) {
      return { valid: false, exists: false, accessible: false, error: '配置不存在' }
    }

    try {
      const exists = fs.existsSync(config.rootPath)
      const accessible = exists && fs.accessSync(config.rootPath, fs.constants.R_OK) === undefined
      
      return {
        valid: exists && accessible,
        exists,
        accessible,
        error: !exists ? '路径不存在' : !accessible ? '路径不可访问' : undefined
      }
    } catch (error: any) {
      return {
        valid: false,
        exists: false,
        accessible: false,
        error: error.message
      }
    }
  }

  async checkPathConflict(rootPath: string, excludeId?: string): Promise<any> {
    if (!this.db) throw new Error('数据库未连接')

    let sql = 'SELECT * FROM library_configs WHERE root_path = ?'
    const values = [rootPath]

    if (excludeId) {
      sql += ' AND id != ?'
      values.push(excludeId)
    }

    const stmt = this.db.prepare(sql)
    const conflicting = stmt.get(...values) as any

    if (conflicting) {
      return {
        hasConflict: true,
        conflictType: 'duplicate',
        conflictingLibrary: this.parseLibraryConfig(conflicting)
      }
    }

    // TODO: 检查父子路径冲突
    return { hasConflict: false }
  }

  async cleanupInvalidLibraries(): Promise<any> {
    // TODO: 实现清理逻辑
    return { success: true, cleanedCount: 0 }
  }

  async getTotalCount(): Promise<number> {
    if (!this.db) throw new Error('数据库未连接')

    const stmt = this.db.prepare('SELECT COUNT(*) as count FROM library_configs')
    const result = stmt.get() as any

    return result?.count || 0
  }

  async clearAll(): Promise<BaseResult> {
    if (!this.db) throw new Error('数据库未连接')

    try {
      const stmt = this.db.prepare('DELETE FROM library_configs')
      stmt.run()
      return { success: true }
    } catch (error: any) {
      return {
        success: false,
        error: `清空失败: ${error.message}`
      }
    }
  }

  async testConnection(): Promise<boolean> {
    try {
      if (!this.db) return false
      const stmt = this.db.prepare('SELECT 1')
      stmt.get()
      return true
    } catch {
      return false
    }
  }

  // ============= 辅助方法 =============

  /**
   * 解析数据库行为 LibraryConfig 对象
   */
  private parseLibraryConfig(row: any): LibraryConfig {
    // 使用 humps 自动将数据库下划线字段转换为驼峰字段
    const baseRecord = dbToTs<LibraryConfig>(row);
    
    // 处理JSON字段的特殊解析 - 使用转换后的驼峰字段名
    return {
      ...baseRecord,
      settings: typeof baseRecord.settings === 'string' ? JSON.parse(baseRecord.settings) : baseRecord.settings,
      scanProgress: typeof baseRecord.scanProgress === 'string' ? JSON.parse(baseRecord.scanProgress) : baseRecord.scanProgress,
      statistics: typeof baseRecord.statistics === 'string' ? JSON.parse(baseRecord.statistics) : baseRecord.statistics,
      tags: baseRecord.tags ? (typeof baseRecord.tags === 'string' ? JSON.parse(baseRecord.tags) : baseRecord.tags) : undefined,
      metadata: baseRecord.metadata ? (typeof baseRecord.metadata === 'string' ? JSON.parse(baseRecord.metadata) : baseRecord.metadata) : undefined
    };
  }

  /**
   * 关闭数据库连接
   */
  close(): void {
    if (this.db) {
      this.db.close()
      this.db = null as any
    }
  }
}