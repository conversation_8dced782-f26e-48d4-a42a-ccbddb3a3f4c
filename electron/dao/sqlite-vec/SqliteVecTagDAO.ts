import Database, { Database as DatabaseType } from 'better-sqlite3'
import * as sqliteVec from 'sqlite-vec'
import * as path from 'path'
import * as fs from 'fs'
import { TagDAO } from '../interfaces/TagDAO'
import { TagRecord, TagVectorRecord, TagSearchParams, ImageCategory } from '@shared/types/database'
import { dbToTs, tsToDb } from '@shared/utils/fieldMapper'

/**
 * SQLite-Vec 标签数据访问对象实现
 * 处理 tags 表和 tag_vectors 虚拟表的所有操作
 * 使用 sqlite-vec 扩展提供向量搜索能力
 */
export class SqliteVecTagDAO implements TagDAO {
  private db: DatabaseType | null = null
  private dbPath: string

  constructor(dbPath?: string) {
    this.dbPath = dbPath || path.join(process.cwd(), 'data', 'sqlite-vec.db')
    this.initDatabase()
  }

  /**
   * 初始化数据库连接和表结构
   */
  private initDatabase(): void {
    this.connectDatabase()
    this.createTables()
    this.createIndexes()
  }

  /**
   * 连接数据库并加载 sqlite-vec 扩展
   */
  private connectDatabase(): void {
    if (this.db) return

    // 确保数据库目录存在
    const dbDir = path.dirname(this.dbPath)
    if (!fs.existsSync(dbDir)) {
      fs.mkdirSync(dbDir, { recursive: true })
    }

    // 创建或打开SQLite数据库
    this.db = new Database(this.dbPath)
    
    // 开启WAL模式以提高并发性能
    this.db.pragma('journal_mode = WAL')
    
    // 加载 sqlite-vec 扩展
    try {
      sqliteVec.load(this.db)
    } catch (error) {
      console.warn('Failed to load sqlite-vec with direct method, trying loadable path:', error)
      try {
        const loadablePath = sqliteVec.getLoadablePath()
        this.db.loadExtension(loadablePath)
      } catch (error2) {
        console.error('Failed to load sqlite-vec extension with both methods:', error2)
        throw new Error(`sqlite-vec extension failed to load: ${error2}`)
      }
    }
  }

  /**
   * 创建数据库表结构
   */
  private createTables(): void {
    if (!this.db) throw new Error('数据库未连接')

    // 创建标签主数据表
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS tags (
        tag_id TEXT PRIMARY KEY,
        tag_text TEXT NOT NULL UNIQUE,
        frequency INTEGER NOT NULL DEFAULT 0,
        category TEXT,
        created_at INTEGER DEFAULT (unixepoch()),
        updated_at INTEGER DEFAULT (unixepoch())
      )
    `)

    // 创建标签向量存储表 (虚拟表)
    this.db.exec(`
      CREATE VIRTUAL TABLE IF NOT EXISTS tag_vectors USING vec0(
        tag_id TEXT PRIMARY KEY,
        tag_embedding FLOAT[1024]
      )
    `)
  }

  /**
   * 创建索引
   */
  private createIndexes(): void {
    if (!this.db) throw new Error('数据库未连接')

    const indexes = [
      'CREATE INDEX IF NOT EXISTS idx_tags_text ON tags(tag_text)',
      'CREATE INDEX IF NOT EXISTS idx_tags_frequency ON tags(frequency DESC)',
      'CREATE INDEX IF NOT EXISTS idx_tags_category ON tags(category)',
      'CREATE INDEX IF NOT EXISTS idx_tags_created_at ON tags(created_at)',
      'CREATE INDEX IF NOT EXISTS idx_tags_updated_at ON tags(updated_at)'
    ]

    indexes.forEach(sql => {
      try {
        this.db!.exec(sql)
      } catch (error) {
        console.warn(`Failed to create index: ${sql}`, error)
      }
    })
  }

  // ============= 基础 CRUD 操作 =============

  async createTag(tag: Omit<TagRecord, 'tag_id' | 'created_at' | 'updated_at'>): Promise<string> {
    if (!this.db) throw new Error('数据库未连接')

    const tagId = `tag_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    const now = Math.floor(Date.now() / 1000)

    try {
      const stmt = this.db.prepare(`
        INSERT INTO tags (tag_id, tag_text, frequency, category, created_at, updated_at)
        VALUES (?, ?, ?, ?, ?, ?)
      `)

      stmt.run(
        tagId,
        tag.tagText,
        tag.frequency || 0,
        tag.category || null,
        now,
        now
      )

      return tagId
    } catch (error: any) {
      throw new Error(`创建标签失败: ${error.message}`)
    }
  }

  async findTagById(tagId: string): Promise<TagRecord | null> {
    if (!this.db) throw new Error('数据库未连接')

    const stmt = this.db.prepare('SELECT * FROM tags WHERE tag_id = ?')
    const row = stmt.get(tagId) as any

    return row ? this.parseTagRecord(row) : null
  }

  async findTagByText(text: string): Promise<TagRecord | null> {
    if (!this.db) throw new Error('数据库未连接')

    const stmt = this.db.prepare('SELECT * FROM tags WHERE tag_text = ?')
    const row = stmt.get(text) as any

    return row ? this.parseTagRecord(row) : null
  }

  async updateTag(tagId: string, updates: Partial<TagRecord>): Promise<boolean> {
    if (!this.db) throw new Error('数据库未连接')

    const setClause: string[] = []
    const values: any[] = []

    // 构建动态更新语句
    Object.entries(updates).forEach(([key, value]) => {
      if (key === 'tag_id' || key === 'created_at') return // 不允许更新这些字段
      
      setClause.push(`${key} = ?`)
      values.push(value)
    })

    if (setClause.length === 0) {
      return true
    }

    // 添加更新时间
    setClause.push('updated_at = ?')
    values.push(Math.floor(Date.now() / 1000))
    values.push(tagId)

    const sql = `UPDATE tags SET ${setClause.join(', ')} WHERE tag_id = ?`
    const stmt = this.db.prepare(sql)

    try {
      const result = stmt.run(...values)
      return result.changes > 0
    } catch (error: any) {
      console.error(`更新标签失败: ${error.message}`)
      return false
    }
  }

  async deleteTag(tagId: string): Promise<boolean> {
    if (!this.db) throw new Error('数据库未连接')

    const transaction = this.db.transaction((id: string) => {
      // 删除向量数据
      const vectorStmt = this.db!.prepare('DELETE FROM tag_vectors WHERE tag_id = ?')
      vectorStmt.run(id)

      // 删除主数据
      const tagStmt = this.db!.prepare('DELETE FROM tags WHERE tag_id = ?')
      const result = tagStmt.run(id)
      
      return result.changes > 0
    })

    try {
      return transaction(tagId)
    } catch (error: any) {
      console.error(`删除标签失败: ${error.message}`)
      return false
    }
  }

  async createTagsBatch(tags: Array<Omit<TagRecord, 'tag_id' | 'created_at' | 'updated_at'>>): Promise<{
    success: boolean
    createdCount: number
    error?: string
  }> {
    if (!this.db) throw new Error('数据库未连接')

    const transaction = this.db.transaction((tagList: any[]) => {
      let createdCount = 0
      const now = Math.floor(Date.now() / 1000)

      const stmt = this.db!.prepare(`
        INSERT INTO tags (tag_id, tag_text, frequency, category, created_at, updated_at)
        VALUES (?, ?, ?, ?, ?, ?)
      `)

      for (const tag of tagList) {
        try {
          const tagId = `tag_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
          
          stmt.run(
            tagId,
            tag.tagText,
            tag.frequency || 0,
            tag.category || null,
            now,
            now
          )

          createdCount++
        } catch (error) {
          console.warn(`跳过创建标签 ${tag.tagText}: ${error}`)
        }
      }

      return createdCount
    })

    try {
      const createdCount = transaction(tags)
      return {
        success: true,
        createdCount
      }
    } catch (error: any) {
      return {
        success: false,
        createdCount: 0,
        error: `批量创建标签失败: ${error.message}`
      }
    }
  }

  // ============= 查询操作 =============

  async getAllTags(params?: TagSearchParams): Promise<TagRecord[]> {
    if (!this.db) throw new Error('数据库未连接')

    let sql = 'SELECT * FROM tags WHERE 1=1'
    const values: any[] = []

    // 构建查询条件
    if (params?.text) {
      sql += ' AND tag_text LIKE ?'
      values.push(`%${params.text}%`)
    }

    if (params?.category) {
      sql += ' AND category = ?'
      values.push(params.category)
    }

    if (params?.minFrequency) {
      sql += ' AND frequency >= ?'
      values.push(params.minFrequency)
    }

    // 排序
    sql += ' ORDER BY frequency DESC, tag_text ASC'

    // 分页
    if (params?.limit) {
      sql += ' LIMIT ?'
      values.push(params.limit)
      
      if (params?.offset) {
        sql += ' OFFSET ?'
        values.push(params.offset)
      }
    }

    try {
      const stmt = this.db.prepare(sql)
      const rows = stmt.all(...values) as any[]
      
      return rows.map(row => this.parseTagRecord(row))
    } catch (error: any) {
      throw new Error(`查询标签失败: ${error.message}`)
    }
  }

  async getTagsByFrequency(limit?: number, minFrequency?: number): Promise<TagRecord[]> {
    if (!this.db) throw new Error('数据库未连接')

    let sql = 'SELECT * FROM tags WHERE 1=1'
    const values: any[] = []

    if (minFrequency) {
      sql += ' AND frequency >= ?'
      values.push(minFrequency)
    }

    sql += ' ORDER BY frequency DESC, tag_text ASC'

    if (limit) {
      sql += ' LIMIT ?'
      values.push(limit)
    }

    const stmt = this.db.prepare(sql)
    const rows = stmt.all(...values) as any[]

    return rows.map(row => this.parseTagRecord(row))
  }

  async getTagsByCategory(category: ImageCategory, limit?: number): Promise<TagRecord[]> {
    if (!this.db) throw new Error('数据库未连接')

    let sql = 'SELECT * FROM tags WHERE category = ? ORDER BY frequency DESC, tag_text ASC'
    const values: any[] = [category]

    if (limit) {
      sql += ' LIMIT ?'
      values.push(limit)
    }

    const stmt = this.db.prepare(sql)
    const rows = stmt.all(...values) as any[]

    return rows.map(row => this.parseTagRecord(row))
  }

  async searchTags(query: string, limit?: number): Promise<TagRecord[]> {
    if (!this.db) throw new Error('数据库未连接')

    let sql = 'SELECT * FROM tags WHERE tag_text LIKE ? ORDER BY frequency DESC, tag_text ASC'
    const values: any[] = [`%${query}%`]

    if (limit) {
      sql += ' LIMIT ?'
      values.push(limit)
    }

    const stmt = this.db.prepare(sql)
    const rows = stmt.all(...values) as any[]

    return rows.map(row => this.parseTagRecord(row))
  }

  // ============= 向量搜索操作 =============

  async createTagVector(tagId: string, embedding: number[]): Promise<boolean> {
    if (!this.db) throw new Error('数据库未连接')

    try {
      const stmt = this.db.prepare(`
        INSERT INTO tag_vectors (tag_id, tag_embedding) VALUES (?, ?)
      `)
      stmt.run(tagId, JSON.stringify(embedding))
      return true
    } catch (error: any) {
      console.error(`创建标签向量失败: ${error.message}`)
      return false
    }
  }

  async updateTagVector(tagId: string, embedding: number[]): Promise<boolean> {
    if (!this.db) throw new Error('数据库未连接')

    try {
      const stmt = this.db.prepare(`
        INSERT OR REPLACE INTO tag_vectors (tag_id, tag_embedding) VALUES (?, ?)
      `)
      stmt.run(tagId, JSON.stringify(embedding))
      return true
    } catch (error: any) {
      console.error(`更新标签向量失败: ${error.message}`)
      return false
    }
  }

  async findSimilarTags(embedding: number[], threshold?: number, limit?: number): Promise<TagRecord[]> {
    if (!this.db) throw new Error('数据库未连接')

    const searchThreshold = threshold || 0.7
    const searchLimit = limit || 20

    try {
      const stmt = this.db.prepare(`
        SELECT t.*, tv.tag_embedding,
               vec_distance_cosine(tv.tag_embedding, ?) as distance
        FROM tag_vectors tv
        JOIN tags t ON tv.tag_id = t.tag_id
        WHERE vec_distance_cosine(tv.tag_embedding, ?) < ?
        ORDER BY distance ASC
        LIMIT ?
      `)

      const vectorStr = JSON.stringify(embedding)
      const rows = stmt.all(vectorStr, vectorStr, 1 - searchThreshold, searchLimit) as any[]
      
      return rows.map(row => {
        const tag = this.parseTagRecord(row)
        tag.similarityScore = 1 - row.distance // 转换为相似度分数
        return tag
      })
    } catch (error: any) {
      throw new Error(`标签向量搜索失败: ${error.message}`)
    }
  }

  async expandTagsWithSimilar(tagIds: string[], threshold?: number, limit?: number): Promise<TagRecord[]> {
    if (!this.db) throw new Error('数据库未连接')

    if (tagIds.length === 0) return []

    const expandedTags = new Map<string, TagRecord>()
    
    // 获取每个标签的相似标签
    for (const tagId of tagIds) {
      const vector = await this.getTagVector(tagId)
      if (vector) {
        const similarTags = await this.findSimilarTags(vector, threshold, limit)
        similarTags.forEach(tag => {
          if (!expandedTags.has(tag.tagId)) {
            expandedTags.set(tag.tagId, tag)
          }
        })
      }
    }

    return Array.from(expandedTags.values())
  }

  async getTagVector(tagId: string): Promise<number[] | null> {
    if (!this.db) throw new Error('数据库未连接')

    const stmt = this.db.prepare('SELECT tag_embedding FROM tag_vectors WHERE tag_id = ?')
    const row = stmt.get(tagId) as any

    return row?.tag_embedding ? JSON.parse(row.tag_embedding) : null
  }

  // ============= 频率和统计操作 =============

  async incrementTagFrequency(tagId: string, increment: number = 1): Promise<boolean> {
    if (!this.db) throw new Error('数据库未连接')

    try {
      const stmt = this.db.prepare(`
        UPDATE tags 
        SET frequency = frequency + ?, updated_at = ?
        WHERE tag_id = ?
      `)
      const result = stmt.run(increment, Math.floor(Date.now() / 1000), tagId)
      return result.changes > 0
    } catch (error: any) {
      console.error(`增加标签频率失败: ${error.message}`)
      return false
    }
  }

  async incrementTagsFrequency(tagIds: string[]): Promise<{ success: boolean; updatedCount: number }> {
    if (!this.db) throw new Error('数据库未连接')

    if (tagIds.length === 0) {
      return { success: true, updatedCount: 0 }
    }

    const transaction = this.db.transaction((ids: string[]) => {
      let updatedCount = 0
      const now = Math.floor(Date.now() / 1000)
      const stmt = this.db!.prepare(`
        UPDATE tags 
        SET frequency = frequency + 1, updated_at = ?
        WHERE tag_id = ?
      `)

      for (const tagId of ids) {
        try {
          const result = stmt.run(now, tagId)
          if (result.changes > 0) {
            updatedCount++
          }
        } catch (error) {
          console.warn(`跳过更新标签频率 ${tagId}: ${error}`)
        }
      }

      return updatedCount
    })

    try {
      const updatedCount = transaction(tagIds)
      return {
        success: true,
        updatedCount
      }
    } catch (error: any) {
      return {
        success: false,
        updatedCount: 0
      }
    }
  }

  async getTotalTagCount(): Promise<number> {
    if (!this.db) throw new Error('数据库未连接')

    const stmt = this.db.prepare('SELECT COUNT(*) as count FROM tags')
    const result = stmt.get() as any

    return result?.count || 0
  }

  async getTagCategoryStats(): Promise<Array<{ category: ImageCategory; count: number }>> {
    if (!this.db) throw new Error('数据库未连接')

    const stmt = this.db.prepare(`
      SELECT 
        COALESCE(category, '未分类') as category,
        COUNT(*) as count
      FROM tags 
      GROUP BY category
      ORDER BY count DESC
    `)
    const rows = stmt.all() as any[]

    return rows.map(row => ({
      category: row.category as ImageCategory,
      count: row.count
    }))
  }

  // ============= 维护操作 =============

  async cleanupUnusedTags(): Promise<{ success: boolean; deletedCount: number }> {
    if (!this.db) throw new Error('数据库未连接')

    try {
      // 删除频率为0的标签
      const stmt = this.db.prepare('DELETE FROM tags WHERE frequency = 0')
      const result = stmt.run()
      
      return {
        success: true,
        deletedCount: result.changes
      }
    } catch (error: any) {
      return {
        success: false,
        deletedCount: 0
      }
    }
  }

  async recalculateTagFrequencies(): Promise<{ success: boolean; updatedCount: number }> {
    if (!this.db) throw new Error('数据库未连接')

    try {
      // 这里需要根据实际的图片-标签关联表来重新计算频率
      // 目前简化实现，将所有标签频率重置为0
      const stmt = this.db.prepare('UPDATE tags SET frequency = 0, updated_at = ?')
      const result = stmt.run(Math.floor(Date.now() / 1000))
      
      return {
        success: true,
        updatedCount: result.changes
      }
    } catch (error: any) {
      return {
        success: false,
        updatedCount: 0
      }
    }
  }

  async clearAll(): Promise<{ success: boolean; error?: string }> {
    if (!this.db) throw new Error('数据库未连接')

    try {
      // 删除所有数据
      this.db.prepare('DELETE FROM tag_vectors').run()
      this.db.prepare('DELETE FROM tags').run()
      
      return { success: true }
    } catch (error: any) {
      return {
        success: false,
        error: `清空失败: ${error.message}`
      }
    }
  }

  async testConnection(): Promise<boolean> {
    try {
      if (!this.db) return false
      const stmt = this.db.prepare('SELECT 1')
      stmt.get()
      return true
    } catch {
      return false
    }
  }

  // ============= 辅助方法 =============

  /**
   * 解析数据库行为 TagRecord 对象
   */
  private parseTagRecord(row: any): TagRecord {
    // 使用 humps 自动将数据库下划线字段转换为驼峰字段
    const baseRecord = dbToTs<TagRecord>(row);
    
    // 处理特殊字段
    return {
      ...baseRecord,
      // 保留相似度分数
      similarityScore: row.similarity_score
    };
  }

  /**
   * 关闭数据库连接
   */
  close(): void {
    if (this.db) {
      this.db.close()
      this.db = null
    }
  }
}