import Database, { Database as DatabaseType } from 'better-sqlite3'
import * as sqliteVec from 'sqlite-vec'
import * as path from 'path'
import * as fs from 'fs'
import { ImageDAO } from '../interfaces/ImageDAO'
import { ImageRecord, QueryParams, QueryResult, BaseResult, ExistsResult } from '@shared/types/database'
import { dbToTs, tsToDb } from '@shared/utils/fieldMapper'

/**
 * SQLite-Vec 图片数据访问对象实现
 * 处理 images 表和 image_vectors 虚拟表的所有操作
 * 使用 sqlite-vec 扩展提供向量搜索能力
 */
export class SqliteVecImageDAO implements ImageDAO {
  private db: DatabaseType | null = null
  private dbPath: string

  constructor(dbPath?: string) {
    this.dbPath = dbPath || path.join(process.cwd(), 'data', 'sqlite-vec.db')
    this.initDatabase()
  }

  /**
   * 初始化数据库连接和表结构
   */
  private initDatabase(): void {
    this.connectDatabase()
    this.createTables()
    this.createIndexes()
  }

  /**
   * 连接数据库并加载 sqlite-vec 扩展
   */
  private connectDatabase(): void {
    if (this.db) return

    // 确保数据库目录存在
    const dbDir = path.dirname(this.dbPath)
    if (!fs.existsSync(dbDir)) {
      fs.mkdirSync(dbDir, { recursive: true })
    }

    // 创建或打开SQLite数据库
    this.db = new Database(this.dbPath)
    
    // 开启WAL模式以提高并发性能
    this.db.pragma('journal_mode = WAL')
    
    // 加载 sqlite-vec 扩展
    try {
      sqliteVec.load(this.db)
    } catch (error) {
      console.warn('Failed to load sqlite-vec with direct method, trying loadable path:', error)
      try {
        const loadablePath = sqliteVec.getLoadablePath()
        this.db.loadExtension(loadablePath)
      } catch (error2) {
        console.error('Failed to load sqlite-vec extension with both methods:', error2)
        throw new Error(`sqlite-vec extension failed to load: ${error2}`)
      }
    }
  }

  /**
   * 创建数据库表结构
   */
  private createTables(): void {
    if (!this.db) throw new Error('数据库未连接')

    // 创建图片主数据表
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS images (
        id TEXT PRIMARY KEY,
        file_path TEXT NOT NULL,
        file_name TEXT NOT NULL,
        file_checksum TEXT,
        description TEXT,
        tags TEXT, -- JSON格式的标签数组
        tags_flat TEXT, -- 扁平化标签数组 (优化查询)
        structured_metadata TEXT, -- 结构化元数据 (JSON格式)
        metadata TEXT, -- 其他元数据 (JSON格式)
        
        -- 位置信息字段
        latitude REAL,
        longitude REAL,
        altitude REAL,
        location_address TEXT,
        location_city TEXT,
        location_country TEXT,
        location_source TEXT,
        
        -- 时间信息字段
        captured_at INTEGER, -- 拍摄时间 (Unix时间戳)
        camera_info TEXT, -- 相机信息 (JSON格式)
        shooting_params TEXT, -- 拍摄参数 (JSON格式)
        
        created_at INTEGER DEFAULT (unixepoch()),
        updated_at INTEGER DEFAULT (unixepoch())
      )
    `)

    // 创建向量存储表 (虚拟表)
    this.db.exec(`
      CREATE VIRTUAL TABLE IF NOT EXISTS image_vectors USING vec0(
        image_id TEXT PRIMARY KEY,
        description_vector FLOAT[1024]
      )
    `)
  }

  /**
   * 创建索引
   */
  private createIndexes(): void {
    if (!this.db) throw new Error('数据库未连接')

    const indexes = [
      'CREATE INDEX IF NOT EXISTS idx_images_file_path ON images(file_path)',
      'CREATE INDEX IF NOT EXISTS idx_images_file_checksum ON images(file_checksum)',
      'CREATE INDEX IF NOT EXISTS idx_images_created_at ON images(created_at)',
      'CREATE INDEX IF NOT EXISTS idx_images_captured_at ON images(captured_at)',
      'CREATE INDEX IF NOT EXISTS idx_images_location_city ON images(location_city)',
      'CREATE INDEX IF NOT EXISTS idx_images_location_country ON images(location_country)',
      'CREATE INDEX IF NOT EXISTS idx_images_latitude_longitude ON images(latitude, longitude)'
    ]

    indexes.forEach(sql => {
      try {
        this.db!.exec(sql)
      } catch (error) {
        console.warn(`Failed to create index: ${sql}`, error)
      }
    })
  }

  // ============= 基础 CRUD 操作 =============

  async create(image: ImageRecord): Promise<string> {
    if (!this.db) throw new Error('数据库未连接')

    const id = image.id || `img_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    const now = Math.floor(Date.now() / 1000)

    const transaction = this.db.transaction((imageData: ImageRecord, imageId: string) => {
      
      // 插入图片主数据
      const imageStmt = this.db!.prepare(`
        INSERT INTO images (
          id, file_path, file_name, file_checksum, description, tags, tags_flat,
          structured_metadata, metadata, latitude, longitude, altitude,
          location_address, location_city, location_country, location_source,
          captured_at, camera_info, shooting_params, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `)

      imageStmt.run(
        imageId,
        imageData.filePath,
        imageData.fileName,
        imageData.fileChecksum || null,
        imageData.description || null,
        imageData.tags ? JSON.stringify(imageData.tags) : null,
        imageData.tagsFlat ? JSON.stringify(imageData.tagsFlat) : null,
        imageData.structuredMetadata ? JSON.stringify(imageData.structuredMetadata) : null,
        imageData.metadata ? JSON.stringify(imageData.metadata) : null,
        imageData.latitude || null,
        imageData.longitude || null,
        imageData.altitude || null,
        imageData.locationAddress || null,
        imageData.locationCity || null,
        imageData.locationCountry || null,
        imageData.locationSource || null,
        imageData.capturedAt || null,
        imageData.cameraInfo || null,
        imageData.shootingParams || null,
        now,
        now
      )

      // 插入向量数据（如果有的话）
      if (imageData.descriptionVector && imageData.descriptionVector.length > 0) {
        const vectorStmt = this.db!.prepare(`
          INSERT INTO image_vectors (image_id, description_vector) VALUES (?, ?)
        `)
        vectorStmt.run(imageId, JSON.stringify(imageData.descriptionVector))
      }

      return imageId
    })

    try {
      return transaction(image, id)
    } catch (error: any) {
      throw new Error(`创建图片记录失败: ${error.message}`)
    }
  }

  async findById(id: string): Promise<ImageRecord | null> {
    if (!this.db) throw new Error('数据库未连接')

    const stmt = this.db.prepare(`
      SELECT i.*
      FROM images i
      WHERE i.id = ?
    `)
    const row = stmt.get(id) as any

    if (!row) return null

    // Get vector data separately
    const vectorStmt = this.db!.prepare('SELECT description_vector FROM image_vectors WHERE image_id = ?')
    const vectorRow = vectorStmt.get(id) as any
    
    if (vectorRow?.description_vector) {
      row.descriptionVector = vectorRow.description_vector
    }

    return this.parseImageRecord(row)
  }

  async findByPath(filePath: string): Promise<ImageRecord | null> {
    if (!this.db) throw new Error('数据库未连接')

    const stmt = this.db.prepare(`
      SELECT i.*
      FROM images i
      WHERE i.file_path = ?
    `)
    const row = stmt.get(filePath) as any

    if (!row) return null

    // Get vector data separately
    const vectorStmt = this.db.prepare('SELECT description_vector FROM image_vectors WHERE image_id = ?')
    const vectorRow = vectorStmt.get(row.id) as any
    
    if (vectorRow?.description_vector) {
      row.descriptionVector = vectorRow.description_vector
    }

    return this.parseImageRecord(row)
  }

  async update(id: string, updates: Partial<ImageRecord>): Promise<boolean> {
    if (!this.db) throw new Error('数据库未连接')

    const setClause: string[] = []
    const values: any[] = []

    // 先将更新对象转换为数据库字段格式
    const dbUpdates = tsToDb(updates)

    // 构建动态更新语句
    Object.entries(dbUpdates).forEach(([key, value]) => {
      if (key === 'id' || key === 'created_at') return // 不允许更新这些字段
      
      if (key === 'description_vector') return // 向量数据单独处理
      
      if (['tags', 'tags_flat', 'structured_metadata', 'metadata', 'camera_info', 'shooting_params'].includes(key)) {
        setClause.push(`${key} = ?`)
        values.push(value ? JSON.stringify(value) : null)
      } else {
        setClause.push(`${key} = ?`)
        values.push(value)
      }
    })

    if (setClause.length === 0 && !updates.descriptionVector) {
      return true
    }

    const transaction = this.db.transaction((updateData: any, imageId: string) => {
      // 更新主表数据
      if (setClause.length > 0) {
        setClause.push('updated_at = ?')
        values.push(Math.floor(Date.now() / 1000))
        values.push(imageId)

        const sql = `UPDATE images SET ${setClause.join(', ')} WHERE id = ?`
        const stmt = this.db!.prepare(sql)
        stmt.run(...values)
      }

      // 更新向量数据
      if (updateData.descriptionVector) {
        const vectorStmt = this.db!.prepare(`
          INSERT OR REPLACE INTO image_vectors (image_id, description_vector) VALUES (?, ?)
        `)
        vectorStmt.run(imageId, JSON.stringify(updateData.descriptionVector))
      }

      return true
    })

    try {
      transaction(updates, id)
      return true
    } catch (error: any) {
      console.error(`更新图片记录失败: ${error.message}`)
      return false
    }
  }

  async delete(id: string): Promise<boolean> {
    if (!this.db) throw new Error('数据库未连接')

    const transaction = this.db.transaction((imageId: string) => {
      // 删除向量数据
      const vectorStmt = this.db!.prepare('DELETE FROM image_vectors WHERE image_id = ?')
      vectorStmt.run(imageId)

      // 删除主数据
      const imageStmt = this.db!.prepare('DELETE FROM images WHERE id = ?')
      const result = imageStmt.run(imageId)
      
      return result.changes > 0
    })

    try {
      return transaction(id)
    } catch (error: any) {
      console.error(`删除图片记录失败: ${error.message}`)
      return false
    }
  }

  async insertBatch(images: ImageRecord[]): Promise<{ success: boolean; insertedCount: number; error?: string }> {
    if (!this.db) throw new Error('数据库未连接')

    const transaction = this.db.transaction((imageList: ImageRecord[]) => {
      let insertedCount = 0
      const now = Math.floor(Date.now() / 1000)

      const imageStmt = this.db!.prepare(`
        INSERT INTO images (
          id, file_path, file_name, file_checksum, description, tags, tags_flat,
          structured_metadata, metadata, latitude, longitude, altitude,
          location_address, location_city, location_country, location_source,
          captured_at, camera_info, shooting_params, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `)

      const vectorStmt = this.db!.prepare(`
        INSERT INTO image_vectors (image_id, description_vector) VALUES (?, ?)
      `)

      for (const image of imageList) {
        try {
          const id = image.id || `img_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
          
          // 插入图片主数据
          imageStmt.run(
            id,
            image.filePath,
            image.fileName,
            image.fileChecksum || null,
            image.description || null,
            image.tags ? JSON.stringify(image.tags) : null,
            image.tagsFlat ? JSON.stringify(image.tagsFlat) : null,
            image.structuredMetadata ? JSON.stringify(image.structuredMetadata) : null,
            image.metadata ? JSON.stringify(image.metadata) : null,
            image.latitude || null,
            image.longitude || null,
            image.altitude || null,
            image.locationAddress || null,
            image.locationCity || null,
            image.locationCountry || null,
            image.locationSource || null,
            image.capturedAt || null,
            image.cameraInfo || null,
            image.shootingParams || null,
            now,
            now
          )

          // 插入向量数据
          if (image.descriptionVector && image.descriptionVector.length > 0) {
            vectorStmt.run(id, JSON.stringify(image.descriptionVector))
          }

          insertedCount++
        } catch (error) {
          console.warn(`跳过插入图片 ${image.filePath}: ${error}`)
        }
      }

      return insertedCount
    })

    try {
      const insertedCount = transaction(images)
      return {
        success: true,
        insertedCount
      }
    } catch (error: any) {
      return {
        success: false,
        insertedCount: 0,
        error: `批量插入失败: ${error.message}`
      }
    }
  }

  // ============= 查询操作 =============

  async queryImages(params: QueryParams): Promise<QueryResult> {
    if (!this.db) throw new Error('数据库未连接')

    let sql = `
      SELECT i.*
      FROM images i
      WHERE 1=1
    `
    const values: any[] = []

    // 构建查询条件
    if (params.tags && params.tags.length > 0) {
      const tagConditions = params.tags.map(() => 'i.tags LIKE ?').join(' OR ')
      sql += ` AND (${tagConditions})`
      params.tags.forEach(tag => values.push(`%"${tag}"%`))
    }

    if (params.locationCity) {
      sql += ' AND i.location_city = ?'
      values.push(params.locationCity)
    }

    if (params.locationCountry) {
      sql += ' AND i.location_country = ?'
      values.push(params.locationCountry)
    }

    if (params.startTime) {
      sql += ' AND i.captured_at >= ?'
      values.push(params.startTime)
    }

    if (params.endTime) {
      sql += ' AND i.captured_at <= ?'
      values.push(params.endTime)
    }

    // 排序
    sql += ' ORDER BY i.created_at DESC'

    // 分页
    if (params.limit) {
      sql += ' LIMIT ?'
      values.push(params.limit)
      
      if (params.offset) {
        sql += ' OFFSET ?'
        values.push(params.offset)
      }
    }

    try {
      const stmt = this.db.prepare(sql)
      const rows = stmt.all(...values) as any[]
      
      const images = rows.map(row => {
        // Get vector data separately for each image
        const vectorStmt = this.db!.prepare('SELECT description_vector FROM image_vectors WHERE image_id = ?')
        const vectorRow = vectorStmt.get(row.id) as any
        
        if (vectorRow?.description_vector) {
          row.descriptionVector = vectorRow.description_vector
        }
        
        return this.parseImageRecord(row)
      })
      
      // 获取总数
      const countSql = sql.replace(/SELECT i\.\* FROM/, 'SELECT COUNT(*) as count FROM')
                          .replace(/ORDER BY.*$/, '')
      const countValues = values.slice(0, -2) // 移除 limit 和 offset 参数
      const countStmt = this.db.prepare(countSql)
      const countResult = countStmt.get(...countValues) as any
      
      return {
        results: images,
        total: countResult?.count || 0
      }
    } catch (error: any) {
      throw new Error(`查询图片失败: ${error.message}`)
    }
  }

  async checkExists(filePath: string, md5?: string): Promise<ExistsResult> {
    if (!this.db) throw new Error('数据库未连接')

    let sql = 'SELECT id FROM images WHERE file_path = ?'
    const values = [filePath]

    if (md5) {
      sql += ' OR file_checksum = ?'
      values.push(md5)
    }

    try {
      const stmt = this.db.prepare(sql)
      const result = stmt.get(...values) as any

      return {
        success: true,
        exists: !!result,
        imageId: result?.id
      }
    } catch (error: any) {
      return {
        success: false,
        exists: false,
        error: `检查图片存在性失败: ${error.message}`
      }
    }
  }

  // ============= 向量搜索操作 =============

  async vectorSearch(embedding: number[], threshold?: number, limit?: number): Promise<QueryResult> {
    if (!this.db) throw new Error('数据库未连接')

    const searchThreshold = threshold || 0.7
    const searchLimit = limit || 20

    try {
      const stmt = this.db.prepare(`
        SELECT i.*, vec_distance_cosine(iv.description_vector, ?) as distance
        FROM image_vectors iv
        JOIN images i ON iv.image_id = i.id
        WHERE vec_distance_cosine(iv.description_vector, ?) < ?
        ORDER BY distance ASC
        LIMIT ?
      `)

      const vectorStr = JSON.stringify(embedding)
      const rows = stmt.all(vectorStr, vectorStr, 1 - searchThreshold, searchLimit) as any[]
      
      const images = rows.map(row => {
        // Get vector data separately
        const vectorStmt = this.db!.prepare('SELECT description_vector FROM image_vectors WHERE image_id = ?')
        const vectorRow = vectorStmt.get(row.id) as any
        
        if (vectorRow?.description_vector) {
          row.descriptionVector = vectorRow.description_vector
        }
        
        const image = this.parseImageRecord(row)
        image.similarityScore = 1 - row.distance // 转换为相似度分数
        return image
      })

      return {
        results: images,
        total: images.length
      }
    } catch (error: any) {
      throw new Error(`向量搜索失败: ${error.message}`)
    }
  }

  async hybridSearch(params: {
    query: string
    keywords: string[]
    embedding: number[]
    limit?: number
    similarityThreshold?: number
    expandKeywords?: boolean
  }): Promise<QueryResult> {
    // 先进行向量搜索
    const vectorResults = await this.vectorSearch(
      params.embedding, 
      params.similarityThreshold, 
      params.limit
    )

    // 如果没有关键词，直接返回向量搜索结果
    if (!params.keywords || params.keywords.length === 0) {
      return vectorResults
    }

    // 结合关键词过滤
    const filteredImages = vectorResults.results.filter(image => {
      if (!image.tags) return false
      
      const imageTags = Array.isArray(image.tags) ? image.tags : JSON.parse(image.tags as string)
      return params.keywords.some(keyword => 
        imageTags.some((tag: string) => 
          tag.toLowerCase().includes(keyword.toLowerCase())
        )
      )
    })

    return {
      results: filteredImages,
      total: filteredImages.length
    }
  }

  async findSimilarImages(imageId: string, options?: { threshold?: number; limit?: number }): Promise<QueryResult> {
    if (!this.db) throw new Error('数据库未连接')

    // 先获取目标图片的向量
    const targetStmt = this.db.prepare('SELECT description_vector FROM image_vectors WHERE image_id = ?')
    const targetRow = targetStmt.get(imageId) as any

    if (!targetRow || !targetRow.descriptionVector) {
      return { results: [], total: 0 }
    }

    const targetVector = JSON.parse(targetRow.descriptionVector)
    
    // 执行向量搜索，排除自己
    const searchResults = await this.vectorSearch(
      targetVector,
      options?.threshold,
      options?.limit
    )

    // 过滤掉目标图片本身
    const similarImages = searchResults.results.filter(img => img.id !== imageId)

    return {
      results: similarImages,
      total: similarImages.length
    }
  }

  // ============= 位置相关查询 =============

  async queryByLocation(latitude: number, longitude: number, radius: number = 1000, limit: number = 20): Promise<ImageRecord[]> {
    if (!this.db) throw new Error('数据库未连接')

    // 简单的距离计算（实际应用中可能需要更复杂的地理计算）
    const stmt = this.db.prepare(`
      SELECT i.*
      FROM images i
      WHERE i.latitude IS NOT NULL AND i.longitude IS NOT NULL
        AND ABS(i.latitude - ?) < ? AND ABS(i.longitude - ?) < ?
      ORDER BY (ABS(i.latitude - ?) + ABS(i.longitude - ?)) ASC
      LIMIT ?
    `)

    const radiusDegree = radius / 111000 // 粗略转换为度数
    const rows = stmt.all(latitude, radiusDegree, longitude, radiusDegree, latitude, longitude, limit) as any[]

    return rows.map(row => {
      // Get vector data separately
      const vectorStmt = this.db!.prepare('SELECT description_vector FROM image_vectors WHERE image_id = ?')
      const vectorRow = vectorStmt.get(row.id) as any
      
      if (vectorRow?.description_vector) {
        row.descriptionVector = vectorRow.description_vector
      }
      
      return this.parseImageRecord(row)
    })
  }

  async queryByLocationName(location: string, limit: number = 20): Promise<ImageRecord[]> {
    if (!this.db) throw new Error('数据库未连接')

    const stmt = this.db.prepare(`
      SELECT i.*
      FROM images i
      WHERE i.location_city LIKE ? OR i.location_country LIKE ? OR i.location_address LIKE ?
      ORDER BY i.created_at DESC
      LIMIT ?
    `)

    const searchTerm = `%${location}%`
    const rows = stmt.all(searchTerm, searchTerm, searchTerm, limit) as any[]

    return rows.map(row => {
      // Get vector data separately
      const vectorStmt = this.db!.prepare('SELECT description_vector FROM image_vectors WHERE image_id = ?')
      const vectorRow = vectorStmt.get(row.id) as any
      
      if (vectorRow?.description_vector) {
        row.descriptionVector = vectorRow.description_vector
      }
      
      return this.parseImageRecord(row)
    })
  }

  // ============= 时间相关查询 =============

  async queryByTimeRange(startTime: number, endTime: number, limit: number = 20): Promise<ImageRecord[]> {
    if (!this.db) throw new Error('数据库未连接')

    const stmt = this.db.prepare(`
      SELECT i.*
      FROM images i
      WHERE i.captured_at BETWEEN ? AND ?
      ORDER BY i.captured_at DESC
      LIMIT ?
    `)

    const rows = stmt.all(startTime, endTime, limit) as any[]
    return rows.map(row => {
      // Get vector data separately
      const vectorStmt = this.db!.prepare('SELECT description_vector FROM image_vectors WHERE image_id = ?')
      const vectorRow = vectorStmt.get(row.id) as any
      
      if (vectorRow?.description_vector) {
        row.descriptionVector = vectorRow.description_vector
      }
      
      return this.parseImageRecord(row)
    })
  }

  // ============= 统计和维护操作 =============

  async getTotalCount(): Promise<number> {
    if (!this.db) throw new Error('数据库未连接')

    const stmt = this.db.prepare('SELECT COUNT(*) as count FROM images')
    const result = stmt.get() as any

    return result?.count || 0
  }

  async clearAll(): Promise<BaseResult> {
    if (!this.db) throw new Error('数据库未连接')

    try {
      // 删除所有数据
      this.db.prepare('DELETE FROM image_vectors').run()
      this.db.prepare('DELETE FROM images').run()
      
      return { success: true }
    } catch (error: any) {
      return {
        success: false,
        error: `清空失败: ${error.message}`
      }
    }
  }

  async testConnection(): Promise<boolean> {
    try {
      if (!this.db) return false
      const stmt = this.db.prepare('SELECT 1')
      stmt.get()
      return true
    } catch {
      return false
    }
  }

  // ============= 辅助方法 =============

  /**
   * 解析数据库行为 ImageRecord 对象
   */
  private parseImageRecord(row: any): ImageRecord {
    // 使用 humps 自动将数据库下划线字段转换为驼峰字段
    const baseRecord = dbToTs<ImageRecord>(row);
    
    // 处理特殊字段
    return {
      ...baseRecord,
      // 向量数据直接使用，不需要JSON解析（sqlite-vec已处理格式）
      descriptionVector: row.descriptionVector ? Array.from(row.descriptionVector) : undefined,
      // 保留相似度分数
      similarityScore: row.similarity_score
    };
  }

  /**
   * 关闭数据库连接
   */
  close(): void {
    if (this.db) {
      this.db.close()
      this.db = null
    }
  }
}