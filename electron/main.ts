import { app, BrowserWindow, protocol } from 'electron'
import { createRequire } from 'node:module'
import { fileURLToPath } from 'node:url'
import path from 'node:path'

import { WindowManager } from './services/window-manager'
import { createIPCHandler } from 'electron-trpc/main'
import { createAppRouter } from './trpc/router'

// ES modules 兼容性
const require = createRequire(import.meta.url)
const __dirname = path.dirname(fileURLToPath(import.meta.url))

// 设置应用根目录
process.env.APP_ROOT = path.join(__dirname, '..')

/**
 * 应用程序主类
 */
class Application {
  private readonly windowManager: WindowManager

  constructor() {
    this.windowManager = new WindowManager()
  }

  /**
   * 初始化应用程序
   */
  async initialize() {
    console.log('🚀 开始初始化应用程序...')

    try {
      // 创建主应用窗口（立即创建）
      if (this.windowManager.getMainWindow() === null) {
        this.windowManager.createMainWindow()
      }

      // 2. 设置 tRPC IPC 处理器
      const mainWindow = this.windowManager.getMainWindow()
      if (mainWindow) {
        console.log('🔧 设置 tRPC IPC 处理器...')
        try {
          const appRouter = await createAppRouter()
          createIPCHandler({ router: appRouter, windows: [mainWindow] })
          console.log('✅ tRPC IPC 处理器设置完成')
        } catch (error) {
          console.error('❌ tRPC IPC 处理器设置失败:', error)
        }
      } else {
        console.error('❌ 主窗口未创建，无法设置 tRPC IPC 处理器')
      }
      
      console.log('✅ 应用程序初始化完成')
    } catch (error) {
      console.error('❌ 应用程序初始化失败:', error)
    }
  }

  /**
   * 应用关闭时的清理工作
   */
  async cleanup() {
    console.log('🔄 开始应用清理工作...')

    try {
      this.windowManager.closeAll()
      console.log('✅ 应用清理完成')
    } catch (error) {
      console.error('❌ 应用清理失败:', error)
    }
  }
}

// 应用程序实例
let application: Application | null = null

// 注册自定义协议
protocol.registerSchemesAsPrivileged([
  { scheme: 'app', privileges: { secure: true, standard: true, supportFetchAPI: true } }
]);

// 应用就绪事件
app.whenReady().then(async () => {
  // 注册app协议处理程序（用于本地文件和应用资源）
  protocol.registerFileProtocol('app', (request, callback) => {
    try {
      // 移除 'app://' 前缀
      let filePath = request.url.substring(6);

      // URL解码
      filePath = decodeURIComponent(filePath);

      // 处理Windows驱动器路径：将 'C/Users/<USER>' 转换为 'C:/Users/<USER>'
      if (/^[a-zA-Z]\//.test(filePath)) {
        filePath = filePath[0] + ':' + filePath.substring(1);
      }

      // 标准化路径分隔符
      filePath = path.normalize(filePath);

      // console.log('🔗 [Protocol] app:// 请求:', request.url);
      // console.log('🔗 [Protocol] 解析路径:', filePath);

      // 基本安全检查：确保路径不包含危险的遍历
      if (filePath.includes('..')) {
        console.error('❌ [Protocol] 危险路径被拒绝:', filePath);
        return callback({ error: -10 }); // 访问被拒绝
      }

      return callback({ path: filePath });
    } catch (error) {
      console.error('❌ [Protocol] app:// 协议处理错误:', error);
      return callback({ error: -2 }); // 文件未找到错误
    }
  });

  application = new Application()
  await application.initialize()
})

// 所有窗口关闭事件（macOS除外）
app.on('window-all-closed', async () => {
  if (application) {
    await application.cleanup()
  }

  if (process.platform !== 'darwin') {
    app.quit()
  }
})

// 应用激活事件（主要用于macOS）
app.on('activate', () => {
  // 在 macOS 上，当点击 dock 图标且没有其他窗口打开时，重新创建窗口
  if (BrowserWindow.getAllWindows().length === 0 && application) {
    application.initialize()
  }
})

// 防止 TypeScript 警告
void require

// 导出服务实例供其他模块使用（如果需要）
export { application }