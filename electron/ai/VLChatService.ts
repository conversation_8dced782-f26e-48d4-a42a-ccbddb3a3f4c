import OpenAI from 'openai';
import { OpenAIConfig } from './OpenAIService';

export interface ChatMessage {
  role: 'user' | 'assistant' | 'tool';
  content: string | Array<{
    type: 'text' | 'image_url';
    text?: string;
    image_url?: {
      url: string;
    };
  }>;
  tool_calls?: OpenAI.Chat.Completions.ChatCompletionMessageToolCall[];
  tool_call_id?: string;
}

export interface VLChatResponse {
  content: string;
  error?: string;
  tool_calls?: OpenAI.Chat.Completions.ChatCompletionMessageToolCall[];
}

export interface ToolDefinition {
  type: 'function';
  function: {
    name: string;
    description: string;
    parameters: any;
  };
}

export class VLChatService {
  private client: OpenAI;
  private config: OpenAIConfig;
  private conversationHistory: ChatMessage[] = [];
  private registeredTools: Map<string, ToolDefinition> = new Map();
  private connectionTested: boolean = false;
  private connectionStatus: boolean = false;
  private connectionError: string | null = null;
  private connectionPromise: Promise<boolean> | null = null;

  constructor(config: OpenAIConfig) {
    this.config = config;
    
    // 打印配置信息用于调试
    console.log('VLChatService 配置信息:');
    console.log('Base URL:', config.vlBaseURL);
    console.log('API Key:', config.vlApiKey ? `${config.vlApiKey.substring(0, 10)}...` : 'undefined');
    console.log('VL Model:', config.vlModel);
    
    this.client = new OpenAI({
      baseURL: config.vlBaseURL,
      apiKey: config.vlApiKey,
      timeout: config.timeout || 60000,
      maxRetries: config.maxRetries || 3,
      dangerouslyAllowBrowser: true,
    });

    // 初始化对话，设置助手角色和系统提示
    this.initializeSystemPrompt();
  }

  /**
   * 初始化系统提示
   */
  private initializeSystemPrompt(): void {
    const systemPrompt = this.generateSystemPrompt();
    this.conversationHistory.push({
      role: 'assistant',
      content: systemPrompt
    });
  }

  /**
   * 生成系统提示
   */
  public generateSystemPrompt(): string {
    return `我是您的智能图像助理，专门帮助您分析图片内容和搜索相关图片。我拥有以下能力：

🔍 **图片分析工具**
- analyze_image: 深度分析图片内容，提供详细描述、对象识别、颜色分析、场景理解等
- get_image_analysis: 获取已存储图片的分析结果

🔎 **图片搜索工具**  
- find_similar_images_by_image: 以图搜图，找到视觉特征相似的图片
- find_similar_images_by_description: 通过文字描述搜索匹配的图片
- find_images_by_tags: 通过标签搜索图片，支持多标签组合

**使用指南：**

当您上传图片时，我会：
1. 自动分析图片内容（对象、颜色、场景、情感等）
2. 根据您的需求提供相应服务：
   - 想了解图片内容 → 使用 analyze_image
   - 想找相似图片 → 使用 find_similar_images_by_image
   - 想通过描述找图 → 使用 find_similar_images_by_description
   - 想通过标签找图 → 使用 find_images_by_tags

**参数智能识别：**
- 数量词汇：几张(5-8)、一些(10-15)、很多(30-50)、大量(50+)
- 相似度：非常相似(0.8+)、比较相似(0.6-0.8)、有点相似(0.3-0.6)
- 标签匹配：全部匹配(AND)、任一匹配(OR)

**错误处理：**
- 图片格式不支持时，我会提供格式转换建议
- 搜索无结果时，我会建议调整搜索条件
- 工具调用失败时，我会尝试替代方案

请随时上传图片或告诉我您想要什么样的图片，我会为您提供最合适的帮助！`;
  }

  /**
   * 确保AI服务连接已建立（延迟连接）
   */
  private async ensureConnection(): Promise<void> {
    // 如果已经测试过连接且成功，直接返回
    if (this.connectionTested && this.connectionStatus) {
      return;
    }

    // 如果已经测试过连接但失败，抛出缓存的错误
    if (this.connectionTested && !this.connectionStatus) {
      throw new Error(this.connectionError || 'VL Chat服务连接失败');
    }

    // 如果正在测试连接，等待结果
    if (this.connectionPromise) {
      const success = await this.connectionPromise;
      if (!success) {
        throw new Error(this.connectionError || 'VL Chat服务连接失败');
      }
      return;
    }

    // 开始测试连接
    this.connectionPromise = this.performConnectionTest();
    const success = await this.connectionPromise;
    
    if (!success) {
      throw new Error(this.connectionError || 'VL Chat服务连接失败');
    }
  }

  /**
   * 执行实际的连接测试
   */
  private async performConnectionTest(): Promise<boolean> {
    try {
      console.log('🔗 首次使用VL Chat服务，正在测试连接...');
      
      // 使用一个简单的文本生成请求来测试连接
      const response = await this.client.chat.completions.create({
        model: this.config.vlModel,
        messages: [
          {
            role: "user",
            content: "Hello, please respond with 'OK'"
          }
        ],
        max_tokens: 10,
        temperature: 0,
      });

      const success = !!response.choices[0]?.message?.content;
      
      this.connectionTested = true;
      this.connectionStatus = success;
      this.connectionError = success ? null : 'VL Chat服务响应无效';
      this.connectionPromise = null;

      if (success) {
        console.log('✅ VL Chat服务连接测试成功');
      } else {
        console.error('❌ VL Chat服务连接测试失败: 响应无效');
      }

      return success;
      
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      console.error('❌ VL Chat服务连接测试失败:', errorMessage);
      
      this.connectionTested = true;
      this.connectionStatus = false;
      this.connectionError = errorMessage;
      this.connectionPromise = null;

      return false;
    }
  }

  /**
   * 重置连接状态（用于重试）
   */
  resetConnection(): void {
    this.connectionTested = false;
    this.connectionStatus = false;
    this.connectionError = null;
    this.connectionPromise = null;
    console.log('🔄 VL Chat服务连接状态已重置');
  }

  /**
   * 获取连接状态信息
   */
  getConnectionStatus(): { tested: boolean; connected: boolean; error: string | null } {
    return {
      tested: this.connectionTested,
      connected: this.connectionStatus,
      error: this.connectionError
    };
  }

  /**
   * 更新系统提示（当工具注册发生变化时）
   */
  updateSystemPrompt(): void {
    // 找到并替换第一条助手消息（系统提示）
    const systemPromptIndex = this.conversationHistory.findIndex(
      msg => msg.role === 'assistant'
    );
    
    if (systemPromptIndex !== -1) {
      this.conversationHistory[systemPromptIndex].content = this.generateSystemPrompt();
      console.log('系统提示已更新');
    }
  }

  /**
   * 发送文本消息
   */
  async sendTextMessage(message: string): Promise<VLChatResponse> {
    try {
      // 确保连接已建立
      await this.ensureConnection();
      
      // 添加用户消息到历史
      this.conversationHistory.push({
        role: 'user',
        content: message
      });

      const response = await this.client.chat.completions.create({
        model: this.config.vlModel,
        messages: this.conversationHistory as any,
        stream: false,
        temperature: 0.7,
        max_tokens: 1000,
      });

      const assistantMessage = response.choices[0]?.message?.content;
      if (!assistantMessage) {
        throw new Error('AI模型返回空结果');
      }

      // 添加助手回复到历史
      this.conversationHistory.push({
        role: 'assistant',
        content: assistantMessage
      });

      return { content: assistantMessage };
      
    } catch (error) {
      console.error('文本消息发送失败:', error);
      return { 
        content: '抱歉，我遇到了一些问题，请稍后再试。',
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  /**
   * 发送文本消息（支持工具调用）
   */
  async sendTextMessageWithTools(message: string, tools?: ToolDefinition[]): Promise<VLChatResponse> {
    try {
      // 确保连接已建立
      await this.ensureConnection();
      
      console.log('VLChatService: 开始发送文本消息（支持工具）')
      console.log('VLChatService: 消息内容:', message)
      console.log('VLChatService: 接收到的工具数量:', tools ? tools.length : 0)
      
      if (tools && tools.length > 0) {
        console.log('VLChatService: 工具列表:', tools.map(tool => tool.function.name))
      } else {
        console.warn('VLChatService: 警告 - 没有工具传入或工具数组为空')
      }

      // 添加用户消息到历史
      this.conversationHistory.push({
        role: 'user',
        content: message
      });

      const requestOptions: OpenAI.Chat.Completions.ChatCompletionCreateParams = {
        model: this.config.vlModel,
        messages: this.conversationHistory as any,
        stream: false,
        temperature: 0.7,
        max_tokens: 1000,
      };

      // 添加工具定义
      if (tools && tools.length > 0) {
        requestOptions.tools = tools;
        requestOptions.tool_choice = 'auto';
        console.log('VLChatService: 已将工具添加到请求中')
      } else {
        console.log('VLChatService: 没有工具添加到请求中')
      }

      console.log('VLChatService: 发送请求到OpenAI API...')
      console.log('VLChatService: 请求参数:', JSON.stringify({
        model: requestOptions.model,
        tools: requestOptions.tools ? requestOptions.tools.length : 0,
        tool_choice: requestOptions.tool_choice
      }, null, 2))

      const response = await this.client.chat.completions.create(requestOptions);

      const choice = response.choices[0];
      if (!choice) {
        throw new Error('AI模型返回空结果');
      }

      const assistantMessage = choice.message.content || '';
      const toolCalls = choice.message.tool_calls;

      console.log('VLChatService: 收到响应')
      console.log('VLChatService: 助手消息:', assistantMessage)
      console.log('VLChatService: 工具调用数量:', toolCalls ? toolCalls.length : 0)
      
      if (toolCalls && toolCalls.length > 0) {
        console.log('VLChatService: 工具调用详情:', toolCalls.map(tc => ({
          id: tc.id,
          function: tc.function.name,
          arguments: tc.function.arguments
        })))
      }

      // 添加助手回复到历史
      this.conversationHistory.push({
        role: 'assistant',
        content: assistantMessage,
        tool_calls: toolCalls
      });

      return { 
        content: assistantMessage,
        tool_calls: toolCalls
      };
      
    } catch (error) {
      console.error('VLChatService: 文本消息发送失败:', error);
      return { 
        content: '抱歉，我遇到了一些问题，请稍后再试。',
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  /**
   * 发送图片消息（图片+文本）
   */
  async sendImageMessage(imageBase64: string, message: string = '请描述这张图片'): Promise<VLChatResponse> {
    try {
      // 确保连接已建立
      await this.ensureConnection();
      
      // 构建多模态消息内容
      const userContent = [
        {
          type: 'text' as const,
          text: message
        },
        {
          type: 'image_url' as const,
          image_url: {
            url: imageBase64.startsWith('data:') ? imageBase64 : `data:image/jpeg;base64,${imageBase64}`
          }
        }
      ];

      // 添加用户消息到历史
      this.conversationHistory.push({
        role: 'user',
        content: userContent
      });

      const response = await this.client.chat.completions.create({
        model: this.config.vlModel,
        messages: this.conversationHistory as any,
        stream: false,
        temperature: 0.7,
        max_tokens: 1000,
      });

      const assistantMessage = response.choices[0]?.message?.content;
      if (!assistantMessage) {
        throw new Error('AI模型返回空结果');
      }

      // 添加助手回复到历史
      this.conversationHistory.push({
        role: 'assistant',
        content: assistantMessage
      });

      return { content: assistantMessage };
      
    } catch (error) {
      console.error('图片消息发送失败:', error);
      return { 
        content: '抱歉，我无法处理这张图片，请检查图片格式或稍后再试。',
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  /**
   * 发送图片消息（支持工具调用）
   */
  async sendImageMessageWithTools(imageBase64: string, message: string = '请描述这张图片', tools?: ToolDefinition[]): Promise<VLChatResponse> {
    try {
      // 确保连接已建立
      await this.ensureConnection();
      
      console.log('VLChatService: 开始发送图片消息（支持工具）')
      console.log('VLChatService: 消息内容:', message)
      console.log('VLChatService: 接收到的工具数量:', tools ? tools.length : 0)
      
      if (tools && tools.length > 0) {
        console.log('VLChatService: 工具列表:', tools.map(tool => tool.function.name))
      } else {
        console.warn('VLChatService: 警告 - 没有工具传入或工具数组为空')
      }

      // 构建多模态消息内容
      const userContent = [
        {
          type: 'text' as const,
          text: message
        },
        {
          type: 'image_url' as const,
          image_url: {
            url: imageBase64.startsWith('data:') ? imageBase64 : `data:image/jpeg;base64,${imageBase64}`
          }
        }
      ];

      // 添加用户消息到历史
      this.conversationHistory.push({
        role: 'user',
        content: userContent
      });

      const requestOptions: OpenAI.Chat.Completions.ChatCompletionCreateParams = {
        model: this.config.vlModel,
        messages: this.conversationHistory as any,
        stream: false,
        temperature: 0.7,
        max_tokens: 1000,
      };

      // 添加工具定义
      if (tools && tools.length > 0) {
        requestOptions.tools = tools;
        requestOptions.tool_choice = 'auto';
        console.log('VLChatService: 已将工具添加到请求中')
      } else {
        console.log('VLChatService: 没有工具添加到请求中')
      }

      console.log('VLChatService: 发送图片请求到OpenAI API...')
      console.log('VLChatService: 请求参数:', JSON.stringify({
        model: requestOptions.model,
        tools: requestOptions.tools ? requestOptions.tools.length : 0,
        tool_choice: requestOptions.tool_choice
      }, null, 2))

      const response = await this.client.chat.completions.create(requestOptions);

      const choice = response.choices[0];
      if (!choice) {
        throw new Error('AI模型返回空结果');
      }

      const assistantMessage = choice.message.content || '';
      const toolCalls = choice.message.tool_calls;

      console.log('VLChatService: 收到图片响应')
      console.log('VLChatService: 助手消息:', assistantMessage)
      console.log('VLChatService: 工具调用数量:', toolCalls ? toolCalls.length : 0)
      
      if (toolCalls && toolCalls.length > 0) {
        console.log('VLChatService: 工具调用详情:', toolCalls.map(tc => ({
          id: tc.id,
          function: tc.function.name,
          arguments: tc.function.arguments
        })))
      }

      // 添加助手回复到历史
      this.conversationHistory.push({
        role: 'assistant',
        content: assistantMessage,
        tool_calls: toolCalls
      });

      return { 
        content: assistantMessage,
        tool_calls: toolCalls
      };
      
    } catch (error) {
      console.error('VLChatService: 图片消息发送失败:', error);
      return { 
        content: '抱歉，我无法处理这张图片，请检查图片格式或稍后再试。',
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  /**
   * 添加工具调用结果到对话历史
   */
  addToolResult(toolCallId: string, result: string): void {
    this.conversationHistory.push({
      role: 'tool',
      content: result,
      tool_call_id: toolCallId
    });
  }

  /**
   * 继续对话（在工具调用完成后）
   */
  async continueConversation(): Promise<VLChatResponse> {
    try {
      // 确保连接已建立
      await this.ensureConnection();
      
      const response = await this.client.chat.completions.create({
        model: this.config.vlModel,
        messages: this.conversationHistory as any,
        stream: false,
        temperature: 0.7,
        max_tokens: 1000,
      });

      const assistantMessage = response.choices[0]?.message?.content;
      if (!assistantMessage) {
        throw new Error('AI模型返回空结果');
      }

      // 添加助手回复到历史
      this.conversationHistory.push({
        role: 'assistant',
        content: assistantMessage
      });

      return { content: assistantMessage };
      
    } catch (error) {
      console.error('继续对话失败:', error);
      return { 
        content: '抱歉，我遇到了一些问题，请稍后再试。',
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  /**
   * 清除对话历史
   */
  clearHistory(): void {
    this.conversationHistory = [{
      role: 'assistant',
      content: '我是您的智能图像助理，可以分析图片内容、回答相关问题。请随时上传图片或提出问题！'
    }];
  }

  /**
   * 获取对话历史
   */
  getHistory(): ChatMessage[] {
    return [...this.conversationHistory];
  }

  /**
   * 设置对话历史（用于恢复对话）
   */
  setHistory(history: ChatMessage[]): void {
    this.conversationHistory = [...history];
  }

  /**
   * 获取历史消息数量
   */
  getHistoryLength(): number {
    return this.conversationHistory.length;
  }

  /**
   * 限制历史消息数量（避免context过长）
   */
  limitHistory(maxMessages: number = 20): void {
    if (this.conversationHistory.length > maxMessages) {
      // 保留第一条系统消息和最近的消息
      const systemMessage = this.conversationHistory[0];
      const recentMessages = this.conversationHistory.slice(-maxMessages + 1);
      this.conversationHistory = [systemMessage, ...recentMessages];
    }
  }

  /**
   * 注册单个工具
   */
  registerTool(tool: ToolDefinition): void {
    // 验证工具定义
    if (!this.validateToolDefinition(tool)) {
      throw new Error(`工具定义无效: ${tool.function?.name || 'unknown'}`);
    }

    const toolName = tool.function.name;
    
    if (this.registeredTools.has(toolName)) {
      console.warn(`工具 '${toolName}' 已存在，将被覆盖`);
    }

    this.registeredTools.set(toolName, tool);
    console.log(`工具 '${toolName}' 注册成功`);
  }

  /**
   * 批量注册工具
   */
  registerTools(tools: ToolDefinition[]): void {
    const successfulTools: string[] = [];
    const failedTools: string[] = [];

    for (const tool of tools) {
      try {
        this.registerTool(tool);
        successfulTools.push(tool.function.name);
      } catch (error) {
        console.error(`注册工具失败: ${tool.function?.name || 'unknown'}`, error);
        failedTools.push(tool.function?.name || 'unknown');
      }
    }

    console.log(`工具注册完成: 成功 ${successfulTools.length} 个，失败 ${failedTools.length} 个`);
    
    if (failedTools.length > 0) {
      console.warn('失败的工具:', failedTools);
    }
  }

  /**
   * 获取已注册的工具
   */
  getRegisteredTools(): ToolDefinition[] {
    return Array.from(this.registeredTools.values());
  }

  /**
   * 获取工具名称列表
   */
  getToolNames(): string[] {
    return Array.from(this.registeredTools.keys());
  }

  /**
   * 检查工具是否已注册
   */
  hasRegisteredTool(toolName: string): boolean {
    return this.registeredTools.has(toolName);
  }

  /**
   * 获取特定工具定义
   */
  getToolDefinition(toolName: string): ToolDefinition | undefined {
    return this.registeredTools.get(toolName);
  }

  /**
   * 取消注册工具
   */
  unregisterTool(toolName: string): boolean {
    const removed = this.registeredTools.delete(toolName);
    if (removed) {
      console.log(`工具 '${toolName}' 已取消注册`);
    }
    return removed;
  }

  /**
   * 清除所有已注册的工具
   */
  clearRegisteredTools(): void {
    const count = this.registeredTools.size;
    this.registeredTools.clear();
    console.log(`已清除 ${count} 个注册的工具`);
  }

  /**
   * 验证工具定义格式
   */
  private validateToolDefinition(tool: ToolDefinition): boolean {
    // 检查基本结构
    if (!tool || typeof tool !== 'object') {
      console.error('工具定义必须是对象');
      return false;
    }

    // 检查 type 字段
    if (tool.type !== 'function') {
      console.error('工具类型必须是 "function"');
      return false;
    }

    // 检查 function 字段
    if (!tool.function || typeof tool.function !== 'object') {
      console.error('工具必须包含 function 对象');
      return false;
    }

    // 检查 name 字段
    if (!tool.function.name || typeof tool.function.name !== 'string') {
      console.error('工具必须有有效的名称');
      return false;
    }

    // 检查名称格式（只允许字母、数字、下划线、连字符）
    if (!/^[a-zA-Z0-9_-]+$/.test(tool.function.name)) {
      console.error('工具名称只能包含字母、数字、下划线和连字符');
      return false;
    }

    // 检查 description 字段
    if (!tool.function.description || typeof tool.function.description !== 'string') {
      console.error('工具必须有有效的描述');
      return false;
    }

    // 检查 parameters 字段
    if (!tool.function.parameters || typeof tool.function.parameters !== 'object') {
      console.error('工具必须有有效的参数定义');
      return false;
    }

    // 验证参数结构是否符合 JSON Schema 格式
    if (tool.function.parameters.type !== 'object') {
      console.error('工具参数类型必须是 "object"');
      return false;
    }

    return true;
  }

  /**
   * 发送消息并自动使用已注册的工具
   */
  async sendMessageWithRegisteredTools(message: string): Promise<VLChatResponse> {
    const tools = this.getRegisteredTools();
    return this.sendTextMessageWithTools(message, tools);
  }

  /**
   * 发送图片消息并自动使用已注册的工具
   */
  async sendImageMessageWithRegisteredTools(imageBase64: string, message: string = '请分析这张图片'): Promise<VLChatResponse> {
    const tools = this.getRegisteredTools();
    return this.sendImageMessageWithTools(imageBase64, message, tools);
  }

  /**
   * 获取工具注册状态信息
   */
  getToolRegistrationStatus(): {
    totalRegistered: number;
    toolNames: string[];
    toolCategories: Record<string, string[]>;
  } {
    const toolNames = this.getToolNames();
    
    // 按工具名称前缀分类
    const categories: Record<string, string[]> = {
      analyze: [],
      find: [],
      get: [],
      other: []
    };

    toolNames.forEach((name: any) => {
      if (name.startsWith('analyze_')) {
        categories.analyze.push(name);
      } else if (name.startsWith('find_')) {
        categories.find.push(name);
      } else if (name.startsWith('get_')) {
        categories.get.push(name);
      } else {
        categories.other.push(name);
      }
    });

    return {
      totalRegistered: toolNames.length,
      toolNames,
      toolCategories: categories
    };
  }

  /**
   * 发送带图片工具的消息（增强版）
   * 支持自动工具调用和结果处理
   */
  async sendMessageWithImageTools(
    message: string, 
    imageBase64?: string,
    options?: {
      maxToolCalls?: number;
      autoExecuteTools?: boolean;
      toolExecutor?: (toolName: string, parameters: any) => Promise<any>;
    }
  ): Promise<VLChatResponse> {
    const { 
      maxToolCalls = 5, 
      autoExecuteTools = false,
      toolExecutor 
    } = options || {};

    try {
      // 确保连接已建立
      await this.ensureConnection();
      
      // 构建消息内容
      let userContent: any;
      
      if (imageBase64) {
        userContent = [
          {
            type: 'text' as const,
            text: message
          },
          {
            type: 'image_url' as const,
            image_url: {
              url: imageBase64.startsWith('data:') ? imageBase64 : `data:image/jpeg;base64,${imageBase64}`
            }
          }
        ];
      } else {
        userContent = message;
      }

      // 添加用户消息到历史
      this.conversationHistory.push({
        role: 'user',
        content: userContent
      });

      const tools = this.getRegisteredTools();
      let toolCallCount = 0;

      while (toolCallCount < maxToolCalls) {
        const requestOptions: OpenAI.Chat.Completions.ChatCompletionCreateParams = {
          model: this.config.vlModel,
          messages: this.conversationHistory as any,
          stream: false,
          temperature: 0.7,
          max_tokens: 1000,
        };

        // 添加工具定义
        if (tools.length > 0) {
          requestOptions.tools = tools;
          requestOptions.tool_choice = 'auto';
        }

        const response = await this.client.chat.completions.create(requestOptions);

        const choice = response.choices[0];
        if (!choice) {
          throw new Error('AI模型返回空结果');
        }

        const assistantMessage = choice.message.content || '';
        const toolCalls = choice.message.tool_calls;

        // 添加助手回复到历史
        this.conversationHistory.push({
          role: 'assistant',
          content: assistantMessage,
          tool_calls: toolCalls
        });

        // 如果没有工具调用，返回结果
        if (!toolCalls || toolCalls.length === 0) {
          return { 
            content: assistantMessage,
            tool_calls: toolCalls
          };
        }

        // 处理工具调用
        if (autoExecuteTools && toolExecutor) {
          for (const toolCall of toolCalls) {
            try {
              const toolName = toolCall.function.name;
              const parameters = JSON.parse(toolCall.function.arguments);
              
              console.log(`执行工具调用: ${toolName}`, parameters);
              
              const toolResult = await toolExecutor(toolName, parameters);
              const resultString = JSON.stringify(toolResult);
              
              // 添加工具结果到对话历史
              this.addToolResult(toolCall.id, resultString);
              
            } catch (error) {
              console.error(`工具调用失败: ${toolCall.function.name}`, error);
              const errorResult = {
                success: false,
                error: error instanceof Error ? error.message : String(error)
              };
              this.addToolResult(toolCall.id, JSON.stringify(errorResult));
            }
          }
          
          toolCallCount++;
        } else {
          // 如果不自动执行工具，返回工具调用信息
          return { 
            content: assistantMessage,
            tool_calls: toolCalls
          };
        }
      }

      // 如果达到最大工具调用次数，继续对话获取最终回复
      const finalResponse = await this.continueConversation();
      return finalResponse;

    } catch (error) {
      console.error('带工具的消息发送失败:', error);
      return { 
        content: '抱歉，我遇到了一些问题，请稍后再试。',
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  /**
   * 处理多个工具调用结果
   */
  async processToolCallResults(
    toolCalls: OpenAI.Chat.Completions.ChatCompletionMessageToolCall[],
    toolExecutor: (toolName: string, parameters: any) => Promise<any>
  ): Promise<{ success: boolean; results: any[]; errors: string[] }> {
    const results: any[] = [];
    const errors: string[] = [];

    for (const toolCall of toolCalls) {
      try {
        const toolName = toolCall.function.name;
        const parameters = JSON.parse(toolCall.function.arguments);
        
        console.log(`处理工具调用: ${toolName}`, parameters);
        
        const toolResult = await toolExecutor(toolName, parameters);
        results.push({
          toolCallId: toolCall.id,
          toolName,
          parameters,
          result: toolResult
        });
        
        // 添加工具结果到对话历史
        this.addToolResult(toolCall.id, JSON.stringify(toolResult));
        
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        console.error(`工具调用失败: ${toolCall.function.name}`, error);
        
        errors.push(`${toolCall.function.name}: ${errorMessage}`);
        
        const errorResult = {
          success: false,
          error: errorMessage
        };
        this.addToolResult(toolCall.id, JSON.stringify(errorResult));
      }
    }

    return {
      success: errors.length === 0,
      results,
      errors
    };
  }

  /**
   * 格式化工具调用结果为用户友好的文本
   */
  formatToolCallResults(results: any[]): string {
    if (results.length === 0) {
      return '没有执行任何工具调用。';
    }

    const formattedResults = results.map((result: any) => {
      const { toolName, result: toolResult } = result;
      
      switch (toolName) {
        case 'analyze_image':
          if (toolResult.success) {
            return `图片分析完成：${toolResult.description}`;
          } else {
            return `图片分析失败：${toolResult.error}`;
          }
          
        case 'find_similar_images_by_image':
        case 'find_similar_images_by_description':
        case 'find_images_by_tags':
          if (toolResult.success || toolResult.images) {
            const count = toolResult.total || toolResult.images?.length || 0;
            return `找到 ${count} 张相关图片`;
          } else {
            return `图片搜索失败：${toolResult.error}`;
          }
          
        case 'get_image_analysis':
          if (toolResult.success) {
            return `获取图片分析结果成功`;
          } else {
            return `获取图片分析失败：${toolResult.error}`;
          }
          
        default:
          return `执行工具 ${toolName} ${toolResult.success ? '成功' : '失败'}`;
      }
    });

    return formattedResults.join('\n');
  }
}