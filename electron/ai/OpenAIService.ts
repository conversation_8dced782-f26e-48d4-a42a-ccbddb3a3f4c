import OpenAI from 'openai';
import { AIService, ImageAnalysisResult, EmbeddingResult, StructuredImageAnalysisResult } from './AIService';
import * as fs from 'node:fs';
import * as path from 'node:path';

export interface OpenAIConfig {
  vlBaseURL: string;
  vlApiKey: string;
  vlModel: string;
  chatBaseURL: string;
  chatApiKey: string;
  chatModel: string;
  embeddingModel: string;
  embeddingBaseURL?: string;
  embeddingApiKey?: string;
  timeout?: number;
  maxRetries?: number;
  dangerouslyAllowBrowser?: boolean;
}

export class OpenAIService implements AIService {
  private vlClient: OpenAI;
  private chatClient: OpenAI;
  private embeddingClient: OpenAI;
  private config: OpenAIConfig;
  private connectionTested: boolean = false;
  private connectionStatus: boolean = false;
  private connectionError?: string;
  private connectionPromise: Promise<boolean> | null = null;

  constructor(config: OpenAIConfig) {
    this.config = config;
    
    // 验证必要配置
    if (!config.vlBaseURL) {
      console.error('❌ 错误: vlBaseURL 未配置');
    }
    if (!config.vlApiKey) {
      console.error('❌ 错误: vlApiKey 未配置');
    }
    if (!config.chatBaseURL) {
      console.error('❌ 错误: chatBaseURL 未配置');
    }
    if (!config.chatApiKey) {
      console.error('❌ 错误: chatApiKey 未配置');
    }
    if (!config.vlModel) {
      console.error('❌ 错误: vlModel 未配置');
    }
    if (!config.chatModel) {
      console.error('❌ 错误: chatModel 未配置');
    }
    if (!config.embeddingModel) {
      console.error('❌ 错误: embeddingModel 未配置');
    }
    
    // VL 服务客户端（用于图像分析）
    this.vlClient = new OpenAI({
      baseURL: config.vlBaseURL,
      apiKey: config.vlApiKey,
      timeout: config.timeout || 90000,
      maxRetries: config.maxRetries || 3,
      dangerouslyAllowBrowser: config.dangerouslyAllowBrowser || true,
    });

    // Chat 服务客户端（用于聊天对话）
    this.chatClient = new OpenAI({
      baseURL: config.chatBaseURL,
      apiKey: config.chatApiKey,
      timeout: config.timeout || 90000,
      maxRetries: config.maxRetries || 3,
      dangerouslyAllowBrowser: config.dangerouslyAllowBrowser || true
    });

    // Embedding 服务客户端（可能使用不同的 URL 和 API key）
    const embeddingBaseURL = config.embeddingBaseURL || config.vlBaseURL;
    const embeddingApiKey = config.embeddingApiKey || config.vlApiKey;
    
    this.embeddingClient = new OpenAI({
      baseURL: embeddingBaseURL,
      apiKey: embeddingApiKey,
      timeout: config.timeout || 60000,
      maxRetries: config.maxRetries || 3,
      dangerouslyAllowBrowser: config.dangerouslyAllowBrowser || true
    });
  }

  /**
   * 确保AI服务连接已建立（延迟连接）
   */
  private async ensureConnection(): Promise<void> {
    return
  }

  /**
   * 执行实际的连接测试
   */
  private async performConnectionTest(): Promise<boolean> {
    try {
      // 测试 VL 服务连接
      await this.vlClient.models.list();
      
      // 测试 Chat 服务连接
      await this.chatClient.models.list();
      
      // 测试 embedding 服务连接
      // await this.embeddingClient.embeddings.create({
      //   model: this.config.embeddingModel,
      //   input: "test"
      // });

      this.connectionTested = true;
      this.connectionStatus = true;
      this.connectionError = undefined;
      return true;
    } catch (error) {
      this.connectionTested = true;
      this.connectionStatus = false;
      this.connectionError = error instanceof Error ? error.message : String(error);
      return false;
    }
  }

  /**
   * 重置连接状态
   */
  resetConnection(): void {
    this.connectionTested = false;
    this.connectionStatus = false;
    this.connectionError = undefined;
    this.connectionPromise = null;
  }

  /**
   * 获取连接状态信息
   */
  getConnectionStatus(): { tested: boolean; connected: boolean; error?: string } {
    return {
      tested: this.connectionTested,
      connected: this.connectionStatus,
      error: this.connectionError
    };
  }

  /**
   * 通过文件路径分析图片，生成描述和标签
   */
  async analyzeImageByPath(imagePath: string): Promise<ImageAnalysisResult> {
    try {
      // 确保连接已建立
      await this.ensureConnection();
      
      // 规范化路径以处理跨平台路径分隔符问题
      const normalizedPath = imagePath.replace(/\\/g, path.sep);
      
      // 检查文件是否存在
      if (!fs.existsSync(normalizedPath)) {
        throw new Error(`图片文件不存在: ${normalizedPath}`);
      }

      // 读取文件并转换为base64
      const fileBuffer = fs.readFileSync(normalizedPath);
      const base64Data = fileBuffer.toString('base64');
      
      // 确定MIME类型
      const extension = path.extname(normalizedPath).toLowerCase();
      let mimeType = 'image/jpeg';
      switch (extension) {
        case '.png':
          mimeType = 'image/png';
          break;
        case '.webp':
          mimeType = 'image/webp';
          break;
        case '.gif':
          mimeType = 'image/gif';
          break;
        case '.bmp':
          mimeType = 'image/bmp';
          break;
        case '.jpg':
        case '.jpeg':
        default:
          mimeType = 'image/jpeg';
          break;
      }

      const dataUrl = `data:${mimeType};base64,${base64Data}`;
      
      // 使用现有的analyzeImage方法进行分析
      return await this.analyzeImage(dataUrl);
      
    } catch (error) {
      console.error('通过路径分析图片失败:', error);
      throw new Error(`通过路径分析图片失败: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * 分析图片，生成描述和标签
   */
  async analyzeImage(imageBase64: string): Promise<ImageAnalysisResult> {
    try {
      // 确保连接已建立
      await this.ensureConnection();

      const response = await this.vlClient.chat.completions.create({
        model: this.config.vlModel,
        messages: [
          {
            role: "user",
            content: [
              {
                type: "text",
                text: `你是一位顶级图像理解专家。请深入分析提供的图片，并仅返回以下 JSON 结构，不要添加任何额外文字。请尽可能完整地填充以下各字段（允许部分字段为空数组）。

{
  "description": "对图片的整体描述（不超过80字），用于语义搜索。",
  "theme": {
    "dominant_colors": ["主要颜色，例如 '白色', '深蓝'"],
    "scene": "场景类型，例如 '室内', '自然风光'",
    "mood": "图片传递的氛围，例如 '温馨', '孤独'",
    "time": "图片中表现的时间或天气，例如 '夜晚', '日出', '雨天'",
    "style": "图像风格，例如 '插画', '写实', '科技感'"
  },
  "tags": {
    "objects": ["图片中的主要物体名词，例如 '人物', '猫', '手机'"],
    "actions": ["动作或行为，例如 '坐着', '行走', '打电话'"],
    "clothing": ["衣着相关标签，例如 '白衬衫', '牛仔裤'"],
    "relationships": ["人物间的关系，例如 '家庭', '独自', '握手'"],
    "activity_domain": ["图片中体现的活动或场景领域，例如 '商务', '旅游', '运动'"],
    "text_overlay": ["图片上是否有文字及其语言，如 '有文字', '英文'"]
  },
  "objects": [
    {
      "name": "主物体或人物名称，例如 '男人', '沙发'",
      "attributes": ["具体属性，例如 '戴眼镜', '白色', '皮质'"]
    }
  ]
}`
              },
              {
                type: "image_url",
                image_url: {
                  url: imageBase64.startsWith('data:') ? imageBase64 : `data:image/jpeg;base64,${imageBase64}`
                }
              }
            ]
          }
        ],
        stream: false,
        temperature: 0.2,
        frequency_penalty: 1.2,
      });

      const content = response.choices[0]?.message?.content;
      if (!content) {
        throw new Error('AI模型返回空结果');
      }

      // 尝试解析JSON结果
      let result: ImageAnalysisResult;
      try {
        // 清理可能的markdown代码块标记
        const cleanContent = content.replace(/```json\n?|```\n?/g, '').trim();
        const parsed = JSON.parse(cleanContent) as StructuredImageAnalysisResult;
        
        // 生成扁平化标签数组
        const tags_flat = this.generateTagsFlat(parsed);
        
        // 向后兼容：生成传统的tags数组
        const legacy_tags = this.generateLegacyTags(parsed);
        
        result = {
          description: parsed.description || '无法生成描述',
          tags: legacy_tags,
          structured_data: parsed,
          tags_flat: tags_flat
        };
      } catch (parseError) {
        console.warn('解析JSON失败，尝试提取文本:', parseError);
        
        // 如果JSON解析失败，尝试从文本中提取信息
        result = this.extractFromText(content);
      }

      return result;
      
    } catch (error) {
      console.error('图片分析失败:', error);
      
      // 如果是连接错误，直接抛出原始错误
      if (error instanceof Error && error.message.includes('AI服务连接失败')) {
        throw error;
      }
      
      throw new Error(`图片分析失败: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * 将文本转换为向量
   */
  async generateEmbedding(text: string): Promise<EmbeddingResult> {
    try {
      // 确保连接已建立
      await this.ensureConnection();
      
      const response = await this.embeddingClient.embeddings.create({
        model: this.config.embeddingModel,
        input: text,
      });

      const embedding = response.data[0]?.embedding;
      if (!embedding) {
        console.error('❌ Embedding模型返回空结果');
        throw new Error('Embedding模型返回空结果');
      }

      const result = {
        embedding,
        dimensions: embedding.length
      };

      return result;
    } catch (error) {
      console.error('❌ 向量生成失败:', error instanceof Error ? error.message : String(error));
      
      // 如果是连接错误，直接抛出原始错误
      if (error instanceof Error && error.message.includes('AI服务连接失败')) {
        throw error;
      }
      
      throw new Error(`向量生成失败: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * 测试AI服务连接
   */
  async testConnection(): Promise<boolean> {
    return true;
  }
  
  /**
   * AI驱动的查询解析，从用户搜索查询中提取关键词
   */
  async parseSearchQuery(query: string): Promise<{
    keywords: string[];
    error?: string;
  }> {
    try {
      // 确保连接已建立
      await this.ensureConnection();
      
      const response = await this.chatClient.chat.completions.create({
        model: this.config.chatModel,
        messages: [
          {
            role: "user",
            content: `你是一个搜索查询分析引擎。你的任务是从用户的搜索文本中，提取出最核心、最可能作为数据库标签的名词和关键形容词。严格以JSON数组的格式返回结果，忽略不重要的词语。

已知数据库标签库包含: ["人物", "物体", "颜色", "场景", "动作", "衣着", "情感", "时间", "地点", "风格"] 等分类。

用户查询: "${query}"

请返回一个包含最适合在数据库中进行过滤的关键词的JSON数组。例如：

用户查询: "一个穿白衬衫的男人坐在椅子上"
你的输出: ["男人", "衬衫", "白色", "椅子", "坐着"]

请只返回JSON数组，不要添加其他文字。不要给我python代码`
          }
        ],
        stream: false,
        temperature: 0.1,
        max_tokens: 100,
      });

      const content = response.choices[0]?.message?.content;
      if (!content) {
        throw new Error('AI模型返回空结果');
      }

      // 尝试解析JSON结果
      try {
        const cleanContent = content.replace(/```json\n?|```\n?/g, '').trim();
        const keywords = JSON.parse(cleanContent);
        
        if (Array.isArray(keywords)) {
          const validKeywords = keywords.filter((k: any) => k && typeof k === 'string' && k.trim().length > 0);
          return { keywords: validKeywords };
        } else {
          throw new Error('返回结果不是数组格式');
        }
      } catch (parseError) {
        console.warn('解析JSON失败，尝试提取关键词:', parseError);
        
        // 如果JSON解析失败，尝试简单的文本提取
        const keywords = this.extractKeywordsFromText(content);
        return { keywords };
      }
      
    } catch (error) {
      console.error('查询解析失败:', error);
      return { 
        keywords: [], 
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  /**
   * 从纯文本中提取描述和标签（备用方案）
   */
  private extractFromText(text: string): ImageAnalysisResult {
    
    // 简单的文本解析逻辑
    const lines = text.split('\n').filter((line: any) => line.trim());
    
    let description = '';
    const tags: string[] = [];
    
    for (const line of lines) {
      if (line.includes('描述') || line.includes('内容') || (!description && line.length > 20)) {
        description = line.replace(/^[^：:]*[：:]?\s*/, '').trim();
      }
      
      // 尝试提取标签
      if (line.includes('标签') || line.includes('关键词') || line.includes('tags')) {
        const tagMatches = line.match(/[，,、]\s*([^，,、\s]+)/g);
        if (tagMatches) {
          tags.push(...tagMatches.map((tag: any) => tag.replace(/[，,、\s]/g, '')));
        }
      }
    }
    
    return {
      description: description || text.substring(0, 100) + '...',
      tags: tags.length > 0 ? tags : ['图片', '内容']
    };
  }
  
  /**
   * 从结构化数据生成扁平化标签数组
   */
  private generateTagsFlat(structured: StructuredImageAnalysisResult): string[] {
    const tags_flat: string[] = [];
    
    // 添加theme中的所有值
    if (structured.theme) {
      const theme = structured.theme;
      if (theme.dominant_colors) tags_flat.push(...theme.dominant_colors);
      if (theme.scene) tags_flat.push(theme.scene);
      if (theme.mood) tags_flat.push(theme.mood);
      if (theme.time) tags_flat.push(theme.time);
      if (theme.style) tags_flat.push(theme.style);
    }
    
    // 添加tags中的所有值
    if (structured.tags) {
      const tags = structured.tags;
      if (tags.objects) tags_flat.push(...tags.objects);
      if (tags.actions) tags_flat.push(...tags.actions);
      if (tags.clothing) tags_flat.push(...tags.clothing);
      if (tags.relationships) tags_flat.push(...tags.relationships);
      if (tags.activity_domain) tags_flat.push(...tags.activity_domain);
      if (tags.text_overlay) tags_flat.push(...tags.text_overlay);
    }
    
    // 添加objects中的名称和属性
    if (structured.objects) {
      for (const obj of structured.objects) {
        if (obj.name) tags_flat.push(obj.name);
        if (obj.attributes) tags_flat.push(...obj.attributes);
      }
    }
    
    // 去重、过滤空值并限制长度
    return Array.from(new Set(tags_flat.filter((tag) => tag && tag.trim().length > 0)))
      .map(tag => tag.length > 32 ? tag.substring(0, 32) : tag);
  }
  
  /**
   * 从结构化数据生成传统的tags数组（向后兼容）
   */
  private generateLegacyTags(structured: StructuredImageAnalysisResult): string[] {
    const legacy_tags: string[] = [];
    
    // 优先添加最重要的标签
    if (structured.tags) {
      const tags = structured.tags;
      if (tags.objects) legacy_tags.push(...tags.objects.slice(0, 3));
      if (tags.actions) legacy_tags.push(...tags.actions.slice(0, 2));
      if (tags.clothing) legacy_tags.push(...tags.clothing.slice(0, 2));
    }
    
    // 添加主要颜色
    if (structured.theme?.dominant_colors) {
      legacy_tags.push(...structured.theme.dominant_colors.slice(0, 2));
    }
    
    // 添加场景类型
    if (structured.theme?.scene) {
      legacy_tags.push(structured.theme.scene);
    }
    
    // 去重、过滤空值、限制数量
    return Array.from(new Set(legacy_tags.filter((tag: any) => tag && tag.trim().length > 0))).slice(0, 10);
  }
  
  /**
   * 从文本中提取关键词（备用方案）
   */
  private extractKeywordsFromText(text: string): string[] {
    
    // 简单的关键词提取逻辑
    const keywords: string[] = [];
    
    // 尝试从方括号中提取
    const bracketMatches = text.match(/\[([^\]]+)\]/g);
    if (bracketMatches) {
      bracketMatches.forEach((match: any) => {
        const content = match.replace(/[[\]]/g, '').trim();
        const items = content.split(/[,，]/).map((item: any) => item.trim()).filter((item: any) => item);
        keywords.push(...items);
      });
    }
    
    // 如果没有找到方括号，尝试提取常见词汇
    if (keywords.length === 0) {
      const commonWords = ['人物', '男人', '女人', '儿童', '动物', '猫', '狗', '白色', '黑色', '红色', '蓝色', '绿色', '室内', '室外', '坐着', '站着', '跑步', '衣服', '衬衫', '裤子'];
      
      commonWords.forEach((word: any) => {
        if (text.includes(word)) {
          keywords.push(word);
        }
      });
    }
    
    // 去重和限制数量
    return Array.from(new Set(keywords)).slice(0, 8);
  }
}