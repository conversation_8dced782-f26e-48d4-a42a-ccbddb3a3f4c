// 结构化分析结果接口
export interface StructuredImageAnalysisResult {
  description: string;
  theme: {
    dominant_colors: string[];
    scene: string;
    mood: string;
    time: string;
    style: string;
  };
  tags: {
    objects: string[];
    actions: string[];
    clothing: string[];
    relationships: string[];
    activity_domain: string[];
    text_overlay: string[];
  };
  objects: Array<{
    name: string;
    attributes: string[];
  }>;
}

export interface ImageAnalysisResult {
  description: string;
  tags: string[];
  // 新增结构化数据支持
  structured_data?: StructuredImageAnalysisResult;
  tags_flat?: string[]; // 扁平化标签
}

export interface EmbeddingResult {
  embedding: number[];
  dimensions: number;
}

export interface AIService {
  /** 分析图片，生成描述和标签 */
  analyzeImage(imageBase64: string): Promise<ImageAnalysisResult>;
  
  /** 通过文件路径分析图片，生成描述和标签 */
  analyzeImageByPath(imagePath: string): Promise<ImageAnalysisResult>;
  
  /** 将文本转换为向量 */
  generateEmbedding(text: string): Promise<EmbeddingResult>;
  
  /** 测试AI服务连接 */
  testConnection(): Promise<boolean>;
  
  /** AI驱动的查询解析，从用户搜索查询中提取关键词 */
  parseSearchQuery(query: string): Promise<{
    keywords: string[];
    error?: string;
  }>;
} 