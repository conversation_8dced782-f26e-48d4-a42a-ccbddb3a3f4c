import { 
  BaseResponse, 
  ConnectionTestResponse, 
  DatabaseInitResponse,
  AIServiceStatusResponse,
  QueryResponse,
  InsertResponse,
  ExistsResponse
} from '../../src/types/ipc-responses';

/**
 * IPC 响应工具函数
 * 确保所有 IPC 响应格式一致
 */

export class IPCResponse {
  /**
   * 创建成功响应
   */
  static success<T extends BaseResponse>(data?: Omit<T, 'success'>): T {
    return {
      success: true,
      ...data
    } as T;
  }

  /**
   * 创建错误响应
   */
  static error<T extends BaseResponse>(error: string | Error): T {
    return {
      success: false,
      error: error instanceof Error ? error.message : error
    } as T;
  }

  /**
   * 连接测试响应
   */
  static connectionTest(connected: boolean): ConnectionTestResponse {
    return this.success<ConnectionTestResponse>({ connected });
  }

  /**
   * 数据库初始化响应
   */
  static databaseInit(): DatabaseInitResponse {
    return this.success<DatabaseInitResponse>();
  }

  /**
   * AI 服务状态响应
   */
  static aiServiceStatus(status: 'connected' | 'disconnected' | 'error'): AIServiceStatusResponse {
    return this.success<AIServiceStatusResponse>({ status });
  }

  /**
   * 查询响应
   */
  static query<T>(results: T[], total?: number): QueryResponse<T> {
    return this.success<QueryResponse<T>>({ 
      results, 
      total: total ?? results.length 
    });
  }

  /**
   * 插入响应
   */
  static insert(insertedIds: string[]): InsertResponse {
    return this.success<InsertResponse>({ insertedIds });
  }

  /**
   * 存在性检查响应
   */
  static exists(exists: boolean): ExistsResponse {
    return this.success<ExistsResponse>({ exists });
  }

  /**
   * 包装异步操作，自动处理错误
   */
  static async wrap<T extends BaseResponse>(
    operation: () => Promise<T | any>,
    errorTransform?: (error: any) => T
  ): Promise<T> {
    try {
      const result = await operation();
      // 如果结果已经是标准格式，直接返回
      if (typeof result === 'object' && 'success' in result) {
        return result;
      }
      // 否则包装为成功响应
      return this.success<T>(result);
    } catch (error) {
      console.error('IPC operation failed:', error);
      if (errorTransform) {
        return errorTransform(error);
      }
      return this.error<T>(error instanceof Error ? error.message : String(error));
    }
  }
}