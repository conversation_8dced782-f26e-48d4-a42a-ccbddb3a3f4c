import { BaseResponse, ConnectionTestResponse } from '../../src/types/ipc-responses';

/**
 * IPC 响应验证工具
 * 用于运行时验证响应格式
 */

export class IPCValidator {
  /**
   * 验证基础响应格式
   */
  static validateBaseResponse(response: any): response is BaseResponse {
    return (
      typeof response === 'object' &&
      response !== null &&
      typeof response.success === 'boolean' &&
      (response.error === undefined || typeof response.error === 'string')
    );
  }

  /**
   * 验证连接测试响应
   */
  static validateConnectionTestResponse(response: any): response is ConnectionTestResponse {
    if (!this.validateBaseResponse(response)) {
      return false;
    }

    if (response.success) {
      return typeof (response as ConnectionTestResponse).connected === 'boolean';
    }

    return true; // 错误响应只需要 success: false 和 error
  }

  /**
   * 包装 IPC 调用，添加运行时验证
   */
  static async wrapIPCCall<T extends BaseResponse>(
    ipcCall: () => Promise<T>,
    validator?: (response: any) => boolean
  ): Promise<T> {
    try {
      const response = await ipcCall();

      if (!this.validateBaseResponse(response)) {
        console.error('Invalid IPC response format:', response);
        return {
          success: false,
          error: 'Invalid response format from backend'
        } as T;
      }

      if (validator && !validator(response)) {
        console.error('IPC response failed custom validation:', response);
        return {
          success: false,
          error: 'Response failed validation'
        } as T;
      }

      return response;
    } catch (error) {
      console.error('IPC call failed:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      } as T;
    }
  }
}