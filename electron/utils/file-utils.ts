import fs from 'node:fs'
import path from 'node:path'

// 支持的图片格式
export const SUPPORTED_IMAGE_EXTENSIONS = ['.jpg', '.jpeg', '.png', '.webp', '.gif', '.bmp']

/**
 * 递归获取文件夹中的所有图片文件
 */
export async function getAllImageFiles(dirPath: string): Promise<string[]> {
  const imageFiles: string[] = []
  
  try {
    const items = await fs.promises.readdir(dirPath, { withFileTypes: true })
    
    for (const item of items) {
      const fullPath = path.join(dirPath, item.name)
      
      if (item.isDirectory()) {
        // 递归处理子文件夹
        try {
          const subImages = await getAllImageFiles(fullPath)
          imageFiles.push(...subImages)
        } catch (error) {
          console.warn(`跳过无法访问的文件夹: ${fullPath}`, error)
        }
      } else if (item.isFile()) {
        // 检查是否为支持的图片格式
        const ext = path.extname(item.name).toLowerCase()
        if (SUPPORTED_IMAGE_EXTENSIONS.includes(ext)) {
          imageFiles.push(fullPath)
        }
      }
    }
  } catch (error) {
    console.error(`读取文件夹失败: ${dirPath}`, error)
    throw error
  }
  
  return imageFiles
}

/**
 * 检查文件是否存在
 */
export function fileExists(filePath: string): boolean {
  try {
    return fs.existsSync(filePath)
  } catch {
    return false
  }
}

/**
 * 获取文件统计信息
 */
export async function getFileStats(filePath: string): Promise<fs.Stats | null> {
  try {
    return await fs.promises.stat(filePath)
  } catch {
    return null
  }
}

/**
 * 检查文件访问权限
 */
export async function checkFileAccess(filePath: string, mode: number = fs.constants.R_OK): Promise<boolean> {
  try {
    await fs.promises.access(filePath, mode)
    return true
  } catch {
    return false
  }
}