/**
 * LibraryService 与 ImageLibraryTaskService 集成示例
 * 展示如何使用任务驱动的图片库管理
 */

import { LibraryService } from '../services/LibraryService'
import { ImageLibraryTaskService, TaskType, TaskStatus } from '../services/ImageLibraryTaskService'
import { SQLiteLibraryDAO } from '../dao/sqlite/SQLiteLibraryDAO'
import { SQLiteImageDAO } from '../dao/sqlite/SQLiteImageDAO'
import { AIService } from '../ai/AIService'
import { ErrorLogger } from '../services/ErrorLogger'
import { ProgressEventManager } from '../services/ProgressEventManager'

/**
 * 示例：创建图片库并启动扫描任务
 */
export async function createLibraryAndStartScan() {
  // 初始化依赖
  const libraryDAO = new SQLiteLibraryDAO()
  const imageDAO = new SQLiteImageDAO()
  const aiService = new AIService()
  const logger = new ErrorLogger()
  const progressManager = new ProgressEventManager()

  // 创建任务服务
  const taskService = new ImageLibraryTaskService(
    imageDAO,
    libraryDAO,
    aiService,
    logger,
    progressManager
  )

  // 创建图片库服务（注入任务服务）
  const libraryService = new LibraryService(libraryDAO, taskService)

  try {
    // 1. 创建新的图片库
    console.log('创建图片库...')
    const libraryId = await libraryService.createLibrary({
      name: '我的图片库',
      rootPath: 'C:\\Users\\<USER>\\MyLibrary',
      description: '个人图片收藏',
      settings: {
        recursive: true,
        includeHidden: false,
        maxDepth: 10,
        supportedFormats: ['.jpg', '.jpeg', '.png', '.webp', '.gif']
      }
    })
    console.log(`图片库创建成功，ID: ${libraryId}`)

    // 2. 启动扫描任务
    console.log('启动扫描任务...')
    const scanResult = await libraryService.startLibraryScanTask(libraryId, {
      recursive: true,
      includeHidden: false,
      maxDepth: 10,
      forceRescan: false,
      priority: 5
    })

    if (scanResult.success && scanResult.taskId) {
      console.log(`扫描任务已启动，任务ID: ${scanResult.taskId}`)

      // 3. 监控扫描进度
      const monitorInterval = setInterval(async () => {
        const status = await libraryService.getScanTaskStatus(scanResult.taskId!)
        if (status) {
          console.log(`扫描进度: ${status.progress.current}/${status.progress.total} (${status.status})`)
          
          if (status.status === TaskStatus.COMPLETED) {
            console.log('扫描完成！')
            clearInterval(monitorInterval)
            
            // 4. 启动其他任务
            await startAdditionalTasks(libraryService, libraryId)
          } else if (status.status === TaskStatus.FAILED) {
            console.error(`扫描失败: ${status.error}`)
            clearInterval(monitorInterval)
          }
        }
      }, 2000)

    } else {
      console.error(`启动扫描任务失败: ${scanResult.error}`)
    }

  } catch (error) {
    console.error('操作失败:', error)
  }
}

/**
 * 启动其他后台任务
 */
async function startAdditionalTasks(libraryService: LibraryService, libraryId: string) {
  try {
    // 启动重复检测任务
    console.log('启动重复检测任务...')
    const duplicateResult = await libraryService.startDuplicateDetectionTask(
      libraryId,
      0.95, // 95% 相似度阈值
      6     // 优先级
    )
    
    if (duplicateResult.success) {
      console.log(`重复检测任务已启动，任务ID: ${duplicateResult.taskId}`)
    }

    // 获取所有任务状态
    const allTasks = await libraryService.getLibraryTasks(libraryId)
    console.log('当前图片库任务:')
    allTasks.forEach(task => {
      console.log(`- ${task.type}: ${task.status} (${task.progress.current}/${task.progress.total})`)
    })

  } catch (error) {
    console.error('启动附加任务失败:', error)
  }
}

/**
 * 示例：为特定图片启动分析任务
 */
export async function analyzeSpecificImage(
  libraryService: LibraryService,
  libraryId: string,
  imageId: string,
  imagePath: string
) {
  try {
    // 启动图片分析任务
    const analysisResult = await libraryService.startImageAnalysisTask(
      libraryId,
      imageId,
      imagePath,
      ['tags', 'description', 'objects'], // 分析类型
      7 // 优先级
    )

    if (analysisResult.success && analysisResult.taskId) {
      console.log(`图片分析任务已启动，任务ID: ${analysisResult.taskId}`)

      // 启动缩略图生成任务
      const thumbnailResult = await libraryService.startThumbnailGenerationTask(
        libraryId,
        imageId,
        imagePath,
        [150, 300, 600], // 缩略图尺寸
        8 // 优先级
      )

      if (thumbnailResult.success) {
        console.log(`缩略图生成任务已启动，任务ID: ${thumbnailResult.taskId}`)
      }
    }

  } catch (error) {
    console.error('启动图片分析任务失败:', error)
  }
}

/**
 * 示例：任务管理操作
 */
export async function manageLibraryTasks(libraryService: LibraryService, libraryId: string) {
  try {
    // 获取所有任务
    const tasks = await libraryService.getLibraryTasks(libraryId)
    console.log(`图片库 ${libraryId} 共有 ${tasks.length} 个任务`)

    // 显示任务详情
    tasks.forEach(task => {
      const duration = task.completedAt && task.startedAt 
        ? task.completedAt.getTime() - task.startedAt.getTime()
        : null

      console.log(`任务 ${task.id}:`)
      console.log(`  类型: ${task.type}`)
      console.log(`  状态: ${task.status}`)
      console.log(`  进度: ${task.progress.current}/${task.progress.total}`)
      console.log(`  创建时间: ${task.createdAt.toISOString()}`)
      if (task.startedAt) {
        console.log(`  开始时间: ${task.startedAt.toISOString()}`)
      }
      if (task.completedAt) {
        console.log(`  完成时间: ${task.completedAt.toISOString()}`)
        if (duration) {
          console.log(`  耗时: ${duration}ms`)
        }
      }
      if (task.error) {
        console.log(`  错误: ${task.error}`)
      }
      console.log('---')
    })

    // 取消待处理的任务（示例）
    const pendingTasks = tasks.filter(task => task.status === TaskStatus.PENDING)
    if (pendingTasks.length > 0) {
      console.log(`发现 ${pendingTasks.length} 个待处理任务`)
      
      // 取消第一个待处理任务（仅作示例）
      const taskToCancel = pendingTasks[0]
      const cancelResult = await libraryService.cancelScanTask(taskToCancel.id)
      
      if (cancelResult.success) {
        console.log(`任务 ${taskToCancel.id} 已取消`)
      } else {
        console.error(`取消任务失败: ${cancelResult.error}`)
      }
    }

  } catch (error) {
    console.error('管理任务失败:', error)
  }
}

/**
 * 示例：批量处理图片库
 */
export async function batchProcessLibraries(libraryService: LibraryService) {
  try {
    // 获取所有活跃的图片库
    const activeLibraries = await libraryService.getActiveLibraries()
    console.log(`发现 ${activeLibraries.length} 个活跃图片库`)

    // 为每个图片库启动扫描任务
    for (const library of activeLibraries) {
      console.log(`处理图片库: ${library.name} (${library.rootPath})`)
      
      // 验证图片库状态
      const validation = await libraryService.validateLibrary(library.id)
      if (!validation.valid) {
        console.warn(`跳过无效图片库 ${library.name}: ${validation.error}`)
        continue
      }

      // 启动扫描任务
      const scanResult = await libraryService.startLibraryScanTask(library.id, {
        priority: 5,
        forceRescan: false
      })

      if (scanResult.success) {
        console.log(`图片库 ${library.name} 扫描任务已启动`)
      } else {
        console.error(`图片库 ${library.name} 扫描任务启动失败: ${scanResult.error}`)
      }
    }

  } catch (error) {
    console.error('批量处理失败:', error)
  }
}

// 导出示例函数
export {
  createLibraryAndStartScan,
  analyzeSpecificImage,
  manageLibraryTasks,
  batchProcessLibraries
}