import { initTRPC } from '@trpc/server'

// Initialize tRPC
const t = initTRPC.create()

// Export the router factory and procedure
export const router = t.router
export const publicProcedure = t.procedure

// 创建一个路由合并函数
export async function createAppRouter() {
  const { ControllerFactory } = await import('../config/ControllerFactory')
  const factory = ControllerFactory.getInstance()
  await factory.initialize()

  const appRouter = router({
    image: factory.getImageController().getRouter(),
    ai: factory.getAIController().getRouter(),
    library: factory.getLibraryController().getRouter(),
    tag: factory.getTagController().getRouter(),
    database: factory.getDatabaseController().getRouter(),
    search: factory.getSearchController().getRouter()
  });
  
  return appRouter
}

// 导出类型（用于客户端）
export type AppRouter = Awaited<ReturnType<typeof createAppRouter>>