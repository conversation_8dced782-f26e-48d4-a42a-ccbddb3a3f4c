import { publicProcedure, router } from '../trpc/router'
import { AIAnalysisService } from '../services/AIAnalysisService'

export class AIController {
  constructor(private aiAnalysisService: AIAnalysisService) {}

  getRouter() {
    return router({
      // 测试AI服务连接
      testConnection: publicProcedure
        .query(async () => {
          try {
            const connected = await this.aiAnalysisService.testConnection()
            return { success: true, connected }
          } catch (error) {
            return { 
              success: false, 
              connected: false,
              error: error instanceof Error ? error.message : 'Unknown error' 
            }
          }
        }),

      // 分析图片（通过路径）
      analyzeImageByPath: publicProcedure
        .input((val: unknown) => {
          if (typeof val !== 'string') throw new Error('Input must be a string')
          return val as string
        })
        .mutation(async ({ input: imagePath }) => {
          try {
            const result = await this.aiAnalysisService.analyzeImageByPath(imagePath)
            return { success: true, result }
          } catch (error) {
            return { 
              success: false, 
              result: null,
              error: error instanceof Error ? error.message : 'Unknown error' 
            }
          }
        }),

      // 分析图片（通过base64数据）
      analyzeImage: publicProcedure
        .input((val: unknown) => {
          if (typeof val !== 'string') throw new Error('Input must be a string')
          return val as string
        })
        .mutation(async ({ input: imageBase64 }) => {
          try {
            const result = await this.aiAnalysisService.analyzeImage(imageBase64)
            return { success: true, result }
          } catch (error) {
            return { 
              success: false, 
              result: null,
              error: error instanceof Error ? error.message : 'Unknown error' 
            }
          }
        }),

      // 批量分析图片
      analyzeImagesBatch: publicProcedure
        .input((val: unknown) => {
          return val as {
            imagePaths: string[]
            batchSize?: number
          }
        })
        .mutation(async ({ input: params }) => {
          try {
            const results = await this.aiAnalysisService.analyzeImagesBatch(
              params.imagePaths,
              params.batchSize
            )
            return { success: true, results }
          } catch (error) {
            return { 
              success: false, 
              results: [],
              error: error instanceof Error ? error.message : 'Unknown error' 
            }
          }
        }),

      // 生成文本向量
      generateEmbedding: publicProcedure
        .input((val: unknown) => {
          if (typeof val !== 'string') throw new Error('Input must be a string')
          return val as string
        })
        .query(async ({ input: text }) => {
          try {
            const embedding = await this.aiAnalysisService.generateEmbedding(text)
            return { success: true, embedding }
          } catch (error) {
            return { 
              success: false, 
              embedding: [],
              error: error instanceof Error ? error.message : 'Unknown error' 
            }
          }
        }),

      // 批量生成文本向量
      generateEmbeddingsBatch: publicProcedure
        .input((val: unknown) => {
          return val as {
            texts: string[]
            batchSize?: number
          }
        })
        .mutation(async ({ input: params }) => {
          try {
            const results = await this.aiAnalysisService.generateEmbeddingsBatch(
              params.texts,
              params.batchSize
            )
            return { success: true, results }
          } catch (error) {
            return { 
              success: false, 
              results: [],
              error: error instanceof Error ? error.message : 'Unknown error' 
            }
          }
        }),

      // 解析搜索查询
      parseSearchQuery: publicProcedure
        .input((val: unknown) => {
          if (typeof val !== 'string') throw new Error('Input must be a string')
          return val as string
        })
        .mutation(async ({ input: query }) => {
          try {
            const result = await this.aiAnalysisService.parseSearchQuery(query)
            return { success: true, ...result }
          } catch (error) {
            return { 
              success: false, 
              keywords: [],
              error: error instanceof Error ? error.message : 'Unknown error' 
            }
          }
        }),

      // 智能搜索关键词提取和扩展
      extractAndExpandKeywords: publicProcedure
        .input((val: unknown) => {
          if (typeof val !== 'string') throw new Error('Input must be a string')
          return val as string
        })
        .query(async ({ input: query }) => {
          try {
            const result = await this.aiAnalysisService.extractAndExpandKeywords(query)
            return { success: true, ...result }
          } catch (error) {
            return { 
              success: false, 
              originalKeywords: [],
              expandedKeywords: [],
              searchEmbedding: [],
              error: error instanceof Error ? error.message : 'Unknown error' 
            }
          }
        }),

      // 提取图片扁平化标签
      extractFlatTags: publicProcedure
        .input((val: unknown) => {
          return val as {
            description: string
            tags: string[]
            structured_data?: any
          }
        })
        .query(async ({ input: analysisResult }) => {
          try {
            const flatTags = this.aiAnalysisService.extractFlatTags(analysisResult)
            return { success: true, flatTags }
          } catch (error) {
            return { 
              success: false, 
              flatTags: [],
              error: error instanceof Error ? error.message : 'Unknown error' 
            }
          }
        }),

      // 生成图片搜索关键词
      generateSearchKeywords: publicProcedure
        .input((val: unknown) => {
          return val as {
            description: string
            tags: string[]
            structured_data?: any
          }
        })
        .query(async ({ input: analysisResult }) => {
          try {
            const keywords = this.aiAnalysisService.generateSearchKeywords(analysisResult)
            return { success: true, keywords }
          } catch (error) {
            return { 
              success: false, 
              keywords: [],
              error: error instanceof Error ? error.message : 'Unknown error' 
            }
          }
        }),

      // 计算向量相似度
      calculateCosineSimilarity: publicProcedure
        .input((val: unknown) => {
          return val as {
            vector1: number[]
            vector2: number[]
          }
        })
        .query(async ({ input: params }) => {
          try {
            const similarity = this.aiAnalysisService.calculateCosineSimilarity(
              params.vector1,
              params.vector2
            )
            return { success: true, similarity }
          } catch (error) {
            return { 
              success: false, 
              similarity: 0,
              error: error instanceof Error ? error.message : 'Unknown error' 
            }
          }
        }),

      // 检查AI服务是否可用
      isAvailable: publicProcedure
        .query(async () => {
          try {
            const available = this.aiAnalysisService.isAvailable()
            return { success: true, available }
          } catch (error) {
            return { 
              success: false, 
              available: false,
              error: error instanceof Error ? error.message : 'Unknown error' 
            }
          }
        }),

      // 获取AI服务状态
      getServiceStatus: publicProcedure
        .query(async () => {
          try {
            const status = await this.aiAnalysisService.getServiceStatus()
            return { success: true, ...status }
          } catch (error) {
            return { 
              success: false, 
              available: false,
              connected: false,
              error: error instanceof Error ? error.message : 'Unknown error' 
            }
          }
        })
    })
  }
}