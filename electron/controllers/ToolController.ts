import { publicProcedure, router } from '../trpc/router'
import { ImageService } from '../services/ImageService'
import { TagService } from '../services/TagService'
import { AIAnalysisService } from '../services/AIAnalysisService'

/**
 * Tool Controller - 处理5个工具的调用
 * 提供同步调用，最多等待30秒
 */
export class ToolController {
  constructor(
    private imageService: ImageService,
    private tagService: TagService,
    private aiAnalysisService: AIAnalysisService
  ) {}

  getRouter() {
    return router({
      // 工具1: 分析图片
      analyzeImage: publicProcedure
        .input((val: unknown) => {
          return val as {
            imagePath?: string
            imageId?: string
            detailLevel?: 'basic' | 'detailed' | 'comprehensive'
            focusAreas?: string[]
            language?: 'zh' | 'en'
            includeStructuredData?: boolean
          }
        })
        .mutation(async ({ input: params }) => {
          const startTime = Date.now()
          const timeout = 30000 // 30秒超时
          
          try {
            // 创建超时Promise
            const timeoutPromise = new Promise((_, reject) => {
              setTimeout(() => reject(new Error('Tool execution timeout (30s)')), timeout)
            })

            // 执行分析
            const analysisPromise = this.executeAnalyzeImage(params)
            
            // 竞争执行，取最先完成的
            const result = await Promise.race([analysisPromise, timeoutPromise])
            
            return {
              success: true,
              data: result,
              executionTime: Date.now() - startTime,
              toolName: 'analyze_image'
            }
          } catch (error) {
            return {
              success: false,
              error: error instanceof Error ? error.message : 'Unknown error',
              executionTime: Date.now() - startTime,
              toolName: 'analyze_image'
            }
          }
        }),

      // 工具2: 根据标签查找图片
      findImagesByTags: publicProcedure
        .input((val: unknown) => {
          return val as {
            tags: string[]
            limit?: number
            matchMode?: 'any' | 'all'
          }
        })
        .mutation(async ({ input: params }) => {
          const startTime = Date.now()
          const timeout = 30000
          
          try {
            const timeoutPromise = new Promise((_, reject) => {
              setTimeout(() => reject(new Error('Tool execution timeout (30s)')), timeout)
            })

            const searchPromise = this.executeFindImagesByTags(params)
            const result = await Promise.race([searchPromise, timeoutPromise])
            
            return {
              success: true,
              data: result,
              executionTime: Date.now() - startTime,
              toolName: 'find_images_by_tags'
            }
          } catch (error) {
            return {
              success: false,
              error: error instanceof Error ? error.message : 'Unknown error',
              executionTime: Date.now() - startTime,
              toolName: 'find_images_by_tags'
            }
          }
        }),

      // 工具3: 根据描述查找相似图片
      findSimilarImagesByDescription: publicProcedure
        .input((val: unknown) => {
          return val as {
            description: string
            limit?: number
            threshold?: number
          }
        })
        .mutation(async ({ input: params }) => {
          const startTime = Date.now()
          const timeout = 30000
          
          try {
            const timeoutPromise = new Promise((_, reject) => {
              setTimeout(() => reject(new Error('Tool execution timeout (30s)')), timeout)
            })

            const searchPromise = this.executeFindSimilarImagesByDescription(params)
            const result = await Promise.race([searchPromise, timeoutPromise])
            
            return {
              success: true,
              data: result,
              executionTime: Date.now() - startTime,
              toolName: 'find_similar_images_by_description'
            }
          } catch (error) {
            return {
              success: false,
              error: error instanceof Error ? error.message : 'Unknown error',
              executionTime: Date.now() - startTime,
              toolName: 'find_similar_images_by_description'
            }
          }
        }),

      // 工具4: 根据图片查找相似图片
      findSimilarImagesByImage: publicProcedure
        .input((val: unknown) => {
          return val as {
            imageId?: string
            imagePath?: string
            limit?: number
            threshold?: number
          }
        })
        .mutation(async ({ input: params }) => {
          const startTime = Date.now()
          const timeout = 30000
          
          try {
            const timeoutPromise = new Promise((_, reject) => {
              setTimeout(() => reject(new Error('Tool execution timeout (30s)')), timeout)
            })

            const searchPromise = this.executeFindSimilarImagesByImage(params)
            const result = await Promise.race([searchPromise, timeoutPromise])
            
            return {
              success: true,
              data: result,
              executionTime: Date.now() - startTime,
              toolName: 'find_similar_images_by_image'
            }
          } catch (error) {
            return {
              success: false,
              error: error instanceof Error ? error.message : 'Unknown error',
              executionTime: Date.now() - startTime,
              toolName: 'find_similar_images_by_image'
            }
          }
        }),

      // 工具5: 获取图片分析结果
      getImageAnalysis: publicProcedure
        .input((val: unknown) => {
          return val as {
            imageId?: string
            imagePath?: string
            includeEmbedding?: boolean
            includeStructuredData?: boolean
            reanalyze?: boolean
          }
        })
        .mutation(async ({ input: params }) => {
          const startTime = Date.now()
          const timeout = 30000
          
          try {
            const timeoutPromise = new Promise((_, reject) => {
              setTimeout(() => reject(new Error('Tool execution timeout (30s)')), timeout)
            })

            const analysisPromise = this.executeGetImageAnalysis(params)
            const result = await Promise.race([analysisPromise, timeoutPromise])
            
            return {
              success: true,
              data: result,
              executionTime: Date.now() - startTime,
              toolName: 'get_image_analysis'
            }
          } catch (error) {
            return {
              success: false,
              error: error instanceof Error ? error.message : 'Unknown error',
              executionTime: Date.now() - startTime,
              toolName: 'get_image_analysis'
            }
          }
        }),

      // 获取所有可用工具的定义
      getToolDefinitions: publicProcedure
        .query(async () => {
          try {
            return {
              success: true,
              tools: [
                {
                  name: 'analyze_image',
                  description: 'Analyze an image to extract detailed information including description, objects, colors, scene, mood, and other visual elements.',
                  parameters: {
                    imagePath: { type: 'string', description: 'Path to the image file' },
                    imageId: { type: 'string', description: 'ID of existing image' },
                    detailLevel: { type: 'string', enum: ['basic', 'detailed', 'comprehensive'], default: 'detailed' },
                    includeStructuredData: { type: 'boolean', default: true }
                  }
                },
                {
                  name: 'find_images_by_tags',
                  description: 'Search for images using specific tags with AND/OR logic.',
                  parameters: {
                    tags: { type: 'array', items: { type: 'string' }, description: 'List of tags to search for' },
                    limit: { type: 'number', default: 20, description: 'Maximum number of results' },
                    matchMode: { type: 'string', enum: ['any', 'all'], default: 'any' }
                  }
                },
                {
                  name: 'find_similar_images_by_description',
                  description: 'Search for images based on natural language descriptions using AI semantic understanding.',
                  parameters: {
                    description: { type: 'string', description: 'Natural language description of what to search for' },
                    limit: { type: 'number', default: 20 },
                    threshold: { type: 'number', default: 0.4, description: 'Similarity threshold (0-1)' }
                  }
                },
                {
                  name: 'find_similar_images_by_image',
                  description: 'Find images that are visually similar to a provided image using computer vision.',
                  parameters: {
                    imageId: { type: 'string', description: 'ID of existing image' },
                    imagePath: { type: 'string', description: 'Path to image file' },
                    limit: { type: 'number', default: 20 },
                    threshold: { type: 'number', default: 0.5 }
                  }
                },
                {
                  name: 'get_image_analysis',
                  description: 'Get existing analysis results for an image or perform new analysis.',
                  parameters: {
                    imageId: { type: 'string', description: 'ID of existing image' },
                    imagePath: { type: 'string', description: 'Path to image file' },
                    includeEmbedding: { type: 'boolean', default: false },
                    includeStructuredData: { type: 'boolean', default: true },
                    reanalyze: { type: 'boolean', default: false }
                  }
                }
              ]
            }
          } catch (error) {
            return {
              success: false,
              error: error instanceof Error ? error.message : 'Unknown error',
              tools: []
            }
          }
        })
    })
  }

  // ============= 私有方法：工具实现 =============

  /**
   * 执行图片分析
   */
  private async executeAnalyzeImage(params: any): Promise<any> {
    const { imagePath, imageId, detailLevel = 'detailed', includeStructuredData = true } = params

    if (imagePath) {
      // 使用路径分析图片
      const result = await this.aiAnalysisService.analyzeImageByPath(imagePath)
      return {
        success: true,
        description: result.description,
        tags: result.tags_flat || result.tags || [],
        structuredData: includeStructuredData ? result.structured_data : undefined,
        confidence: result.confidence || 0.9,
        analysisTime: new Date().toISOString(),
        model: result.model || 'AI-Analysis'
      }
    } else if (imageId) {
      // 通过imageId获取已有分析结果
      const images = await this.imageService.queryImages({ 
        query: imageId,
        limit: 1 
      })
      
      if (images.results.length > 0) {
        const image = images.results[0]
        return {
          success: true,
          description: image.description || 'No description available',
          tags: image.tags || [],
          structuredData: includeStructuredData ? image.metadata : undefined,
          confidence: 0.9,
          analysisTime: image.createdAt || new Date().toISOString(),
          model: 'Database-Cached'
        }
      } else {
        throw new Error(`Image with ID ${imageId} not found`)
      }
    } else {
      throw new Error('Either imagePath or imageId must be provided')
    }
  }
