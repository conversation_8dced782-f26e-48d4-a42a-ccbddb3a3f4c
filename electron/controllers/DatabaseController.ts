import { publicProcedure, router } from '../trpc/router'
import { DatabaseService } from '../services/DatabaseService'

export class DatabaseController {
  constructor(private databaseService: DatabaseService) {}

  getRouter() {
    return router({
      // 获取所有表名
      getAllTables: publicProcedure
        .query(async () => {
          try {
            return await this.databaseService.getAllTables()
          } catch (error) {
            return {
              success: false,
              error: error instanceof Error ? error.message : '获取表列表失败'
            }
          }
        }),

      // 获取表数据（支持分页）
      getTableData: publicProcedure
        .input((val: unknown) => {
          return val as {
            tableName: string
            page?: number
            pageSize?: number
          }
        })
        .query(async ({ input }) => {
          try {
            const { tableName, page = 1, pageSize = 20 } = input
            return await this.databaseService.getTableData(tableName, page, pageSize)
          } catch (error) {
            return {
              success: false,
              error: error instanceof Error ? error.message : '查询表数据失败'
            }
          }
        })
    })
  }
}
