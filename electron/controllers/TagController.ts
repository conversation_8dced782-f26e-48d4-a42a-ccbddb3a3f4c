import { publicProcedure, router } from '../trpc/router'
import { TagService } from '../services/TagService'
import { ImageCategory } from '@shared/types/database'

export class TagController {
  constructor(private tagService: TagService) {}

  getRouter() {
    return router({
      // 创建标签
      createTag: publicProcedure
        .input((val: unknown) => {
          return val as {
            tagText: string
            category?: ImageCategory
            frequency?: number
            metadata?: any
          }
        })
        .mutation(async ({ input }) => {
          try {
            const tagId = await this.tagService.createTag(input)
            return { success: true, tagId }
          } catch (error) {
            return { 
              success: false, 
              tagId: null,
              error: error instanceof Error ? error.message : 'Unknown error' 
            }
          }
        }),

      // 获取标签详情
      getTag: publicProcedure
        .input((val: unknown) => {
          if (typeof val !== 'string') throw new Error('Input must be a string')
          return val as string
        })
        .query(async ({ input: tagId }) => {
          try {
            const tag = await this.tagService.getTag(tagId)
            return { success: true, tag }
          } catch (error) {
            return { 
              success: false, 
              tag: null,
              error: error instanceof Error ? error.message : 'Unknown error' 
            }
          }
        }),

      // 根据文本获取标签
      getTagByText: publicProcedure
        .input((val: unknown) => {
          if (typeof val !== 'string') throw new Error('Input must be a string')
          return val as string
        })
        .query(async ({ input: tagText }) => {
          try {
            const tag = await this.tagService.getTagByText(tagText)
            return { success: true, tag }
          } catch (error) {
            return { 
              success: false, 
              tag: null,
              error: error instanceof Error ? error.message : 'Unknown error' 
            }
          }
        }),

      // 更新标签
      updateTag: publicProcedure
        .input((val: unknown) => {
          return val as {
            tagId: string
            updates: {
              tagText?: string
              category?: ImageCategory
              frequency?: number
              metadata?: string
              updatedAt?: number
            }
          }
        })
        .mutation(async ({ input }) => {
          try {
            const success = await this.tagService.updateTag(input.tagId, input.updates)
            return { success }
          } catch (error) {
            return { 
              success: false, 
              error: error instanceof Error ? error.message : 'Unknown error' 
            }
          }
        }),

      // 删除标签
      deleteTag: publicProcedure
        .input((val: unknown) => {
          if (typeof val !== 'string') throw new Error('Input must be a string')
          return val as string
        })
        .mutation(async ({ input: tagId }) => {
          try {
            const success = await this.tagService.deleteTag(tagId)
            return { success }
          } catch (error) {
            return { 
              success: false, 
              error: error instanceof Error ? error.message : 'Unknown error' 
            }
          }
        }),

      // 批量创建标签
      createTagsBatch: publicProcedure
        .input((val: unknown) => {
          if (!Array.isArray(val)) throw new Error('Input must be an array')
          return val as string[]
        })
        .mutation(async ({ input: tagTexts }) => {
          try {
            return await this.tagService.createTagsBatch(tagTexts)
          } catch (error) {
            return { 
              success: false, 
              createdCount: 0,
              existingCount: 0,
              tagIds: [],
              error: error instanceof Error ? error.message : 'Unknown error' 
            }
          }
        }),

      // 获取所有标签
      getAllTags: publicProcedure
        .input((val: unknown) => {
          return val as {
            category?: ImageCategory
            limit?: number
            offset?: number
            sortBy?: 'tagText' | 'frequency' | 'createdAt'
            sortOrder?: 'asc' | 'desc'
          } | undefined
        })
        .query(async ({ input: params }) => {
          try {
            const tags = await this.tagService.getAllTags(params)
            return { success: true, tags }
          } catch (error) {
            return { 
              success: false, 
              tags: [],
              error: error instanceof Error ? error.message : 'Unknown error' 
            }
          }
        }),

      // 获取热门标签
      getPopularTags: publicProcedure
        .input((val: unknown) => {
          return val as {
            limit?: number
            minFrequency?: number
          } | undefined
        })
        .query(async ({ input: params }) => {
          try {
            const tags = await this.tagService.getPopularTags(
              params?.limit,
              params?.minFrequency
            )
            return { success: true, tags }
          } catch (error) {
            return { 
              success: false, 
              tags: [],
              error: error instanceof Error ? error.message : 'Unknown error' 
            }
          }
        }),

      // 按分类获取标签
      getTagsByCategory: publicProcedure
        .input((val: unknown) => {
          return val as {
            category: ImageCategory
            limit?: number
          }
        })
        .query(async ({ input: params }) => {
          try {
            const tags = await this.tagService.getTagsByCategory(
              params.category,
              params.limit
            )
            return { success: true, tags }
          } catch (error) {
            return { 
              success: false, 
              tags: [],
              error: error instanceof Error ? error.message : 'Unknown error' 
            }
          }
        }),

      // 搜索标签
      searchTags: publicProcedure
        .input((val: unknown) => {
          return val as {
            query: string
            limit?: number
          }
        })
        .query(async ({ input: params }) => {
          try {
            const tags = await this.tagService.searchTags(params.query, params.limit)
            return { success: true, tags }
          } catch (error) {
            return { 
              success: false, 
              tags: [],
              error: error instanceof Error ? error.message : 'Unknown error' 
            }
          }
        }),

      // 查找相似标签
      findSimilarTags: publicProcedure
        .input((val: unknown) => {
          return val as {
            query: string
            threshold?: number
            limit?: number
          }
        })
        .query(async ({ input: params }) => {
          try {
            const tags = await this.tagService.findSimilarTags(
              params.query,
              params.threshold,
              params.limit
            )
            return { success: true, tags }
          } catch (error) {
            return { 
              success: false, 
              tags: [],
              error: error instanceof Error ? error.message : 'Unknown error' 
            }
          }
        }),

      // 根据向量查找相似标签
      findSimilarTagsByVector: publicProcedure
        .input((val: unknown) => {
          return val as {
            embedding: number[]
            threshold?: number
            limit?: number
          }
        })
        .query(async ({ input: params }) => {
          try {
            const tags = await this.tagService.findSimilarTagsByVector(
              params.embedding,
              params.threshold,
              params.limit
            )
            return { success: true, tags }
          } catch (error) {
            return { 
              success: false, 
              tags: [],
              error: error instanceof Error ? error.message : 'Unknown error' 
            }
          }
        }),

      // 扩展标签集合
      expandTagsWithSimilar: publicProcedure
        .input((val: unknown) => {
          return val as {
            tagIds: string[]
            threshold?: number
            limit?: number
          }
        })
        .query(async ({ input: params }) => {
          try {
            const tags = await this.tagService.expandTagsWithSimilar(
              params.tagIds,
              params.threshold,
              params.limit
            )
            return { success: true, tags }
          } catch (error) {
            return { 
              success: false, 
              tags: [],
              error: error instanceof Error ? error.message : 'Unknown error' 
            }
          }
        }),

      // 增加标签使用频率
      incrementTagUsage: publicProcedure
        .input((val: unknown) => {
          return val as {
            tagId: string
            increment?: number
          }
        })
        .mutation(async ({ input: params }) => {
          try {
            const success = await this.tagService.incrementTagUsage(
              params.tagId,
              params.increment
            )
            return { success }
          } catch (error) {
            return { 
              success: false, 
              error: error instanceof Error ? error.message : 'Unknown error' 
            }
          }
        }),

      // 批量增加标签使用频率
      incrementTagsUsage: publicProcedure
        .input((val: unknown) => {
          if (!Array.isArray(val)) throw new Error('Input must be an array')
          return val as string[]
        })
        .mutation(async ({ input: tagIds }) => {
          try {
            return await this.tagService.incrementTagsUsage(tagIds)
          } catch (error) {
            return { 
              success: false, 
              updatedCount: 0,
              error: error instanceof Error ? error.message : 'Unknown error' 
            }
          }
        }),

      // 处理图片标签
      processImageTags: publicProcedure
        .input((val: unknown) => {
          if (!Array.isArray(val)) throw new Error('Input must be an array')
          return val as string[]
        })
        .mutation(async ({ input: tagTexts }) => {
          try {
            const tagIds = await this.tagService.processImageTags(tagTexts)
            return { success: true, tagIds }
          } catch (error) {
            return { 
              success: false, 
              tagIds: [],
              error: error instanceof Error ? error.message : 'Unknown error' 
            }
          }
        }),

      // 获取标签总数
      getTotalTagCount: publicProcedure
        .query(async () => {
          try {
            const count = await this.tagService.getTotalTagCount()
            return { success: true, count }
          } catch (error) {
            return { 
              success: false, 
              count: 0,
              error: error instanceof Error ? error.message : 'Unknown error' 
            }
          }
        }),

      // 获取标签分类统计
      getTagCategoryStats: publicProcedure
        .query(async () => {
          try {
            const stats = await this.tagService.getTagCategoryStats()
            return { success: true, stats }
          } catch (error) {
            return { 
              success: false, 
              stats: [],
              error: error instanceof Error ? error.message : 'Unknown error' 
            }
          }
        }),

      // 获取标签云数据
      getTagCloud: publicProcedure
        .input((val: unknown) => {
          return val as { limit?: number } | undefined
        })
        .query(async ({ input: params }) => {
          try {
            const tagCloud = await this.tagService.getTagCloud(params?.limit)
            return { success: true, tagCloud }
          } catch (error) {
            return { 
              success: false, 
              tagCloud: [],
              error: error instanceof Error ? error.message : 'Unknown error' 
            }
          }
        }),

      // 清理未使用的标签
      cleanupUnusedTags: publicProcedure
        .mutation(async () => {
          try {
            return await this.tagService.cleanupUnusedTags()
          } catch (error) {
            return { 
              success: false, 
              deletedCount: 0,
              error: error instanceof Error ? error.message : 'Unknown error' 
            }
          }
        }),

      // 重新计算标签频率
      recalculateTagFrequencies: publicProcedure
        .mutation(async () => {
          try {
            return await this.tagService.recalculateTagFrequencies()
          } catch (error) {
            return { 
              success: false, 
              updatedCount: 0,
              error: error instanceof Error ? error.message : 'Unknown error' 
            }
          }
        }),

      // 合并重复标签
      mergeDuplicateTags: publicProcedure
        .mutation(async () => {
          try {
            return await this.tagService.mergeDuplicateTags()
          } catch (error) {
            return { 
              success: false, 
              mergedCount: 0,
              error: error instanceof Error ? error.message : 'Unknown error' 
            }
          }
        }),

      // 清空所有标签
      clearAllTags: publicProcedure
        .mutation(async () => {
          try {
            return await this.tagService.clearAll()
          } catch (error) {
            return { 
              success: false, 
              error: error instanceof Error ? error.message : 'Unknown error' 
            }
          }
        }),

      // 测试连接
      testConnection: publicProcedure
        .query(async () => {
          try {
            const connected = await this.tagService.testConnection()
            return { success: true, connected }
          } catch (error) {
            return { 
              success: false, 
              connected: false,
              error: error instanceof Error ? error.message : 'Unknown error' 
            }
          }
        })
    })
  }
}