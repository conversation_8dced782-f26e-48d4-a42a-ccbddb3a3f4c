import { publicProcedure, router } from '../trpc/router'
import { ImageService } from '../services/ImageService'

export class ImageController {
  constructor(private imageService: ImageService) {}

  getRouter() {
    return router({
      // 查询图片
      queryImages: publicProcedure
        .input((val: unknown) => {
          return val as {
            query?: string
            tags?: string[]
            limit?: number
            offset?: number
            sortBy?: string
            sortOrder?: 'asc' | 'desc'
            dateRange?: { start: number; end: number }
            metadata?: Record<string, any>
          }
        })
        .query(async ({ input: params }) => {
          try {
            return await this.imageService.queryImages(params)
          } catch (error) {
            return { 
              results: [],
              total: 0, 
              error: error instanceof Error ? error.message : 'Unknown error' 
            }
          }
        }),

      // 插入图片
      insertImages: publicProcedure
        .input((val: unknown) => {
          if (!Array.isArray(val)) throw new Error('Input must be an array')
          return val as Array<{
            id: string
            filePath: string
            fileName: string
            description?: string
            tags?: string
            tagsFlat?: string
            metadata?: string
            structuredMetadata?: string
            descriptionVector?: number[]
            createdAt: number
            updatedAt: number
          }>
        })
        .mutation(async ({ input: images }) => {
          try {
            return await this.imageService.insertImagesBatch(images)
          } catch (error) {
            return { 
              success: false, 
              insertedCount: 0,
              error: error instanceof Error ? error.message : 'Unknown error' 
            }
          }
        }),

      // 检查图片是否存在
      checkImageExists: publicProcedure
        .input((val: unknown) => {
          if (typeof val !== 'object' || val === null || !('filePath' in val)) {
            throw new Error('Input must be an object with filePath')
          }
          return val as { filePath: string; md5?: string }
        })
        .query(async ({ input }) => {
          try {
            return await this.imageService.checkImageExists(input.filePath, input.md5)
          } catch (error) {
            return { 
              success: false, 
              exists: false, 
              error: error instanceof Error ? error.message : 'Unknown error' 
            }
          }
        }),

      // 智能搜索
      smartSearch: publicProcedure
        .input((val: unknown) => {
          return val as {
            query: string
            limit?: number
            threshold?: number
          }
        })
        .query(async ({ input: params }) => {
          try {
            const result = await this.imageService.smartSearch(params.query)
            return {
              query: params.query,
              results: result.results,
              totalResults: result.total,
              error: undefined
            }
          } catch (error) {
            return {
              query: params.query,
              results: [],
              totalResults: 0,
              error: error instanceof Error ? error.message : 'Unknown error'
            }
          }
        }),

      // 向量搜索
      vectorSearch: publicProcedure
        .input((val: unknown) => {
          return val as {
            embedding: number[]
            threshold?: number
            limit?: number
          }
        })
        .query(async ({ input: params }) => {
          try {
            return await this.imageService.vectorSearchImages(
              params.embedding, 
              params.threshold, 
              params.limit
            )
          } catch (error) {
            return { 
              images: [], 
              total: 0, 
              error: error instanceof Error ? error.message : 'Unknown error' 
            }
          }
        }),

      // 查找相似图片
      findSimilarImages: publicProcedure
        .input((val: unknown) => {
          return val as {
            imageId: string
            threshold?: number
            limit?: number
          }
        })
        .query(async ({ input: params }) => {
          try {
            return await this.imageService.findSimilarImages(
              params.imageId, 
              params.threshold, 
              params.limit
            )
          } catch (error) {
            return { 
              images: [], 
              total: 0, 
              error: error instanceof Error ? error.message : 'Unknown error' 
            }
          }
        }),

      // 根据ID获取图片
      getImageById: publicProcedure
        .input((val: unknown) => {
          if (typeof val !== 'string') throw new Error('Input must be a string')
          return val as string
        })
        .query(async ({ input: id }) => {
          try {
            const image = await this.imageService.getImageById(id)
            return { success: true, image }
          } catch (error) {
            return { 
              success: false, 
              image: null, 
              error: error instanceof Error ? error.message : 'Unknown error' 
            }
          }
        }),

      // 根据路径获取图片
      getImageByPath: publicProcedure
        .input((val: unknown) => {
          if (typeof val !== 'string') throw new Error('Input must be a string')
          return val as string
        })
        .query(async ({ input: filePath }) => {
          try {
            const image = await this.imageService.getImageByPath(filePath)
            return { success: true, image }
          } catch (error) {
            return { 
              success: false, 
              image: null, 
              error: error instanceof Error ? error.message : 'Unknown error' 
            }
          }
        }),

      // 更新图片
      updateImage: publicProcedure
        .input((val: unknown) => {
          return val as {
            id: string
            updates: {
              description?: string
              tags?: string
              tagsFlat?: string
              metadata?: string
              structuredMetadata?: string
              descriptionVector?: number[]
              updatedAt?: number
            }
          }
        })
        .mutation(async ({ input }) => {
          try {
            const success = await this.imageService.updateImage(input.id, input.updates)
            return { success }
          } catch (error) {
            return { 
              success: false, 
              error: error instanceof Error ? error.message : 'Unknown error' 
            }
          }
        }),

      // 删除图片
      deleteImage: publicProcedure
        .input((val: unknown) => {
          if (typeof val !== 'string') throw new Error('Input must be a string')
          return val as string
        })
        .mutation(async ({ input: id }) => {
          try {
            const success = await this.imageService.deleteImage(id)
            return { success }
          } catch (error) {
            return { 
              success: false, 
              error: error instanceof Error ? error.message : 'Unknown error' 
            }
          }
        }),

      // 处理并存储图片
      processAndStoreImage: publicProcedure
        .input((val: unknown) => {
          return val as {
            imagePath: string
            filename?: string
            metadata?: any
          }
        })
        .mutation(async ({ input }) => {
          try {
            const imageId = await this.imageService.processAndStoreImage(
              input.imagePath, 
              input.metadata
            )
            return { success: true, imageId }
          } catch (error) {
            return { 
              success: false, 
              imageId: null,
              error: error instanceof Error ? error.message : 'Unknown error' 
            }
          }
        }),

      // 按地理位置查询图片
      queryImagesByLocation: publicProcedure
        .input((val: unknown) => {
          return val as {
            latitude: number
            longitude: number
            radius?: number
            limit?: number
          }
        })
        .query(async ({ input: params }) => {
          try {
            const images = await this.imageService.queryImagesByLocation(
              params.latitude, 
              params.longitude, 
              params.radius, 
              params.limit
            )
            return { success: true, images }
          } catch (error) {
            return { 
              success: false, 
              images: [],
              error: error instanceof Error ? error.message : 'Unknown error' 
            }
          }
        }),

      // 按地点名称查询图片
      queryImagesByLocationName: publicProcedure
        .input((val: unknown) => {
          return val as {
            location: string
            limit?: number
          }
        })
        .query(async ({ input: params }) => {
          try {
            const images = await this.imageService.queryImagesByLocationName(
              params.location, 
              params.limit
            )
            return { success: true, images }
          } catch (error) {
            return { 
              success: false, 
              images: [],
              error: error instanceof Error ? error.message : 'Unknown error' 
            }
          }
        }),

      // 按时间范围查询图片
      queryImagesByTimeRange: publicProcedure
        .input((val: unknown) => {
          return val as {
            startTime: number
            endTime: number
            limit?: number
          }
        })
        .query(async ({ input: params }) => {
          try {
            const images = await this.imageService.queryImagesByTimeRange(
              params.startTime, 
              params.endTime, 
              params.limit
            )
            return { success: true, images }
          } catch (error) {
            return { 
              success: false, 
              images: [],
              error: error instanceof Error ? error.message : 'Unknown error' 
            }
          }
        }),

      // 获取图片总数
      getTotalImageCount: publicProcedure
        .query(async () => {
          try {
            const count = await this.imageService.getTotalImageCount()
            return { success: true, count }
          } catch (error) {
            return { 
              success: false, 
              count: 0,
              error: error instanceof Error ? error.message : 'Unknown error' 
            }
          }
        }),

      // 清空所有图片
      clearAllImages: publicProcedure
        .mutation(async () => {
          try {
            const result = await this.imageService.clearAllImages()
            return result
          } catch (error) {
            return { 
              success: false, 
              error: error instanceof Error ? error.message : 'Unknown error' 
            }
          }
        }),

      // 测试连接
      testConnection: publicProcedure
        .query(async () => {
          try {
            const connected = await this.imageService.testConnection()
            return { success: true, connected }
          } catch (error) {
            return { 
              success: false, 
              connected: false,
              error: error instanceof Error ? error.message : 'Unknown error' 
            }
          }
        })
    })
  }
}