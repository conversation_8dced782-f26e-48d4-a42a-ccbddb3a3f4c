import { publicProcedure, router } from '../trpc/router'
import { LibraryService } from '../services/LibraryService'
import { dialog } from 'electron'

export class LibraryController {
  constructor(private libraryService: LibraryService) {}

  getRouter() {
    return router({
      // 创建图片库
      createLibrary: publicProcedure
        .input((val: unknown) => {
          return val as {
            name: string
            rootPath: string
            type?: 'local' | 'cloud' | 'network'
            description?: string
            settings?: {
              recursive?: boolean
              includeHidden?: boolean
              maxDepth?: number
              supportedFormats?: string[]
              excludePatterns?: string[]
              autoScanInterval?: number
            }
          }
        })
        .mutation(async ({ input }) => {
          try {
            const libraryId = await this.libraryService.createLibrary(input)
            return { success: true, libraryId, error: null }
          } catch (error) {
            return { 
              success: false, 
              libraryId: null,
              error: error instanceof Error ? error.message : 'Unknown error' 
            }
          }
        }),

      // 获取图片库详情
      getLibrary: publicProcedure
        .input((val: unknown) => {
          if (typeof val !== 'string') throw new Error('Input must be a string')
          return val as string
        })
        .query(async ({ input: id }) => {
          try {
            const library = await this.libraryService.getLibrary(id)
            return { success: true, library, error: null }
          } catch (error) {
            return { 
              success: false, 
              library: null,
              error: error instanceof Error ? error.message : 'Unknown error' 
            }
          }
        }),

      // 获取所有图片库
      getAllLibraries: publicProcedure
        .query(async () => {
          try {
            const libraries = await this.libraryService.getAllLibraries()
            return { success: true, libraries, error: null }
          } catch (error) {
            return { 
              success: false, 
              libraries: [],
              error: error instanceof Error ? error.message : 'Unknown error' 
            }
          }
        }),

      // 获取活跃的图片库
      getActiveLibraries: publicProcedure
        .query(async () => {
          try {
            const libraries = await this.libraryService.getActiveLibraries()
            return { success: true, libraries, error: null }
          } catch (error) {
            return { 
              success: false, 
              libraries: [],
              error: error instanceof Error ? error.message : 'Unknown error' 
            }
          }
        }),

      // 更新图片库
      updateLibrary: publicProcedure
        .input((val: unknown) => {
          return val as {
            id: string
            updates: {
              name?: string
              rootPath?: string
              type?: 'local' | 'cloud' | 'network'
              status?: 'active' | 'offline' | 'removed'
              description?: string
              settings?: string
              updatedAt?: string
            }
          }
        })
        .mutation(async ({ input }) => {
          try {
            return await this.libraryService.updateLibrary(input.id, input.updates)
          } catch (error) {
            return { 
              success: false, 
              error: error instanceof Error ? error.message : 'Unknown error' 
            }
          }
        }),

      // 删除图片库
      deleteLibrary: publicProcedure
        .input((val: unknown) => {
          if (typeof val !== 'string') throw new Error('Input must be a string')
          return val as string
        })
        .mutation(async ({ input: id }) => {
          try {
            return await this.libraryService.deleteLibrary(id)
          } catch (error) {
            return { 
              success: false, 
              error: error instanceof Error ? error.message : 'Unknown error' 
            }
          }
        }),

      // 更新图片库名称
      updateLibraryName: publicProcedure
        .input((val: unknown) => {
          return val as {
            id: string
            newName: string
          }
        })
        .mutation(async ({ input }) => {
          try {
            return await this.libraryService.updateLibraryName(input.id, input.newName)
          } catch (error) {
            return { 
              success: false, 
              error: error instanceof Error ? error.message : 'Unknown error' 
            }
          }
        }),

      // 查询图片库
      queryLibraries: publicProcedure
        .input((val: unknown) => {
          return val as {
            name?: string
            status?: 'active' | 'offline' | 'removed'
            type?: 'local' | 'cloud' | 'network'
            limit?: number
            offset?: number
            sortBy?: string
            sortOrder?: 'asc' | 'desc'
          } | undefined
        })
        .query(async ({ input: params }) => {
          try {
            const result = await this.libraryService.queryLibraries(params || {})
            return result
          } catch (error) {
            return { 
              success: false, 
              libraries: [],
              total: 0,
              error: error instanceof Error ? error.message : 'Unknown error' 
            }
          }
        }),

      // 按状态获取图片库
      getLibrariesByStatus: publicProcedure
        .input((val: unknown) => {
          return val as { status: 'active' | 'offline' | 'removed' }
        })
        .query(async ({ input }) => {
          try {
            const libraries = await this.libraryService.getLibrariesByStatus(input.status)
            return { success: true, libraries, error: null }
          } catch (error) {
            return { 
              success: false, 
              libraries: [],
              error: error instanceof Error ? error.message : 'Unknown error' 
            }
          }
        }),

      // 按类型获取图片库
      getLibrariesByType: publicProcedure
        .input((val: unknown) => {
          return val as { type: 'local' | 'cloud' | 'network' }
        })
        .query(async ({ input }) => {
          try {
            const libraries = await this.libraryService.getLibrariesByType(input.type)
            return { success: true, libraries, error: null }
          } catch (error) {
            return { 
              success: false, 
              libraries: [],
              error: error instanceof Error ? error.message : 'Unknown error' 
            }
          }
        }),

      // 根据路径获取图片库
      getLibraryByPath: publicProcedure
        .input((val: unknown) => {
          if (typeof val !== 'string') throw new Error('Input must be a string')
          return val as string
        })
        .query(async ({ input: rootPath }) => {
          try {
            const library = await this.libraryService.getLibraryByPath(rootPath)
            return { success: true, library, error: null }
          } catch (error) {
            return { 
              success: false, 
              library: null,
              error: error instanceof Error ? error.message : 'Unknown error' 
            }
          }
        }),

      // 更新扫描进度
      updateScanProgress: publicProcedure
        .input((val: unknown) => {
          return val as {
            id: string
            progress: {
              total?: number
              processed?: number
              failed?: number
              status?: string
              lastScannedPath?: string
              estimatedTimeRemaining?: number
            }
          }
        })
        .mutation(async ({ input }) => {
          try {
            return await this.libraryService.updateScanProgress(input.id, input.progress)
          } catch (error) {
            return { 
              success: false, 
              error: error instanceof Error ? error.message : 'Unknown error' 
            }
          }
        }),

      // 开始扫描
      startScan: publicProcedure
        .input((val: unknown) => {
          if (typeof val !== 'string') throw new Error('Input must be a string')
          return val as string
        })
        .mutation(async ({ input: id }) => {
          try {
            return await this.libraryService.startScan(id)
          } catch (error) {
            return { 
              success: false, 
              error: error instanceof Error ? error.message : 'Unknown error' 
            }
          }
        }),

      // 获取需要扫描的图片库
      getLibrariesForScan: publicProcedure
        .input((val: unknown) => {
          return val as { maxIdleHours?: number } | undefined
        })
        .query(async ({ input: params }) => {
          try {
            const libraries = await this.libraryService.getLibrariesForScan(params?.maxIdleHours)
            return { success: true, libraries, error: null }
          } catch (error) {
            return { 
              success: false, 
              libraries: [],
              error: error instanceof Error ? error.message : 'Unknown error' 
            }
          }
        }),

      // 获取统计摘要
      getStatisticsSummary: publicProcedure
        .query(async () => {
          try {
            const summary = await this.libraryService.getStatisticsSummary()
            return { success: true, summary, error: null }
          } catch (error) {
            return { 
              success: false, 
              summary: null,
              error: error instanceof Error ? error.message : 'Unknown error' 
            }
          }
        }),

      // 计算图片库大小
      calculateLibrarySize: publicProcedure
        .input((val: unknown) => {
          if (typeof val !== 'string') throw new Error('Input must be a string')
          return val as string
        })
        .query(async ({ input: id }) => {
          try {
            const size = await this.libraryService.calculateLibrarySize(id)
            return { success: true, ...size, error: null }
          } catch (error) {
            return { 
              success: false, 
              totalSize: 0,
              fileCount: 0,
              error: error instanceof Error ? error.message : 'Unknown error' 
            }
          }
        }),

      // 更新设置
      updateSettings: publicProcedure
        .input((val: unknown) => {
          return val as {
            id: string
            settings: {
              recursive?: boolean
              includeHidden?: boolean
              maxDepth?: number
              supportedFormats?: string[]
              excludePatterns?: string[]
              autoScanInterval?: number
            }
          }
        })
        .mutation(async ({ input }) => {
          try {
            return await this.libraryService.updateSettings(input.id, input.settings)
          } catch (error) {
            return { 
              success: false, 
              error: error instanceof Error ? error.message : 'Unknown error' 
            }
          }
        }),

      // 验证图片库路径
      validateLibraryPath: publicProcedure
        .input((val: unknown) => {
          if (typeof val !== 'string') throw new Error('Input must be a string')
          return val as string
        })
        .query(async ({ input: rootPath }) => {
          try {
            const validation = await this.libraryService.validateLibraryPath(rootPath)
            return { success: true, ...validation, error: null }
          } catch (error) {
            return { 
              success: false,
              valid: false,
              exists: false,
              accessible: false,
              error: error instanceof Error ? error.message : 'Unknown error' 
            }
          }
        }),

      // 验证图片库
      validateLibrary: publicProcedure
        .input((val: unknown) => {
          if (typeof val !== 'string') throw new Error('Input must be a string')
          return val as string
        })
        .query(async ({ input: id }) => {
          try {
            const validation = await this.libraryService.validateLibrary(id)
            return { success: true, ...validation, error: null }
          } catch (error) {
            return { 
              success: false,
              valid: false,
              pathValid: false,
              accessible: false,
              error: error instanceof Error ? error.message : 'Unknown error' 
            }
          }
        }),

      // 批量验证所有图片库
      validateAllLibraries: publicProcedure
        .query(async () => {
          try {
            const validations = await this.libraryService.validateAllLibraries()
            return { success: true, validations, error: null }
          } catch (error) {
            return { 
              success: false, 
              validations: [],
              error: error instanceof Error ? error.message : 'Unknown error' 
            }
          }
        }),

      // 清理无效图片库
      cleanupInvalidLibraries: publicProcedure
        .mutation(async () => {
          try {
            const result = await this.libraryService.cleanupInvalidLibraries()
            return result
          } catch (error) {
            return { 
              success: false, 
              error: error instanceof Error ? error.message : 'Unknown error' 
            }
          }
        }),

      // 清空所有图片库
      clearAll: publicProcedure
        .mutation(async () => {
          try {
            return await this.libraryService.clearAll()
          } catch (error) {
            return { 
              success: false, 
              error: error instanceof Error ? error.message : 'Unknown error' 
            }
          }
        }),

      // 测试连接
      testConnection: publicProcedure
        .query(async () => {
          try {
            const connected = await this.libraryService.testConnection()
            return { success: true, connected, error: null }
          } catch (error) {
            return { 
              success: false, 
              connected: false,
              error: error instanceof Error ? error.message : 'Unknown error' 
            }
          }
        }),

      // 选择文件夹
      selectFolder: publicProcedure
        .mutation(async () => {
          try {
            const result = await dialog.showOpenDialog({
              properties: ['openDirectory'],
              title: '选择文件夹'
            })

            if (result.canceled) {
              return { 
                success: false, 
                folderPath: null, 
                canceled: true,
                error: null 
              }
            }

            return { 
              success: true, 
              folderPath: result.filePaths[0], 
              canceled: false,
              error: null 
            }
          } catch (error) {
            return { 
              success: false, 
              folderPath: null, 
              canceled: false,
              error: error instanceof Error ? error.message : 'Unknown error' 
            }
          }
        }),

      // 分析文件夹
      analyzeFolder: publicProcedure
        .input((val: unknown) => {
          return val as {
            folderPath: string
            options?: {
              maxDepth?: number
              includeHidden?: boolean
              recursive?: boolean
              supportedFormats?: string[]
            }
          }
        })
        .mutation(async ({ input }) => {
          try {
            const result = await this.libraryService.analyzeFolder(input.folderPath, input.options)
            return { 
              success: true, 
              data: result, 
              error: null 
            }
          } catch (error) {
            return { 
              success: false, 
              data: null,
              error: error instanceof Error ? error.message : 'Unknown error' 
            }
          }
        })
    })
  }
}