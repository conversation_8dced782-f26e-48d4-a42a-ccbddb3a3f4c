module.exports = {
  root: true,
  env: { browser: true, es2020: true },
  extends: [
    'eslint:recommended',
    'plugin:@typescript-eslint/recommended',
    'plugin:react-hooks/recommended',
  ],
  ignorePatterns: ['dist', '.eslintrc.cjs'],
  parser: '@typescript-eslint/parser',
  plugins: ['react-refresh'],
  rules: {
    'react-refresh/only-export-components': [
      'warn',
      { allowConstantExport: true },
    ],
    '@typescript-eslint/no-unused-vars': 'off',
    '@typescript-eslint/no-explicit-any': 'off',
    '@typescript-eslint/no-empty-object-type': 'off',
    '@typescript-eslint/no-require-imports': 'off',
    '@typescript-eslint/triple-slash-reference': 'off',
    'no-case-declarations': 'off',
    'no-prototype-builtins': 'off',
    'prefer-const': 'warn',
    'no-control-regex': 'off',
    'no-useless-escape': 'off',
    'no-extra-semi': 'off',
    'no-constant-condition': 'off',
    'no-unexpected-multiline': 'off',
    '@typescript-eslint/no-unused-expressions': 'off'
  },
}
