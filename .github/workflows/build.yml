name: Build and Release

on:
  push:
    tags:
      - 'v*'
  workflow_dispatch:

jobs:
  build:
    runs-on: ${{ matrix.os }}
    strategy:
      matrix:
        os: [windows-latest, macos-latest]
    
    env:
      npm_config_msvs_version: 2022
      npm_config_python: python3

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'

      - name: Enable Yarn
        run: corepack enable

      - name: Install Yarn
        run: |
          corepack prepare yarn@stable --activate
          yarn --version

      - name: Verify files (Windows)
        if: matrix.os == 'windows-latest'
        run: |
          if (Test-Path yarn.lock) { Write-Host "yarn.lock found" } else { Write-Host "yarn.lock not found" }
        shell: powershell

      - name: Verify files (macOS)
        if: matrix.os == 'macos-latest'
        run: |
          ls -la yarn.lock || echo "yarn.lock not found"

      - name: Install Python (for native modules)
        uses: actions/setup-python@v4
        with:
          python-version: '3.11'

      - name: Setup Visual Studio Build Tools (Windows)
        if: matrix.os == 'windows-latest'
        uses: microsoft/setup-msbuild@v1.1

      - name: Setup build tools on Windows
        if: matrix.os == 'windows-latest'
        run: |
          yarn global add node-gyp
          yarn config set msvs_version 2022
          yarn config set python python3
        env:
          npm_config_msvs_version: 2022

      - name: Setup build tools on macOS
        if: matrix.os == 'macos-latest'
        run: |
          yarn global add node-gyp

      - name: Install dependencies
        run: yarn install --ignore-engines

      - name: Rebuild native modules for Electron
        run: |
          yarn rebuild
          echo "Native modules rebuilt for Electron"

      - name: Build Electron application
        run: yarn build:electron
        env:
          GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          NODE_ENV: production

      - name: Upload Windows artifacts
        if: matrix.os == 'windows-latest'
        uses: actions/upload-artifact@v4
        with:
          name: windows-build
          path: dist/*.exe

      - name: Upload macOS artifacts
        if: matrix.os == 'macos-latest'
        uses: actions/upload-artifact@v4
        with:
          name: macos-build
          path: dist/*.dmg

  release:
    needs: build
    runs-on: ubuntu-latest
    if: startsWith(github.ref, 'refs/tags/')
    
    steps:
      - name: Download Windows artifacts
        uses: actions/download-artifact@v4
        with:
          name: windows-build
          path: ./windows

      - name: Download macOS artifacts
        uses: actions/download-artifact@v4
        with:
          name: macos-build
          path: ./macos

      - name: Create Release
        uses: softprops/action-gh-release@v1
        with:
          files: |
            windows/*
            macos/*
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}