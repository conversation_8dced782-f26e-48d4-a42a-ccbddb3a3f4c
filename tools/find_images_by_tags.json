{"name": "find_images_by_tags", "description": "Search for images using specific tags with AND/OR logic for precise or broad matching. Use this tool when users mention specific tags, categories, or when they say things like 'find images tagged with...', 'show me pictures with tags...', 'filter by tags...', or 'images categorized as...'. Supports multi-tag combinations for filtering by classification, theme, or specific features.", "parameters": {"type": "object", "properties": {"tags": {"type": "array", "description": "List of tags to search for. Extract relevant keywords and tags from user's request. Supports both English and Chinese tags like ['animals', 'cute'], ['landscape', 'mountains'], ['people', 'smiling']. More specific tags yield more precise results.", "items": {"type": "string", "minLength": 1, "maxLength": 50}, "minItems": 1, "maxItems": 10, "uniqueItems": true}, "limit": {"type": "number", "description": "Maximum number of results to return. Extract from user language: 'a few'=5-8, 'some'=10-15, 'many'=20-30, 'lots'=40-50. Default is 20 if not specified.", "default": 20, "minimum": 1, "maximum": 100}, "matchMode": {"type": "string", "description": "Tag matching mode. Extract from user language: 'any'=OR logic (matches any tag, more results), 'all'=AND logic (matches all tags, more precise). Use 'all' when user says 'with both', 'having all', 'must have all'. Use 'any' when user says 'or', 'either', 'any of'. Default is 'any'.", "enum": ["any", "all"], "default": "any"}}, "required": ["tags"], "additionalProperties": false}, "returns": {"type": "object", "properties": {"success": {"type": "boolean", "description": "操作是否成功"}, "results": {"type": "array", "description": "匹配的图片列表", "items": {"type": "object", "properties": {"id": {"type": "string", "description": "图片ID"}, "url": {"type": "string", "description": "图片URL"}, "title": {"type": "string", "description": "图片标题"}, "description": {"type": "string", "description": "图片描述"}, "tags": {"type": "array", "items": {"type": "string"}, "description": "图片标签"}, "uploadTime": {"type": "string", "description": "上传时间"}, "fileSize": {"type": "string", "description": "文件大小"}, "matchedTags": {"type": "array", "items": {"type": "string"}, "description": "匹配的标签"}, "tagMatchCount": {"type": "number", "description": "匹配的标签数量"}}}}, "total": {"type": "number", "description": "找到的匹配图片总数"}, "searchInfo": {"type": "object", "description": "搜索信息", "properties": {"queryTags": {"type": "array", "items": {"type": "string"}, "description": "查询的标签"}, "expandedTags": {"type": "array", "items": {"type": "string"}, "description": "扩展的相似标签"}, "logic": {"type": "string", "description": "使用的逻辑关系"}, "sortBy": {"type": "string", "description": "排序方式"}, "includedSimilarTags": {"type": "boolean", "description": "是否包含了相似标签"}}}, "tagStats": {"type": "object", "description": "标签统计信息", "properties": {"availableTags": {"type": "array", "items": {"type": "string"}, "description": "数据库中可用的相关标签"}, "tagFrequency": {"type": "object", "description": "标签频率统计"}}}, "error": {"type": "string", "description": "错误信息（如果操作失败）"}}}, "examples": [{"description": "搜索包含'狗'或'猫'标签的图片", "input": {"tags": ["狗", "猫"], "logic": "OR", "limit": 5, "sortBy": "uploadTime"}, "output": {"success": true, "results": [{"id": "img_pet_001", "url": "/api/image/pets/dog_cat.jpg", "title": "狗和猫的友谊", "description": "一只狗和一只猫在一起玩耍", "tags": ["狗", "猫", "宠物", "友谊", "可爱"], "uploadTime": "2024-01-15", "fileSize": "2.1 MB", "matchedTags": ["狗", "猫"], "tagMatchCount": 2}, {"id": "img_dog_003", "url": "/api/image/pets/golden_retriever.jpg", "title": "金毛犬", "description": "一只美丽的金毛犬", "tags": ["狗", "金毛", "宠物"], "uploadTime": "2024-01-14", "fileSize": "1.9 MB", "matchedTags": ["狗"], "tagMatchCount": 1}], "total": 2, "searchInfo": {"queryTags": ["狗", "猫"], "expandedTags": [], "logic": "OR", "sortBy": "uploadTime", "includedSimilarTags": false}, "tagStats": {"availableTags": ["狗", "猫", "宠物", "动物", "可爱"], "tagFrequency": {"狗": 15, "猫": 12, "宠物": 20}}}}, {"description": "搜索同时包含'风景'和'山脉'标签的图片", "input": {"tags": ["风景", "山脉"], "logic": "AND", "limit": 3, "includeSimilarTags": true}, "output": {"success": true, "results": [{"id": "img_nature_001", "url": "/api/image/nature/mountain_view.jpg", "title": "壮丽山景", "description": "连绵起伏的山脉风景", "tags": ["风景", "山脉", "自然", "壮丽"], "uploadTime": "2024-01-10", "fileSize": "3.5 MB", "matchedTags": ["风景", "山脉"], "tagMatchCount": 2}], "total": 1, "searchInfo": {"queryTags": ["风景", "山脉"], "expandedTags": ["自然", "山", "景色"], "logic": "AND", "sortBy": "relevance", "includedSimilarTags": true}, "tagStats": {"availableTags": ["风景", "山脉", "自然", "山", "景色"], "tagFrequency": {"风景": 25, "山脉": 8, "自然": 30}}}}]}