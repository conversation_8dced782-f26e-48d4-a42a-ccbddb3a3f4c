{"name": "analyze_image", "description": "Analyze an uploaded image to extract detailed information including description, objects, colors, scene, mood, and other visual elements. Use this when users want to understand what's in an image or need comprehensive image analysis.", "parameters": {"type": "object", "properties": {"imagePath": {"type": "string", "description": "Local file path to the image file. The image should be in JPEG, PNG, or WebP format. Path should be relative to the project root or absolute path.", "minLength": 1, "maxLength": 500}, "detailLevel": {"type": "string", "enum": ["basic", "detailed", "comprehensive"], "default": "detailed", "description": "Level of analysis detail required. 'basic' provides simple description and tags, 'detailed' includes objects and colors, 'comprehensive' adds mood, emotions, and advanced analysis."}, "focusAreas": {"type": "array", "items": {"type": "string", "enum": ["objects", "colors", "emotions", "text", "scene", "people", "activities", "style"]}, "description": "Specific areas to focus analysis on. If not specified, all areas will be analyzed based on detailLevel.", "uniqueItems": true}, "language": {"type": "string", "enum": ["zh", "en"], "default": "zh", "description": "Language for the analysis results. 'zh' for Chinese, 'en' for English."}}, "required": ["imagePath"], "additionalProperties": false}, "returns": {"type": "object", "properties": {"success": {"type": "boolean", "description": "Whether the analysis was successful"}, "description": {"type": "string", "description": "Detailed natural language description of the image content"}, "tags": {"type": "array", "items": {"type": "string"}, "description": "Relevant tags and keywords extracted from the image"}, "structuredData": {"type": "object", "description": "Structured analysis results", "properties": {"objects": {"type": "array", "items": {"type": "object", "properties": {"name": {"type": "string", "description": "Object name"}, "confidence": {"type": "number", "description": "Detection confidence (0-1)"}, "attributes": {"type": "array", "items": {"type": "string"}, "description": "Object attributes like color, size, position"}}}, "description": "Detected objects in the image"}, "colors": {"type": "array", "items": {"type": "string"}, "description": "Dominant colors in the image"}, "scene": {"type": "string", "description": "Overall scene or setting description"}, "mood": {"type": "string", "description": "Overall mood or atmosphere of the image"}, "emotions": {"type": "array", "items": {"type": "string"}, "description": "Emotions detected in people or conveyed by the scene"}, "textContent": {"type": "string", "description": "Any text detected in the image"}, "style": {"type": "string", "description": "Artistic or photographic style"}, "composition": {"type": "string", "description": "Image composition and layout description"}}}, "confidence": {"type": "number", "description": "Overall analysis confidence score (0-1)"}, "analysisTime": {"type": "string", "description": "Timestamp when the analysis was performed"}, "model": {"type": "string", "description": "AI model used for analysis"}, "error": {"type": "string", "description": "Error message if analysis failed"}}}, "examples": [{"description": "Basic image analysis", "input": {"imagePath": "images/landscape.jpg", "detailLevel": "basic"}, "output": {"success": true, "description": "一张美丽的风景照片，展示了夕阳下的山脉和湖泊", "tags": ["风景", "山脉", "湖泊", "夕阳", "自然"], "structuredData": {"scene": "自然风景", "colors": ["橙色", "蓝色", "紫色"], "mood": "宁静祥和"}, "confidence": 0.92, "analysisTime": "2024-01-15T10:30:00Z", "model": "VL-Chat-Vision"}}, {"description": "Comprehensive analysis with focus areas", "input": {"imagePath": "photos/family_dinner.png", "detailLevel": "comprehensive", "focusAreas": ["objects", "people", "emotions"], "language": "en"}, "output": {"success": true, "description": "A family gathering scene with parents and children sitting around a dining table, sharing a meal in a warm, well-lit room", "tags": ["family", "dining", "children", "parents", "home", "meal", "happiness"], "structuredData": {"objects": [{"name": "dining table", "confidence": 0.95, "attributes": ["wooden", "rectangular", "large"]}, {"name": "chairs", "confidence": 0.88, "attributes": ["wooden", "multiple", "around table"]}], "people": [{"name": "adult", "confidence": 0.92, "attributes": ["smiling", "seated", "parent"]}, {"name": "child", "confidence": 0.89, "attributes": ["young", "happy", "eating"]}], "scene": "indoor dining room", "mood": "warm and joyful", "emotions": ["happiness", "contentment", "togetherness"], "colors": ["warm brown", "cream", "soft yellow"], "style": "candid family photography"}, "confidence": 0.94, "analysisTime": "2024-01-15T10:35:00Z", "model": "VL-Chat-Vision"}}]}