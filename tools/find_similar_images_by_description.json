{"name": "find_similar_images_by_description", "description": "Search for images based on natural language descriptions using AI semantic understanding and vector search. Use this tool when users describe what they're looking for without providing an image, such as 'find images of cats playing', 'show me sunset photos', 'search for pictures of mountains', or 'find images with happy people'. This tool understands complex scene descriptions, emotions, colors, objects, and abstract concepts.", "parameters": {"type": "object", "properties": {"description": {"type": "string", "description": "Natural language description of what to search for. Can describe objects ('cute puppy'), scenes ('sunset at beach'), emotions ('happy family gathering'), colors ('blue sky'), actions ('running horse'), etc. More detailed descriptions yield more precise results. Extract the full descriptive content from user's request.", "minLength": 2, "maxLength": 500}, "limit": {"type": "number", "description": "Maximum number of results to return. Extract from user language: 'a few'=5-8, 'some'=10-15, 'many'=20-30, 'lots'=40-50. Default is 20 if not specified.", "default": 20, "minimum": 1, "maximum": 100}, "threshold": {"type": "number", "description": "Similarity threshold (0-1). Extract from user language: 'very similar'=0.7-0.8, 'similar'=0.5-0.6, 'somewhat similar'=0.4-0.5, 'loosely related'=0.2-0.4, 'broadly related'=0.1-0.3. Default is 0.4 for balanced results.", "default": 0.4, "minimum": 0, "maximum": 1}}, "required": ["description"], "additionalProperties": false}, "returns": {"type": "object", "properties": {"success": {"type": "boolean", "description": "操作是否成功"}, "results": {"type": "array", "description": "匹配的图片列表，按相似度排序", "items": {"type": "object", "properties": {"id": {"type": "string", "description": "图片ID"}, "url": {"type": "string", "description": "图片URL"}, "title": {"type": "string", "description": "图片标题"}, "description": {"type": "string", "description": "图片描述"}, "tags": {"type": "array", "items": {"type": "string"}, "description": "图片标签"}, "similarity": {"type": "number", "description": "相似度分数（0-100）"}, "uploadTime": {"type": "string", "description": "上传时间"}, "fileSize": {"type": "string", "description": "文件大小"}, "matchedKeywords": {"type": "array", "items": {"type": "string"}, "description": "匹配的关键词"}}}}, "total": {"type": "number", "description": "找到的匹配图片总数"}, "searchInfo": {"type": "object", "description": "搜索信息", "properties": {"query": {"type": "string", "description": "原始查询文本"}, "extractedKeywords": {"type": "array", "items": {"type": "string"}, "description": "AI提取的关键词"}, "expandedTags": {"type": "array", "items": {"type": "string"}, "description": "扩展的相似标签"}, "searchMethod": {"type": "string", "description": "使用的搜索方法"}, "threshold": {"type": "number", "description": "使用的相似度阈值"}}}, "error": {"type": "string", "description": "错误信息（如果操作失败）"}}}, "examples": [{"description": "搜索包含动物的图片", "input": {"description": "可爱的小狗在草地上玩耍", "limit": 5, "threshold": 0.7, "enableKeywordExpansion": true}, "output": {"success": true, "results": [{"id": "img_dog_001", "url": "/api/image/dogs/happy_dog.jpg", "title": "快乐的金毛犬", "description": "一只金毛犬在公园的草地上奔跑", "tags": ["狗", "金毛", "草地", "公园", "动物"], "similarity": 92, "uploadTime": "2024-01-10", "fileSize": "1.8 MB", "matchedKeywords": ["狗", "草地", "玩耍"]}, {"id": "img_dog_002", "url": "/api/image/dogs/puppy_play.jpg", "title": "小狗玩球", "description": "一只小狗在户外玩球", "tags": ["小狗", "玩具", "户外", "可爱"], "similarity": 85, "uploadTime": "2024-01-08", "fileSize": "2.1 MB", "matchedKeywords": ["小狗", "玩耍"]}], "total": 2, "searchInfo": {"query": "可爱的小狗在草地上玩耍", "extractedKeywords": ["小狗", "草地", "玩耍", "可爱"], "expandedTags": ["狗", "犬", "宠物", "草坪", "游戏"], "searchMethod": "enhanced_hybrid_search", "threshold": 0.7}}}, {"description": "搜索风景图片", "input": {"description": "美丽的山脉和湖泊", "limit": 3}, "output": {"success": true, "results": [{"id": "img_landscape_001", "url": "/api/image/nature/mountain_lake.jpg", "title": "高山湖泊", "description": "雪山倒映在清澈的湖水中", "tags": ["山脉", "湖泊", "自然", "风景", "倒影"], "similarity": 88, "uploadTime": "2024-01-12", "fileSize": "3.2 MB", "matchedKeywords": ["山脉", "湖泊", "美丽"]}], "total": 1, "searchInfo": {"query": "美丽的山脉和湖泊", "extractedKeywords": ["山脉", "湖泊", "美丽"], "expandedTags": ["山", "水", "自然", "风景"], "searchMethod": "enhanced_hybrid_search", "threshold": 0.6}}}]}