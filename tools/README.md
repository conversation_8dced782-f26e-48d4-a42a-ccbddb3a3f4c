# AI Tools Documentation

本目录包含为AI助手提供的图片搜索和分析工具，支持多种图片检索方式和智能分析功能。

## 工具概览

### 🔍 图片搜索工具
- **`find_similar_images_by_image`** - 以图搜图，通过视觉特征匹配相似图片
- **`find_similar_images_by_description`** - 文本描述搜索，支持自然语言查询
- **`find_images_by_tags`** - 标签搜索，支持多标签组合查询

### 🔬 图片分析工具
- **`get_image_analysis`** - 图片AI分析，提供详细的内容理解和结构化数据

## 详细说明

### 1. find_similar_images_by_image
**功能**：根据输入图片查找视觉特征相似的图片
**适用场景**：
- 用户上传图片寻找相似内容
- 基于现有图片发现相关图片
- 视觉风格匹配

**关键参数**：
- `imageId` 或 `imageBase64`：源图片（二选一）
- `threshold`：相似度阈值，推荐值0.3-0.7
- `limit`：结果数量，建议5-50张

### 2. find_similar_images_by_description
**功能**：通过自然语言描述搜索匹配的图片
**适用场景**：
- 用户描述想要的图片内容
- 语义搜索和概念匹配
- 抽象概念的图片查找

**关键参数**：
- `description`：自然语言描述，越详细越精确
- `threshold`：相似度阈值，推荐值0.2-0.6
- `limit`：结果数量

**描述示例**：
- 物体描述："一只可爱的小狗"
- 场景描述："夕阳下的海滩"
- 情感描述："温馨的家庭聚会"
- 颜色描述："蓝色的天空"

### 3. find_images_by_tags
**功能**：基于标签进行精确或组合搜索
**适用场景**：
- 按分类筛选图片
- 多条件组合查询
- 精确主题匹配

**关键参数**：
- `tags`：标签数组，支持中英文
- `matchMode`：匹配模式
  - `"any"`：OR逻辑，匹配任一标签（结果较多）
  - `"all"`：AND逻辑，匹配所有标签（结果精确）

**标签示例**：
- 动物类：["狗", "猫", "宠物"]
- 风景类：["山脉", "湖泊", "自然"]
- 人物类：["笑容", "儿童", "家庭"]

### 4. get_image_analysis
**功能**：获取图片的全面AI分析结果
**适用场景**：
- 理解图片内容
- 提取图片标签
- 获取结构化元数据

**关键参数**：
- `imageId` 或 `imageBase64`：待分析图片（二选一）
- `includeStructured`：是否包含详细结构化数据

**分析内容**：
- 基础信息：描述、标签、置信度
- 结构化数据：颜色、场景、情绪、对象检测
- 相似图片推荐

## 使用指南

### 工具选择策略
1. **用户上传图片**：优先使用 `find_similar_images_by_image`
2. **用户文字描述**：使用 `find_similar_images_by_description`
3. **明确标签需求**：使用 `find_images_by_tags`
4. **需要理解图片**：使用 `get_image_analysis`

### 参数调优建议
- **相似度阈值**：
  - 严格匹配：0.6-0.8
  - 平衡匹配：0.3-0.6
  - 宽松匹配：0.1-0.3
- **结果数量**：
  - 快速预览：5-10张
  - 常规浏览：20-30张
  - 详细查看：50-100张

### 错误处理
所有工具都包含完善的错误处理机制：
- 参数验证错误
- 图片不存在错误
- 服务不可用错误
- 执行超时错误

### 性能优化
- 图片base64数据建议不超过10MB
- 批量查询时适当调整limit参数
- 合理设置相似度阈值避免过多结果

## 技术实现

### 搜索算法
- **向量相似度**：基于图片特征向量的余弦相似度
- **语义搜索**：结合AI理解和关键词匹配
- **标签匹配**：支持精确匹配和模糊匹配

### 数据格式
- **输入**：JSON格式参数
- **输出**：结构化JSON响应
- **图片**：支持base64和图片ID两种方式

### 集成方式
工具通过Tool Registry统一管理，支持：
- 动态注册和发现
- 参数验证和类型检查
- 错误处理和重试机制
- 执行状态跟踪

## 注意事项

### 数据要求
- 图片ID必须是数据库中存在的有效ID
- Base64数据必须包含完整的data URL前缀
- 支持的图片格式：JPEG、PNG、WebP

### 使用限制
- 单次查询最大返回100张图片
- 描述文本最长500字符
- 标签数量最多10个
- 相似度阈值范围0-1

### 最佳实践
- 根据用户意图选择合适的工具
- 合理设置参数以平衡精度和召回率
- 对搜索结果进行适当的后处理和展示
- 提供用户友好的错误提示和建议