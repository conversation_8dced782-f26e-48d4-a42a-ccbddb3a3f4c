// import { databaseService } from '../src/lib/database'
// import { ImageData } from '../src/data/mockData'
// import { logger } from '../src/lib/LoggerService'
// import { ImagePathError, ImagePathErrorType } from '../src/types/errors'
//
// /**
//  * 图片工具实现
//  */
// export class ImageTools {
//
//   /**
//    * 分析图片内容，提供详细的描述、标签和结构化数据
//    */
//   static async analyzeImage(params: {
//     imagePath: string
//     detailLevel?: 'basic' | 'detailed' | 'comprehensive'
//     focusAreas?: string[]
//     language?: 'zh' | 'en'
//   }) {
//     let inputImagePath: string = ''
//     try {
//       const {
//         imagePath,
//         detailLevel = 'detailed',
//         focusAreas = [],
//         language = 'zh'
//       } = params
//
//       // 保存imagePath到函数作用域变量，用于错误处理
//       inputImagePath = imagePath
//
//       // 验证必需参数
//       if (!imagePath) {
//         return {
//           success: false,
//           error: '必须提供imagePath参数'
//         }
//       }
//
//       // 验证文件路径格式
//       if (typeof imagePath !== 'string' || imagePath.trim().length === 0) {
//         return {
//           success: false,
//           error: '图片路径必须是有效的字符串'
//         }
//       }
//
//       // 开始性能计时
//       const perfMarkId = `analyzeImage_${Date.now()}`
//       logger.startPerformanceTimer(perfMarkId)
//
//       // 调用基于路径的图片分析接口
//       const analysisResult = await window.electronAPI?.ai.analyzeImageByPath(imagePath)
//
//       if (analysisResult?.error) {
//         // 记录错误
//         logger.error(`图片分析失败: ${analysisResult.error}`, 'ImageTools', {
//           path: imagePath,
//           operation: 'analyzeImage'
//         })
//
//         // 结束性能计时
//         logger.endPerformanceTimer(perfMarkId, 'analyzeImage_error', {
//           path: imagePath,
//           error: analysisResult.error
//         })
//
//         return {
//           success: false,
//           error: `图片分析失败: ${analysisResult.error}`
//         }
//       }
//
//       if (!analysisResult) {
//         // 记录错误
//         logger.error('AI服务不可用或返回空结果', 'ImageTools', {
//           path: imagePath,
//           operation: 'analyzeImage'
//         })
//
//         // 结束性能计时
//         logger.endPerformanceTimer(perfMarkId, 'analyzeImage_empty', {
//           path: imagePath
//         })
//
//         return {
//           success: false,
//           error: 'AI服务不可用或返回空结果'
//         }
//       }
//
//       // 记录性能指标
//       logger.endPerformanceTimer(perfMarkId, 'analyzeImage', {
//         path: imagePath,
//         tagsCount: analysisResult.tags?.length || 0
//       })
//
//       // 构建结构化数据
//       const structuredData: any = {
//         objects: [],
//         colors: [],
//         scene: '',
//         mood: '',
//         emotions: [],
//         textContent: '',
//         style: '',
//         composition: ''
//       }
//
//       // 从现有的结构化数据中提取信息
//       if (analysisResult.structured_data) {
//         const structured = analysisResult.structured_data
//
//         // 提取对象信息
//         if (structured.objects) {
//           structuredData.objects = structured.objects.map((obj: any) => ({
//             name: obj.name,
//             confidence: 0.9, // 默认置信度
//             attributes: obj.attributes || []
//           }))
//         }
//
//         // 提取主题信息
//         if (structured.theme) {
//           structuredData.colors = structured.theme.dominant_colors || []
//           structuredData.scene = structured.theme.scene || ''
//           structuredData.mood = structured.theme.mood || ''
//           structuredData.style = structured.theme.style || ''
//         }
//
//         // 提取标签信息
//         if (structured.tags) {
//           // 从动作标签中提取情感相关信息
//           if (structured.tags.relationships) {
//             structuredData.emotions = structured.tags.relationships.filter((rel: any) =>
//               ['快乐', '悲伤', '愤怒', '惊讶', '恐惧', '厌恶', '温馨', '孤独'].some((emotion: any) =>
//                 rel.includes(emotion)
//               )
//             )
//           }
//
//           // 检查是否有文字内容
//           if (structured.tags.text_overlay && structured.tags.text_overlay.length > 0) {
//             structuredData.textContent = '图片包含文字内容'
//           }
//         }
//       }
//
//       // 根据detailLevel过滤返回的数据
//       let filteredStructuredData = structuredData
//       if (detailLevel === 'basic') {
//         filteredStructuredData = {
//           scene: structuredData.scene,
//           colors: structuredData.colors.slice(0, 3),
//           mood: structuredData.mood
//         }
//       } else if (detailLevel === 'detailed') {
//         filteredStructuredData = {
//           objects: structuredData.objects.slice(0, 5),
//           colors: structuredData.colors,
//           scene: structuredData.scene,
//           mood: structuredData.mood,
//           style: structuredData.style
//         }
//       }
//       // comprehensive 返回所有数据
//
//       // 根据focusAreas过滤数据
//       if (focusAreas.length > 0) {
//         const focusedData: any = {}
//         focusAreas.forEach((area: any) => {
//           switch (area) {
//             case 'objects':
//               focusedData.objects = structuredData.objects
//               break
//             case 'colors':
//               focusedData.colors = structuredData.colors
//               break
//             case 'emotions':
//               focusedData.emotions = structuredData.emotions
//               focusedData.mood = structuredData.mood
//               break
//             case 'text':
//               focusedData.textContent = structuredData.textContent
//               break
//             case 'scene':
//               focusedData.scene = structuredData.scene
//               break
//             case 'people':
//               focusedData.objects = structuredData.objects.filter((obj: any) =>
//                 ['人', '男人', '女人', '儿童', '人物'].some((person: any) =>
//                   obj.name.includes(person)
//                 )
//               )
//               break
//             case 'activities':
//               focusedData.activities = analysisResult.tags.filter((tag: any) =>
//                 ['坐着', '站着', '跑步', '行走', '工作', '学习'].some((activity: any) =>
//                   tag.includes(activity)
//                 )
//               )
//               break
//             case 'style':
//               focusedData.style = structuredData.style
//               focusedData.composition = structuredData.composition
//               break
//           }
//         })
//         filteredStructuredData = focusedData
//       }
//
//       // 计算整体置信度
//       const confidence = 0.85 + Math.random() * 0.1 // 模拟置信度 0.85-0.95
//
//       return {
//         success: true,
//         description: analysisResult.description,
//         tags: analysisResult.tags_flat || analysisResult.tags,
//         structuredData: filteredStructuredData,
//         confidence: Math.round(confidence * 100) / 100,
//         analysisTime: new Date().toISOString(),
//         model: 'VL-Chat-Vision'
//       }
//
//     } catch (error) {
//       // 记录错误
//       logger.error('analyze_image工具执行失败', 'ImageTools', {
//         path: inputImagePath,
//         error: error instanceof Error ? error.message : String(error),
//         stack: error instanceof Error ? error.stack : undefined
//       })
//
//       // 转换为ImagePathError（如果不是）
//       const imageError = error instanceof ImagePathError
//         ? error
//         : ImagePathError.fromError(
//             error instanceof Error ? error : new Error(String(error)),
//             inputImagePath
//           )
//
//       return {
//         success: false,
//         error: imageError.message,
//         errorType: imageError.type,
//         errorDetails: imageError.getDetails()
//       }
//     }
//   }
//
//   /**
//    * 根据图片查找相似图片
//    */
//   static async findSimilarImagesByImage(params: {
//     imageId?: string
//     imagePath?: string
//     limit?: number
//     threshold?: number
//   }) {
//     try {
//       const { imageId, imagePath, limit = 10, threshold = 0.7 } = params
//
//       if (!imageId && !imagePath) {
//         return {
//           success: false,
//           error: '必须提供imageId或imagePath参数'
//         }
//       }
//
//       let embedding: number[] = []
//       let sourceImageId: string | undefined
//
//       if (imageId) {
//         // 通过图片ID获取已存在图片的embedding
//         const result = await databaseService.queryImages({
//           expr: `id == "${imageId}"`,
//           limit: 1
//         })
//
//         if (result.error || result.results.length === 0) {
//           return {
//             success: false,
//             error: `未找到ID为${imageId}的图片`
//           }
//         }
//
//         const imageRecord = result.results[0]
//         if (!imageRecord.embedding) {
//           return {
//             success: false,
//             error: '该图片没有可用的向量数据'
//           }
//         }
//
//         embedding = imageRecord.embedding
//         sourceImageId = imageId
//       } else if (imagePath) {
//         // 分析新图片并生成embedding
//         const analysisResult = await window.electronAPI?.ai.analyzeImageByPath(imagePath)
//         if (analysisResult?.error) {
//           return {
//             success: false,
//             error: `图片分析失败: ${analysisResult.error}`
//           }
//         }
//
//         const embeddingResult = await window.electronAPI?.ai.generateEmbedding(analysisResult.description)
//         if (embeddingResult?.error || !embeddingResult?.embedding) {
//           return {
//             success: false,
//             error: `向量生成失败: ${embeddingResult?.error || '未知错误'}`
//           }
//         }
//
//         embedding = embeddingResult.embedding
//       } else {
//         return {
//           success: false,
//           error: '无效的参数'
//         }
//       }
//
//       // 执行相似度搜索
//       const searchResult = await databaseService.searchSimilarImages(
//         embedding,
//         limit + (sourceImageId ? 1 : 0) // 如果有源图片ID，多查询一个以便过滤
//       )
//
//       if (searchResult.error) {
//         return {
//           success: false,
//           error: searchResult.error
//         }
//       }
//
//       // 转换结果并过滤掉源图片
//       const images = searchResult.results
//         .filter((record: any) => {
//           // 过滤掉源图片本身
//           if (sourceImageId && record.id === sourceImageId) {
//             return false
//           }
//           // 应用相似度阈值
//           return !record.score || record.score >= threshold
//         })
//         .slice(0, limit)
//         .map((record: any) => {
//           const imageData = this.convertToImageData(record)
//           // 设置相似度分数
//           if (record.score) {
//             imageData.similarity = Math.round(record.score * 100)
//           }
//           return imageData
//         })
//
//       return {
//         success: true,
//         results: images,
//         total: images.length,
//         queryInfo: {
//           sourceImageId,
//           searchMethod: 'vector_similarity',
//           threshold
//         }
//       }
//
//     } catch (error) {
//       return {
//         success: false,
//         error: error instanceof Error ? error.message : String(error)
//       }
//     }
//   }
//
//   /**
//    * 根据文本描述搜索图片
//    */
//   static async findSimilarImagesByDescription(params: {
//     description: string
//     limit?: number
//     threshold?: number
//     enableKeywordExpansion?: boolean
//   }) {
//     return {
//       success: false,
//       error: '功能暂未实现'
//     }
//   }
//
//   /**
//    * 根据标签搜索图片
//    */
//   static async findImagesByTags(params: {
//     tags: string[]
//     logic?: 'AND' | 'OR'
//     limit?: number
//     includeSimilarTags?: boolean
//     sortBy?: 'relevance' | 'uploadTime' | 'title' | 'fileSize'
//     sortOrder?: 'asc' | 'desc'
//   }) {
//     return {
//       success: false,
//       error: '功能暂未实现'
//     }
//   }
//
//   /**
//    * 获取图片分析结果
//    */
//   static async getImageAnalysis(params: {
//     imageId?: string
//     imagePath?: string
//     includeEmbedding?: boolean
//     includeStructuredData?: boolean
//     reanalyze?: boolean
//   }) {
//     try {
//       const {
//         imageId,
//         imagePath,
//         includeEmbedding = false,
//         includeStructuredData = true,
//         reanalyze = false
//       } = params
//
//       if (!imageId && !imagePath) {
//         return {
//           success: false,
//           error: '必须提供imageId或imagePath参数'
//         }
//       }
//
//       let imageInfo: any = {}
//       let analysis: any = {}
//       let structuredData: any = {}
//       let embedding: number[] | undefined
//       let similarImages: any[] = []
//
//       if (imageId) {
//         // 获取已存在图片的信息
//         const result = await databaseService.queryImages({
//           expr: `id == "${imageId}"`,
//           limit: 1
//         })
//
//         if (result.error || result.results.length === 0) {
//           return {
//             success: false,
//             error: `未找到ID为${imageId}的图片`
//           }
//         }
//
//         const record = result.results[0]
//
//         // 基本信息
//         imageInfo = {
//           id: record.id,
//           url: record.imagePath,
//           title: record.metadata?.filename?.replace(/\.[^/.]+$/, "") || 'Untitled',
//           uploadTime: record.metadata?.uploadTime,
//           fileSize: this.formatFileSize(record.metadata?.filesize || 0),
//           resolution: record.metadata?.dimensions || 'Unknown',
//           format: (record.metadata?.filename || '').split('.').pop()?.toUpperCase() || 'UNKNOWN'
//         }
//
//         // 分析结果
//         analysis = {
//           description: record.description,
//           tags: record.tags,
//           confidence: 0.9, // 默认置信度
//           analysisTime: record.metadata.uploadTime,
//           model: 'Stored Analysis'
//         }
//
//         // 结构化数据
//         if (includeStructuredData && record.structured_metadata) {
//           structuredData = record.structured_metadata
//         }
//
//         // 向量数据
//         if (includeEmbedding && record.embedding) {
//           embedding = record.embedding
//         }
//
//         // 查找相似图片
//         if (record.embedding) {
//           try {
//             const similarResult = await databaseService.searchSimilarImages(
//               record.embedding,
//               6 // 多查询一些，然后过滤掉自己
//             )
//
//             if (!similarResult.error) {
//               similarImages = similarResult.results
//                 .filter((r: any) => r.id !== imageId) // 过滤掉自己
//                 .slice(0, 5) // 只取前5个
//                 .map((r: any) => ({
//                   id: r.id,
//                   title: (r.metadata?.filename || 'Untitled').replace(/\.[^/.]+$/, ""),
//                   similarity: Math.round((r.score || 0) * 100)
//                 }))
//             }
//           } catch (error) {
//             console.warn('查找相似图片失败:', error)
//           }
//         }
//
//         // 如果需要重新分析
//         if (reanalyze && record.imagePath) {
//           try {
//             // 使用文件路径重新分析图片
//             const reanalysisResult = await window.electronAPI?.ai.analyzeImageByPath(record.imagePath)
//             if (!reanalysisResult?.error) {
//               analysis = {
//                 description: reanalysisResult.description,
//                 tags: reanalysisResult.tags,
//                 confidence: reanalysisResult.confidence || 0.9,
//                 analysisTime: new Date().toISOString(),
//                 model: reanalysisResult.model || 'Re-analysis'
//               }
//
//               if (includeStructuredData && reanalysisResult.structured_data) {
//                 structuredData = reanalysisResult.structured_data
//               }
//             }
//           } catch (error) {
//             console.warn('重新分析失败:', error)
//           }
//         }
//
//       } else if (imagePath) {
//         // 实时分析新图片
//         const analysisResult = await window.electronAPI?.ai.analyzeImageByPath(imagePath)
//         if (analysisResult?.error) {
//           return {
//             success: false,
//             error: `图片分析失败: ${analysisResult.error}`
//           }
//         }
//
//         // 基本信息（从文件路径推断）
//         const pathParts = imagePath.split('.')
//         const format = pathParts[pathParts.length - 1]?.toUpperCase() || 'UNKNOWN'
//         imageInfo = {
//           format,
//           resolution: 'Unknown' // 需要额外的图片解析来获取尺寸
//         }
//
//         // 分析结果
//         analysis = {
//           description: analysisResult.description,
//           tags: analysisResult.tags,
//           confidence: analysisResult.confidence || 0.9,
//           analysisTime: new Date().toISOString(),
//           model: analysisResult.model || 'AI Analysis'
//         }
//
//         // 结构化数据
//         if (includeStructuredData && analysisResult.structured_data) {
//           structuredData = analysisResult.structured_data
//         }
//
//         // 生成向量
//         if (includeEmbedding || true) { // 总是生成向量用于相似图片搜索
//           const embeddingResult = await window.electronAPI?.ai.generateEmbedding(analysisResult.description)
//           if (!embeddingResult?.error && embeddingResult?.embedding) {
//             embedding = embeddingResult.embedding
//
//             // 如果不需要返回向量，但需要用于相似图片搜索
//             if (!includeEmbedding) {
//               // 查找相似图片后清除向量数据
//               if (embedding) {
//                 try {
//                   const similarResult = await databaseService.searchSimilarImages(
//                     embedding,
//                     5
//                   )
//
//                   if (!similarResult.error) {
//                     similarImages = similarResult.results.map((r: any) => ({
//                       id: r.id,
//                       title: (r.metadata?.filename || 'Untitled').replace(/\.[^/.]+$/, ""),
//                       similarity: Math.round((r.score || 0) * 100)
//                     }))
//                   }
//                 } catch (error) {
//                   console.warn('查找相似图片失败:', error)
//                 }
//               }
//
//               // 清除向量数据（如果不需要返回）
//               embedding = undefined
//             }
//           }
//         }
//       }
//
//       return {
//         success: true,
//         imageInfo,
//         analysis,
//         structuredData: includeStructuredData ? structuredData : undefined,
//         embedding: includeEmbedding ? embedding : undefined,
//         similarImages,
//         total: similarImages.length
//       }
//
//     } catch (error) {
//       return {
//         success: false,
//         error: error instanceof Error ? error.message : String(error)
//       }
//     }
//   }
//
//   /**
//    * 将数据库记录转换为ImageData格式
//    */
//   private static convertToImageData(record: any): ImageData {
//     return {
//       id: record.id,
//       url: record.imagePath, // 使用imagePath字段
//       title: record.metadata?.filename?.replace(/\.[^/.]+$/, "") || 'Untitled',
//       description: record.description,
//       tags: record.tags || [],
//       uploadTime: record.metadata?.uploadTime,
//       fileSize: this.formatFileSize(record.metadata?.filesize || 0),
//       similarity: record.score ? Math.round(record.score * 100) : 0,
//       location: record.metadata?.location || '',
//       camera: record.metadata?.camera || '',
//       colors: record.metadata?.colors || [],
//       aiAnalysis: true,
//       resolution: record.metadata?.dimensions || '',
//       exif: {
//         iso: record.metadata?.exif?.iso || 0,
//         aperture: record.metadata?.exif?.aperture || '',
//         shutterSpeed: record.metadata?.exif?.shutterSpeed || '',
//         focalLength: record.metadata?.exif?.focalLength || ''
//       }
//     }
//   }
//
//   /**
//    * 格式化文件大小
//    */
//   private static formatFileSize(bytes: number): string {
//     if (bytes === 0) return '0 Bytes'
//
//     const k = 1024
//     const sizes = ['Bytes', 'KB', 'MB', 'GB']
//     const i = Math.floor(Math.log(bytes) / Math.log(k))
//
//     return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
//   }
//
//   /**
//    * 对图片结果进行排序
//    */
//   private static sortImages(images: any[], sortBy: string, sortOrder: string): any[] {
//     return images.sort((a, b) => {
//       let comparison = 0
//
//       switch (sortBy) {
//         case 'relevance':
//           comparison = (b.tagMatchCount || 0) - (a.tagMatchCount || 0)
//           break
//         case 'uploadTime':
//           comparison = new Date(b.uploadTime).getTime() - new Date(a.uploadTime).getTime()
//           break
//         case 'title':
//           comparison = a.title.localeCompare(b.title)
//           break
//         case 'fileSize':
//           // 简单的文件大小比较（基于字符串）
//           const aSizeNum = parseFloat(a.fileSize)
//           const bSizeNum = parseFloat(b.fileSize)
//           comparison = bSizeNum - aSizeNum
//           break
//         default:
//           comparison = 0
//       }
//
//       return sortOrder === 'asc' ? comparison : -comparison
//     })
//   }
// }