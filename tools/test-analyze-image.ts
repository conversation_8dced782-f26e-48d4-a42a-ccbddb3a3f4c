// import { ImageTools } from './imageTools';
//
// /**
//  * 测试 analyze_image 工具的实现
//  */
// async function testAnalyzeImage() {
//   console.log('开始测试 analyze_image 工具...');
//
//   // 测试用例1: 缺少必需参数
//   console.log('\n测试用例1: 缺少必需参数');
//   try {
//     const result1 = await ImageTools.analyzeImage({
//       imageBase64: ''
//     });
//     console.log('结果:', result1);
//   } catch (error) {
//     console.error('错误:', error);
//   }
//
//   // 测试用例2: 无效的图片格式
//   console.log('\n测试用例2: 无效的图片格式');
//   try {
//     const result2 = await ImageTools.analyzeImage({
//       imageBase64: 'invalid-base64-data'
//     });
//     console.log('结果:', result2);
//   } catch (error) {
//     console.error('错误:', error);
//   }
//
//   // 测试用例3: 有效的base64图片数据（模拟）
//   console.log('\n测试用例3: 有效的base64图片数据');
//   try {
//     // 创建一个简单的1x1像素PNG图片的base64数据用于测试
//     const testImageBase64 = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==';
//
//     const result3 = await ImageTools.analyzeImage({
//       imageBase64: testImageBase64,
//       detailLevel: 'detailed',
//       focusAreas: ['objects', 'colors'],
//       language: 'zh'
//     });
//     console.log('结果:', JSON.stringify(result3, null, 2));
//   } catch (error) {
//     console.error('错误:', error);
//   }
//
//   console.log('\n测试完成');
// }
//
// // 如果直接运行此文件，执行测试
// if (require.main === module) {
//   testAnalyzeImage().catch(console.error);
// }
//
// export { testAnalyzeImage };