{"name": "find_similar_images_by_image", "description": "Find images that are visually similar to a provided image using computer vision and vector similarity algorithms. Use this tool when users upload an image and want to find similar ones, or when they say things like 'find images like this', 'search for similar pictures', 'show me more like this image', or 'find visually similar images'. This tool analyzes visual features like colors, shapes, objects, and composition to find matches.", "parameters": {"type": "object", "properties": {"imageId": {"type": "string", "description": "ID of an existing image in the database to find similar images. Use this when referencing a previously uploaded or known image. Either imageId or imageBase64 must be provided.", "minLength": 1, "maxLength": 100}, "imagePath": {"type": "string", "description": "Local file path to the image file for real-time analysis and similarity search. Use this when user provides a new image file. Supports JPEG, PNG, WebP formats. Either imageId or imagePath must be provided.", "minLength": 1, "maxLength": 500}, "limit": {"type": "number", "description": "Maximum number of results to return. Extract from user language: 'a few'=5-8, 'some'=10-15, 'many'=20-30, 'lots'=40-50. Default is 20 if not specified.", "default": 20, "minimum": 1, "maximum": 100}, "threshold": {"type": "number", "description": "Similarity threshold (0-1). Extract from user language: 'very similar'=0.7-0.8, 'similar'=0.5-0.6, 'somewhat similar'=0.4-0.5, 'loosely related'=0.2-0.4. De<PERSON><PERSON> is 0.5 for balanced results.", "default": 0.5, "minimum": 0, "maximum": 1}}, "required": [], "oneOf": [{"required": ["imageId"]}, {"required": ["imagePath"]}], "additionalProperties": false}, "returns": {"type": "object", "properties": {"success": {"type": "boolean", "description": "操作是否成功"}, "results": {"type": "array", "description": "相似图片列表", "items": {"type": "object", "properties": {"id": {"type": "string", "description": "图片ID"}, "url": {"type": "string", "description": "图片URL"}, "title": {"type": "string", "description": "图片标题"}, "description": {"type": "string", "description": "图片描述"}, "tags": {"type": "array", "items": {"type": "string"}, "description": "图片标签"}, "similarity": {"type": "number", "description": "相似度分数（0-100）"}, "uploadTime": {"type": "string", "description": "上传时间"}, "fileSize": {"type": "string", "description": "文件大小"}}}}, "total": {"type": "number", "description": "找到的相似图片总数"}, "queryInfo": {"type": "object", "description": "查询信息", "properties": {"sourceImageId": {"type": "string", "description": "源图片ID"}, "searchMethod": {"type": "string", "description": "搜索方法"}, "threshold": {"type": "number", "description": "使用的相似度阈值"}}}, "error": {"type": "string", "description": "错误信息（如果操作失败）"}}}, "examples": [{"description": "通过图片ID查找相似图片", "input": {"imageId": "img_123456", "limit": 5, "threshold": 0.8}, "output": {"success": true, "results": [{"id": "img_789012", "url": "/api/image/path/to/image.jpg", "title": "相似图片1", "description": "这是一张与源图片相似的图片", "tags": ["风景", "山脉", "自然"], "similarity": 85, "uploadTime": "2024-01-15", "fileSize": "2.3 MB"}], "total": 1, "queryInfo": {"sourceImageId": "img_123456", "searchMethod": "vector_similarity", "threshold": 0.8}}}, {"description": "通过文件路径查找相似图片", "input": {"imagePath": "uploads/sunset_beach.jpg", "limit": 3}, "output": {"success": true, "results": [], "total": 0, "queryInfo": {"searchMethod": "embedding_analysis", "threshold": 0.7}}}]}