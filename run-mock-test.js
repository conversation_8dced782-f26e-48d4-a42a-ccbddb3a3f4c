#!/usr/bin/env node

/**
 * 运行模拟数据库测试
 * 避免 better-sqlite3 编译问题
 */

const { spawn } = require('child_process')
const path = require('path')

console.log('🧪 运行 ImageLibraryTaskService 模拟测试')
console.log('📁 测试文件: task-service-mock.test.ts')
console.log('💡 使用内存数据库，避免 better-sqlite3 编译问题')
console.log('=' .repeat(60))

// 运行测试
const testProcess = spawn('npx', [
  'vitest',
  'run',
  'test/services/task-service-mock.test.ts',
  '--reporter=verbose'
], {
  stdio: 'inherit',
  shell: true,
  cwd: __dirname
})

testProcess.on('close', (code) => {
  console.log('=' .repeat(60))
  if (code === 0) {
    console.log('✅ 测试完成！')
  } else {
    console.log('❌ 测试失败，退出码:', code)
    console.log('\n🔍 如果仍有问题，请检查：')
    console.log('1. 测试目录是否存在: C:\\Users\\<USER>\\Pictures\\测试图片2')
    console.log('2. Node.js 版本是否兼容')
    console.log('3. 依赖是否正确安装')
  }
  process.exit(code)
})

testProcess.on('error', (error) => {
  console.error('❌ 启动测试失败:', error.message)
  process.exit(1)
})