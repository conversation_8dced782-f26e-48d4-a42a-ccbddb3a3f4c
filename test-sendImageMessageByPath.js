// // 测试 sendImageMessageByPath 功能
// // 这个文件用于验证新实现的功能是否正常工作
//
// import {
//   initializeSimpleChatService,
//   sendImageMessageByPath,
//   isSimpleChatServiceInitialized
// } from './src/lib/ChatServiceWithTools.ts';
//
// async function testSendImageMessageByPath() {
//   try {
//     console.log('正在初始化聊天服务...');
//
//     // 初始化聊天服务
//     await initializeSimpleChatService({
//       apiKey: process.env.OPENAI_API_KEY || 'your-api-key-here',
//       baseURL: process.env.OPENAI_BASE_URL || 'https://api.openai.com/v1',
//       chatModel: 'gpt-4o-mini'
//     });
//
//     console.log('聊天服务初始化完成');
//
//     // 测试图片路径
//     const imagePath = 'c:\\Users\\<USER>\\Desktop\\picmind0704\\demo\\10042.jpg';
//     const message = '这张图片里有什么？请详细描述一下。';
//
//     console.log(`正在分析图片: ${imagePath}`);
//     console.log(`用户消息: ${message}`);
//     console.log('注意：现在AI会自动决定调用哪个工具来分析图片...\n');
//
//     // 调用sendImageMessageByPath函数
//     const result = await sendImageMessageByPath(imagePath, message);
//
//     console.log('=== AI 分析结果 ===');
//     console.log('内容:', result.content);
//
//     if (result.toolResults) {
//       console.log('\n=== 工具调用结果 ===');
//       result.toolResults.forEach((toolResult, index) => {
//         console.log(`工具调用 ${index + 1}:`, toolResult);
//       });
//     }
//
//     if (result.error) {
//       console.log('\n=== 错误信息 ===');
//       console.log('错误:', result.error);
//     }
//
//   } catch (error) {
//     console.error('测试失败:', error);
//   }
// }
//
// // 运行测试
// testSendImageMessageByPath();