{"name": "pic-mind", "description": "AI-powered image management and analysis tool", "author": "PicMind Team", "private": true, "version": "1.0.0", "type": "module", "packageManager": "yarn@1.22.22", "scripts": {"dev": "vite", "dev:web": "vite --open", "build": "yarn build:web && yarn build:electron", "build:web": "tsc && vite build", "build:electron": "yarn build:web && electron-builder", "rebuild": "electron-rebuild --force --types prod,dev,optional --module-dir .", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "tsc": "tsc --noEmit", "preview": "vite preview", "electron:dev": "vite build && electron dist-electron/main.js", "test:run": "vitest run", "test:ui": "vitest --ui", "postinstall": "yarn rebuild", "generate-icons": "node scripts/generate-icon.js", "test": "vitest"}, "dependencies": {"@trpc/client": "10.45.1", "@trpc/server": "10.45.1", "@types/better-sqlite3": "^7.6.13", "@types/chokidar": "^1.7.5", "better-sqlite3": "^12.2.0", "chokidar": "^4.0.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "dotenv": "^17.0.1", "electron-trpc": "^0.6.0", "exifr": "^7.1.3", "humps": "^2.0.1", "lucide-react": "^0.525.0", "openai": "^4.67.3", "react": "^18.2.0", "react-dom": "^18.2.0", "sonner": "^2.0.6", "sqlite-vec": "^0.1.7-alpha.2", "tailwind-merge": "^3.3.1", "tailwindcss": "^4.1.7"}, "devDependencies": {"@tailwindcss/vite": "^4.1.7", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@types/humps": "^2.0.6", "@types/react": "^18.2.64", "@types/react-dom": "^18.2.21", "@typescript-eslint/eslint-plugin": "^8.38.0", "@typescript-eslint/parser": "^8.38.0", "@vitejs/plugin-react": "^4.2.1", "@vitest/ui": "^3.2.4", "autoprefixer": "^10.4.21", "electron": "37.2.4", "electron-builder": "^24.13.3", "electron-rebuild": "^3.2.9", "eslint": "^8.57.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "jsdom": "^26.1.0", "postcss": "^8.5.6", "sharp": "^0.34.3", "tw-animate-css": "^1.2.9", "typescript": "^5.2.2", "vite": "^7.0.6", "vite-plugin-electron": "^0.28.6", "vite-plugin-electron-renderer": "^0.14.5", "vitest": "^3.2.4"}, "main": "dist-electron/main.js", "build": {"appId": "com.picmind.app", "productName": "PicMind", "directories": {"buildResources": "build"}, "files": ["dist/**/*", "dist-electron/**/*"], "mac": {"icon": "build/icon.png", "category": "public.app-category.photography"}, "win": {"icon": "build/icon.png"}, "linux": {"icon": "build/icon.png"}}}