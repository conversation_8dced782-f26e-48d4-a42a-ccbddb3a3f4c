#!/usr/bin/env node

/**
 * 重新编译 better-sqlite3 模块
 * 解决 Node.js 版本不兼容问题
 */

const { execSync, exec } = require('child_process')
const path = require('path')
const fs = require('fs')
const os = require('os')

console.log('🔧 开始重新编译 better-sqlite3 模块...')
console.log('🖥️  操作系统:', os.platform(), os.arch())
console.log('📦 Node.js 版本:', process.version)

// 检查 Node.js 版本
const nodeVersion = process.version.match(/v(\d+)\.(\d+)\.(\d+)/)
const majorVersion = parseInt(nodeVersion[1])
console.log('🔍 Node.js 主版本:', majorVersion)

try {
  // 检查 node_modules 是否存在
  const nodeModulesPath = path.join(__dirname, 'node_modules')
  if (!fs.existsSync(nodeModulesPath)) {
    console.log('📦 node_modules 不存在，先安装依赖...')
    execSync('npm install', { stdio: 'inherit', cwd: __dirname })
  }

  // 检查 better-sqlite3 是否已安装
  const sqlitePath = path.join(nodeModulesPath, 'better-sqlite3')
  if (!fs.existsSync(sqlitePath)) {
    console.log('📦 better-sqlite3 未安装，正在安装...')
    execSync('npm install better-sqlite3', { stdio: 'inherit', cwd: __dirname })
  }

  console.log('🔨 重新编译 better-sqlite3...')
  
  // 方法1: 清理缓存并重新编译
  try {
    console.log('🧹 清理 npm 缓存...')
    execSync('npm cache clean --force', { stdio: 'inherit', cwd: __dirname })
    
    console.log('🔨 使用 npm rebuild 重新编译...')
    execSync('npm rebuild better-sqlite3', { stdio: 'inherit', cwd: __dirname })
    console.log('✅ 使用 npm rebuild 重新编译成功')
  } catch (error) {
    console.log('⚠️  npm rebuild 失败，尝试其他方法...')
    
    // 方法2: 删除并重新安装
    try {
      console.log('🗑️  删除 better-sqlite3 目录...')
      const rimraf = require('rimraf')
      rimraf.sync(sqlitePath)
      
      console.log('📦 重新安装 better-sqlite3...')
      execSync('npm install better-sqlite3 --build-from-source', { stdio: 'inherit', cwd: __dirname })
      
      console.log('✅ 重新安装成功')
    } catch (reinstallError) {
      console.error('❌ 重新安装失败:', reinstallError.message)
      
      // 方法3: 使用 electron-rebuild
      try {
        console.log('🔨 尝试使用 electron-rebuild...')
        execSync('npx electron-rebuild', { stdio: 'inherit', cwd: __dirname })
        console.log('✅ electron-rebuild 编译成功')
      } catch (electronRebuildError) {
        console.error('❌ electron-rebuild 失败:', electronRebuildError.message)
        
        // 方法4: 手动编译
        try {
          console.log('🔨 尝试手动编译...')
          process.chdir(sqlitePath)
          execSync('node-gyp clean', { stdio: 'inherit' })
          execSync('node-gyp configure', { stdio: 'inherit' })
          execSync('node-gyp build', { stdio: 'inherit' })
          console.log('✅ 手动编译成功')
        } catch (manualError) {
          console.error('❌ 手动编译失败:', manualError.message)
          throw new Error('所有编译方法都失败了')
        }
      }
    }
  }

  // 验证编译结果
  try {
    console.log('🔍 验证编译结果...')
    const Database = require('better-sqlite3')
    const testDb = new Database(':memory:')
    testDb.close()
    console.log('✅ better-sqlite3 工作正常')
  } catch (testError) {
    console.error('❌ 编译验证失败:', testError.message)
    throw testError
  }

  console.log('🎉 better-sqlite3 重新编译完成！')
  console.log('💡 现在可以运行测试了：')
  console.log('   node run-simple-test.js')
  console.log('   或者：npm run test:run test/services/task-service-simple.test.ts')

} catch (error) {
  console.error('❌ 重新编译失败:', error.message)
  console.log('\n🔍 可能的解决方案：')
  
  if (os.platform() === 'win32') {
    console.log('Windows 系统解决方案：')
    console.log('1. 安装 Visual Studio Build Tools 2019 或更新版本')
    console.log('2. 安装 Python 3.x (推荐 3.8+)')
    console.log('3. 以管理员身份运行命令提示符')
    console.log('4. 设置环境变量：')
    console.log('   set npm_config_msvs_version=2019')
    console.log('   set npm_config_python=python')
  }
  
  console.log('\n通用解决方案：')
  console.log('1. 检查 Node.js 版本是否与 better-sqlite3 兼容')
  console.log('2. 清理并重新安装所有依赖：')
  console.log('   rmdir /s node_modules (Windows) 或 rm -rf node_modules (Linux/Mac)')
  console.log('   npm install')
  console.log('3. 尝试使用不同版本的 better-sqlite3：')
  console.log('   npm install better-sqlite3@11.3.0')
  console.log('4. 如果是 Electron 项目，确保使用正确的 Node.js 版本')
  
  process.exit(1)
}