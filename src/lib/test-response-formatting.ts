/**
 * Test script for response formatting and conversation management
 */

import { ResponseFormatter, ToolExecutionContext } from './responseFormatter';
import { conversationContextManager } from './conversationContextManager';

// Test response formatting
function testResponseFormatting() {
  console.log('=== Testing Response Formatting ===');
  
  // Test image analysis result formatting
  const analysisResult = {
    success: true,
    description: "A beautiful sunset over mountains with orange and pink colors in the sky",
    tags: ["sunset", "mountains", "landscape", "orange", "pink", "sky"],
    structuredData: {
      objects: [
        { name: "mountains", confidence: 0.9, attributes: ["rocky", "tall"] },
        { name: "sky", confidence: 0.95, attributes: ["colorful", "dramatic"] }
      ],
      colors: ["orange", "pink", "purple", "blue"],
      scene: "outdoor landscape",
      mood: "peaceful"
    },
    confidence: 0.85,
    analysisTime: 1250
  };
  
  const analysisContext: ToolExecutionContext = {
    toolName: 'analyze_image',
    parameters: { imageBase64: 'data:image/jpeg;base64,...' },
    userQuery: 'What do you see in this image?',
    hasImage: true
  };
  
  const formattedAnalysis = ResponseFormatter.formatAnalysisResult(analysisResult, analysisContext);
  console.log('Formatted Analysis Result:');
  console.log(formattedAnalysis);
  console.log('\n');
  
  // Test search result formatting
  const searchResult = {
    success: true,
    results: [
      {
        id: 'img1',
        path: '/path/to/image1.jpg',
        similarity: 0.85,
        tags: ['sunset', 'landscape'],
        description: 'Mountain sunset scene'
      },
      {
        id: 'img2',
        path: '/path/to/image2.jpg',
        similarity: 0.72,
        tags: ['mountains', 'nature'],
        description: 'Rocky mountain landscape'
      }
    ],
    total: 15
  };
  
  const searchContext: ToolExecutionContext = {
    toolName: 'find_similar_images_by_description',
    parameters: { description: 'sunset mountains', limit: 20, threshold: 0.5 },
    userQuery: 'Find images of sunset over mountains',
    hasImage: false
  };
  
  const formattedSearch = ResponseFormatter.formatSearchResult(searchResult, searchContext);
  console.log('Formatted Search Result:');
  console.log(formattedSearch);
  console.log('\n');
  
  // Test no results scenario
  const noResultsContext: ToolExecutionContext = {
    toolName: 'find_images_by_tags',
    parameters: { tags: ['unicorn', 'rainbow'], matchMode: 'all' },
    userQuery: 'Find images with unicorns and rainbows',
    hasImage: false
  };
  
  const noResults = ResponseFormatter.formatNoResultsFound(noResultsContext);
  console.log('No Results Response:');
  console.log(noResults);
  console.log('\n');
}

// Test conversation context management
function testConversationContext() {
  console.log('=== Testing Conversation Context Management ===');
  
  const conversationId = 'test-conversation-123';
  
  // Start a workflow
  const workflowId = conversationContextManager.startWorkflow(conversationId, 'analysis', {
    imageBase64: 'data:image/jpeg;base64,test-image-data'
  });
  
  console.log(`Started workflow: ${workflowId}`);
  
  // Add some workflow steps
  conversationContextManager.addWorkflowStep(
    conversationId,
    'analyze_image',
    { imageBase64: 'data:image/jpeg;base64,test-image-data', detailLevel: 'detailed' },
    {
      success: true,
      description: 'A cat sitting on a windowsill',
      tags: ['cat', 'window', 'indoor'],
      confidence: 0.9
    },
    'What is in this image?',
    true
  );
  
  conversationContextManager.addWorkflowStep(
    conversationId,
    'find_similar_images_by_image',
    { imageBase64: 'data:image/jpeg;base64,test-image-data', limit: 10, threshold: 0.6 },
    {
      success: true,
      results: [
        { id: 'img1', similarity: 0.8, tags: ['cat', 'pet'] },
        { id: 'img2', similarity: 0.7, tags: ['cat', 'indoor'] }
      ],
      total: 8
    },
    'Find similar images',
    true
  );
  
  // Test follow-up detection
  const followUpCheck1 = conversationContextManager.isFollowUpQuery(conversationId, 'show me more like these');
  console.log('Follow-up check 1:', followUpCheck1.isFollowUp);
  
  const followUpCheck2 = conversationContextManager.isFollowUpQuery(conversationId, 'what is the weather today?');
  console.log('Follow-up check 2:', followUpCheck2.isFollowUp);
  
  // Get contextual suggestions
  const suggestions = conversationContextManager.getContextualSuggestions(conversationId);
  console.log('Contextual suggestions:');
  suggestions.forEach((suggestion, index) => {
    console.log(`${index + 1}. [${suggestion.type}] ${suggestion.text} (priority: ${suggestion.priority})`);
  });
  
  // Get conversation summary
  const summary = conversationContextManager.getConversationSummary(conversationId);
  console.log('\nConversation Summary:');
  console.log(`Total steps: ${summary.totalSteps}`);
  console.log(`Current focus: ${summary.currentFocus}`);
  console.log('Recent activity:', summary.recentActivity);
  console.log('Suggested actions:', summary.suggestedActions);
  
  // Complete workflow
  conversationContextManager.completeWorkflow(conversationId, 'completed');
  console.log('Workflow completed');
}

// Test error scenarios
function testErrorScenarios() {
  console.log('=== Testing Error Scenarios ===');
  
  const errorContext: ToolExecutionContext = {
    toolName: 'analyze_image',
    parameters: { imageBase64: 'invalid-data' },
    userQuery: 'Analyze this broken image',
    hasImage: true
  };
  
  const errorSuggestions = ResponseFormatter.generateFailureSuggestions(
    'Invalid image format. Please use JPEG, PNG, or WebP format.',
    errorContext
  );
  
  console.log('Error suggestions:');
  console.log(errorSuggestions);
}

// Run all tests
export function runResponseFormattingTests() {
  console.log('🧪 Running Response Formatting and Context Management Tests\n');
  
  try {
    testResponseFormatting();
    testConversationContext();
    testErrorScenarios();
    
    console.log('✅ All tests completed successfully!');
    return true;
  } catch (error) {
    console.error('❌ Test failed:', error);
    return false;
  }
}

// Run tests if this file is executed directly
if (require.main === module) {
  runResponseFormattingTests();
}