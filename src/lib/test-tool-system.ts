/**
 * Test script for the tool initialization and registration system
 * This script demonstrates and tests the functionality implemented in task 8
 */

import { ToolDiscovery, ToolAutoRegistration } from './toolDiscovery';
import { ToolLoader, getToolLoader } from './toolLoader';
import { toolConfigManager, enableTool, disableTool, isToolEnabled } from './toolConfiguration';
import { toolMonitoring, startToolExecution, endToolExecution } from './toolMonitoring';
import { VLChatService } from '../../electron/ai/VLChatService';

/**
 * Test tool discovery system
 */
async function testToolDiscovery(): Promise<void> {
  console.log('\n=== Testing Tool Discovery System ===');
  
  try {
    const discovery = new ToolDiscovery({
      enableValidation: true,
      enableCompatibilityCheck: true,
      autoRegister: false
    });

    const result = await discovery.discoverTools();
    
    console.log('Discovery Results:');
    console.log(`- Discovered: ${result.discovered.length} tools`);
    console.log(`- Validated: ${result.validated.length} tools`);
    console.log(`- Compatible: ${result.compatible.length} tools`);
    console.log(`- Errors: ${result.errors.length}`);
    
    if (result.errors.length > 0) {
      console.log('Errors encountered:');
      result.errors.forEach((error: any) => {
        console.log(`  - ${error.toolName} (${error.stage}): ${error.error}`);
      });
    }

    // Test getting discovered tools
    const discoveredTools = discovery.getDiscoveredTools();
    console.log(`\nDiscovered tool names: ${discoveredTools.map((t: any) => t.function.name).join(', ')}`);

    // Test getting statistics
    const stats = discovery.getDiscoveryStats();
    console.log(`Discovery success rate: ${stats.successRate.toFixed(1)}%`);

  } catch (error) {
    console.error('Tool discovery test failed:', error);
  }
}

/**
 * Test tool auto-registration system
 */
async function testAutoRegistration(): Promise<void> {
  console.log('\n=== Testing Auto-Registration System ===');
  
  try {
    const autoReg = new ToolAutoRegistration({
      enableValidation: true,
      enableCompatibilityCheck: true,
      autoRegister: true
    });

    const result = await autoReg.discoverAndRegister();
    
    console.log('Auto-registration Results:');
    console.log(`- Discovery successful: ${result.discoveryResult.discovered.length > 0}`);
    console.log(`- Registration successful: ${result.registrationSuccess}`);
    console.log(`- Tools ready: ${result.registeredTools.length}`);

    // Test getting tools for registration
    const toolsForReg = autoReg.getToolsForRegistration();
    console.log(`\nTools ready for VL Chat Service: ${toolsForReg.map((t: any) => t.function.name).join(', ')}`);

    // Test registration statistics
    const regStats = autoReg.getRegistrationStats();
    console.log(`Registration categories:`, regStats.categories);

  } catch (error) {
    console.error('Auto-registration test failed:', error);
  }
}

/**
 * Test tool configuration system
 */
function testToolConfiguration(): void {
  console.log('\n=== Testing Tool Configuration System ===');
  
  try {
    // Test getting configuration
    const analyzeConfig = toolConfigManager.getToolConfig('analyze_image');
    console.log(`analyze_image timeout: ${analyzeConfig.timeout}ms`);
    console.log(`analyze_image enabled: ${analyzeConfig.enabled}`);

    // Test enabling/disabling tools
    console.log('\nTesting tool enable/disable:');
    console.log(`analyze_image initially enabled: ${isToolEnabled('analyze_image')}`);
    
    disableTool('analyze_image');
    console.log(`analyze_image after disable: ${isToolEnabled('analyze_image')}`);
    
    enableTool('analyze_image');
    console.log(`analyze_image after enable: ${isToolEnabled('analyze_image')}`);

    // Test setting defaults
    console.log('\nTesting parameter defaults:');
    const originalDefaults = toolConfigManager.getToolDefaults('analyze_image');
    console.log('Original defaults:', originalDefaults);
    
    toolConfigManager.setToolDefaults('analyze_image', { 
      detailLevel: 'comprehensive',
      customParam: 'test' 
    });
    
    const updatedDefaults = toolConfigManager.getToolDefaults('analyze_image');
    console.log('Updated defaults:', updatedDefaults);

    // Test getting enabled/disabled tools
    const enabledTools = toolConfigManager.getEnabledTools();
    const disabledTools = toolConfigManager.getDisabledTools();
    console.log(`\nEnabled tools: ${enabledTools.join(', ')}`);
    console.log(`Disabled tools: ${disabledTools.join(', ')}`);

    // Test configuration summary
    const summary = toolConfigManager.getConfigurationSummary();
    console.log('\nConfiguration Summary:', summary);

    // Test configuration validation
    const validation = toolConfigManager.validateConfiguration();
    console.log('\nConfiguration Validation:');
    console.log(`Valid: ${validation.valid}`);
    if (validation.errors.length > 0) {
      console.log('Errors:', validation.errors);
    }
    if (validation.warnings.length > 0) {
      console.log('Warnings:', validation.warnings);
    }

  } catch (error) {
    console.error('Tool configuration test failed:', error);
  }
}

/**
 * Test tool monitoring system
 */
function testToolMonitoring(): void {
  console.log('\n=== Testing Tool Monitoring System ===');
  
  try {
    // Test execution monitoring
    console.log('Testing execution monitoring:');
    
    const executionId1 = startToolExecution('analyze_image', { 
      imageBase64: 'data:image/jpeg;base64,test',
      detailLevel: 'detailed' 
    });
    console.log(`Started execution: ${executionId1}`);
    
    // Simulate some processing time
    setTimeout(() => {
      endToolExecution(executionId1, true, { 
        description: 'Test image analysis result',
        tags: ['test', 'image']
      });
      console.log(`Completed execution: ${executionId1}`);
    }, 100);

    // Test failed execution
    const executionId2 = startToolExecution('find_images_by_tags', { tags: ['test'] });
    setTimeout(() => {
      endToolExecution(executionId2, false, undefined, 'Test error message');
      console.log(`Failed execution: ${executionId2}`);
    }, 50);

    // Test logging
    toolMonitoring.log('info', 'test_tool', 'This is a test log message', { test: true });
    toolMonitoring.log('warn', 'test_tool', 'This is a test warning', { warning: true });
    toolMonitoring.log('error', 'test_tool', 'This is a test error', { error: true });

    // Wait a bit for async operations to complete
    setTimeout(() => {
      // Test getting performance stats
      const stats = toolMonitoring.getPerformanceStats();
      console.log(`\nPerformance stats for ${stats.length} tools`);
      stats.forEach((stat: any) => {
        console.log(`- ${stat.toolName}: ${stat.totalExecutions} executions, ${stat.successRate.toFixed(1)}% success rate`);
      });

      // Test getting execution metrics
      const metrics = toolMonitoring.getExecutionMetrics(undefined, 5);
      console.log(`\nRecent executions: ${metrics.length}`);
      metrics.forEach((metric: any) => {
        console.log(`- ${metric.toolName}: ${metric.success ? 'SUCCESS' : 'FAILED'} in ${metric.duration}ms`);
      });

      // Test getting logs
      const logs = toolMonitoring.getLogs(undefined, undefined, 5);
      console.log(`\nRecent logs: ${logs.length}`);
      logs.forEach((log: any) => {
        console.log(`- [${log.level.toUpperCase()}] ${log.toolName}: ${log.message}`);
      });

      // Test system overview
      const overview = toolMonitoring.getSystemOverview();
      console.log('\nSystem Overview:', overview);

    }, 200);

  } catch (error) {
    console.error('Tool monitoring test failed:', error);
  }
}

/**
 * Test tool loader integration
 */
async function testToolLoader(): Promise<void> {
  console.log('\n=== Testing Tool Loader Integration ===');
  
  try {
    // Create a mock VL Chat Service for testing
    const mockVLChatService = {
      registerTool: (tool: any) => {
        console.log(`Mock: Registering tool ${tool.function.name}`);
      },
      unregisterTool: (toolName: string) => {
        console.log(`Mock: Unregistering tool ${toolName}`);
      },
      getRegisteredTools: () => [],
      hasRegisteredTool: () => false
    } as any;

    const toolLoader = getToolLoader({
      enableValidation: true,
      enableCompatibilityCheck: true,
      autoRegister: true,
      enableHealthChecks: false, // Disable for testing
      enablePerformanceMonitoring: true
    });

    const result = await toolLoader.initialize(mockVLChatService);
    
    console.log('Tool Loader Results:');
    console.log(`- Success: ${result.success}`);
    console.log(`- Loaded tools: ${result.loadedTools.length}`);
    console.log(`- Failed tools: ${result.failedTools.length}`);
    console.log(`- Loading time: ${result.loadingTime}ms`);

    if (result.loadedTools.length > 0) {
      console.log(`\nLoaded tools: ${result.loadedTools.map((t: any) => t.function.name).join(', ')}`);
    }

    // Test tool loader status
    const status = toolLoader.getStatus();
    console.log('\nTool Loader Status:', status);

    // Test getting loaded tools
    const loadedTools = toolLoader.getLoadedTools();
    console.log(`\nTotal loaded tools: ${loadedTools.length}`);

    // Test checking if tools are loaded
    const isAnalyzeLoaded = toolLoader.isToolLoaded('analyze_image');
    console.log(`analyze_image loaded: ${isAnalyzeLoaded}`);

  } catch (error) {
    console.error('Tool loader test failed:', error);
  }
}

/**
 * Run all tests
 */
export async function runToolSystemTests(): Promise<void> {
  console.log('🧪 Starting Tool System Tests...\n');
  
  try {
    await testToolDiscovery();
    await testAutoRegistration();
    testToolConfiguration();
    testToolMonitoring();
    await testToolLoader();
    
    console.log('\n✅ All tool system tests completed!');
    
  } catch (error) {
    console.error('\n❌ Tool system tests failed:', error);
  }
}

/**
 * Export individual test functions for selective testing
 */
export {
  testToolDiscovery,
  testAutoRegistration,
  testToolConfiguration,
  testToolMonitoring,
  testToolLoader
};

// Run tests if this file is executed directly
if (require.main === module) {
  runToolSystemTests().catch(console.error);
}