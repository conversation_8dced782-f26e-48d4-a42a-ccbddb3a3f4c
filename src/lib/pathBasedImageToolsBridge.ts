// import { ToolDefinition } from '../../electron/ai/VLChatService';
// import { ImageTools } from '../../tools/imageTools';
// import { toolMonitoring, startToolExecution, endToolExecution } from './toolMonitoring';
// import { toolConfigManager } from './toolConfiguration';
//
// /**
//  * Tool execution result interface
//  */
// export interface ToolExecutionResult {
//   success: boolean;
//   data?: any;
//   error?: string;
//   executionTime?: number;
//   toolName?: string;
// }
//
// /**
//  * Path-based tool execution bridge for VL Chat Service
//  * Uses file paths instead of base64 for better performance and consistency with SimpleImage component
//  */
// export class PathBasedImageToolsBridge {
//
//   /**
//    * Execute a tool by name with parameters (path-based)
//    */
//   static async executeTool(toolName: string, parameters: any): Promise<ToolExecutionResult> {
//     // Check if tool is enabled
//     if (!toolConfigManager.isToolEnabled(toolName)) {
//       return {
//         success: false,
//         error: `Tool ${toolName} is disabled`,
//         executionTime: 0,
//         toolName
//       };
//     }
//
//     // Start monitoring
//     const executionId = startToolExecution(toolName, parameters);
//     const startTime = Date.now();
//
//     try {
//       // Get tool configuration
//       const config = toolConfigManager.getToolConfig(toolName);
//
//       // Apply parameter defaults and overrides
//       const mergedParameters = {
//         ...toolConfigManager.getToolDefaults(toolName),
//         ...parameters
//       };
//
//       toolMonitoring.log('debug', toolName, `Executing tool with merged parameters`, {
//         originalParams: parameters,
//         mergedParams: mergedParameters
//       }, executionId);
//
//       // Mark validation checkpoint
//       toolMonitoring.markPerformanceCheckpoint(executionId, 'validation');
//
//       // Execute with timeout
//       const executeWithTimeout = async (): Promise<any> => {
//         switch (toolName) {
//           case 'analyze_image':
//             // Map analyze_image to analyze_image_by_path for consistency
//             return await this.executeAnalyzeImage(mergedParameters);
//
//           case 'analyze_image_by_path':
//             return await this.executeAnalyzeImageByPath(mergedParameters);
//
//           case 'find_similar_images_by_path':
//             return await this.executeFindSimilarImagesByPath(mergedParameters);
//
//           case 'find_similar_images_by_description':
//             return await this.executeFindSimilarImagesByDescription(mergedParameters);
//
//           case 'find_images_by_tags':
//             return await this.executeFindImagesByTags(mergedParameters);
//
//           case 'get_image_analysis_by_path':
//             return await this.executeGetImageAnalysisByPath(mergedParameters);
//
//           default:
//             throw new Error(`Unknown tool: ${toolName}`);
//         }
//       };
//
//       // Mark execution checkpoint
//       toolMonitoring.markPerformanceCheckpoint(executionId, 'execution');
//
//       // Execute with timeout
//       const result = await Promise.race([
//         executeWithTimeout(),
//         new Promise((_, reject) =>
//           setTimeout(() => reject(new Error(`Tool execution timeout after ${config.timeout}ms`)), config.timeout)
//         )
//       ]);
//
//       // Mark formatting checkpoint
//       toolMonitoring.markPerformanceCheckpoint(executionId, 'formatting');
//
//       const executionTime = Date.now() - startTime;
//
//       // End monitoring with success
//       endToolExecution(executionId, true, result);
//
//       toolMonitoring.log('info', toolName, `Tool execution completed successfully`, {
//         executionTime,
//         resultSize: JSON.stringify(result).length
//       }, executionId, executionTime);
//
//       return {
//         success: true,
//         data: result,
//         executionTime,
//         toolName
//       };
//
//     } catch (error) {
//       const executionTime = Date.now() - startTime;
//       const errorMessage = error instanceof Error ? error.message : String(error);
//
//       // End monitoring with failure
//       endToolExecution(executionId, false, undefined, errorMessage);
//
//       toolMonitoring.log('error', toolName, `Tool execution failed: ${errorMessage}`, {
//         executionTime,
//         error: errorMessage
//       }, executionId, executionTime);
//
//       return {
//         success: false,
//         error: errorMessage,
//         executionTime,
//         toolName
//       };
//     }
//   }
//
//   /**
//    * Execute analyze_image_by_path tool
//    */
//   private static async executeAnalyzeImageByPath(parameters: any): Promise<any> {
//     // Validate required parameters
//     if (!parameters.imagePath) {
//       throw new Error('imagePath parameter is required');
//     }
//
//     // Convert app:// URL to file path if needed
//     let filePath = parameters.imagePath;
//     if (filePath.startsWith('app://')) {
//       // Convert app:// URL back to Windows path format
//       filePath = filePath.replace('app://', '');
//       if (filePath.match(/^[a-zA-Z]\//)) {
//         // Convert C/ to C:/ format
//         filePath = filePath.replace(/^([a-zA-Z])\//, '$1:/');
//       }
//       // Convert forward slashes to backslashes for Windows
//       filePath = filePath.replace(/\//g, '\\');
//     }
//
//     // Map parameters to tool parameters
//     const toolParams = {
//       imagePath: filePath,
//       includeStructuredData: parameters.includeStructuredData !== false
//     };
//
//     const result = await ImageTools.analyzeImage(toolParams);
//
//     if (!result.success) {
//       throw new Error(result.error || 'Image analysis failed');
//     }
//
//     // Format result for VL model consumption
//     return {
//       success: true,
//       description: result.description,
//       tags: result.tags,
//       structuredData: result.structuredData,
//       confidence: result.confidence,
//       analysisTime: result.analysisTime,
//       model: result.model,
//       imagePath: filePath
//     };
//   }
//
//   /**
//    * Execute find_similar_images_by_path tool
//    */
//   private static async executeFindSimilarImagesByPath(parameters: any): Promise<any> {
//     // Validate that either imageId or imagePath is provided
//     if (!parameters.imageId && !parameters.imagePath) {
//       throw new Error('Either imageId or imagePath parameter is required');
//     }
//
//     if (parameters.imageId && parameters.imagePath) {
//       throw new Error('Cannot provide both imageId and imagePath parameters');
//     }
//
//     // Convert app:// URL to file path if needed
//     let filePath = parameters.imagePath;
//     if (filePath && filePath.startsWith('app://')) {
//       filePath = filePath.replace('app://', '');
//       if (filePath.match(/^[a-zA-Z]\//)) {
//         filePath = filePath.replace(/^([a-zA-Z])\//, '$1:/');
//       }
//       filePath = filePath.replace(/\//g, '\\');
//     }
//
//     // Map parameters with intelligent defaults
//     const toolParams = {
//       imageId: parameters.imageId,
//       imagePath: filePath,
//       limit: this.extractLimit(parameters.limit),
//       threshold: this.extractThreshold(parameters.threshold)
//     };
//
//     // Use the existing ImageTools but with path instead of base64
//     const result = parameters.imageId
//       ? await ImageTools.findSimilarImagesByImage({ imageId: parameters.imageId, limit: toolParams.limit, threshold: toolParams.threshold })
//       : await ImageTools.findSimilarImagesByImage({ imagePath: filePath, limit: toolParams.limit, threshold: toolParams.threshold });
//
//     if (!result.success) {
//       throw new Error(result.error || 'Similar image search failed');
//     }
//
//     // Format result for VL model consumption
//     return {
//       success: true,
//       results: result.results,
//       total: result.total,
//       queryInfo: result.queryInfo
//     };
//   }
//
//   /**
//    * Execute find_similar_images_by_description tool
//    */
//   private static async executeFindSimilarImagesByDescription(parameters: any): Promise<any> {
//     // Validate required parameters
//     if (!parameters.description || typeof parameters.description !== 'string') {
//       throw new Error('description parameter is required and must be a string');
//     }
//
//     if (parameters.description.trim().length === 0) {
//       throw new Error('description cannot be empty');
//     }
//
//     if (parameters.description.length > 500) {
//       throw new Error('description is too long (maximum 500 characters)');
//     }
//
//     // Map parameters with intelligent defaults
//     const toolParams = {
//       description: parameters.description.trim(),
//       limit: this.extractLimit(parameters.limit),
//       threshold: this.extractThreshold(parameters.threshold),
//       enableKeywordExpansion: true // Always enable for better results
//     };
//
//     const result = await ImageTools.findSimilarImagesByDescription(toolParams);
//
//     if (!result.success) {
//       throw new Error(result.error || 'Description-based image search failed');
//     }
//
//     // Format result for VL model consumption
//     return {
//       success: true,
//       results: result.results,
//       total: result.total,
//       searchInfo: result.searchInfo
//     };
//   }
//
//   /**
//    * Execute find_images_by_tags tool
//    */
//   private static async executeFindImagesByTags(parameters: any): Promise<any> {
//     // Validate required parameters
//     if (!parameters.tags || !Array.isArray(parameters.tags)) {
//       throw new Error('tags parameter is required and must be an array');
//     }
//
//     if (parameters.tags.length === 0) {
//       throw new Error('tags array cannot be empty');
//     }
//
//     if (parameters.tags.length > 10) {
//       throw new Error('too many tags (maximum 10 tags allowed)');
//     }
//
//     // Validate individual tags
//     for (const tag of parameters.tags) {
//       if (typeof tag !== 'string' || tag.trim().length === 0) {
//         throw new Error('all tags must be non-empty strings');
//       }
//       if (tag.length > 50) {
//         throw new Error('tag is too long (maximum 50 characters per tag)');
//       }
//     }
//
//     // Map parameters with intelligent defaults
//     const toolParams = {
//       tags: parameters.tags.map((tag: string) => tag.trim()),
//       logic: this.extractMatchMode(parameters.matchMode),
//       limit: this.extractLimit(parameters.limit),
//       includeSimilarTags: true, // Enable for better results
//       sortBy: 'relevance' as 'title' | 'uploadTime' | 'fileSize' | 'relevance',
//       sortOrder: 'desc' as const
//     };
//
//     const result = await ImageTools.findImagesByTags(toolParams);
//
//     if (!result.success) {
//       throw new Error(result.error || 'Tag-based image search failed');
//     }
//
//     // Format result for VL model consumption
//     return {
//       success: true,
//       results: result.results,
//       total: result.total,
//       searchInfo: result.searchInfo,
//       tagStats: result.tagStats
//     };
//   }
//
//   /**
//    * Execute get_image_analysis_by_path tool
//    */
//   private static async executeGetImageAnalysisByPath(parameters: any): Promise<any> {
//     // Validate that either imageId or imagePath is provided
//     if (!parameters.imageId && !parameters.imagePath) {
//       throw new Error('Either imageId or imagePath parameter is required');
//     }
//
//     if (parameters.imageId && parameters.imagePath) {
//       throw new Error('Cannot provide both imageId and imagePath parameters');
//     }
//
//     // Convert app:// URL to file path if needed
//     let filePath = parameters.imagePath;
//     if (filePath && filePath.startsWith('app://')) {
//       filePath = filePath.replace('app://', '');
//       if (filePath.match(/^[a-zA-Z]\//)) {
//         filePath = filePath.replace(/^([a-zA-Z])\//, '$1:/');
//       }
//       filePath = filePath.replace(/\//g, '\\');
//     }
//
//     // Map parameters
//     const toolParams = {
//       imageId: parameters.imageId,
//       imagePath: filePath,
//       includeEmbedding: false, // Don't include embedding by default for performance
//       includeStructuredData: parameters.includeStructured !== false, // Include by default
//       reanalyze: parameters.reanalyze === true
//     };
//
//     const result = parameters.imageId
//       ? await ImageTools.getImageAnalysis({ imageId: parameters.imageId, includeEmbedding: toolParams.includeEmbedding, includeStructuredData: toolParams.includeStructuredData, reanalyze: toolParams.reanalyze })
//       : await ImageTools.getImageAnalysis({ imagePath: filePath, includeEmbedding: toolParams.includeEmbedding, includeStructuredData: toolParams.includeStructuredData, reanalyze: toolParams.reanalyze });
//
//     if (!result.success) {
//       throw new Error(result.error || 'Image analysis retrieval failed');
//     }
//
//     // Format result for VL model consumption
//     return {
//       success: true,
//       imageInfo: result.imageInfo,
//       analysis: result.analysis,
//       structuredData: result.structuredData,
//       similarImages: result.similarImages,
//       total: result.total
//     };
//   }
//
//   /**
//    * Execute analyze_image tool (supports both imageBase64 and imagePath)
//    */
//   private static async executeAnalyzeImage(parameters: any): Promise<any> {
//     // Handle both imageBase64 and imagePath parameters
//     let imagePath = parameters.imagePath;
//     const imageId = parameters.imageId;
//
//     // If imageBase64 is provided, save it to a temporary file
//     if (parameters.imageBase64 && !imagePath) {
//       console.log('Converting imageBase64 to temporary file for path-based processing...');
//
//       // Validate image format
//       if (!parameters.imageBase64.match(/^data:image\/(jpeg|jpg|png|webp);base64,/)) {
//         throw new Error('Invalid image format. Please use JPEG, PNG, or WebP format with proper data URL prefix');
//       }
//
//       // Save base64 image to temporary file
//       const tempFileName = `temp_analyze_${Date.now()}.jpg`;
//       const saveResult = await window.electronAPI?.fileSystem.saveImage({
//         base64Data: parameters.imageBase64,
//         filename: tempFileName
//       });
//
//       if (!saveResult?.success || !saveResult.filePath) {
//         throw new Error(saveResult?.error || 'Failed to save image to temporary file');
//       }
//
//       imagePath = saveResult.filePath;
//     }
//
//     // If neither imagePath nor imageId is available, throw error
//     if (!imagePath && !imageId) {
//       throw new Error('Either imageBase64, imagePath, or imageId parameter is required');
//     }
//
//     // Map parameters for ImageTools.analyzeImage
//     const toolParams: any = {
//       includeStructuredData: parameters.includeStructuredData !== false, // Default to true
//       detailLevel: parameters.detailLevel || 'detailed',
//       focusAreas: parameters.focusAreas || [],
//       language: parameters.language || 'zh'
//     };
//
//     // Add either imagePath or imageId
//     if (imagePath) {
//       toolParams.imagePath = imagePath;
//     } else if (imageId) {
//       toolParams.imageId = imageId;
//     }
//
//     // Call the appropriate method based on available parameters
//     let result;
//     if (imagePath) {
//       result = await ImageTools.analyzeImage(toolParams);
//     } else if (imageId) {
//       // Use getImageAnalysis for imageId-based requests
//       result = await ImageTools.getImageAnalysis({
//         imageId: imageId,
//         includeStructuredData: toolParams.includeStructuredData,
//         includeEmbedding: false,
//         reanalyze: false
//       });
//     }
//
//     if (!result?.success) {
//       throw new Error(result?.error || 'Image analysis failed');
//     }
//
//     // Return formatted result (result is guaranteed to be successful here)
//     return {
//       success: true,
//       description: (result as any).description || '',
//       tags: (result as any).tags || [],
//       structuredData: (result as any).structuredData || undefined,
//       confidence: (result as any).confidence || 0.85,
//       analysisTime: (result as any).analysisTime || new Date().toISOString(),
//       model: (result as any).model || 'Unknown',
//       metadata: (result as any).metadata || {}
//     };
//   }
//
//   /**
//    * Extract and validate limit parameter with intelligent defaults
//    */
//   private static extractLimit(limit?: number): number {
//     if (limit === undefined || limit === null) {
//       return 20; // Default limit
//     }
//
//     if (typeof limit !== 'number' || !Number.isInteger(limit)) {
//       throw new Error('limit must be an integer');
//     }
//
//     if (limit < 1) {
//       throw new Error('limit must be at least 1');
//     }
//
//     if (limit > 100) {
//       throw new Error('limit cannot exceed 100');
//     }
//
//     return limit;
//   }
//
//   /**
//    * Extract and validate threshold parameter with intelligent defaults
//    */
//   private static extractThreshold(threshold?: number): number {
//     if (threshold === undefined || threshold === null) {
//       return 0.5; // Default threshold for balanced results
//     }
//
//     if (typeof threshold !== 'number') {
//       throw new Error('threshold must be a number');
//     }
//
//     if (threshold < 0 || threshold > 1) {
//       throw new Error('threshold must be between 0 and 1');
//     }
//
//     return threshold;
//   }
//
//   /**
//    * Extract and validate match mode parameter
//    */
//   private static extractMatchMode(matchMode?: string): 'AND' | 'OR' {
//     if (!matchMode) {
//       return 'OR'; // Default to OR logic
//     }
//
//     if (matchMode === 'any') {
//       return 'OR';
//     }
//
//     if (matchMode === 'all') {
//       return 'AND';
//     }
//
//     throw new Error('matchMode must be either "any" or "all"');
//   }
//
//   /**
//    * Get tool definitions for path-based image tools
//    */
//   static getToolDefinitions(): ToolDefinition[] {
//     return [
//       {
//         type: 'function',
//         function: {
//           name: 'analyze_image_by_path',
//           description: 'Analyze an image from a file path to extract detailed information including description, objects, colors, scene, mood, and other visual elements. Use this when users want to understand what\'s in an image stored locally.',
//           parameters: {
//             type: 'object',
//             properties: {
//               imagePath: {
//                 type: 'string',
//                 description: 'File path to the image (supports app:// URLs or local file paths)'
//               },
//               includeStructuredData: {
//                 type: 'boolean',
//                 default: true,
//                 description: 'Whether to include structured data in the analysis'
//               }
//             },
//             required: ['imagePath']
//           }
//         }
//       },
//       {
//         type: 'function',
//         function: {
//           name: 'find_similar_images_by_path',
//           description: 'Find images that are visually similar to a provided image using computer vision and vector similarity algorithms. Use this tool when users provide an image path and want to find similar ones.',
//           parameters: {
//             type: 'object',
//             properties: {
//               imageId: {
//                 type: 'string',
//                 description: 'ID of an existing image in the database to find similar images. Either imageId or imagePath must be provided.'
//               },
//               imagePath: {
//                 type: 'string',
//                 description: 'File path to the image (supports app:// URLs or local file paths). Either imageId or imagePath must be provided.'
//               },
//               limit: {
//                 type: 'number',
//                 description: 'Maximum number of results to return',
//                 default: 20,
//                 minimum: 1,
//                 maximum: 100
//               },
//               threshold: {
//                 type: 'number',
//                 description: 'Similarity threshold (0-1)',
//                 default: 0.5,
//                 minimum: 0,
//                 maximum: 1
//               }
//             },
//             oneOf: [
//               { required: ['imageId'] },
//               { required: ['imagePath'] }
//             ]
//           }
//         }
//       },
//       {
//         type: 'function',
//         function: {
//           name: 'find_similar_images_by_description',
//           description: 'Search for images based on natural language descriptions using AI semantic understanding and vector search.',
//           parameters: {
//             type: 'object',
//             properties: {
//               description: {
//                 type: 'string',
//                 description: 'Natural language description of what to search for',
//                 minLength: 2,
//                 maxLength: 500
//               },
//               limit: {
//                 type: 'number',
//                 description: 'Maximum number of results to return',
//                 default: 20,
//                 minimum: 1,
//                 maximum: 100
//               },
//               threshold: {
//                 type: 'number',
//                 description: 'Similarity threshold (0-1)',
//                 default: 0.4,
//                 minimum: 0,
//                 maximum: 1
//               }
//             },
//             required: ['description']
//           }
//         }
//       },
//       {
//         type: 'function',
//         function: {
//           name: 'find_images_by_tags',
//           description: 'Search for images using specific tags with AND/OR logic for precise or broad matching.',
//           parameters: {
//             type: 'object',
//             properties: {
//               tags: {
//                 type: 'array',
//                 description: 'List of tags to search for',
//                 items: {
//                   type: 'string',
//                   minLength: 1,
//                   maxLength: 50
//                 },
//                 minItems: 1,
//                 maxItems: 10,
//                 uniqueItems: true
//               },
//               limit: {
//                 type: 'number',
//                 description: 'Maximum number of results to return',
//                 default: 20,
//                 minimum: 1,
//                 maximum: 100
//               },
//               matchMode: {
//                 type: 'string',
//                 description: 'Tag matching mode',
//                 enum: ['any', 'all'],
//                 default: 'any'
//               }
//             },
//             required: ['tags']
//           }
//         }
//       },
//       {
//         type: 'function',
//         function: {
//           name: 'get_image_analysis_by_path',
//           description: 'Retrieve detailed analysis results for an existing image or analyze a new image by path.',
//           parameters: {
//             type: 'object',
//             properties: {
//               imageId: {
//                 type: 'string',
//                 description: 'ID of an existing image in the database. Either imageId or imagePath must be provided.'
//               },
//               imagePath: {
//                 type: 'string',
//                 description: 'File path to the image (supports app:// URLs or local file paths). Either imageId or imagePath must be provided.'
//               },
//               includeStructured: {
//                 type: 'boolean',
//                 description: 'Whether to include structured metadata in the response',
//                 default: true
//               },
//               reanalyze: {
//                 type: 'boolean',
//                 description: 'Whether to perform fresh analysis (only for existing images)',
//                 default: false
//               }
//             },
//             oneOf: [
//               { required: ['imageId'] },
//               { required: ['imagePath'] }
//             ]
//           }
//         }
//       }
//     ];
//   }
//
//   /**
//    * Create a tool executor function for VL Chat Service
//    */
//   static createToolExecutor() {
//     return async (toolName: string, parameters: any): Promise<any> => {
//       const result = await this.executeTool(toolName, parameters);
//
//       if (!result.success) {
//         throw new Error(result.error || `Tool execution failed: ${toolName}`);
//       }
//
//       return result.data;
//     };
//   }
// }