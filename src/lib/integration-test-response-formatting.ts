/**
 * Integration test for response formatting and conversation management
 * Tests all requirements from the specification
 */

import { ResponseFormatter, ToolExecutionContext } from './responseFormatter';
import { conversationContextManager } from './conversationContextManager';

interface TestResult {
  testName: string;
  passed: boolean;
  details: string;
}

class ResponseFormattingIntegrationTest {
  private results: TestResult[] = [];

  /**
   * Test Requirement 4.1: Natural language summary of search results
   */
  testNaturalLanguageSummary(): TestResult {
    const searchResult = {
      success: true,
      results: [
        { id: '1', path: '/img1.jpg', similarity: 0.85, tags: ['cat', 'window'] },
        { id: '2', path: '/img2.jpg', similarity: 0.72, tags: ['cat', 'indoor'] }
      ],
      total: 15
    };

    const context: ToolExecutionContext = {
      toolName: 'find_similar_images_by_description',
      parameters: { description: 'cat on window', threshold: 0.6 },
      userQuery: 'Find cat images',
      hasImage: false
    };

    const formatted = ResponseFormatter.formatSearchResult(searchResult, context);
    
    const hasNaturalSummary = formatted.includes('Found 15 image') && 
                             formatted.includes('AI-powered description matching');
    
    return {
      testName: 'Natural Language Summary',
      passed: hasNaturalSummary,
      details: hasNaturalSummary ? 'Provides natural language summary' : 'Missing natural language summary'
    };
  }

  /**
   * Test Requirement 4.2: Alternative suggestions for no results
   */
  testNoResultsSuggestions(): TestResult {
    const context: ToolExecutionContext = {
      toolName: 'find_images_by_tags',
      parameters: { tags: ['unicorn', 'rainbow'] },
      userQuery: 'Find unicorn images',
      hasImage: false
    };

    const noResultsResponse = ResponseFormatter.formatNoResultsFound(context);
    
    const hasSuggestions = noResultsResponse.includes('Try these alternatives') &&
                          noResultsResponse.includes('different or more common tags');
    
    return {
      testName: 'No Results Suggestions',
      passed: hasSuggestions,
      details: hasSuggestions ? 'Provides alternative suggestions' : 'Missing alternative suggestions'
    };
  }

  /**
   * Test Requirement 4.3: Search method explanation
   */
  testSearchMethodExplanation(): TestResult {
    const searchResult = {
      success: true,
      results: [{ id: '1', path: '/img1.jpg', similarity: 0.8 }],
      total: 5
    };

    const context: ToolExecutionContext = {
      toolName: 'find_similar_images_by_image',
      parameters: { imageBase64: 'data:image/jpeg;base64,...' },
      userQuery: 'Find similar images',
      hasImage: true
    };

    const formatted = ResponseFormatter.formatSearchResult(searchResult, context);
    
    const hasMethodExplanation = formatted.includes('visual similarity matching');
    
    return {
      testName: 'Search Method Explanation',
      passed: hasMethodExplanation,
      details: hasMethodExplanation ? 'Explains search method used' : 'Missing search method explanation'
    };
  }

  /**
   * Test Requirement 4.4: Metadata insights highlighting
   */
  testMetadataInsights(): TestResult {
    const searchResult = {
      success: true,
      results: [
        { id: '1', path: '/img1.jpg', similarity: 0.9, tags: ['cat', 'window'] },
        { id: '2', path: '/img2.jpg', similarity: 0.8, tags: ['cat', 'indoor'] },
        { id: '3', path: '/img3.jpg', similarity: 0.7, tags: ['cat', 'sunny'] }
      ],
      total: 8
    };

    const context: ToolExecutionContext = {
      toolName: 'find_similar_images_by_description',
      parameters: { description: 'cat photos', threshold: 0.6 },
      userQuery: 'Show me cat photos',
      hasImage: false
    };

    const formatted = ResponseFormatter.formatSearchResult(searchResult, context);
    
    const hasInsights = formatted.includes('Best match:') || 
                       formatted.includes('Common themes:') ||
                       formatted.includes('Excellent matches found');
    
    return {
      testName: 'Metadata Insights',
      passed: hasInsights,
      details: hasInsights ? 'Highlights interesting metadata insights' : 'Missing metadata insights'
    };
  }

  /**
   * Test Requirement 5.1: Multi-step workflow handling
   */
  testMultiStepWorkflow(): TestResult {
    const conversationId = 'test-workflow-123';
    
    // Start workflow
    conversationContextManager.startWorkflow(conversationId, 'mixed', {
      imageBase64: 'data:image/jpeg;base64,test-image'
    });
    
    // Add analysis step
    conversationContextManager.addWorkflowStep(
      conversationId,
      'analyze_image',
      { imageBase64: 'data:image/jpeg;base64,test-image' },
      { success: true, description: 'A cat on windowsill', tags: ['cat', 'window'] },
      'Analyze this image',
      true
    );
    
    // Add search step
    conversationContextManager.addWorkflowStep(
      conversationId,
      'find_similar_images_by_image',
      { imageBase64: 'data:image/jpeg;base64,test-image' },
      { success: true, results: [{ id: '1', similarity: 0.8 }], total: 5 },
      'Find similar images',
      true
    );
    
    const summary = conversationContextManager.getConversationSummary(conversationId);
    const hasWorkflow = summary.totalSteps === 2 && (summary.currentFocus?.includes('Working with uploaded image') ?? false);
    
    // Cleanup
    conversationContextManager.clearContext(conversationId);
    
    return {
      testName: 'Multi-step Workflow',
      passed: hasWorkflow,
      details: hasWorkflow ? 'Handles multi-step workflows correctly' : 'Multi-step workflow handling failed'
    };
  }

  /**
   * Test Requirement 5.3: Follow-up query detection
   */
  testFollowUpDetection(): TestResult {
    const conversationId = 'test-followup-456';
    
    // Setup context
    conversationContextManager.startWorkflow(conversationId, 'search');
    conversationContextManager.addWorkflowStep(
      conversationId,
      'find_similar_images_by_description',
      { description: 'cats playing' },
      { success: true, results: [{ id: '1' }], total: 3 },
      'Find cat images',
      true
    );
    
    // Test follow-up detection
    const followUpResult = conversationContextManager.isFollowUpQuery(conversationId, 'show me more like these');
    const nonFollowUpResult = conversationContextManager.isFollowUpQuery(conversationId, 'what is the weather?');
    
    const passed = followUpResult.isFollowUp && !nonFollowUpResult.isFollowUp;
    
    // Cleanup
    conversationContextManager.clearContext(conversationId);
    
    return {
      testName: 'Follow-up Detection',
      passed,
      details: passed ? 'Correctly detects follow-up queries' : 'Follow-up detection failed'
    };
  }

  /**
   * Test Requirement 5.4: Context maintenance
   */
  testContextMaintenance(): TestResult {
    const conversationId = 'test-context-789';
    
    // Setup workflow with context
    conversationContextManager.startWorkflow(conversationId, 'search');
    conversationContextManager.addWorkflowStep(
      conversationId,
      'find_similar_images_by_description',
      { description: 'sunset mountains', threshold: 0.6 },
      { success: true, results: [{ id: '1', similarity: 0.8 }], total: 10 },
      'Find sunset images',
      true
    );
    
    // Get contextual suggestions
    const suggestions = conversationContextManager.getContextualSuggestions(conversationId);
    const hasContextualSuggestions = suggestions.length > 0 && 
                                   suggestions.some((s: any) => s.type === 'refinement' || s.type === 'follow_up');
    
    // Cleanup
    conversationContextManager.clearContext(conversationId);
    
    return {
      testName: 'Context Maintenance',
      passed: hasContextualSuggestions,
      details: hasContextualSuggestions ? 'Maintains context and provides suggestions' : 'Context maintenance failed'
    };
  }

  /**
   * Test Requirement 6.1: User-friendly error messages
   */
  testUserFriendlyErrors(): TestResult {
    const context: ToolExecutionContext = {
      toolName: 'analyze_image',
      parameters: { imageBase64: 'invalid-data' },
      userQuery: 'Analyze this image',
      hasImage: true
    };

    const errorSuggestions = ResponseFormatter.generateFailureSuggestions(
      'Invalid image format. Please use JPEG, PNG, or WebP format.',
      context
    );
    
    const isUserFriendly = errorSuggestions.includes('Try these alternatives') &&
                          errorSuggestions.includes('image format') &&
                          !errorSuggestions.includes('Error:') &&
                          !errorSuggestions.includes('Exception:');
    
    return {
      testName: 'User-friendly Errors',
      passed: isUserFriendly,
      details: isUserFriendly ? 'Provides user-friendly error messages' : 'Error messages not user-friendly'
    };
  }

  /**
   * Test Requirement 6.2: Alternative search suggestions
   */
  testAlternativeSearchSuggestions(): TestResult {
    const context: ToolExecutionContext = {
      toolName: 'find_similar_images_by_description',
      parameters: { description: 'very specific rare thing' },
      userQuery: 'Find very specific images',
      hasImage: false
    };

    const suggestions = ResponseFormatter.generateFailureSuggestions('No results found', context);
    
    const hasAlternatives = suggestions.includes('different or more general keywords') ||
                           suggestions.includes('broader terms') ||
                           suggestions.includes('Search by tags');
    
    return {
      testName: 'Alternative Search Suggestions',
      passed: hasAlternatives,
      details: hasAlternatives ? 'Provides alternative search suggestions' : 'Missing alternative suggestions'
    };
  }

  /**
   * Run all tests
   */
  runAllTests(): { passed: number; failed: number; results: TestResult[] } {
    console.log('🧪 Running Response Formatting Integration Tests...\n');
    
    this.results = [
      this.testNaturalLanguageSummary(),
      this.testNoResultsSuggestions(),
      this.testSearchMethodExplanation(),
      this.testMetadataInsights(),
      this.testMultiStepWorkflow(),
      this.testFollowUpDetection(),
      this.testContextMaintenance(),
      this.testUserFriendlyErrors(),
      this.testAlternativeSearchSuggestions()
    ];

    let passed = 0;
    let failed = 0;

    this.results.forEach((result: any) => {
      const status = result.passed ? '✅' : '❌';
      console.log(`${status} ${result.testName}: ${result.details}`);
      
      if (result.passed) {
        passed++;
      } else {
        failed++;
      }
    });

    console.log(`\n📊 Test Results: ${passed} passed, ${failed} failed`);
    
    if (failed === 0) {
      console.log('🎉 All tests passed! Response formatting and conversation management are working correctly.');
    } else {
      console.log('⚠️  Some tests failed. Please review the implementation.');
    }

    return { passed, failed, results: this.results };
  }
}

// Export for use in other modules
export { ResponseFormattingIntegrationTest };

// Run tests if this file is executed directly
if (require.main === module) {
  const tester = new ResponseFormattingIntegrationTest();
  tester.runAllTests();
}