// import { SimpleChatService, SimpleChatConfig, SimpleChatResponse, StreamingChatResponse, StreamingCallback, ReActStep, ReActStepCallback } from './simpleChatService';
//
// // Export types for external use
// export type ChatResponse = SimpleChatResponse;
// export type { StreamingChatResponse, StreamingCallback, ReActStep, ReActStepCallback };
//
// let simpleChatService: SimpleChatService | null = null;
//
// // Default configuration (from environment variables)
// const defaultConfig: SimpleChatConfig = {
//   baseURL: import.meta.env.VITE_AI_CHAT_BASE_URL || 'https://api.openai.com/v1',
//   apiKey: import.meta.env.VITE_AI_CHAT_API_KEY || 'your-api-key-here',
//   chatModel: import.meta.env.VITE_AI_CHAT_MODEL || 'MiniMax/MiniMax-M1-80k', // 专用聊天模型
//   timeout: 60000,
//   maxRetries: 3
// };
//
// // 打印配置信息用于调试
// console.log('简单聊天服务配置:');
// console.log('Chat Model (聊天):', defaultConfig.chatModel);
// console.log('Base URL:', defaultConfig.baseURL);
//
// /**
//  * 初始化简单聊天服务
//  */
// export function initializeSimpleChatService(config?: Partial<SimpleChatConfig>) {
//   try {
//     console.log('SimpleChatService: 开始初始化简单聊天服务...')
//
//     // 创建简单聊天服务实例
//     console.log('SimpleChatService: 创建聊天服务实例...')
//     const finalConfig = { ...defaultConfig, ...config };
//
//     console.log('SimpleChatService: 最终配置:')
//     console.log('- 聊天模型:', finalConfig.chatModel)
//     console.log('- 基础URL:', finalConfig.baseURL)
//
//     simpleChatService = new SimpleChatService(finalConfig);
//
//     console.log('SimpleChatService: 简单聊天服务初始化成功');
//   } catch (error) {
//     console.error('SimpleChatService: 初始化简单聊天服务失败:', error);
//   }
// }
//
// /**
//  * 发送文本消息
//  */
// export async function sendTextMessage(message: string): Promise<SimpleChatResponse> {
//   if (!simpleChatService) {
//     throw new Error('Simple chat service not initialized. Call initializeSimpleChatService() first.');
//   }
//
//   return await simpleChatService.sendMessage(message);
// }
//
// /**
//  * 检查简单聊天服务是否已初始化
//  */
// export function isSimpleChatServiceInitialized(): boolean {
//   return simpleChatService !== null;
// }
//
// // 为了向后兼容，保留旧的函数名
// export const initializeSeparatedChatService = initializeSimpleChatService;
//
// /**
//  * 设置ReAct步骤回调
//  */
// export function setReActStepCallback(callback: ReActStepCallback): void {
//   if (!simpleChatService) {
//     throw new Error('Simple chat service not initialized. Call initializeSimpleChatService() first.');
//   }
//
//   simpleChatService.setReActStepCallback(callback);
// }
//
// export async function sendImageMessageByPath(imagePath: string, message?: string): Promise<SimpleChatResponse> {
//   if (!simpleChatService) {
//     throw new Error('Simple chat service not initialized. Call initializeSimpleChatService() first.');
//   }
//
//   return await simpleChatService.sendImageMessageByPath(imagePath, message);
// }