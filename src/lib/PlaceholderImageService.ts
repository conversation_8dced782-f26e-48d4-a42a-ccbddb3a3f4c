/**
 * 占位图片服务
 * 提供默认占位图片，用于处理图片加载失败的情况
 */
import { ImagePathErrorType } from '../types/errors'
import { logger } from './LoggerService'

// 占位图片配置
export interface PlaceholderConfig {
  width: number
  height: number
  backgroundColor: string
  textColor: string
  fontSize: number
  fontFamily: string
  text: string
  borderColor: string
  borderWidth: number
}

/**
 * 占位图片服务类
 */
export class PlaceholderImageService {
  private defaultConfig: PlaceholderConfig
  private canvas: HTMLCanvasElement
  private ctx: CanvasRenderingContext2D
  private errorTypeConfig: Map<ImagePathErrorType, Partial<PlaceholderConfig>>

  constructor(config?: Partial<PlaceholderConfig>) {
    // 默认配置
    this.defaultConfig = {
      width: 200,
      height: 150,
      backgroundColor: '#f0f0f0',
      textColor: '#666666',
      fontSize: 14,
      fontFamily: 'Arial, sans-serif',
      text: '图片不可用',
      borderColor: '#cccccc',
      borderWidth: 1,
      ...config
    }

    // 为不同错误类型设置不同的占位图配置
    this.errorTypeConfig = new Map([
      [ImagePathErrorType.PATH_NOT_FOUND, {
        backgroundColor: '#f8f8f8',
        textColor: '#888888',
        text: '图片不存在',
        borderColor: '#dddddd'
      }],
      [ImagePathErrorType.PATH_NOT_ALLOWED, {
        backgroundColor: '#fff0f0',
        textColor: '#cc0000',
        text: '路径不允许访问',
        borderColor: '#ffcccc'
      }],
      [ImagePathErrorType.INVALID_FORMAT, {
        backgroundColor: '#f0f8ff',
        textColor: '#0066cc',
        text: '不支持的图片格式',
        borderColor: '#99ccff'
      }],
      [ImagePathErrorType.FILE_TOO_LARGE, {
        backgroundColor: '#fff8f0',
        textColor: '#cc6600',
        text: '图片文件过大',
        borderColor: '#ffcc99'
      }],
      [ImagePathErrorType.PERMISSION_DENIED, {
        backgroundColor: '#fff0f0',
        textColor: '#cc0000',
        text: '无权限访问',
        borderColor: '#ffcccc'
      }]
    ])

    // 创建canvas
    this.canvas = document.createElement('canvas')
    const ctx = this.canvas.getContext('2d')
    if (!ctx) {
      throw new Error('无法创建Canvas 2D上下文')
    }
    this.ctx = ctx
  }

  /**
   * 生成占位图片
   */
  generatePlaceholder(
    errorType?: ImagePathErrorType,
    customText?: string,
    customConfig?: Partial<PlaceholderConfig>
  ): string {
    try {
      // 合并配置
      const baseConfig = errorType ? 
        { ...this.defaultConfig, ...this.errorTypeConfig.get(errorType) } : 
        this.defaultConfig
      
      const config = { ...baseConfig, ...customConfig }
      
      // 设置文本
      const text = customText || config.text
      
      // 设置canvas尺寸
      this.canvas.width = config.width
      this.canvas.height = config.height
      
      // 绘制背景
      this.ctx.fillStyle = config.backgroundColor
      this.ctx.fillRect(0, 0, config.width, config.height)
      
      // 绘制边框
      if (config.borderWidth > 0) {
        this.ctx.strokeStyle = config.borderColor
        this.ctx.lineWidth = config.borderWidth
        this.ctx.strokeRect(
          config.borderWidth / 2, 
          config.borderWidth / 2, 
          config.width - config.borderWidth, 
          config.height - config.borderWidth
        )
      }
      
      // 绘制文本
      this.ctx.fillStyle = config.textColor
      this.ctx.font = `${config.fontSize}px ${config.fontFamily}`
      this.ctx.textAlign = 'center'
      this.ctx.textBaseline = 'middle'
      this.ctx.fillText(text, config.width / 2, config.height / 2)
      
      // 转换为data URL
      return this.canvas.toDataURL('image/png')
    } catch (error) {
      logger.error('生成占位图片失败', 'PlaceholderImageService', { error })
      
      // 返回一个极简的占位图，以防canvas失败
      return 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mNk+A8AAQUBAScY42YAAAAASUVORK5CYII='
    }
  }

  /**
   * 生成错误占位图片
   */
  generateErrorPlaceholder(error: Error, imagePath?: string): string {
    // 确定错误类型
    let errorType = ImagePathErrorType.UNKNOWN_ERROR
    let customText = '图片加载失败'
    
    if (error.name === 'ImagePathError' && 'type' in error) {
      errorType = (error as any).type
      
      // 根据路径生成更具体的错误信息
      if (imagePath) {
        const filename = this.extractFilename(imagePath)
        customText = `${filename || '图片'} - ${(error as any).message || '加载失败'}`
      } else {
        customText = (error as any).message || '图片加载失败'
      }
    } else {
      // 普通错误
      customText = error.message || '图片加载失败'
    }
    
    return this.generatePlaceholder(errorType, customText)
  }

  /**
   * 从路径中提取文件名
   */
  private extractFilename(path: string): string {
    if (!path) return ''
    
    // 移除查询参数
    const pathWithoutQuery = path.split('?')[0]
    
    // 获取最后一个斜杠后的内容
    const parts = pathWithoutQuery.split(/[/\\]/)
    return parts[parts.length - 1] || ''
  }

  /**
   * 更新默认配置
   */
  updateDefaultConfig(config: Partial<PlaceholderConfig>): void {
    this.defaultConfig = { ...this.defaultConfig, ...config }
  }

  /**
   * 更新特定错误类型的配置
   */
  updateErrorTypeConfig(errorType: ImagePathErrorType, config: Partial<PlaceholderConfig>): void {
    const currentConfig = this.errorTypeConfig.get(errorType) || {}
    this.errorTypeConfig.set(errorType, { ...currentConfig, ...config })
  }
}

// 导出单例实例
export const placeholderImageService = new PlaceholderImageService()