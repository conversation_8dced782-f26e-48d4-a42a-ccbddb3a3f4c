/**
 * Comprehensive error handling and result validation for image tools
 */

export interface ErrorContext {
  toolName: string;
  parameters: Record<string, unknown>;
  userQuery?: string;
  executionTime?: number;
}

export interface ErrorSuggestion {
  message: string;
  action?: string;
  parameters?: any;
}

export interface ValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
  suggestions: ErrorSuggestion[];
}

/**
 * Image Tools Error Handler
 * Provides comprehensive error handling, validation, and user-friendly suggestions
 */
export class ImageToolsErrorHandler {
  
  /**
   * Handle and format tool execution errors
   */
  static handleToolError(error: any, context: ErrorContext): {
    userMessage: string;
    technicalMessage: string;
    suggestions: ErrorSuggestion[];
    recoverable: boolean;
  } {
    const errorMessage = error instanceof Error ? error.message : String(error);
    const { toolName, parameters, userQuery } = context;
    
    // Categorize error types
    const errorType = this.categorizeError(errorMessage, toolName);
    
    // Generate user-friendly message
    const userMessage = this.generateUserFriendlyMessage(errorType, toolName, errorMessage);
    
    // Generate suggestions
    const suggestions = this.generateErrorSuggestions(errorType, toolName, parameters, userQuery);
    
    // Determine if error is recoverable
    const recoverable = this.isRecoverableError(errorType);
    
    return {
      userMessage,
      technicalMessage: errorMessage,
      suggestions,
      recoverable
    };
  }
  
  /**
   * Validate tool results for consistency and completeness
   */
  static validateToolResult(toolName: string, result: any): ValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];
    const suggestions: ErrorSuggestion[] = [];
    
    if (!result) {
      errors.push('Tool returned null or undefined result');
      return { isValid: false, errors, warnings, suggestions };
    }
    
    // Check for success flag
    if (result.success === false) {
      errors.push(result.error || 'Tool execution failed');
    }
    
    // Tool-specific validation
    switch (toolName) {
      case 'analyze_image':
        this.validateAnalyzeImageResult(result, errors, warnings, suggestions);
        break;
        
      case 'find_similar_images_by_image':
      case 'find_similar_images_by_description':
      case 'find_images_by_tags':
        this.validateSearchResult(result, errors, warnings, suggestions);
        break;
        
      case 'get_image_analysis':
        this.validateAnalysisResult(result, errors, warnings, suggestions);
        break;
    }
    
    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      suggestions
    };
  }
  
  /**
   * Categorize error types for better handling
   */
  private static categorizeError(errorMessage: string, toolName: string): string {
    const message = errorMessage.toLowerCase();
    
    // Parameter validation errors
    if (message.includes('parameter') && (message.includes('required') || message.includes('missing'))) {
      return 'MISSING_PARAMETER';
    }
    
    if (message.includes('invalid') && message.includes('format')) {
      return 'INVALID_FORMAT';
    }
    
    if (message.includes('too long') || message.includes('too many') || message.includes('exceed')) {
      return 'PARAMETER_LIMIT';
    }
    
    // Service availability errors
    if (message.includes('service') && (message.includes('unavailable') || message.includes('not available'))) {
      return 'SERVICE_UNAVAILABLE';
    }
    
    if (message.includes('timeout') || message.includes('timed out')) {
      return 'TIMEOUT';
    }
    
    if (message.includes('network') || message.includes('connection')) {
      return 'NETWORK_ERROR';
    }
    
    // Data errors
    if (message.includes('not found') || message.includes('does not exist')) {
      return 'DATA_NOT_FOUND';
    }
    
    if (message.includes('no results') || message.includes('empty')) {
      return 'NO_RESULTS';
    }
    
    // Analysis errors
    if (message.includes('analysis') && message.includes('failed')) {
      return 'ANALYSIS_FAILED';
    }
    
    if (message.includes('embedding') || message.includes('vector')) {
      return 'EMBEDDING_ERROR';
    }
    
    // Database errors
    if (message.includes('database') || message.includes('query')) {
      return 'DATABASE_ERROR';
    }
    
    return 'UNKNOWN_ERROR';
  }
  
  /**
   * Generate user-friendly error messages
   */
  private static generateUserFriendlyMessage(errorType: string, toolName: string, originalError: string): string {
    const toolDisplayName = this.getToolDisplayName(toolName);
    
    // Tool-specific error handling
    if (toolName === 'analyze_image') {
      return this.generateAnalyzeImageErrorMessage(errorType, originalError);
    }
    
    // Search tool specific error handling
    if (toolName.includes('find_') || toolName === 'get_image_analysis') {
      return this.generateSearchToolErrorMessage(toolName, errorType, originalError);
    }
    
    switch (errorType) {
      case 'MISSING_PARAMETER':
        return `I need more information to ${toolDisplayName.toLowerCase()}. Please provide the required details.`;
        
      case 'INVALID_FORMAT':
        return `The image format isn't supported. Please use JPEG, PNG, or WebP format.`;
        
      case 'PARAMETER_LIMIT':
        return `The request is too large. Please try with fewer items or shorter text.`;
        
      case 'SERVICE_UNAVAILABLE':
        return `The ${toolDisplayName.toLowerCase()} service is temporarily unavailable. Please try again in a moment.`;
        
      case 'TIMEOUT':
        return `The ${toolDisplayName.toLowerCase()} is taking longer than expected. Please try again.`;
        
      case 'NETWORK_ERROR':
        return `I'm having trouble connecting to the image services. Please check your connection and try again.`;
        
      case 'DATA_NOT_FOUND':
        return `I couldn't find the requested image or data. Please check the image ID or try a different image.`;
        
      case 'NO_RESULTS':
        return `I couldn't find any matching images. Try adjusting your search terms or criteria.`;
        
      case 'ANALYSIS_FAILED':
        return `I had trouble analyzing the image. Please try with a different image or check the image quality.`;
        
      case 'EMBEDDING_ERROR':
        return `I encountered an issue processing the image data. Please try again with a different image.`;
        
      case 'DATABASE_ERROR':
        return `I'm having trouble accessing the image database. Please try again in a moment.`;
        
      default:
        return `I encountered an unexpected issue with ${toolDisplayName.toLowerCase()}. Please try again.`;
    }
  }
  
  /**
   * Generate actionable error suggestions
   */
  private static generateErrorSuggestions(
    errorType: string, 
    toolName: string, 
    parameters: any, 
    userQuery?: string
  ): ErrorSuggestion[] {
    const suggestions: ErrorSuggestion[] = [];
    
    switch (errorType) {
      case 'MISSING_PARAMETER':
        if (toolName === 'analyze_image') {
          suggestions.push({
            message: 'Please upload an image to analyze',
            action: 'upload_image'
          });
        } else if (toolName.includes('find_similar_images_by_image')) {
          suggestions.push({
            message: 'Please provide an image ID or upload an image',
            action: 'provide_image'
          });
        } else if (toolName === 'find_similar_images_by_description') {
          suggestions.push({
            message: 'Please describe what kind of images you\'re looking for',
            action: 'provide_description'
          });
        } else if (toolName === 'find_images_by_tags') {
          suggestions.push({
            message: 'Please specify which tags to search for',
            action: 'provide_tags'
          });
        }
        break;
        
      case 'INVALID_FORMAT':
        suggestions.push({
          message: 'Convert your image to JPEG, PNG, or WebP format',
          action: 'convert_format'
        });
        suggestions.push({
          message: 'Try uploading a different image',
          action: 'try_different_image'
        });
        break;
        
      case 'PARAMETER_LIMIT':
        if (parameters?.description && parameters.description.length > 500) {
          suggestions.push({
            message: 'Shorten your description to under 500 characters',
            action: 'shorten_description'
          });
        }
        if (parameters?.tags && parameters.tags.length > 10) {
          suggestions.push({
            message: 'Use fewer tags (maximum 10)',
            action: 'reduce_tags'
          });
        }
        if (parameters?.limit && parameters.limit > 100) {
          suggestions.push({
            message: 'Request fewer results (maximum 100)',
            action: 'reduce_limit',
            parameters: { limit: 50 }
          });
        }
        break;
        
      case 'SERVICE_UNAVAILABLE':
      case 'TIMEOUT':
      case 'NETWORK_ERROR':
        suggestions.push({
          message: 'Wait a moment and try again',
          action: 'retry'
        });
        suggestions.push({
          message: 'Check your internet connection',
          action: 'check_connection'
        });
        break;
        
      case 'NO_RESULTS':
        if (toolName === 'find_similar_images_by_description') {
          suggestions.push({
            message: 'Try using different or more general keywords',
            action: 'modify_description'
          });
          suggestions.push({
            message: 'Lower the similarity threshold for broader results',
            action: 'lower_threshold',
            parameters: { threshold: 0.3 }
          });
        } else if (toolName === 'find_images_by_tags') {
          suggestions.push({
            message: 'Try using "any" match mode instead of "all"',
            action: 'change_match_mode',
            parameters: { matchMode: 'any' }
          });
          suggestions.push({
            message: 'Use more general or common tags',
            action: 'use_general_tags'
          });
        } else if (toolName.includes('find_similar_images_by_image')) {
          suggestions.push({
            message: 'Lower the similarity threshold',
            action: 'lower_threshold',
            parameters: { threshold: 0.4 }
          });
          suggestions.push({
            message: 'Try with a different reference image',
            action: 'try_different_image'
          });
        }
        break;
        
      case 'ANALYSIS_FAILED':
        suggestions.push({
          message: 'Try with a clearer, higher-quality image',
          action: 'use_better_image'
        });
        suggestions.push({
          message: 'Ensure the image is not corrupted',
          action: 'check_image_integrity'
        });
        break;
    }
    
    // Always add a general retry suggestion
    suggestions.push({
      message: 'Try the request again',
      action: 'retry'
    });
    
    return suggestions;
  }
  
  /**
   * Check if an error is recoverable
   */
  private static isRecoverableError(errorType: string): boolean {
    const recoverableErrors = [
      'MISSING_PARAMETER',
      'INVALID_FORMAT', 
      'PARAMETER_LIMIT',
      'TIMEOUT',
      'NETWORK_ERROR',
      'NO_RESULTS'
    ];
    
    return recoverableErrors.includes(errorType);
  }
  
  /**
   * Get display name for tools
   */
  private static getToolDisplayName(toolName: string): string {
    const displayNames: Record<string, string> = {
      'analyze_image': 'Image Analysis',
      'find_similar_images_by_image': 'Visual Similarity Search',
      'find_similar_images_by_description': 'Description-based Search',
      'find_images_by_tags': 'Tag-based Search',
      'get_image_analysis': 'Image Analysis Retrieval'
    };
    
    return displayNames[toolName] || toolName;
  }
  
  /**
   * Validate analyze_image tool result
   */
  private static validateAnalyzeImageResult(
    result: any, 
    errors: string[], 
    warnings: string[], 
    suggestions: ErrorSuggestion[]
  ): void {
    if (!result.description) {
      errors.push('Missing image description');
    } else if (result.description.length < 10) {
      warnings.push('Image description is very short');
      suggestions.push({
        message: 'Try analyzing with higher detail level',
        action: 'increase_detail',
        parameters: { detailLevel: 'comprehensive' }
      });
    }
    
    if (!result.tags || !Array.isArray(result.tags)) {
      warnings.push('No tags extracted from image');
    } else if (result.tags.length === 0) {
      warnings.push('No tags found in image');
    }
    
    if (result.confidence && result.confidence < 0.5) {
      warnings.push('Low confidence in analysis results');
      suggestions.push({
        message: 'Try with a clearer or higher-quality image',
        action: 'use_better_image'
      });
    }
  }
  
  /**
   * Validate search tool results
   */
  private static validateSearchResult(
    result: any, 
    errors: string[], 
    warnings: string[], 
    suggestions: ErrorSuggestion[]
  ): void {
    if (!result.results || !Array.isArray(result.results)) {
      errors.push('Invalid search results format');
      return;
    }
    
    if (result.results.length === 0) {
      warnings.push('No images found matching the criteria');
      suggestions.push({
        message: 'Try broadening your search criteria',
        action: 'broaden_search'
      });
    }
    
    if (result.total !== undefined && result.total !== result.results.length) {
      warnings.push('Result count mismatch');
    }
    
    // Validate individual result items
    result.results.forEach((item: any, index: number) => {
      if (!item.id) {
        warnings.push(`Result ${index + 1} missing ID`);
      }
      if (!item.url) {
        warnings.push(`Result ${index + 1} missing URL`);
      }
      if (!item.title && !item.description) {
        warnings.push(`Result ${index + 1} missing title and description`);
      }
    });
  }
  
  /**
   * Validate analysis retrieval result
   */
  private static validateAnalysisResult(
    result: any, 
    errors: string[], 
    warnings: string[], 
    suggestions: ErrorSuggestion[]
  ): void {
    if (!result.analysis) {
      errors.push('Missing analysis data');
      return;
    }
    
    if (!result.analysis.description) {
      warnings.push('Analysis missing description');
    }
    
    if (!result.analysis.tags || result.analysis.tags.length === 0) {
      warnings.push('Analysis missing tags');
    }
    
    if (result.imageInfo && !result.imageInfo.url) {
      warnings.push('Image information missing URL');
    }
  }
  
  /**
   * Generate analyze_image specific error messages
   */
  private static generateAnalyzeImageErrorMessage(errorType: string, originalError: string): string {
    const message = originalError.toLowerCase();
    
    // VL model specific errors
    if (message.includes('ai服务不可用') || message.includes('ai service unavailable')) {
      return 'The AI vision service is currently unavailable. Please try again in a moment.';
    }
    
    if (message.includes('图片格式不支持') || message.includes('format')) {
      return 'The image format is not supported. Please use JPEG, PNG, or WebP format with proper data URL prefix.';
    }
    
    if (message.includes('base64') && message.includes('必须')) {
      return 'Please provide a valid base64 encoded image with the proper data URL format (data:image/jpeg;base64,...)';
    }
    
    if (message.includes('图片分析失败') || message.includes('analysis failed')) {
      return 'I had trouble analyzing the image. This could be due to image quality, format, or temporary service issues.';
    }
    
    if (message.includes('向量生成失败') || message.includes('embedding')) {
      return 'I encountered an issue processing the image for similarity search. The image analysis may still work.';
    }
    
    // Size and quality issues
    if (message.includes('too large') || message.includes('size')) {
      return 'The image is too large to process. Please try with a smaller image (under 10MB).';
    }
    
    if (message.includes('corrupted') || message.includes('invalid')) {
      return 'The image appears to be corrupted or invalid. Please try with a different image.';
    }
    
    // Default analyze_image error
    switch (errorType) {
      case 'MISSING_PARAMETER':
        return 'Please provide an image to analyze. Upload an image or provide base64 image data.';
      case 'INVALID_FORMAT':
        return 'The image format is not supported. Please use JPEG, PNG, or WebP format.';
      case 'SERVICE_UNAVAILABLE':
        return 'The image analysis service is temporarily unavailable. Please try again in a moment.';
      case 'ANALYSIS_FAILED':
        return 'I had trouble analyzing the image. Please try with a clearer, higher-quality image.';
      default:
        return 'I encountered an issue analyzing the image. Please try again with a different image.';
    }
  }
  
  /**
   * Generate search tool specific error messages
   */
  private static generateSearchToolErrorMessage(toolName: string, errorType: string, originalError: string): string {
    const message = originalError.toLowerCase();
    
    // Common search errors
    if (message.includes('未找到') || message.includes('not found')) {
      if (toolName === 'find_similar_images_by_image') {
        return 'The reference image was not found. Please check the image ID or upload a new image.';
      }
      return 'No matching images were found. Try adjusting your search criteria.';
    }
    
    if (message.includes('向量数据') || message.includes('embedding')) {
      return 'The image doesn\'t have the necessary data for similarity search. Try analyzing the image first.';
    }
    
    if (message.includes('搜索失败') || message.includes('search failed')) {
      return 'The search operation failed. This might be due to database connectivity issues.';
    }
    
    if (message.includes('标签列表不能为空') || message.includes('tags')) {
      return 'Please provide at least one tag to search for.';
    }
    
    if (message.includes('描述文本不能为空') || message.includes('description')) {
      return 'Please provide a description of what you\'re looking for.';
    }
    
    // Tool-specific messages
    switch (toolName) {
      case 'find_similar_images_by_image':
        if (errorType === 'MISSING_PARAMETER') {
          return 'Please provide either an image ID or upload an image to find similar images.';
        }
        break;
      case 'find_similar_images_by_description':
        if (errorType === 'MISSING_PARAMETER') {
          return 'Please describe what kind of images you\'re looking for.';
        }
        break;
      case 'find_images_by_tags':
        if (errorType === 'MISSING_PARAMETER') {
          return 'Please specify which tags to search for.';
        }
        break;
    }
    
    return `I encountered an issue with the image search. Please try again.`;
  }
  
  /**
   * Handle parameter validation errors with specific corrections
   */
  static handleParameterValidationError(toolName: string, parameterName: string, value: any, constraint: string): {
    message: string;
    suggestion: ErrorSuggestion;
  } {
    let message = '';
    let suggestion: ErrorSuggestion = { message: 'Please correct the parameter and try again', action: 'fix_parameter' };
    
    switch (toolName) {
      case 'analyze_image':
        if (parameterName === 'imageBase64') {
          if (!value) {
            message = 'Image data is required for analysis';
            suggestion = { message: 'Please upload an image or provide base64 image data', action: 'upload_image' };
          } else if (!value.match(/^data:image\/(jpeg|jpg|png|webp);base64,/)) {
            message = 'Invalid image format or missing data URL prefix';
            suggestion = { 
              message: 'Ensure image is in JPEG, PNG, or WebP format with proper data URL prefix', 
              action: 'fix_format',
              parameters: { expectedFormat: 'data:image/jpeg;base64,...' }
            };
          }
        } else if (parameterName === 'detailLevel') {
          message = 'Invalid detail level specified';
          suggestion = { 
            message: 'Use "basic", "detailed", or "comprehensive"', 
            action: 'fix_detail_level',
            parameters: { validValues: ['basic', 'detailed', 'comprehensive'] }
          };
        }
        break;
        
      case 'find_similar_images_by_image':
        if (parameterName === 'threshold') {
          message = 'Similarity threshold must be between 0 and 1';
          suggestion = { 
            message: 'Use a value between 0.0 (very loose) and 1.0 (exact match)', 
            action: 'fix_threshold',
            parameters: { min: 0, max: 1, recommended: 0.5 }
          };
        } else if (parameterName === 'limit') {
          message = 'Result limit must be between 1 and 100';
          suggestion = { 
            message: 'Use a number between 1 and 100', 
            action: 'fix_limit',
            parameters: { min: 1, max: 100, recommended: 20 }
          };
        }
        break;
        
      case 'find_similar_images_by_description':
        if (parameterName === 'description') {
          if (!value || value.trim().length === 0) {
            message = 'Description cannot be empty';
            suggestion = { message: 'Please describe what kind of images you\'re looking for', action: 'provide_description' };
          } else if (value.length > 500) {
            message = 'Description is too long (maximum 500 characters)';
            suggestion = { 
              message: 'Shorten your description to under 500 characters', 
              action: 'shorten_description',
              parameters: { maxLength: 500, currentLength: value.length }
            };
          }
        }
        break;
        
      case 'find_images_by_tags':
        if (parameterName === 'tags') {
          if (!value || !Array.isArray(value) || value.length === 0) {
            message = 'At least one tag is required';
            suggestion = { message: 'Please specify which tags to search for', action: 'provide_tags' };
          } else if (value.length > 10) {
            message = 'Too many tags specified (maximum 10)';
            suggestion = { 
              message: 'Use fewer tags for better results', 
              action: 'reduce_tags',
              parameters: { maxTags: 10, currentCount: value.length }
            };
          }
        } else if (parameterName === 'matchMode') {
          message = 'Invalid match mode specified';
          suggestion = { 
            message: 'Use "any" (OR logic) or "all" (AND logic)', 
            action: 'fix_match_mode',
            parameters: { validValues: ['any', 'all'] }
          };
        }
        break;
    }
    
    return { message, suggestion };
  }
  
  /**
   * Generate context-aware error recovery suggestions
   */
  static generateContextualRecoveryOptions(
    toolName: string, 
    errorType: string, 
    parameters: any,
    previousAttempts: number = 0
  ): ErrorSuggestion[] {
    const suggestions: ErrorSuggestion[] = [];
    
    // Progressive suggestions based on attempt count
    if (previousAttempts === 0) {
      // First attempt - basic suggestions
      switch (errorType) {
        case 'ANALYSIS_FAILED':
          suggestions.push({
            message: 'Try with a different image',
            action: 'try_different_image'
          });
          break;
        case 'NO_RESULTS':
          suggestions.push({
            message: 'Broaden your search criteria',
            action: 'broaden_search'
          });
          break;
        case 'SERVICE_UNAVAILABLE':
          suggestions.push({
            message: 'Wait a moment and try again',
            action: 'retry_after_delay'
          });
          break;
      }
    } else if (previousAttempts === 1) {
      // Second attempt - more specific suggestions
      switch (toolName) {
        case 'analyze_image':
          suggestions.push({
            message: 'Try with basic detail level',
            action: 'reduce_detail_level',
            parameters: { detailLevel: 'basic' }
          });
          break;
        case 'find_similar_images_by_description':
          suggestions.push({
            message: 'Use simpler, more common words',
            action: 'simplify_description'
          });
          break;
        case 'find_images_by_tags':
          suggestions.push({
            message: 'Switch to "any" match mode',
            action: 'change_match_mode',
            parameters: { matchMode: 'any' }
          });
          break;
      }
    } else {
      // Multiple attempts - alternative approaches
      suggestions.push({
        message: 'Try a completely different approach',
        action: 'alternative_method'
      });
      
      if (toolName.includes('find_')) {
        suggestions.push({
          message: 'Use manual search instead',
          action: 'manual_search'
        });
      }
    }
    
    return suggestions;
  }
  
  /**
   * Validate tool execution environment and prerequisites
   */
  static validateToolEnvironment(toolName: string): ValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];
    const suggestions: ErrorSuggestion[] = [];
    
    // Check for required services based on tool
    switch (toolName) {
      case 'analyze_image':
        // Check if AI service is available
        if (typeof window !== 'undefined' && !window.electronAPI?.ai) {
          errors.push('AI service is not available');
          suggestions.push({
            message: 'Restart the application to initialize AI services',
            action: 'restart_app'
          });
        }
        break;
        
      case 'find_similar_images_by_image':
      case 'find_similar_images_by_description':
      case 'find_images_by_tags':
      case 'get_image_analysis':
        // Check if database service is available
        if (typeof window !== 'undefined' && !window.electronAPI?.database) {
          errors.push('Database service is not available');
          suggestions.push({
            message: 'Check database connection and restart if needed',
            action: 'check_database'
          });
        }
        break;
    }
    
    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      suggestions
    };
  }
  
  /**
   * Generate recovery suggestions based on error patterns
   */
  static generateRecoverySuggestions(
    errorHistory: Array<{ toolName: string; error: string; timestamp: Date }>
  ): ErrorSuggestion[] {
    const suggestions: ErrorSuggestion[] = [];
    
    if (errorHistory.length === 0) {
      return suggestions;
    }
    
    // Check for repeated failures
    const recentErrors = errorHistory.filter(
      h => Date.now() - h.timestamp.getTime() < 5 * 60 * 1000 // Last 5 minutes
    );
    
    if (recentErrors.length >= 3) {
      suggestions.push({
        message: 'Multiple recent failures detected. Consider restarting the image tools.',
        action: 'restart_tools'
      });
    }
    
    // Check for specific error patterns
    const serviceErrors = recentErrors.filter((h: any) => 
      h.error.toLowerCase().includes('service') || 
      h.error.toLowerCase().includes('unavailable')
    );
    
    if (serviceErrors.length >= 2) {
      suggestions.push({
        message: 'Image services may be experiencing issues. Try again later.',
        action: 'wait_and_retry'
      });
    }
    
    return suggestions;
  }
}

/**
 * Advanced Error Recovery and Suggestion System
 * Provides intelligent alternatives and parameter adjustments when tools fail
 */
export class ImageToolsRecoverySystem {
  
  /**
   * Suggest alternative tools when primary tool fails
   */
  static suggestAlternativeTools(
    failedToolName: string, 
    originalParameters: any, 
    errorType: string,
    userQuery?: string
  ): Array<{
    toolName: string;
    reason: string;
    adjustedParameters: any;
    confidence: number;
  }> {
    const alternatives: Array<{
      toolName: string;
      reason: string;
      adjustedParameters: any;
      confidence: number;
    }> = [];
    
    switch (failedToolName) {
      case 'analyze_image':
        // If image analysis fails, suggest getting existing analysis
        if (originalParameters.imageId) {
          alternatives.push({
            toolName: 'get_image_analysis',
            reason: 'Try retrieving existing analysis data for this image',
            adjustedParameters: {
              imageId: originalParameters.imageId,
              includeStructuredData: true
            },
            confidence: 0.8
          });
        }
        
        // Suggest simpler analysis
        if (originalParameters.detailLevel !== 'basic') {
          alternatives.push({
            toolName: 'analyze_image',
            reason: 'Try with basic analysis level for better reliability',
            adjustedParameters: {
              ...originalParameters,
              detailLevel: 'basic',
              focusAreas: ['objects', 'colors'] // Limit focus areas
            },
            confidence: 0.7
          });
        }
        break;
        
      case 'find_similar_images_by_image':
        // If image similarity search fails, try description-based search
        if (originalParameters.imageId) {
          alternatives.push({
            toolName: 'get_image_analysis',
            reason: 'Get image description first, then search by description',
            adjustedParameters: {
              imageId: originalParameters.imageId,
              includeStructuredData: false
            },
            confidence: 0.6
          });
        }
        
        // Try with lower threshold
        if (originalParameters.threshold > 0.3) {
          alternatives.push({
            toolName: 'find_similar_images_by_image',
            reason: 'Try with lower similarity threshold for broader results',
            adjustedParameters: {
              ...originalParameters,
              threshold: Math.max(0.2, (originalParameters.threshold || 0.5) - 0.2)
            },
            confidence: 0.8
          });
        }
        
        // Suggest tag-based search if we can extract tags
        if (userQuery) {
          const extractedTags = this.extractTagsFromQuery(userQuery);
          if (extractedTags.length > 0) {
            alternatives.push({
              toolName: 'find_images_by_tags',
              reason: 'Try searching by tags extracted from your query',
              adjustedParameters: {
                tags: extractedTags,
                matchMode: 'any',
                limit: originalParameters.limit || 20
              },
              confidence: 0.5
            });
          }
        }
        break;
        
      case 'find_similar_images_by_description':
        // If description search fails, try tag-based search
        const tags = this.extractTagsFromQuery(originalParameters.description);
        if (tags.length > 0) {
          alternatives.push({
            toolName: 'find_images_by_tags',
            reason: 'Try searching by individual keywords as tags',
            adjustedParameters: {
              tags: tags,
              matchMode: 'any',
              limit: originalParameters.limit || 20
            },
            confidence: 0.7
          });
        }
        
        // Try with simplified description
        const simplifiedDescription = this.simplifyDescription(originalParameters.description);
        if (simplifiedDescription !== originalParameters.description) {
          alternatives.push({
            toolName: 'find_similar_images_by_description',
            reason: 'Try with simplified description using common words',
            adjustedParameters: {
              ...originalParameters,
              description: simplifiedDescription,
              threshold: Math.max(0.2, (originalParameters.threshold || 0.5) - 0.1)
            },
            confidence: 0.6
          });
        }
        break;
        
      case 'find_images_by_tags':
        // If tag search fails with AND logic, try OR logic
        if (originalParameters.matchMode === 'all') {
          alternatives.push({
            toolName: 'find_images_by_tags',
            reason: 'Try with "any" match mode instead of requiring all tags',
            adjustedParameters: {
              ...originalParameters,
              matchMode: 'any'
            },
            confidence: 0.9
          });
        }
        
        // Try with fewer tags
        if (originalParameters.tags && originalParameters.tags.length > 3) {
          alternatives.push({
            toolName: 'find_images_by_tags',
            reason: 'Try with fewer, more common tags',
            adjustedParameters: {
              ...originalParameters,
              tags: originalParameters.tags.slice(0, 3),
              matchMode: 'any'
            },
            confidence: 0.7
          });
        }
        
        // Try description-based search
        if (originalParameters.tags && originalParameters.tags.length > 0) {
          const description = originalParameters.tags.join(' ');
          alternatives.push({
            toolName: 'find_similar_images_by_description',
            reason: 'Try searching by description using your tags as keywords',
            adjustedParameters: {
              description: description,
              limit: originalParameters.limit || 20,
              threshold: 0.4
            },
            confidence: 0.6
          });
        }
        break;
        
      case 'get_image_analysis':
        // If getting analysis fails, try analyzing the image fresh
        if (originalParameters.imageId) {
          alternatives.push({
            toolName: 'analyze_image',
            reason: 'Try analyzing the image fresh instead of retrieving stored analysis',
            adjustedParameters: {
              imageBase64: `image_id:${originalParameters.imageId}`, // Special format to indicate ID
              detailLevel: 'detailed'
            },
            confidence: 0.5
          });
        }
        break;
    }
    
    // Sort by confidence
    return alternatives.sort((a, b) => b.confidence - a.confidence);
  }
  
  /**
   * Generate parameter adjustment recommendations for failed searches
   */
  static generateParameterAdjustments(
    toolName: string,
    originalParameters: any,
    errorType: string,
    resultCount: number = 0
  ): Array<{
    parameter: string;
    originalValue: any;
    suggestedValue: any;
    reason: string;
    impact: 'low' | 'medium' | 'high';
  }> {
    const adjustments: Array<{
      parameter: string;
      originalValue: any;
      suggestedValue: any;
      reason: string;
      impact: 'low' | 'medium' | 'high';
    }> = [];
    
    switch (toolName) {
      case 'find_similar_images_by_image':
      case 'find_similar_images_by_description':
        // Adjust similarity threshold
        if (originalParameters.threshold !== undefined) {
          if (resultCount === 0 && originalParameters.threshold > 0.3) {
            adjustments.push({
              parameter: 'threshold',
              originalValue: originalParameters.threshold,
              suggestedValue: Math.max(0.2, originalParameters.threshold - 0.2),
              reason: 'Lower threshold to find more loosely related images',
              impact: 'high'
            });
          } else if (resultCount > 50 && originalParameters.threshold < 0.7) {
            adjustments.push({
              parameter: 'threshold',
              originalValue: originalParameters.threshold,
              suggestedValue: Math.min(0.8, originalParameters.threshold + 0.2),
              reason: 'Raise threshold to get more precise matches',
              impact: 'medium'
            });
          }
        }
        
        // Adjust result limit
        if (originalParameters.limit !== undefined) {
          if (resultCount === 0) {
            adjustments.push({
              parameter: 'limit',
              originalValue: originalParameters.limit,
              suggestedValue: Math.min(100, originalParameters.limit * 2),
              reason: 'Increase limit to cast a wider net',
              impact: 'low'
            });
          }
        }
        break;
        
      case 'find_images_by_tags':
        // Adjust match mode
        if (originalParameters.matchMode === 'all' && resultCount === 0) {
          adjustments.push({
            parameter: 'matchMode',
            originalValue: 'all',
            suggestedValue: 'any',
            reason: 'Use OR logic instead of AND to find images with any of the tags',
            impact: 'high'
          });
        }
        
        // Adjust tags
        if (originalParameters.tags && originalParameters.tags.length > 5 && resultCount === 0) {
          adjustments.push({
            parameter: 'tags',
            originalValue: originalParameters.tags,
            suggestedValue: originalParameters.tags.slice(0, 3),
            reason: 'Use fewer tags to broaden the search',
            impact: 'medium'
          });
        }
        break;
        
      case 'analyze_image':
        // Adjust detail level
        if (originalParameters.detailLevel === 'comprehensive' && errorType === 'TIMEOUT') {
          adjustments.push({
            parameter: 'detailLevel',
            originalValue: 'comprehensive',
            suggestedValue: 'detailed',
            reason: 'Use less detailed analysis for faster processing',
            impact: 'medium'
          });
        } else if (originalParameters.detailLevel === 'detailed' && errorType === 'ANALYSIS_FAILED') {
          adjustments.push({
            parameter: 'detailLevel',
            originalValue: 'detailed',
            suggestedValue: 'basic',
            reason: 'Use basic analysis for better reliability',
            impact: 'high'
          });
        }
        
        // Adjust focus areas
        if (originalParameters.focusAreas && originalParameters.focusAreas.length > 3) {
          adjustments.push({
            parameter: 'focusAreas',
            originalValue: originalParameters.focusAreas,
            suggestedValue: originalParameters.focusAreas.slice(0, 2),
            reason: 'Focus on fewer areas for more reliable analysis',
            impact: 'low'
          });
        }
        break;
    }
    
    return adjustments.sort((a, b) => {
      const impactOrder = { high: 3, medium: 2, low: 1 };
      return impactOrder[b.impact] - impactOrder[a.impact];
    });
  }
  
  /**
   * Provide troubleshooting guidance for common error scenarios
   */
  static generateTroubleshootingGuide(
    toolName: string,
    errorType: string,
    errorMessage: string,
    context: any = {}
  ): {
    steps: Array<{
      step: number;
      action: string;
      description: string;
      technical: boolean;
    }>;
    preventionTips: string[];
    relatedIssues: string[];
  } {
    const guide = {
      steps: [] as Array<{
        step: number;
        action: string;
        description: string;
        technical: boolean;
      }>,
      preventionTips: [] as string[],
      relatedIssues: [] as string[]
    };
    
    // Common troubleshooting steps based on error type
    switch (errorType) {
      case 'SERVICE_UNAVAILABLE':
        guide.steps = [
          {
            step: 1,
            action: 'Check Service Status',
            description: 'Verify that the required services are running',
            technical: true
          },
          {
            step: 2,
            action: 'Wait and Retry',
            description: 'Wait 30 seconds and try the operation again',
            technical: false
          },
          {
            step: 3,
            action: 'Restart Application',
            description: 'Close and reopen the application to reinitialize services',
            technical: false
          },
          {
            step: 4,
            action: 'Check Network Connection',
            description: 'Ensure you have a stable internet connection',
            technical: false
          }
        ];
        guide.preventionTips = [
          'Keep the application updated to the latest version',
          'Ensure stable internet connection before starting image operations',
          'Avoid running too many image operations simultaneously'
        ];
        break;
        
      case 'INVALID_FORMAT':
        guide.steps = [
          {
            step: 1,
            action: 'Check Image Format',
            description: 'Ensure the image is in JPEG, PNG, or WebP format',
            technical: false
          },
          {
            step: 2,
            action: 'Verify File Size',
            description: 'Make sure the image is under 10MB',
            technical: false
          },
          {
            step: 3,
            action: 'Convert Image',
            description: 'Convert the image to a supported format using an image editor',
            technical: false
          },
          {
            step: 4,
            action: 'Check Data URL Format',
            description: 'For base64 images, ensure proper data URL prefix (data:image/jpeg;base64,...)',
            technical: true
          }
        ];
        guide.preventionTips = [
          'Use common image formats (JPEG, PNG, WebP)',
          'Keep image files under 10MB for best performance',
          'Avoid corrupted or partially downloaded images'
        ];
        break;
        
      case 'NO_RESULTS':
        if (toolName === 'find_similar_images_by_description') {
          guide.steps = [
            {
              step: 1,
              action: 'Simplify Description',
              description: 'Use simpler, more common words in your description',
              technical: false
            },
            {
              step: 2,
              action: 'Lower Similarity Threshold',
              description: 'Reduce the similarity threshold to 0.3 or lower',
              technical: false
            },
            {
              step: 3,
              action: 'Try Different Keywords',
              description: 'Use alternative words or synonyms',
              technical: false
            },
            {
              step: 4,
              action: 'Switch to Tag Search',
              description: 'Try searching by individual tags instead',
              technical: false
            }
          ];
        } else if (toolName === 'find_images_by_tags') {
          guide.steps = [
            {
              step: 1,
              action: 'Change Match Mode',
              description: 'Switch from "all" to "any" to use OR logic',
              technical: false
            },
            {
              step: 2,
              action: 'Use Fewer Tags',
              description: 'Try with 2-3 most important tags',
              technical: false
            },
            {
              step: 3,
              action: 'Check Tag Spelling',
              description: 'Verify that tag names are spelled correctly',
              technical: false
            },
            {
              step: 4,
              action: 'Browse Available Tags',
              description: 'Check what tags are actually available in the database',
              technical: false
            }
          ];
        }
        guide.preventionTips = [
          'Start with broad searches and then narrow down',
          'Use common, descriptive terms',
          'Check available tags before searching'
        ];
        break;
        
      case 'ANALYSIS_FAILED':
        guide.steps = [
          {
            step: 1,
            action: 'Check Image Quality',
            description: 'Ensure the image is clear and not corrupted',
            technical: false
          },
          {
            step: 2,
            action: 'Reduce Detail Level',
            description: 'Try with "basic" instead of "detailed" or "comprehensive"',
            technical: false
          },
          {
            step: 3,
            action: 'Limit Focus Areas',
            description: 'Specify fewer focus areas (e.g., just "objects" and "colors")',
            technical: false
          },
          {
            step: 4,
            action: 'Try Different Image',
            description: 'Test with a different, simpler image',
            technical: false
          }
        ];
        guide.preventionTips = [
          'Use high-quality, clear images',
          'Avoid extremely complex or abstract images',
          'Start with basic analysis before trying comprehensive'
        ];
        break;
    }
    
    // Add tool-specific related issues
    switch (toolName) {
      case 'analyze_image':
        guide.relatedIssues = [
          'Image format not supported',
          'AI service connection issues',
          'Image file corrupted or incomplete',
          'Image too large or complex to process'
        ];
        break;
      case 'find_similar_images_by_image':
        guide.relatedIssues = [
          'Reference image not found in database',
          'Image missing embedding data',
          'Database connection issues',
          'Similarity threshold too restrictive'
        ];
        break;
      case 'find_similar_images_by_description':
        guide.relatedIssues = [
          'Description too vague or complex',
          'No images match the description',
          'Search service unavailable',
          'Language or terminology mismatch'
        ];
        break;
      case 'find_images_by_tags':
        guide.relatedIssues = [
          'Tags not found in database',
          'Match mode too restrictive',
          'Tag spelling or format issues',
          'Database indexing problems'
        ];
        break;
    }
    
    return guide;
  }
  
  /**
   * Extract potential tags from a user query
   */
  private static extractTagsFromQuery(query: string): string[] {
    if (!query) return [];
    
    // Simple keyword extraction - in a real implementation, this could use NLP
    const commonWords = ['the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'is', 'are', 'was', 'were', 'be', 'been', 'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could', 'should', 'may', 'might', 'can', 'this', 'that', 'these', 'those'];
    
    return query
      .toLowerCase()
      .replace(/[^\w\s]/g, ' ')
      .split(/\s+/)
      .filter((word: any) => word.length > 2 && !commonWords.includes(word))
      .slice(0, 5); // Limit to 5 tags
  }
  
  /**
   * Simplify a description by removing complex words and phrases
   */
  private static simplifyDescription(description: string): string {
    if (!description) return '';
    
    // Simple simplification - replace complex phrases with simpler ones
    const simplifications: Record<string, string> = {
      'photograph': 'photo',
      'image': 'picture',
      'depicting': 'showing',
      'containing': 'with',
      'featuring': 'with',
      'illustration': 'drawing',
      'representation': 'picture',
      'magnificent': 'beautiful',
      'spectacular': 'amazing',
      'extraordinary': 'special'
    };
    
    let simplified = description.toLowerCase();
    
    Object.entries(simplifications).forEach(([complex, simple]) => {
      simplified = simplified.replace(new RegExp(complex, 'g'), simple);
    });
    
    // Remove very descriptive adjectives that might be too specific
    const complexAdjectives = ['magnificent', 'spectacular', 'extraordinary', 'phenomenal', 'remarkable', 'exceptional'];
    complexAdjectives.forEach((adj: any) => {
      simplified = simplified.replace(new RegExp(`\\b${adj}\\b`, 'g'), '');
    });
    
    // Clean up extra spaces
    simplified = simplified.replace(/\s+/g, ' ').trim();
    
    return simplified;
  }
}