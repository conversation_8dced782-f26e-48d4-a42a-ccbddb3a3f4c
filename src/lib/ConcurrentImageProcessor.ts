/**
 * 并发图片处理器
 * 
 * 提供高效的并发图片处理功能：
 * - 控制并发处理数量，避免资源过度占用
 * - 支持优先级队列，优先处理重要图片
 * - 提供批量处理和进度跟踪
 * - 支持处理超时和错误恢复
 */

import { logger } from './LoggerService'
import { ImagePathError, ImagePathErrorType } from '../types/errors'

// 处理任务
interface ProcessTask<T> {
  id: string
  path: string
  priority: number
  processor: (path: string) => Promise<T>
  resolve: (result: T) => void
  reject: (error: Error) => void
  startTime?: number
  timeout?: number
}

// 处理结果
export interface ProcessResult<T> {
  successful: Array<{ path: string; result: T }>
  failed: Array<{ path: string; error: Error }>
}

// 处理选项
export interface ProcessOptions {
  priority?: 'high' | 'normal' | 'low'
  timeout?: number
  metadata?: Record<string, any>
}

// 批处理选项
export interface BatchProcessOptions extends ProcessOptions {
  concurrency?: number
  progressCallback?: (completed: number, total: number) => void
}

/**
 * 并发图片处理器类
 */
export class ConcurrentImageProcessor {
  private queue: ProcessTask<any>[] = []
  private processing = new Set<string>()
  private maxConcurrent: number
  private isProcessing = false
  private priorityMap = {
    high: 100,
    normal: 50,
    low: 10
  }

  constructor(maxConcurrent = 5) {
    this.maxConcurrent = maxConcurrent
  }

  /**
   * 处理单个图片
   * @param path 图片路径
   * @param processor 处理函数
   * @param options 处理选项
   */
  async processImage<T>(
    path: string,
    processor: (path: string) => Promise<T>,
    options: ProcessOptions = {}
  ): Promise<T> {
    const taskId = `${path}_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`
    const priority = this.getPriorityValue(options.priority || 'normal')
    
    // 创建处理任务
    const task = new Promise<T>((resolve, reject) => {
      this.queue.push({
        id: taskId,
        path,
        priority,
        processor,
        resolve,
        reject,
        timeout: options.timeout
      })
    })
    
    // 开始处理队列
    this.processQueue()
    
    return task
  }

  /**
   * 批量处理图片
   * @param paths 图片路径列表
   * @param processor 处理函数
   * @param options 批处理选项
   */
  async batchProcess<T>(
    paths: string[],
    processor: (path: string) => Promise<T>,
    options: BatchProcessOptions = {}
  ): Promise<ProcessResult<T>> {
    const concurrency = options.concurrency || this.maxConcurrent
    const results: ProcessResult<T> = {
      successful: [],
      failed: []
    }
    
    // 如果没有图片，直接返回
    if (paths.length === 0) {
      return results
    }
    
    // 开始性能计时
    const perfMarkId = `batchProcess_${Date.now()}`
    logger.startPerformanceTimer(perfMarkId)
    
    let completed = 0
    const total = paths.length
    
    // 创建处理任务
    const tasks = paths.map(path => 
      this.processImage(path, processor, options)
        .then(result => {
          results.successful.push({ path, result })
          completed++
          options.progressCallback?.(completed, total)
          return { success: true, path, result }
        })
        .catch(error => {
          results.failed.push({ path, error })
          completed++
          options.progressCallback?.(completed, total)
          return { success: false, path, error }
        })
    )
    
    // 等待所有任务完成
    await Promise.allSettled(tasks)
    
    // 记录性能指标
    logger.endPerformanceTimer(perfMarkId, 'batchProcess', {
      total,
      successful: results.successful.length,
      failed: results.failed.length
    })
    
    return results
  }

  /**
   * 处理队列
   */
  private async processQueue(): Promise<void> {
    // 如果已经在处理，直接返回
    if (this.isProcessing) {
      return
    }
    
    this.isProcessing = true
    
    try {
      while (this.queue.length > 0 && this.processing.size < this.maxConcurrent) {
        // 按优先级排序队列
        this.queue.sort((a, b) => b.priority - a.priority)
        
        // 获取下一个任务
        const task = this.queue.shift()
        if (!task) {
          continue
        }
        
        // 添加到处理集合
        this.processing.add(task.id)
        
        // 设置开始时间
        task.startTime = Date.now()
        
        // 处理任务
        this.processTask(task).finally(() => {
          // 从处理集合中移除
          this.processing.delete(task.id)
          
          // 继续处理队列
          this.processQueue()
        })
      }
    } finally {
      this.isProcessing = false
    }
  }

  /**
   * 处理单个任务
   */
  private async processTask<T>(task: ProcessTask<T>): Promise<void> {
    try {
      // 创建超时Promise
      const timeoutPromise = task.timeout 
        ? new Promise<never>((_, reject) => {
            setTimeout(() => {
              reject(new ImagePathError(
                ImagePathErrorType.TIMEOUT_ERROR,
                `处理超时（${task.timeout}ms）`,
                task.path
              ))
            }, task.timeout)
          })
        : null
      
      // 创建处理Promise
      const processingPromise = task.processor(task.path)
      
      // 使用Promise.race处理超时
      const result = timeoutPromise 
        ? await Promise.race([processingPromise, timeoutPromise])
        : await processingPromise
      
      // 成功处理
      task.resolve(result)
    } catch (error) {
      // 处理错误
      const imageError = error instanceof ImagePathError 
        ? error 
        : new ImagePathError(
            ImagePathErrorType.UNKNOWN_ERROR,
            error instanceof Error ? error.message : String(error),
            task.path,
            error instanceof Error ? error : undefined
          )
      
      task.reject(imageError)
    }
  }

  /**
   * 获取优先级数值
   */
  private getPriorityValue(priority: 'high' | 'normal' | 'low'): number {
    return this.priorityMap[priority] || this.priorityMap.normal
  }

  /**
   * 设置最大并发数
   */
  setMaxConcurrent(value: number): void {
    this.maxConcurrent = Math.max(1, value)
    
    // 如果降低了并发数，可能需要停止一些任务
    if (this.processing.size > this.maxConcurrent) {
      // 这里不做处理，让当前任务完成
      // 新的并发限制会在下一次处理队列时生效
    } else {
      // 如果增加了并发数，可能需要处理更多任务
      this.processQueue()
    }
  }

  /**
   * 获取当前状态
   */
  getStatus(): {
    queueLength: number
    processing: number
    maxConcurrent: number
  } {
    return {
      queueLength: this.queue.length,
      processing: this.processing.size,
      maxConcurrent: this.maxConcurrent
    }
  }

  /**
   * 清空队列
   */
  clearQueue(): number {
    const count = this.queue.length
    this.queue = []
    return count
  }
}

// 导出单例实例
export const concurrentImageProcessor = new ConcurrentImageProcessor()