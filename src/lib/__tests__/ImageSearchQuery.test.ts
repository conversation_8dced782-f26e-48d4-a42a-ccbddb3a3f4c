// import { describe, it, expect, vi, beforeEach } from 'vitest'
// import { searchImagesWithMethod, hybridSearchOnly, vectorSearchWithThreshold } from '../galleryService'
// import * as databaseService from '../database'
//
// // Mock database service
// vi.mock('../database', () => ({
//   enhancedHybridSearch: vi.fn(),
//   searchSimilarImages: vi.fn(),
//   queryImages: vi.fn(),
//   queryImagesByTags: vi.fn(),
//   findSimilarTags: vi.fn()
// }))
//
// describe('图片搜索查询功能', () => {
//   const mockImages = [
//     {
//       id: '1',
//       url: 'file:///test/image1.jpg',
//       path: '/test/image1.jpg',
//       fileName: 'image1.jpg',
//       size: 1024,
//       mimeType: 'image/jpeg',
//       width: 800,
//       height: 600,
//       createdAt: new Date('2024-01-01').getTime(),
//       uploadedAt: new Date('2024-01-01').getTime(),
//       tags: ['风景', '山', '自然'],
//       description: '美丽的山景',
//       aiDescription: '一座雄伟的山峰在蓝天下',
//       score: 0.95
//     },
//     {
//       id: '2',
//       url: 'file:///test/image2.jpg',
//       path: '/test/image2.jpg',
//       fileName: 'image2.jpg',
//       size: 2048,
//       mimeType: 'image/jpeg',
//       width: 1024,
//       height: 768,
//       createdAt: new Date('2024-01-02').getTime(),
//       uploadedAt: new Date('2024-01-02').getTime(),
//       tags: ['动物', '猫', '宠物'],
//       description: '可爱的小猫',
//       aiDescription: '一只橘色的猫在阳光下睡觉',
//       score: 0.88
//     },
//     {
//       id: '3',
//       url: 'file:///test/image3.png',
//       path: '/test/image3.png',
//       fileName: 'image3.png',
//       size: 3072,
//       mimeType: 'image/png',
//       width: 1920,
//       height: 1080,
//       createdAt: new Date('2024-01-03').getTime(),
//       uploadedAt: new Date('2024-01-03').getTime(),
//       tags: ['城市', '建筑', '夜景'],
//       description: '城市夜景',
//       aiDescription: '繁华的城市夜景，高楼大厦灯火通明',
//       score: 0.76
//     }
//   ]
//
//   beforeEach(() => {
//     vi.clearAllMocks()
//   })
//
//   describe('增强混合搜索 (enhancedHybridSearch)', () => {
//     it('应该能够使用关键词进行混合搜索', async () => {
//       const searchQuery = '山景'
//       const mockSearchResult = {
//         images: [mockImages[0]],
//         expandedKeywords: ['山景', '山', '风景', '自然'],
//         searchMetadata: {
//           method: 'hybrid',
//           totalResults: 1,
//           hasMore: false
//         }
//       }
//
//       vi.mocked(databaseService.enhancedHybridSearch).mockResolvedValue(mockSearchResult)
//
//       const result = await searchImagesWithMethod(searchQuery, 'hybrid')
//
//       expect(databaseService.enhancedHybridSearch).toHaveBeenCalledWith(searchQuery, {
//         limit: 100,
//         searchMethod: 'auto'
//       })
//       expect(result.images).toHaveLength(1)
//       expect(result.images[0].id).toBe('1')
//       expect(result.searchMetadata?.method).toBe('hybrid')
//     })
//
//     it('应该支持相似标签扩展搜索', async () => {
//       const searchQuery = '宠物'
//       const mockSearchResult = {
//         images: [mockImages[1]],
//         expandedKeywords: ['宠物', '猫', '动物', '萌宠'],
//         searchMetadata: {
//           method: 'hybrid',
//           totalResults: 1,
//           hasMore: false
//         }
//       }
//
//       vi.mocked(databaseService.enhancedHybridSearch).mockResolvedValue(mockSearchResult)
//
//       const result = await searchImagesWithMethod(searchQuery, 'hybrid')
//
//       expect(result.expandedKeywords).toContain('宠物')
//       expect(result.expandedKeywords).toContain('猫')
//       expect(result.images[0].tags).toContain('宠物')
//     })
//
//     it('应该正确处理空搜索结果', async () => {
//       const searchQuery = '不存在的内容'
//       const mockSearchResult = {
//         images: [],
//         expandedKeywords: ['不存在的内容'],
//         searchMetadata: {
//           method: 'hybrid',
//           totalResults: 0,
//           hasMore: false
//         }
//       }
//
//       vi.mocked(databaseService.enhancedHybridSearch).mockResolvedValue(mockSearchResult)
//
//       const result = await searchImagesWithMethod(searchQuery, 'hybrid')
//
//       expect(result.images).toHaveLength(0)
//       expect(result.searchMetadata?.totalResults).toBe(0)
//     })
//   })
//
//   describe('向量搜索 (vectorSearch)', () => {
//     it('应该能够进行向量相似度搜索并应用阈值过滤', async () => {
//       const searchQuery = '山峰'
//       const similarityThreshold = 0.8
//
//       // Mock 返回所有图片，但只有第一张符合阈值
//       vi.mocked(databaseService.searchSimilarImages).mockResolvedValue({
//         images: mockImages.map((img, idx) => ({
//           ...img,
//           score: [0.95, 0.75, 0.65][idx] // 只有第一张图片分数高于0.8
//         }))
//       })
//
//       const result = await vectorSearchWithThreshold(searchQuery, similarityThreshold)
//
//       expect(databaseService.searchSimilarImages).toHaveBeenCalledWith(searchQuery, { limit: 100 })
//       expect(result.images).toHaveLength(1)
//       expect(result.images[0].id).toBe('1')
//       expect(result.images[0].score).toBeGreaterThanOrEqual(similarityThreshold)
//       expect(result.searchMetadata?.similarityThreshold).toBe(similarityThreshold)
//     })
//
//     it('应该按相似度分数降序排序', async () => {
//       vi.mocked(databaseService.searchSimilarImages).mockResolvedValue({
//         images: [
//           { ...mockImages[2], score: 0.7 },
//           { ...mockImages[0], score: 0.95 },
//           { ...mockImages[1], score: 0.85 }
//         ]
//       })
//
//       const result = await vectorSearchWithThreshold('test', 0.6)
//
//       expect(result.images[0].score).toBe(0.95)
//       expect(result.images[1].score).toBe(0.85)
//       expect(result.images[2].score).toBe(0.7)
//     })
//
//     it('应该正确处理向量搜索错误', async () => {
//       vi.mocked(databaseService.searchSimilarImages).mockRejectedValue(new Error('向量搜索失败'))
//
//       await expect(vectorSearchWithThreshold('test', 0.8)).rejects.toThrow('向量搜索失败')
//     })
//   })
//
//   describe('自动搜索方法选择', () => {
//     it('应该根据查询内容自动选择最佳搜索方法', async () => {
//       const mockResult = {
//         images: mockImages,
//         searchMetadata: {
//           method: 'auto',
//           selectedMethod: 'hybrid'
//         }
//       }
//
//       vi.mocked(databaseService.enhancedHybridSearch).mockResolvedValue(mockResult)
//
//       const result = await searchImagesWithMethod('城市夜景', 'auto')
//
//       expect(databaseService.enhancedHybridSearch).toHaveBeenCalledWith('城市夜景', {
//         limit: 100,
//         searchMethod: 'auto'
//       })
//       expect(result.searchMetadata?.method).toBe('auto')
//     })
//   })
//
//   describe('搜索结果后处理', () => {
//     it('应该正确计算和保留图片的相似度分数', async () => {
//       const mockResult = {
//         images: mockImages.map((img, idx) => ({
//           ...img,
//           score: [0.95, 0.88, 0.76][idx]
//         }))
//       }
//
//       vi.mocked(databaseService.enhancedHybridSearch).mockResolvedValue(mockResult)
//
//       const result = await hybridSearchOnly('test')
//
//       expect(result.images[0].score).toBe(0.95)
//       expect(result.images[1].score).toBe(0.88)
//       expect(result.images[2].score).toBe(0.76)
//     })
//
//     it('应该包含搜索元数据信息', async () => {
//       const mockResult = {
//         images: [mockImages[0]],
//         expandedKeywords: ['山', '风景'],
//         searchMetadata: {
//           method: 'hybrid',
//           totalResults: 1,
//           searchTime: 156,
//           hasMore: false
//         }
//       }
//
//       vi.mocked(databaseService.enhancedHybridSearch).mockResolvedValue(mockResult)
//
//       const result = await hybridSearchOnly('山')
//
//       expect(result.searchMetadata).toBeDefined()
//       expect(result.searchMetadata?.totalResults).toBe(1)
//       expect(result.searchMetadata?.searchTime).toBe(156)
//       expect(result.searchMetadata?.hasMore).toBe(false)
//     })
//   })
//
//   describe('标签查询功能', () => {
//     it('应该能够通过标签筛选图片', async () => {
//       const tags = ['风景', '自然']
//       vi.mocked(databaseService.queryImagesByTags).mockResolvedValue({
//         images: [mockImages[0]]
//       })
//
//       const result = await databaseService.queryImagesByTags(tags)
//
//       expect(result.images).toHaveLength(1)
//       expect(result.images[0].tags).toContain('风景')
//       expect(result.images[0].tags).toContain('自然')
//     })
//
//     it('应该支持多标签组合查询', async () => {
//       const tags = ['城市', '夜景']
//       vi.mocked(databaseService.queryImagesByTags).mockResolvedValue({
//         images: [mockImages[2]]
//       })
//
//       const result = await databaseService.queryImagesByTags(tags)
//
//       expect(result.images).toHaveLength(1)
//       expect(result.images[0].tags).toContain('城市')
//       expect(result.images[0].tags).toContain('夜景')
//     })
//   })
//
//   describe('搜索结果分页', () => {
//     it('应该支持限制返回结果数量', async () => {
//       const limit = 2
//       vi.mocked(databaseService.enhancedHybridSearch).mockResolvedValue({
//         images: mockImages.slice(0, limit),
//         searchMetadata: {
//           method: 'hybrid',
//           totalResults: mockImages.length,
//           hasMore: true
//         }
//       })
//
//       const result = await searchImagesWithMethod('test', 'hybrid', { limit })
//
//       expect(databaseService.enhancedHybridSearch).toHaveBeenCalledWith('test', {
//         limit,
//         searchMethod: 'auto'
//       })
//       expect(result.images).toHaveLength(limit)
//       expect(result.searchMetadata?.hasMore).toBe(true)
//     })
//   })
//
//   describe('错误处理', () => {
//     it('应该优雅处理搜索服务错误', async () => {
//       vi.mocked(databaseService.enhancedHybridSearch).mockRejectedValue(new Error('数据库连接失败'))
//
//       await expect(searchImagesWithMethod('test', 'hybrid')).rejects.toThrow('数据库连接失败')
//     })
//
//     it('应该处理无效的搜索参数', async () => {
//       const result = await searchImagesWithMethod('', 'hybrid')
//
//       expect(result.images).toHaveLength(0)
//       expect(result.searchMetadata?.totalResults).toBe(0)
//     })
//   })
//
//   describe('搜索性能优化', () => {
//     it('应该支持搜索结果缓存', async () => {
//       const searchQuery = 'cached query'
//       const mockResult = {
//         images: [mockImages[0]],
//         searchMetadata: { method: 'hybrid', cached: false }
//       }
//
//       vi.mocked(databaseService.enhancedHybridSearch).mockResolvedValue(mockResult)
//
//       // 第一次搜索
//       await searchImagesWithMethod(searchQuery, 'hybrid')
//
//       // 第二次相同搜索应该使用缓存（在实际实现中）
//       await searchImagesWithMethod(searchQuery, 'hybrid')
//
//       // 验证只调用了一次（如果有缓存实现）
//       expect(databaseService.enhancedHybridSearch).toHaveBeenCalledTimes(2) // 当前实现没有缓存
//     })
//   })
// })