// import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
// import { TagSearchService, type TagSearchResult } from '../tagSearchService'
// import * as databaseService from '../database'
//
// // Mock database service
// vi.mock('../database', () => ({
//   databaseService: {
//     queryAllTags: vi.fn(),
//     findSimilarTags: vi.fn()
//   }
// }))
//
// describe('TagSearchService', () => {
//   let tagSearchService: TagSearchService
//   const mockTags = [
//     '风景', '山', '自然', '城市', '建筑', '动物', '猫', '狗', '宠物',
//     '美食', '旅行', '夜景', '日出', '日落', '海滩', '森林', '花', '树',
//     '红色', '蓝色', '绿色', '现代', '复古', '艺术', '摄影', '人物'
//   ]
//
//   beforeEach(() => {
//     vi.clearAllMocks()
//     // 重置单例实例
//     TagSearchService['instance'] = null
//     tagSearchService = TagSearchService.getInstance()
//   })
//
//   afterEach(() => {
//     TagSearchService['instance'] = null
//   })
//
//   describe('单例模式', () => {
//     it('应该返回同一个实例', () => {
//       const instance1 = TagSearchService.getInstance()
//       const instance2 = TagSearchService.getInstance()
//       expect(instance1).toBe(instance2)
//     })
//   })
//
//   describe('标签初始化', () => {
//     it('应该能够成功初始化标签数据', async () => {
//       vi.mocked(databaseService.databaseService.queryAllTags).mockResolvedValue({
//         tags: mockTags,
//         error: null
//       })
//
//       await tagSearchService.initializeTags()
//
//       expect(databaseService.databaseService.queryAllTags).toHaveBeenCalledTimes(1)
//     })
//
//     it('应该处理初始化失败的情况', async () => {
//       vi.mocked(databaseService.databaseService.queryAllTags).mockResolvedValue({
//         tags: [],
//         error: 'Database connection failed'
//       })
//
//       const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {})
//
//       await tagSearchService.initializeTags()
//
//       expect(consoleSpy).not.toHaveBeenCalledWith('标签搜索服务: 初始化失败', expect.any(Error))
//       consoleSpy.mockRestore()
//     })
//
//     it('应该避免重复初始化', async () => {
//       vi.mocked(databaseService.databaseService.queryAllTags).mockResolvedValue({
//         tags: mockTags,
//         error: null
//       })
//
//       await tagSearchService.initializeTags()
//       await tagSearchService.initializeTags() // 第二次调用
//
//       expect(databaseService.databaseService.queryAllTags).toHaveBeenCalledTimes(1)
//     })
//   })
//
//   describe('标签搜索', () => {
//     beforeEach(async () => {
//       vi.mocked(databaseService.databaseService.queryAllTags).mockResolvedValue({
//         tags: mockTags,
//         error: null
//       })
//       await tagSearchService.initializeTags()
//     })
//
//     it('应该能够进行基本的文本搜索', async () => {
//       const results = await tagSearchService.searchTags('风景', {
//         useVector: false,
//         useFuzzy: true
//       })
//
//       expect(results).toEqual(
//         expect.arrayContaining([
//           expect.objectContaining({
//             tag: '风景',
//             similarity: expect.any(Number),
//             category: 'scene',
//             score: expect.any(Number)
//           })
//         ])
//       )
//     })
//
//     it('应该能够进行混合搜索（文本+向量）', async () => {
//       const vectorResults: TagSearchResult[] = [
//         {
//           tag: '自然',
//           similarity: 0.85,
//           frequency: 5,
//           category: 'scene',
//           score: 0.85
//         }
//       ]
//
//       vi.mocked(databaseService.databaseService.findSimilarTags).mockResolvedValue(vectorResults)
//
//       const results = await tagSearchService.searchTags('风景', {
//         useFuzzy: true,
//         useVector: true
//       })
//
//       expect(results.length).toBeGreaterThan(0)
//       expect(databaseService.databaseService.findSimilarTags).toHaveBeenCalledWith('风景', {
//         limit: 10,
//         threshold: 0.3,
//         category: undefined,
//         includeFrequency: true
//       })
//     })
//
//     it('应该处理空查询', async () => {
//       const results = await tagSearchService.searchTags('')
//       expect(results).toEqual([])
//     })
//
//     it('应该处理空格查询', async () => {
//       const results = await tagSearchService.searchTags('   ')
//       expect(results).toEqual([])
//     })
//
//     it('应该支持分类过滤', async () => {
//       const results = await tagSearchService.searchTags('红', {
//         category: 'color',
//         useVector: false
//       })
//
//       results.forEach(result => {
//         expect(result.category).toBe('color')
//       })
//     })
//
//     it('应该支持限制结果数量', async () => {
//       const limit = 5
//       const results = await tagSearchService.searchTags('风景', {
//         limit,
//         useVector: false
//       })
//
//       expect(results.length).toBeLessThanOrEqual(limit)
//     })
//
//     it('应该处理向量搜索失败的情况', async () => {
//       vi.mocked(databaseService.databaseService.findSimilarTags).mockRejectedValue(
//         new Error('Vector search failed')
//       )
//
//       const consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => {})
//
//       const results = await tagSearchService.searchTags('风景', {
//         useFuzzy: true,
//         useVector: true
//       })
//
//       expect(consoleSpy).toHaveBeenCalledWith(
//         '向量搜索失败，仅使用文本搜索:',
//         expect.any(Error)
//       )
//       expect(results.length).toBeGreaterThan(0) // 应该仍有文本搜索结果
//
//       consoleSpy.mkockRestore()
//     })
//   })
//
//   describe('文本相似度计算', () => {
//     beforeEach(async () => {
//       vi.mocked(databaseService.databaseService.queryAllTags).mockResolvedValue({
//         tags: ['测试标签', '测试', '标签', '完全不同'],
//         error: null
//       })
//       await tagSearchService.initializeTags()
//     })
//
//     it('应该正确处理完全匹配', async () => {
//       const results = await tagSearchService.searchTags('测试标签', {
//         useVector: false
//       })
//
//       const exactMatch = results.find(r => r.tag === '测试标签')
//       expect(exactMatch?.similarity).toBe(1.0)
//     })
//
//     it('应该正确处理包含匹配', async () => {
//       const results = await tagSearchService.searchTags('测试', {
//         useVector: false
//       })
//
//       const containsMatch = results.find(r => r.tag === '测试标签')
//       expect(containsMatch?.similarity).toBe(0.8)
//     })
//
//     it('应该按相似度降序排序', async () => {
//       const results = await tagSearchService.searchTags('测试', {
//         useVector: false
//       })
//
//       for (let i = 1; i < results.length; i++) {
//         expect(results[i - 1].similarity).toBeGreaterThanOrEqual(results[i].similarity)
//       }
//     })
//   })
//
//   describe('标签分类', () => {
//     const testCases = [
//       { tag: '红色', expectedCategory: 'color' },
//       { tag: 'red', expectedCategory: 'color' },
//       { tag: '风景', expectedCategory: 'scene' },
//       { tag: '建筑', expectedCategory: 'object' },
//       { tag: '跑步', expectedCategory: 'action' },
//       { tag: '衬衫', expectedCategory: 'clothing' },
//       { tag: '快乐', expectedCategory: 'mood' },
//       { tag: '城市', expectedCategory: 'location' },
//       { tag: '日出', expectedCategory: 'time' },
//       { tag: '复古', expectedCategory: 'style' },
//       { tag: '家庭', expectedCategory: 'relationship' },
//       { tag: '商务', expectedCategory: 'domain' },
//       { tag: '未知标签', expectedCategory: 'other' }
//     ]
//
//     beforeEach(async () => {
//       vi.mocked(databaseService.databaseService.queryAllTags).mockResolvedValue({
//         tags: testCases.map(tc => tc.tag),
//         error: null
//       })
//       await tagSearchService.initializeTags()
//     })
//
//     testCases.forEach(({ tag, expectedCategory }) => {
//       it(`应该将"${tag}"分类为"${expectedCategory}"`, async () => {
//         const results = await tagSearchService.searchTags(tag, {
//           useVector: false
//         })
//
//         const tagResult = results.find(r => r.tag === tag)
//         expect(tagResult?.category).toBe(expectedCategory)
//       })
//     })
//   })
//
//   describe('批量搜索', () => {
//     beforeEach(async () => {
//       vi.mocked(databaseService.databaseService.queryAllTags).mockResolvedValue({
//         tags: mockTags,
//         error: null
//       })
//       await tagSearchService.initializeTags()
//     })
//
//     it('应该能够批量搜索多个查询', async () => {
//       const queries = ['风景', '动物', '美食']
//       const results = await tagSearchService.searchTagsBatch(queries, {
//         useVector: false
//       })
//
//       expect(results.size).toBe(3)
//       queries.forEach(query => {
//         expect(results.has(query)).toBe(true)
//         expect(results.get(query)).toEqual(expect.any(Array))
//       })
//     })
//   })
//
//   describe('热门标签', () => {
//     beforeEach(async () => {
//       vi.mocked(databaseService.databaseService.queryAllTags).mockResolvedValue({
//         tags: mockTags,
//         error: null
//       })
//     })
//
//     it('应该能够获取热门标签', async () => {
//       const limit = 10
//       const results = await tagSearchService.getPopularTags(limit)
//
//       expect(results.length).toBeLessThanOrEqual(limit)
//       results.forEach(result => {
//         expect(result).toEqual(
//           expect.objectContaining({
//             tag: expect.any(String),
//             similarity: 1.0,
//             frequency: expect.any(Number),
//             category: expect.any(String),
//             score: 1.0
//           })
//         )
//       })
//     })
//
//     it('应该处理数据库错误', async () => {
//       vi.mocked(databaseService.databaseService.queryAllTags).mockResolvedValue({
//         tags: [],
//         error: 'Database error'
//       })
//
//       const results = await tagSearchService.getPopularTags()
//       expect(results).toEqual([])
//     })
//   })
//
//   describe('相关标签建议', () => {
//     beforeEach(async () => {
//       vi.mocked(databaseService.databaseService.queryAllTags).mockResolvedValue({
//         tags: mockTags,
//         error: null
//       })
//       await tagSearchService.initializeTags()
//
//       vi.mocked(databaseService.databaseService.findSimilarTags).mockResolvedValue([
//         {
//           tag: '自然',
//           similarity: 0.8,
//           frequency: 3,
//           category: 'scene',
//           score: 0.8
//         },
//         {
//           tag: '户外',
//           similarity: 0.7,
//           frequency: 2,
//           category: 'scene',
//           score: 0.7
//         }
//       ])
//     })
//
//     it('应该能够获取相关标签建议', async () => {
//       const inputTags = ['风景']
//       const results = await tagSearchService.getRelatedTags(inputTags)
//
//       expect(results.length).toBeGreaterThan(0)
//       // 应该不包含原始标签
//       results.forEach(result => {
//         expect(inputTags).not.toContain(result.tag)
//       })
//     })
//
//     it('应该按分数降序排序相关标签', async () => {
//       const inputTags = ['风景', '自然']
//       const results = await tagSearchService.getRelatedTags(inputTags, 20)
//
//       for (let i = 1; i < results.length; i++) {
//         expect(results[i - 1].score).toBeGreaterThanOrEqual(results[i].score)
//       }
//     })
//   })
//
//   describe('重新初始化', () => {
//     it('应该能够重新初始化标签数据', async () => {
//       vi.mocked(databaseService.databaseService.queryAllTags)
//         .mockResolvedValueOnce({
//           tags: ['旧标签'],
//           error: null
//         })
//         .mockResolvedValueOnce({
//           tags: ['新标签'],
//           error: null
//         })
//
//       await tagSearchService.initializeTags()
//       await tagSearchService.reinitialize()
//
//       expect(databaseService.databaseService.queryAllTags).toHaveBeenCalledTimes(2)
//     })
//   })
//
//   describe('结果合并和去重', () => {
//     beforeEach(async () => {
//       vi.mocked(databaseService.databaseService.queryAllTags).mockResolvedValue({
//         tags: ['重复标签', '唯一标签'],
//         error: null
//       })
//       await tagSearchService.initializeTags()
//
//       // Mock 向量搜索返回重复标签
//       vi.mocked(databaseService.databaseService.findSimilarTags).mockResolvedValue([
//         {
//           tag: '重复标签',
//           similarity: 0.9,
//           frequency: 5,
//           category: 'other',
//           score: 0.9
//         }
//       ])
//     })
//
//     it('应该正确合并和去重搜索结果', async () => {
//       const results = await tagSearchService.searchTags('重复', {
//         useFuzzy: true,
//         useVector: true
//       })
//
//       // 应该只有一个"重复标签"结果
//       const duplicateResults = results.filter(r => r.tag === '重复标签')
//       expect(duplicateResults.length).toBe(1)
//
//       // 应该保留最高分数
//       const mergedResult = duplicateResults[0]
//       expect(mergedResult.score).toBeGreaterThan(0)
//     })
//   })
// })