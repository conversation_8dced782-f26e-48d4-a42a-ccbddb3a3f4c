// /**
//  * 图片缓存优化测试
//  *
//  * 测试智能缓存策略、缩略图生成和并发处理功能
//  */
//
// import { describe, it, expect, beforeEach, beforeAll, vi } from 'vitest'
// import { SmartCacheManager } from '../SmartCacheManager'
// import { imageThumbnailService } from '../ImageThumbnailService'
// import { concurrentImageProcessor } from '../ConcurrentImageProcessor'
// import { frontendImageManager } from '../FrontendImageManager'
//
// // 模拟 window.electronAPI
// const mockElectronAPI = {
//   fileSystem: {
//     getImageBlob: vi.fn(),
//     getImageMetadata: vi.fn()
//   }
// };
//
// // 模拟 Blob
// class MockBlob {
//   size: number
//   type: string
//
//   constructor(size: number, type = 'image/jpeg') {
//     this.size = size
//     this.type = type
//   }
// }
//
// describe('图片缓存优化测试', () => {
//   beforeAll(() => {
//     // 模拟 window.electronAPI
//     (global as any).window = {
//       electronAPI: mockElectronAPI
//     }
//
//     // 模拟 FileReader
//     (global as any).FileReader = class {
//       onload: any
//       onerror: any
//
//       readAsDataURL() {
//         setTimeout(() => {
//           this.onload({ target: { result: 'data:image/jpeg;base64,mockbase64data' } })
//         }, 10)
//       }
//     }
//
//     // 模拟 URL.createObjectURL
//     (global as any).URL = {
//       createObjectURL: vi.fn().mockReturnValue('blob:mock-url'),
//       revokeObjectURL: vi.fn()
//     }
//   })
//
//   beforeEach(() => {
//     vi.clearAllMocks()
//
//     // 模拟成功的图片获取
//     mockElectronAPI.fileSystem.getImageBlob.mockResolvedValue({
//       success: true,
//       data: new MockBlob(1024)
//     })
//
//     // 模拟成功的元数据获取
//     mockElectronAPI.fileSystem.getImageMetadata.mockResolvedValue({
//       success: true,
//       data: {
//         format: 'jpeg',
//         size: 1024,
//         dimensions: { width: 100, height: 100 },
//         createdAt: new Date(),
//         modifiedAt: new Date(),
//         checksum: 'mock-checksum'
//       }
//     })
//   })
//
//   describe('SmartCacheManager', () => {
//     test('应该基于文件修改时间验证缓存', async () => {
//       const cache = new SmartCacheManager<Blob>()
//       const mockBlob = new MockBlob(1024)
//       const modifiedTime = Date.now()
//
//       // 缓存项
//       await cache.set('test-image.jpg', mockBlob, mockBlob.size, modifiedTime)
//
//       // 使用相同的修改时间获取，应该命中缓存
//       const result1 = await cache.get('test-image.jpg', modifiedTime)
//       expect(result1).toBe(mockBlob)
//
//       // 使用更新的修改时间获取，应该缓存失效
//       const result2 = await cache.get('test-image.jpg', modifiedTime + 1000)
//       expect(result2).toBeNull()
//     })
//
//     test('应该基于访问频率优化缓存清理', async () => {
//       const cache = new SmartCacheManager<Blob>({
//         maxSize: 3000, // 只能存储3个项目
//         maxEntries: 3
//       })
//
//       // 添加3个缓存项
//       await cache.set('image1.jpg', new MockBlob(1000), 1000, Date.now())
//       await cache.set('image2.jpg', new MockBlob(1000), 1000, Date.now())
//       await cache.set('image3.jpg', new MockBlob(1000), 1000, Date.now())
//
//       // 多次访问image1和image2
//       await cache.get('image1.jpg')
//       await cache.get('image1.jpg')
//       await cache.get('image2.jpg')
//
//       // 添加第4个项目，应该触发清理
//       await cache.set('image4.jpg', new MockBlob(1000), 1000, Date.now())
//
//       // image3应该被清理（访问频率最低）
//       expect(await cache.get('image1.jpg')).not.toBeNull()
//       expect(await cache.get('image2.jpg')).not.toBeNull()
//       expect(await cache.get('image3.jpg')).toBeNull()
//       expect(await cache.get('image4.jpg')).not.toBeNull()
//     })
//   })
//
//   describe('ConcurrentImageProcessor', () => {
//     test('应该控制并发处理数量', async () => {
//       const processor = new concurrentImageProcessor.constructor(2) // 最大并发数为2
//       const mockProcessor = vi.fn().mockImplementation(async (path) => {
//         return new MockBlob(1000)
//       })
//
//       // 处理5个图片
//       const paths = ['image1.jpg', 'image2.jpg', 'image3.jpg', 'image4.jpg', 'image5.jpg']
//       const result = await processor.batchProcess(paths, mockProcessor)
//
//       // 应该处理所有图片
//       expect(result.successful.length).toBe(5)
//       expect(mockProcessor).toHaveBeenCalledTimes(5)
//     })
//
//     test('应该处理超时错误', async () => {
//       const processor = new concurrentImageProcessor.constructor(2)
//       const mockProcessor = vi.fn().mockImplementation(async (path) => {
//         if (path === 'timeout.jpg') {
//           return new Promise((resolve) => {
//             setTimeout(() => resolve(new MockBlob(1000)), 1000)
//           })
//         }
//         return new MockBlob(1000)
//       })
//
//       // 处理包含超时图片的批次
//       const paths = ['image1.jpg', 'timeout.jpg']
//       const result = await processor.batchProcess(paths, mockProcessor, { timeout: 10 })
//
//       // 应该有一个成功，一个失败
//       expect(result.successful.length + result.failed.length).toBe(2)
//     })
//   })
//
//   describe('FrontendImageManager', () => {
//     test('应该使用智能缓存和缩略图', async () => {
//       // 模拟缩略图服务
//       vi.spyOn(imageThumbnailService, 'getThumbnail').mockResolvedValue(new MockBlob(500))
//
//       // 获取图片，使用缩略图
//       const blob1 = await frontendImageManager.getImageBlob('test.jpg', { thumbnailSize: 200 })
//       expect(blob1).toBeInstanceOf(MockBlob)
//       expect(imageThumbnailService.getThumbnail).toHaveBeenCalledWith('test.jpg', 200, expect.any(Number))
//
//       // 再次获取相同图片，应该使用缓存
//       mockElectronAPI.fileSystem.getImageMetadata.mockResolvedValueOnce({
//         success: true,
//         data: {
//           format: 'jpeg',
//           size: 1024,
//           dimensions: { width: 100, height: 100 },
//           createdAt: new Date(),
//           modifiedAt: new Date(), // 相同的修改时间
//           checksum: 'mock-checksum'
//         }
//       })
//
//       const blob2 = await frontendImageManager.getImageBlob('test.jpg')
//       expect(mockElectronAPI.fileSystem.getImageBlob).toHaveBeenCalledTimes(1) // 不应该再次调用后端
//     })
//
//     test('应该在文件修改时更新缓存', async () => {
//       // 首次获取图片
//       const blob1 = await frontendImageManager.getImageBlob('modified.jpg')
//       expect(mockElectronAPI.fileSystem.getImageBlob).toHaveBeenCalledTimes(1)
//
//       // 模拟文件已修改
//       mockElectronAPI.fileSystem.getImageMetadata.mockResolvedValueOnce({
//         success: true,
//         data: {
//           format: 'jpeg',
//           size: 1024,
//           dimensions: { width: 100, height: 100 },
//           createdAt: new Date(),
//           modifiedAt: new Date(Date.now() + 10000), // 更新的修改时间
//           checksum: 'new-checksum'
//         }
//       })
//
//       // 再次获取相同图片，应该重新从后端获取
//       const blob2 = await frontendImageManager.getImageBlob('modified.jpg')
//       expect(mockElectronAPI.fileSystem.getImageBlob).toHaveBeenCalledTimes(2)
//     })
//   })
// })