// import { describe, it, expect, vi, beforeEach, Mock } from 'vitest'
// import { galleryService } from '../galleryService'
// import { databaseService } from '../database'

// // Mock databaseService
// vi.mock('../database', () => ({
//   databaseService: {
//     enhancedHybridSearch: vi.fn(),
//     vectorSearch: vi.fn(),
//     getAllTags: vi.fn()
//   }
// }))

// describe('GalleryService', () => {
//   beforeEach(() => {
//     vi.clearAllMocks()
//     vi.spyOn(console, 'log').mockImplementation(() => {})
//     vi.spyOn(console, 'error').mockImplementation(() => {})
//     vi.spyOn(console, 'warn').mockImplementation(() => {})
//   })

//   afterEach(() => {
//     vi.restoreAllMocks()
//   })

//   describe('searchImagesWithMethod', () => {
//     const mockHybridResults = {
//       results: [
//         {
//           id: '1',
//           title: '测试图片1',
//           description: '测试描述',
//           file_path: 'C:\\test\\image1.jpg',
//           tags: '自然,风景',
//           upload_time: '2024-01-01',
//           file_size: 2560000,
//           resolution_width: 1920,
//           resolution_height: 1080,
//           similarity: 0.95
//         }
//       ],
//       metadata: {
//         originalQuery: '自然风景',
//         parsedKeywords: ['自然', '风景'],
//         expandedKeywords: ['自然', '风景', '户外'],
//         keywordExpansions: [],
//         totalMatches: 1
//       }
//     }

//     const mockVectorResults = {
//       results: [
//         {
//           id: '2',
//           title: '测试图片2',
//           description: '向量搜索结果',
//           file_path: 'C:\\test\\image2.jpg',
//           tags: '城市,建筑',
//           upload_time: '2024-01-02',
//           file_size: 3200000,
//           resolution_width: 2560,
//           resolution_height: 1440,
//           similarity: 0.87
//         }
//       ],
//       total: 1
//     }

//     it('performs auto search with hybrid fallback to vector', async () => {
//       // Mock hybrid search failure and vector search success
//       ;(databaseService.enhancedHybridSearch as Mock).mockRejectedValue(new Error('Hybrid search failed'))
//       ;(databaseService.vectorSearch as Mock).mockResolvedValue(mockVectorResults)

//       const result = await galleryService.searchImagesWithMethod('测试查询', 'auto')

//       expect(databaseService.enhancedHybridSearch).toHaveBeenCalledWith('测试查询', 50, true, 0.7)
//       expect(databaseService.vectorSearch).toHaveBeenCalledWith('测试查询', 0.6, 50)
      
//       expect(result.images).toHaveLength(1)
//       expect(result.metadata?.actualMethod).toBe('vector')
//       expect(result.metadata?.fallbackOccurred).toBe(true)
//     })

//     it('performs successful hybrid search', async () => {
//       ;(databaseService.enhancedHybridSearch as Mock).mockResolvedValue(mockHybridResults)

//       const result = await galleryService.searchImagesWithMethod('自然风景', 'hybrid')

//       expect(databaseService.enhancedHybridSearch).toHaveBeenCalledWith('自然风景', 50, true, 0.7)
//       expect(result.images).toHaveLength(1)
//       expect(result.images[0].title).toBe('测试图片1')
//       expect(result.metadata?.actualMethod).toBe('hybrid')
//       expect(result.metadata?.fallbackOccurred).toBe(false)
//     })

//     it('performs vector search with custom threshold', async () => {
//       ;(databaseService.vectorSearch as Mock).mockResolvedValue(mockVectorResults)

//       const result = await galleryService.searchImagesWithMethod('城市建筑', 'vector', 0.8)

//       expect(databaseService.vectorSearch).toHaveBeenCalledWith('城市建筑', 0.8, 50)
//       expect(result.images).toHaveLength(1)
//       expect(result.metadata?.actualMethod).toBe('vector')
//       expect(result.metadata?.threshold).toBe(0.8)
//     })

//     it('logs search process information', async () => {
//       const consoleSpy = vi.spyOn(console, 'log')
//       ;(databaseService.enhancedHybridSearch as Mock).mockResolvedValue(mockHybridResults)

//       await galleryService.searchImagesWithMethod('测试日志', 'hybrid')

//       expect(consoleSpy).toHaveBeenCalledWith('Gallery Service: 使用方法 "hybrid" 搜索 - 查询: "测试日志"')
//       expect(consoleSpy).toHaveBeenCalledWith('🔍 [GalleryService] 搜索参数:', {
//         query: '测试日志',
//         method: 'hybrid',
//         threshold: undefined,
//         limit: 50
//       })
//     })

//     it('logs enhanced search results', async () => {
//       const consoleSpy = vi.spyOn(console, 'log')
//       ;(databaseService.enhancedHybridSearch as Mock).mockResolvedValue(mockHybridResults)

//       await galleryService.searchImagesWithMethod('测试结果', 'hybrid')

//       expect(consoleSpy).toHaveBeenCalledWith(
//         expect.stringContaining('🎯 [GalleryService] 搜索完成 - 方法: hybrid')
//       )
//       expect(consoleSpy).toHaveBeenCalledWith(
//         '📋 [GalleryService] 增强结果详情:',
//         expect.arrayContaining([
//           expect.objectContaining({
//             id: '1',
//             title: '测试图片1',
//             url: 'C:\\test\\image1.jpg',
//             similarity: 0.95
//           })
//         ])
//       )
//     })

//     it('handles invalid threshold validation', async () => {
//       const result = await galleryService.searchImagesWithMethod('测试', 'vector', -0.1)

//       expect(result.images).toHaveLength(0)
//       expect(result.error).toContain('INVALID_THRESHOLD')
//     })

//     it('handles search errors properly', async () => {
//       const errorMessage = '数据库连接失败'
//       ;(databaseService.enhancedHybridSearch as Mock).mockRejectedValue(new Error(errorMessage))

//       const result = await galleryService.searchImagesWithMethod('错误测试', 'hybrid')

//       expect(result.images).toHaveLength(0)
//       expect(result.error).toContain(errorMessage)
//       expect(result.metadata?.method).toBe('hybrid')
//     })

//     it('converts database records to ImageData format correctly', async () => {
//       ;(databaseService.enhancedHybridSearch as Mock).mockResolvedValue(mockHybridResults)

//       const result = await galleryService.searchImagesWithMethod('格式转换测试', 'hybrid')

//       const image = result.images[0]
//       expect(image).toMatchObject({
//         id: '1',
//         title: '测试图片1',
//         description: '测试描述',
//         url: 'C:\\test\\image1.jpg',
//         tags: ['自然', '风景'],
//         uploadTime: '2024-01-01',
//         fileSize: '2.44 MB',
//         resolution: '1920x1080',
//         similarity: 95
//       })
//     })

//     it('handles empty search results', async () => {
//       ;(databaseService.enhancedHybridSearch as Mock).mockResolvedValue({
//         results: [],
//         metadata: {
//           originalQuery: '无结果',
//           parsedKeywords: [],
//           expandedKeywords: [],
//           keywordExpansions: [],
//           totalMatches: 0
//         }
//       })

//       const result = await galleryService.searchImagesWithMethod('无结果查询', 'hybrid')

//       expect(result.images).toHaveLength(0)
//       expect(result.total).toBe(0)
//       expect(result.loading).toBe(false)
//     })

//     it('includes search metadata in results', async () => {
//       ;(databaseService.enhancedHybridSearch as Mock).mockResolvedValue(mockHybridResults)

//       const result = await galleryService.searchImagesWithMethod('元数据测试', 'hybrid')

//       expect(result.metadata).toMatchObject({
//         method: 'hybrid',
//         actualMethod: 'hybrid',
//         fallbackOccurred: false,
//         totalResults: 1,
//         threshold: undefined
//       })
//       expect(result.metadata?.duration).toBeGreaterThan(0)
//       expect(result.metadata?.timestamp).toBeDefined()
//     })

//     it('calculates average similarity correctly', async () => {
//       const multipleResults = {
//         results: [
//           { ...mockHybridResults.results[0], similarity: 0.9 },
//           { ...mockHybridResults.results[0], id: '2', similarity: 0.8 }
//         ],
//         metadata: mockHybridResults.metadata
//       }
//       ;(databaseService.enhancedHybridSearch as Mock).mockResolvedValue(multipleResults)

//       const result = await galleryService.searchImagesWithMethod('相似度测试', 'hybrid')

//       expect(result.metadata?.averageSimilarity).toBe(0.85) // (0.9 + 0.8) / 2
//     })

//     it('retries failed searches', async () => {
//       // First call fails, second succeeds
//       ;(databaseService.enhancedHybridSearch as Mock)
//         .mockRejectedValueOnce(new Error('临时失败'))
//         .mockResolvedValueOnce(mockHybridResults)

//       const result = await galleryService.searchImagesWithMethod('重试测试', 'hybrid')

//       expect(databaseService.enhancedHybridSearch).toHaveBeenCalledTimes(2)
//       expect(result.images).toHaveLength(1)
//     })
//   })

//   describe('getAllTags', () => {
//     it('returns cached tags when available', async () => {
//       const mockTags = ['自然', '城市', '人物', '动物']
//       ;(databaseService.getAllTags as Mock).mockResolvedValue(mockTags)

//       // First call
//       const result1 = await galleryService.getAllTags()
//       // Second call should use cache
//       const result2 = await galleryService.getAllTags()

//       expect(databaseService.getAllTags).toHaveBeenCalledTimes(1)
//       expect(result1).toEqual(mockTags)
//       expect(result2).toEqual(mockTags)
//     })

//     it('refreshes cache after expiration', async () => {
//       const mockTags1 = ['tag1', 'tag2']
//       const mockTags2 = ['tag1', 'tag2', 'tag3']
      
//       ;(databaseService.getAllTags as Mock)
//         .mockResolvedValueOnce(mockTags1)
//         .mockResolvedValueOnce(mockTags2)

//       // Mock time passage beyond cache duration
//       const originalDate = Date.now
//       Date.now = vi.fn().mockReturnValue(0)

//       const result1 = await galleryService.getAllTags()

//       // Advance time beyond cache duration (30 seconds)
//       Date.now = vi.fn().mockReturnValue(31000)

//       const result2 = await galleryService.getAllTags()

//       expect(databaseService.getAllTags).toHaveBeenCalledTimes(2)
//       expect(result1).toEqual(mockTags1)
//       expect(result2).toEqual(mockTags2)

//       Date.now = originalDate
//     })
//   })
// })