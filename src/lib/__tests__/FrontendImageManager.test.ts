// import { describe, it, expect, beforeEach, vi } from 'vitest'
// import { FrontendImageManager } from '../FrontendImageManager'
// import { ImagePathError, ImagePathErrorType } from '../../types/errors'
// import { logger } from '../LoggerService'
// import { placeholderImageService } from '../PlaceholderImageService'
//
// // Mock window.electronAPI
// Object.defineProperty(window, 'electronAPI', {
//   value: {
//     fileSystem: {
//       getImageBlob: vi.fn()
//     }
//   },
//   writable: true
// })
//
// // Mock logger
// vi.mock('../LoggerService', () => ({
//   logger: {
//     debug: vi.fn(),
//     info: vi.fn(),
//     warn: vi.fn(),
//     error: vi.fn(),
//     startPerformanceTimer: vi.fn(),
//     endPerformanceTimer: vi.fn()
//   }
// }))
//
// // Mock placeholderImageService
// vi.mock('../PlaceholderImageService', () => ({
//   placeholderImageService: {
//     generateErrorPlaceholder: vi.fn().mockReturnValue('data:image/png;base64,mockPlaceholder')
//   }
// }))
//
// describe('FrontendImageManager', () => {
//   let manager: FrontendImageManager
//
//   beforeEach(() => {
//     vi.clearAllMocks()
//
//     // Create a new manager with small cache for testing
//     manager = new FrontendImageManager({
//       maxSize: 1024 * 1024, // 1MB
//       maxEntries: 10,
//       ttl: 1000, // 1 second
//       cleanupInterval: 500 // 500ms
//     })
//
//     // Mock FileReader
//     global.FileReader = vi.fn().mockImplementation(() => ({
//       readAsDataURL: function(blob: Blob) {
//         setTimeout(() => {
//           this.onload && this.onload({ target: { result: 'data:image/png;base64,mockDataUrl' } })
//         }, 0)
//       }
//     }))
//
//     // Mock Blob
//     global.Blob = vi.fn().mockImplementation((content, options) => ({
//       size: content[0].length,
//       type: options?.type || 'image/png'
//     }))
//   })
//
//   describe('getImageBlob', () => {
//     const mockImagePath = 'test-image.jpg'
//     const mockBlob = new Blob(['mockImageData'], { type: 'image/jpeg' })
//
//     beforeEach(() => {
//       // Mock successful backend response
//       window.electronAPI.fileSystem.getImageBlob.mockResolvedValue({
//         success: true,
//         data: {
//           buffer: new Uint8Array([1, 2, 3]).buffer,
//           type: 'image/jpeg'
//         }
//       })
//     })
//
//     it('should fetch blob from backend when not in cache', async () => {
//       const blob = await manager.getImageBlob(mockImagePath)
//
//       expect(window.electronAPI.fileSystem.getImageBlob).toHaveBeenCalledWith(mockImagePath)
//       expect(blob).toBeDefined()
//       expect(logger.startPerformanceTimer).toHaveBeenCalled()
//       expect(logger.endPerformanceTimer).toHaveBeenCalled()
//     })
//
//     it('should return cached blob when available', async () => {
//       // First call to populate cache
//       await manager.getImageBlob(mockImagePath)
//
//       // Reset mocks
//       vi.clearAllMocks()
//
//       // Second call should use cache
//       const blob = await manager.getImageBlob(mockImagePath)
//
//       expect(window.electronAPI.fileSystem.getImageBlob).not.toHaveBeenCalled()
//       expect(blob).toBeDefined()
//       expect(logger.debug).toHaveBeenCalledWith(expect.stringContaining('缓存命中'), 'FrontendImageManager')
//     })
//
//     it('should handle backend errors and return placeholder', async () => {
//       // Mock backend error
//       window.electronAPI.fileSystem.getImageBlob.mockResolvedValue({
//         success: false,
//         error: 'File not found'
//       })
//
//       const blob = await manager.getImageBlob(mockImagePath)
//
//       expect(blob).toBeDefined()
//       expect(logger.error).toHaveBeenCalled()
//       expect(placeholderImageService.generateErrorPlaceholder).toHaveBeenCalled()
//     })
//
//     it('should handle network errors and return placeholder', async () => {
//       // Mock network error
//       window.electronAPI.fileSystem.getImageBlob.mockRejectedValue(new Error('Network error'))
//
//       const blob = await manager.getImageBlob(mockImagePath)
//
//       expect(blob).toBeDefined()
//       expect(logger.error).toHaveBeenCalled()
//       expect(placeholderImageService.generateErrorPlaceholder).toHaveBeenCalled()
//     })
//   })
//
//   describe('getImageDataUrl', () => {
//     const mockImagePath = 'test-image.jpg'
//
//     beforeEach(() => {
//       // Mock getImageBlob to return a blob
//       vi.spyOn(manager, 'getImageBlob').mockResolvedValue(
//         new Blob(['mockImageData'], { type: 'image/jpeg' })
//       )
//
//       // Mock blobToDataUrl
//       vi.spyOn(manager, 'blobToDataUrl').mockResolvedValue('data:image/jpeg;base64,mockDataUrl')
//     })
//
//     it('should convert blob to data URL', async () => {
//       const dataUrl = await manager.getImageDataUrl(mockImagePath)
//
//       expect(manager.getImageBlob).toHaveBeenCalledWith(mockImagePath)
//       expect(manager.blobToDataUrl).toHaveBeenCalled()
//       expect(dataUrl).toBe('data:image/jpeg;base64,mockDataUrl')
//     })
//
//     it('should handle errors and return placeholder', async () => {
//       // Mock error in getImageBlob
//       vi.spyOn(manager, 'getImageBlob').mockRejectedValue(
//         new ImagePathError(ImagePathErrorType.PATH_NOT_FOUND, 'File not found', mockImagePath)
//       )
//
//       const dataUrl = await manager.getImageDataUrl(mockImagePath)
//
//       expect(logger.error).toHaveBeenCalled()
//       expect(placeholderImageService.generateErrorPlaceholder).toHaveBeenCalled()
//       expect(dataUrl).toBe('data:image/png;base64,mockPlaceholder')
//     })
//   })
//
//   describe('cache management', () => {
//     const mockImagePath1 = 'test-image1.jpg'
//     const mockImagePath2 = 'test-image2.jpg'
//     const mockBlob1 = new Blob(['mockImageData1'], { type: 'image/jpeg' })
//     const mockBlob2 = new Blob(['mockImageData2'], { type: 'image/jpeg' })
//
//     it('should cache blobs', async () => {
//       await manager.setCachedBlob(mockImagePath1, mockBlob1)
//       const cachedBlob = await manager.getCachedBlob(mockImagePath1)
//
//       expect(cachedBlob).toBeDefined()
//     })
//
//     it('should return null for non-cached paths', async () => {
//       const cachedBlob = await manager.getCachedBlob('non-existent.jpg')
//
//       expect(cachedBlob).toBeNull()
//     })
//
//     it('should expire cache entries after TTL', async () => {
//       await manager.setCachedBlob(mockImagePath1, mockBlob1)
//
//       // Wait for TTL to expire
//       await new Promise(resolve => setTimeout(resolve, 1100))
//
//       const cachedBlob = await manager.getCachedBlob(mockImagePath1)
//       expect(cachedBlob).toBeNull()
//     })
//
//     it('should enforce cache size limits', async () => {
//       // Create a manager with very small size limit
//       const smallManager = new FrontendImageManager({
//         maxSize: 10, // Only 10 bytes
//         maxEntries: 10
//       })
//
//       // Add a blob that exceeds the limit
//       const largeBlob = new Blob(['largeImageDataThatExceedsLimit'], { type: 'image/jpeg' })
//       await smallManager.setCachedBlob(mockImagePath1, largeBlob)
//
//       // Try to add another blob
//       await smallManager.setCachedBlob(mockImagePath2, mockBlob2)
//
//       // First blob should be evicted
//       const firstBlob = await smallManager.getCachedBlob(mockImagePath1)
//       expect(firstBlob).toBeNull()
//
//       // Second blob should be cached
//       const secondBlob = await smallManager.getCachedBlob(mockImagePath2)
//       expect(secondBlob).toBeDefined()
//     })
//
//     it('should clear cache', async () => {
//       await manager.setCachedBlob(mockImagePath1, mockBlob1)
//       await manager.setCachedBlob(mockImagePath2, mockBlob2)
//
//       await manager.clearCache()
//
//       const blob1 = await manager.getCachedBlob(mockImagePath1)
//       const blob2 = await manager.getCachedBlob(mockImagePath2)
//
//       expect(blob1).toBeNull()
//       expect(blob2).toBeNull()
//     })
//
//     it('should provide cache statistics', async () => {
//       await manager.setCachedBlob(mockImagePath1, mockBlob1)
//
//       // Simulate a cache hit
//       await manager.getCachedBlob(mockImagePath1)
//
//       // Simulate a cache miss
//       await manager.getCachedBlob('non-existent.jpg')
//
//       const stats = manager.getCacheStats()
//
//       expect(stats.count).toBe(1)
//       expect(stats.size).toBeGreaterThan(0)
//       expect(stats.totalHits).toBe(1)
//       expect(stats.totalMisses).toBe(1)
//       expect(stats.hitRate).toBe(0.5) // 1 hit out of 2 requests
//     })
//   })
//
//   describe('preloadImages', () => {
//     beforeEach(() => {
//       // Mock getImageBlob to succeed for even paths and fail for odd paths
//       vi.spyOn(manager, 'getImageBlob').mockImplementation(async (path) => {
//         if (path.includes('even')) {
//           return new Blob(['mockData'], { type: 'image/jpeg' })
//         } else {
//           throw new ImagePathError(ImagePathErrorType.PATH_NOT_FOUND, 'File not found', path)
//         }
//       })
//     })
//
//     it('should preload multiple images with concurrency control', async () => {
//       const paths = [
//         'even1.jpg',
//         'odd1.jpg',
//         'even2.jpg',
//         'odd2.jpg',
//         'even3.jpg'
//       ]
//
//       const result = await manager.preloadImages(paths, { concurrent: 2 })
//
//       expect(result.successful.length).toBe(3) // even paths
//       expect(result.failed.length).toBe(2) // odd paths
//       expect(logger.info).toHaveBeenCalledWith(
//         expect.stringContaining('图片预加载完成'),
//         'FrontendImageManager'
//       )
//     })
//
//     it('should handle timeouts during preload', async () => {
//       // Mock a slow image load
//       vi.spyOn(manager, 'getImageBlob').mockImplementation(async (path) => {
//         if (path === 'slow.jpg') {
//           await new Promise(resolve => setTimeout(resolve, 100))
//         }
//         return new Blob(['mockData'], { type: 'image/jpeg' })
//       })
//
//       const result = await manager.preloadImages(['slow.jpg'], { timeout: 50 })
//
//       expect(result.failed.length).toBe(1)
//       expect(result.failed[0].error).toContain('timed out')
//     })
//   })
//
//   describe('batchProcess', () => {
//     it('should process images in batches', async () => {
//       const paths = ['image1.jpg', 'image2.jpg', 'image3.jpg']
//
//       // Mock getImageBlob to return blobs
//       vi.spyOn(manager, 'getImageBlob').mockResolvedValue(
//         new Blob(['mockData'], { type: 'image/jpeg' })
//       )
//
//       // Create a mock processor
//       const processor = vi.fn().mockImplementation(async (blob, path) => {
//         return { processed: true, path }
//       })
//
//       const result = await manager.batchProcess(paths, processor, { concurrent: 2 })
//
//       expect(result.successful.length).toBe(3)
//       expect(processor).toHaveBeenCalledTimes(3)
//     })
//
//     it('should handle processor errors', async () => {
//       const paths = ['success.jpg', 'error.jpg']
//
//       // Mock getImageBlob to return blobs
//       vi.spyOn(manager, 'getImageBlob').mockResolvedValue(
//         new Blob(['mockData'], { type: 'image/jpeg' })
//       )
//
//       // Create a processor that fails for 'error.jpg'
//       const processor = vi.fn().mockImplementation(async (blob, path) => {
//         if (path === 'error.jpg') {
//           throw new Error('Processing error')
//         }
//         return { processed: true, path }
//       })
//
//       const result = await manager.batchProcess(paths, processor)
//
//       expect(result.successful.length).toBe(1)
//       expect(result.failed.length).toBe(1)
//       expect(result.failed[0].path).toBe('error.jpg')
//     })
//   })
//
//   describe('data conversion utilities', () => {
//     it('should convert blob to data URL', async () => {
//       const blob = new Blob(['testData'], { type: 'image/png' })
//       const dataUrl = await manager.blobToDataUrl(blob)
//
//       expect(dataUrl).toBe('data:image/png;base64,mockDataUrl')
//     })
//
//     it('should handle blob to data URL conversion errors', async () => {
//       // Mock FileReader to fail
//       global.FileReader = vi.fn().mockImplementation(() => ({
//         readAsDataURL: function(blob: Blob) {
//           setTimeout(() => {
//             this.error = new Error('FileReader error')
//             this.onerror && this.onerror()
//           }, 0)
//         }
//       }))
//
//       const blob = new Blob(['testData'], { type: 'image/png' })
//
//       await expect(manager.blobToDataUrl(blob)).rejects.toThrow('Failed to convert blob to data URL')
//     })
//
//     it('should convert data URL to blob', async () => {
//       const dataUrl = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mNk+A8AAQUBAScY42YAAAAASUVORK5CYII='
//       const blob = await manager.dataUrlToBlob(dataUrl)
//
//       expect(blob).toBeDefined()
//       expect(blob.type).toBe('image/png')
//     })
//
//     it('should handle data URL to blob conversion errors', async () => {
//       const invalidDataUrl = 'not-a-data-url'
//       const blob = await manager.dataUrlToBlob(invalidDataUrl)
//
//       // Should return a fallback blob
//       expect(blob).toBeDefined()
//       expect(blob.type).toBe('image/png')
//       expect(logger.error).toHaveBeenCalledWith(
//         expect.stringContaining('DataURL转换为Blob失败'),
//         'FrontendImageManager',
//         expect.anything()
//       )
//     })
//   })
//
//   describe('cleanup and resource management', () => {
//     it('should clean up resources on destroy', () => {
//       // Mock clearInterval
//       const originalClearInterval = global.clearInterval
//       global.clearInterval = vi.fn()
//
//       manager.destroy()
//
//       expect(global.clearInterval).toHaveBeenCalled()
//
//       // Restore original
//       global.clearInterval = originalClearInterval
//     })
//
//     it('should update configuration', () => {
//       const newConfig = {
//         maxSize: 2 * 1024 * 1024,
//         ttl: 5000
//       }
//
//       manager.updateConfig(newConfig)
//
//       // We can't directly test private properties, but we can test behavior
//       // For example, cache entries should now expire after 5 seconds
//       // This would require more complex testing
//     })
//   })
// })