/**
 * Tool configuration and management system
 * Provides centralized configuration for tool parameters, defaults, and runtime settings
 */

/**
 * Tool configuration interface
 */
export interface ToolConfig {
  enabled: boolean;
  priority: number;
  timeout: number;
  retryAttempts: number;
  retryDelay: number;
  parameters: {
    defaults: Record<string, any>;
    overrides: Record<string, any>;
    validation: {
      enabled: boolean;
      strict: boolean;
    };
  };
  performance: {
    monitoring: boolean;
    alertThresholds: {
      responseTime: number;
      errorRate: number;
      successRate: number;
    };
  };
  logging: {
    enabled: boolean;
    level: 'debug' | 'info' | 'warn' | 'error';
    includeParameters: boolean;
    includeResults: boolean;
  };
}

/**
 * Global tool configuration
 */
export interface GlobalToolConfig {
  enabled: boolean;
  autoDiscovery: boolean;
  autoRegistration: boolean;
  healthChecks: {
    enabled: boolean;
    interval: number;
    timeout: number;
  };
  performance: {
    monitoring: boolean;
    metricsRetention: number;
    alerting: boolean;
  };
  logging: {
    enabled: boolean;
    level: 'debug' | 'info' | 'warn' | 'error';
    destination: 'console' | 'file' | 'both';
    maxLogSize: number;
  };
  security: {
    validateParameters: boolean;
    sanitizeInputs: boolean;
    rateLimiting: {
      enabled: boolean;
      maxCallsPerMinute: number;
      maxCallsPerHour: number;
    };
  };
}

/**
 * Tool configuration defaults
 */
const DEFAULT_TOOL_CONFIG: ToolConfig = {
  enabled: true,
  priority: 1,
  timeout: 90000, // 30 seconds
  retryAttempts: 3,
  retryDelay: 1000,
  parameters: {
    defaults: {},
    overrides: {},
    validation: {
      enabled: true,
      strict: false
    }
  },
  performance: {
    monitoring: true,
    alertThresholds: {
      responseTime: 10000, // 10 seconds
      errorRate: 10, // 10%
      successRate: 90 // 90%
    }
  },
  logging: {
    enabled: true,
    level: 'info',
    includeParameters: true,
    includeResults: false
  }
};

/**
 * Global configuration defaults
 */
const DEFAULT_GLOBAL_CONFIG: GlobalToolConfig = {
  enabled: true,
  autoDiscovery: true,
  autoRegistration: true,
  healthChecks: {
    enabled: true,
    interval: 90000, // 1 minute
    timeout: 5000 // 5 seconds
  },
  performance: {
    monitoring: true,
    metricsRetention: 86400000, // 24 hours
    alerting: false
  },
  logging: {
    enabled: true,
    level: 'info',
    destination: 'console',
    maxLogSize: 10485760 // 10MB
  },
  security: {
    validateParameters: true,
    sanitizeInputs: true,
    rateLimiting: {
      enabled: false,
      maxCallsPerMinute: 60,
      maxCallsPerHour: 1000
    }
  }
};

/**
 * Tool-specific default configurations
 */
const TOOL_SPECIFIC_DEFAULTS: Record<string, Partial<ToolConfig>> = {
  analyze_image: {
    timeout: 90000, // Longer timeout for image analysis
    parameters: {
      defaults: {
        includeStructuredData: true
      },
      overrides: {},
      validation: {
        enabled: true,
        strict: false
      }
    },
    performance: {
      monitoring: true,
      alertThresholds: {
        responseTime: 15000, // 15 seconds for image analysis
        errorRate: 5,
        successRate: 95
      }
    }
  },
  find_similar_images_by_image: {
    timeout: 90000,
    parameters: {
      defaults: {
        limit: 20,
        threshold: 0.5
      },
      overrides: {},
      validation: {
        enabled: true,
        strict: false
      }
    },
    performance: {
      monitoring: true,
      alertThresholds: {
        responseTime: 8000,
        errorRate: 8,
        successRate: 92
      }
    }
  },
  find_similar_images_by_description: {
    timeout: 90000,
    parameters: {
      defaults: {
        limit: 20,
        threshold: 0.4
      },
      overrides: {},
      validation: {
        enabled: true,
        strict: false
      }
    },
    performance: {
      monitoring: true,
      alertThresholds: {
        responseTime: 10000,
        errorRate: 10,
        successRate: 90
      }
    }
  },
  find_images_by_tags: {
    timeout: 90000,
    parameters: {
      defaults: {
        limit: 20,
        matchMode: 'any'
      },
      overrides: {},
      validation: {
        enabled: true,
        strict: false
      }
    },
    performance: {
      monitoring: true,
      alertThresholds: {
        responseTime: 6000,
        errorRate: 5,
        successRate: 95
      }
    }
  },
  get_image_analysis: {
    timeout: 90000,
    parameters: {
      defaults: {
        includeStructured: true,
        reanalyze: false
      },
      overrides: {},
      validation: {
        enabled: true,
        strict: false
      }
    },
    performance: {
      monitoring: true,
      alertThresholds: {
        responseTime: 5000,
        errorRate: 3,
        successRate: 97
      }
    }
  }
};

/**
 * Configuration change event
 */
export interface ConfigurationChangeEvent {
  type: 'tool' | 'global';
  target: string; // tool name or 'global'
  property: string;
  oldValue: any;
  newValue: any;
  timestamp: Date;
}

/**
 * Configuration manager class
 */
export class ToolConfigurationManager {
  private static instance: ToolConfigurationManager | null = null;
  private globalConfig: GlobalToolConfig;
  private toolConfigs: Map<string, ToolConfig> = new Map();
  private configChangeListeners: Array<(event: ConfigurationChangeEvent) => void> = [];
  private configHistory: ConfigurationChangeEvent[] = [];

  private constructor() {
    this.globalConfig = { ...DEFAULT_GLOBAL_CONFIG };
    this.initializeToolConfigs();
  }

  /**
   * Get singleton instance
   */
  static getInstance(): ToolConfigurationManager {
    if (!ToolConfigurationManager.instance) {
      ToolConfigurationManager.instance = new ToolConfigurationManager();
    }
    return ToolConfigurationManager.instance;
  }

  /**
   * Initialize tool configurations with defaults
   */
  private initializeToolConfigs(): void {
    const toolNames = Object.keys(TOOL_SPECIFIC_DEFAULTS);
    
    for (const toolName of toolNames) {
      const baseConfig = { ...DEFAULT_TOOL_CONFIG };
      const specificConfig = TOOL_SPECIFIC_DEFAULTS[toolName] || {};
      
      // Deep merge configurations
      const mergedConfig = this.deepMerge(baseConfig, specificConfig);
      this.toolConfigs.set(toolName, mergedConfig);
    }

    console.log(`Initialized configurations for ${toolNames.length} tools`);
  }

  /**
   * Get global configuration
   */
  getGlobalConfig(): GlobalToolConfig {
    return { ...this.globalConfig };
  }

  /**
   * Update global configuration
   */
  updateGlobalConfig(updates: Partial<GlobalToolConfig>): void {
    const oldConfig = { ...this.globalConfig };
    this.globalConfig = this.deepMerge(this.globalConfig, updates);
    
    // Emit change events for each updated property
    this.emitConfigChanges('global', 'global', oldConfig, this.globalConfig);
    
    console.log('Global configuration updated');
  }

  /**
   * Get tool configuration
   */
  getToolConfig(toolName: string): ToolConfig {
    const config = this.toolConfigs.get(toolName);
    if (config) {
      return { ...config };
    }

    // Return default config for unknown tools
    console.warn(`No specific configuration found for tool ${toolName}, using defaults`);
    return { ...DEFAULT_TOOL_CONFIG };
  }

  /**
   * Update tool configuration
   */
  updateToolConfig(toolName: string, updates: Partial<ToolConfig>): void {
    const oldConfig = this.getToolConfig(toolName);
    const newConfig = this.deepMerge(oldConfig, updates);
    
    this.toolConfigs.set(toolName, newConfig);
    
    // Emit change events
    this.emitConfigChanges('tool', toolName, oldConfig, newConfig);
    
    console.log(`Configuration updated for tool: ${toolName}`);
  }

  /**
   * Enable/disable a tool
   */
  setToolEnabled(toolName: string, enabled: boolean): void {
    this.updateToolConfig(toolName, { enabled });
    console.log(`Tool ${toolName} ${enabled ? 'enabled' : 'disabled'}`);
  }

  /**
   * Check if a tool is enabled
   */
  isToolEnabled(toolName: string): boolean {
    const config = this.getToolConfig(toolName);
    return config.enabled && this.globalConfig.enabled;
  }

  /**
   * Set tool priority
   */
  setToolPriority(toolName: string, priority: number): void {
    if (priority < 1 || priority > 10) {
      throw new Error('Tool priority must be between 1 and 10');
    }
    
    this.updateToolConfig(toolName, { priority });
    console.log(`Tool ${toolName} priority set to ${priority}`);
  }

  /**
   * Get tool priority
   */
  getToolPriority(toolName: string): number {
    return this.getToolConfig(toolName).priority;
  }

  /**
   * Get tool default parameters
   */
  getToolDefaults(toolName: string): Record<string, any> {
    const config = this.getToolConfig(toolName);
    return { ...config.parameters.defaults, ...config.parameters.overrides };
  }

  /**
   * Set tool default parameters
   */
  setToolDefaults(toolName: string, defaults: Record<string, any>): void {
    const config = this.getToolConfig(toolName);
    config.parameters.defaults = { ...config.parameters.defaults, ...defaults };
    this.toolConfigs.set(toolName, config);
    
    this.emitConfigChange('tool', toolName, 'parameters.defaults', {}, defaults);
    console.log(`Default parameters updated for tool: ${toolName}`);
  }

  /**
   * Set tool parameter overrides
   */
  setToolOverrides(toolName: string, overrides: Record<string, any>): void {
    const config = this.getToolConfig(toolName);
    config.parameters.overrides = { ...config.parameters.overrides, ...overrides };
    this.toolConfigs.set(toolName, config);
    
    this.emitConfigChange('tool', toolName, 'parameters.overrides', {}, overrides);
    console.log(`Parameter overrides updated for tool: ${toolName}`);
  }

  /**
   * Get all tool configurations
   */
  getAllToolConfigs(): Record<string, ToolConfig> {
    const configs: Record<string, ToolConfig> = {};
    this.toolConfigs.forEach((config, toolName) => {
      configs[toolName] = { ...config };
    });
    return configs;
  }

  /**
   * Get enabled tools
   */
  getEnabledTools(): string[] {
    const enabledTools: string[] = [];
    this.toolConfigs.forEach((_, toolName) => {
      if (this.isToolEnabled(toolName)) {
        enabledTools.push(toolName);
      }
    });
    return enabledTools;
  }

  /**
   * Get disabled tools
   */
  getDisabledTools(): string[] {
    const disabledTools: string[] = [];
    this.toolConfigs.forEach((_, toolName) => {
      if (!this.isToolEnabled(toolName)) {
        disabledTools.push(toolName);
      }
    });
    return disabledTools;
  }

  /**
   * Get tools sorted by priority
   */
  getToolsByPriority(): Array<{ toolName: string; priority: number; enabled: boolean }> {
    const tools: Array<{ toolName: string; priority: number; enabled: boolean }> = [];
    
    this.toolConfigs.forEach((config, toolName) => {
      tools.push({
        toolName,
        priority: config.priority,
        enabled: this.isToolEnabled(toolName)
      });
    });
    
    return tools.sort((a, b) => b.priority - a.priority);
  }

  /**
   * Reset tool configuration to defaults
   */
  resetToolConfig(toolName: string): void {
    const defaultConfig = TOOL_SPECIFIC_DEFAULTS[toolName] 
      ? this.deepMerge(DEFAULT_TOOL_CONFIG, TOOL_SPECIFIC_DEFAULTS[toolName])
      : { ...DEFAULT_TOOL_CONFIG };
    
    const oldConfig = this.getToolConfig(toolName);
    this.toolConfigs.set(toolName, defaultConfig);
    
    this.emitConfigChanges('tool', toolName, oldConfig, defaultConfig);
    console.log(`Configuration reset to defaults for tool: ${toolName}`);
  }

  /**
   * Reset global configuration to defaults
   */
  resetGlobalConfig(): void {
    const oldConfig = { ...this.globalConfig };
    this.globalConfig = { ...DEFAULT_GLOBAL_CONFIG };
    
    this.emitConfigChanges('global', 'global', oldConfig, this.globalConfig);
    console.log('Global configuration reset to defaults');
  }

  /**
   * Export configuration
   */
  exportConfiguration(): {
    global: GlobalToolConfig;
    tools: Record<string, ToolConfig>;
    exportTime: string;
  } {
    return {
      global: this.getGlobalConfig(),
      tools: this.getAllToolConfigs(),
      exportTime: new Date().toISOString()
    };
  }

  /**
   * Import configuration
   */
  importConfiguration(config: {
    global?: GlobalToolConfig;
    tools?: Record<string, ToolConfig>;
  }): void {
    if (config.global) {
      this.updateGlobalConfig(config.global);
    }
    
    if (config.tools) {
      for (const [toolName, toolConfig] of Object.entries(config.tools)) {
        this.updateToolConfig(toolName, toolConfig);
      }
    }
    
    console.log('Configuration imported successfully');
  }

  /**
   * Add configuration change listener
   */
  addConfigChangeListener(listener: (event: ConfigurationChangeEvent) => void): void {
    this.configChangeListeners.push(listener);
  }

  /**
   * Remove configuration change listener
   */
  removeConfigChangeListener(listener: (event: ConfigurationChangeEvent) => void): void {
    const index = this.configChangeListeners.indexOf(listener);
    if (index > -1) {
      this.configChangeListeners.splice(index, 1);
    }
  }

  /**
   * Get configuration change history
   */
  getConfigHistory(limit?: number): ConfigurationChangeEvent[] {
    const history = [...this.configHistory];
    return limit ? history.slice(-limit) : history;
  }

  /**
   * Clear configuration change history
   */
  clearConfigHistory(): void {
    this.configHistory = [];
    console.log('Configuration change history cleared');
  }

  /**
   * Validate configuration
   */
  validateConfiguration(): {
    valid: boolean;
    errors: string[];
    warnings: string[];
  } {
    const errors: string[] = [];
    const warnings: string[] = [];

    // Validate global configuration
    if (this.globalConfig.healthChecks.interval < 10000) {
      warnings.push('Health check interval is very short (< 10 seconds)');
    }

    if (this.globalConfig.performance.metricsRetention < 3900000) {
      warnings.push('Metrics retention is very short (< 1 hour)');
    }

    // Validate tool configurations
    this.toolConfigs.forEach((config, toolName) => {
      if (config.timeout < 1000) {
        warnings.push(`Tool ${toolName} has very short timeout (< 1 second)`);
      }

      if (config.timeout > 120000) {
        warnings.push(`Tool ${toolName} has very long timeout (> 2 minutes)`);
      }

      if (config.retryAttempts > 10) {
        warnings.push(`Tool ${toolName} has many retry attempts (> 10)`);
      }

      if (config.priority < 1 || config.priority > 10) {
        errors.push(`Tool ${toolName} has invalid priority (must be 1-10)`);
      }
    });

    return {
      valid: errors.length === 0,
      errors,
      warnings
    };
  }

  /**
   * Get configuration summary
   */
  getConfigurationSummary(): {
    globalEnabled: boolean;
    totalTools: number;
    enabledTools: number;
    disabledTools: number;
    averagePriority: number;
    healthChecksEnabled: boolean;
    performanceMonitoringEnabled: boolean;
    loggingEnabled: boolean;
  } {
    const enabledTools = this.getEnabledTools();
    const disabledTools = this.getDisabledTools();
    const totalTools = enabledTools.length + disabledTools.length;
    
    const priorities = Array.from(this.toolConfigs.values()).map((c: any) => c.priority);
    const averagePriority = priorities.length > 0 
      ? priorities.reduce((sum, p) => sum + p, 0) / priorities.length 
      : 0;

    return {
      globalEnabled: this.globalConfig.enabled,
      totalTools,
      enabledTools: enabledTools.length,
      disabledTools: disabledTools.length,
      averagePriority: Math.round(averagePriority * 100) / 100,
      healthChecksEnabled: this.globalConfig.healthChecks.enabled,
      performanceMonitoringEnabled: this.globalConfig.performance.monitoring,
      loggingEnabled: this.globalConfig.logging.enabled
    };
  }

  /**
   * Deep merge two objects
   */
  private deepMerge(target: any, source: any): any {
    const result = { ...target };
    
    for (const key in source) {
      if (source[key] && typeof source[key] === 'object' && !Array.isArray(source[key])) {
        result[key] = this.deepMerge(result[key] || {}, source[key]);
      } else {
        result[key] = source[key];
      }
    }
    
    return result;
  }

  /**
   * Emit configuration change event
   */
  private emitConfigChange(
    type: 'tool' | 'global',
    target: string,
    property: string,
    oldValue: any,
    newValue: any
  ): void {
    const event: ConfigurationChangeEvent = {
      type,
      target,
      property,
      oldValue,
      newValue,
      timestamp: new Date()
    };

    this.configHistory.push(event);
    
    // Keep only recent history (last 1000 changes)
    if (this.configHistory.length > 1000) {
      this.configHistory = this.configHistory.slice(-1000);
    }

    // Notify listeners
    this.configChangeListeners.forEach((listener: any) => {
      try {
        listener(event);
      } catch (error) {
        console.error('Error in configuration change listener:', error);
      }
    });
  }

  /**
   * Emit multiple configuration changes
   */
  private emitConfigChanges(
    type: 'tool' | 'global',
    target: string,
    oldConfig: any,
    newConfig: any,
    prefix: string = ''
  ): void {
    for (const key in newConfig) {
      const propertyPath = prefix ? `${prefix}.${key}` : key;
      const oldValue = oldConfig[key];
      const newValue = newConfig[key];
      
      if (oldValue !== newValue) {
        if (typeof newValue === 'object' && newValue !== null && !Array.isArray(newValue)) {
          this.emitConfigChanges(type, target, oldValue || {}, newValue, propertyPath);
        } else {
          this.emitConfigChange(type, target, propertyPath, oldValue, newValue);
        }
      }
    }
  }
}

/**
 * Global configuration manager instance
 */
export const toolConfigManager = ToolConfigurationManager.getInstance();

/**
 * Convenience functions
 */
export function getToolConfig(toolName: string): ToolConfig {
  return toolConfigManager.getToolConfig(toolName);
}

export function updateToolConfig(toolName: string, updates: Partial<ToolConfig>): void {
  toolConfigManager.updateToolConfig(toolName, updates);
}

export function enableTool(toolName: string): void {
  toolConfigManager.setToolEnabled(toolName, true);
}

export function disableTool(toolName: string): void {
  toolConfigManager.setToolEnabled(toolName, false);
}

export function isToolEnabled(toolName: string): boolean {
  return toolConfigManager.isToolEnabled(toolName);
}

export function getGlobalConfig(): GlobalToolConfig {
  return toolConfigManager.getGlobalConfig();
}

export function updateGlobalConfig(updates: Partial<GlobalToolConfig>): void {
  toolConfigManager.updateGlobalConfig(updates);
}