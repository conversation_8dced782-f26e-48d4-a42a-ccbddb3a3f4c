/**
 * Intent Recognition System for VL Image Tools
 * 
 * This module provides intelligent intent recognition and parameter extraction
 * for image-related user requests, supporting the VL Chat Service with
 * automatic tool selection and parameter intelligence.
 */

export interface ImageToolIntent {
  toolName: 'analyze_image' | 'find_similar_images_by_image' | 'find_similar_images_by_description' | 'find_images_by_tags';
  confidence: number;
  parameters: Record<string, any>;
  reasoning: string;
  fallbackOptions?: string[];
}

export interface SearchParameters {
  limit?: number;
  threshold?: number;
  tags?: string[];
  matchMode?: 'any' | 'all';
  detailLevel?: 'basic' | 'detailed' | 'comprehensive';
  focusAreas?: string[];
}

export interface IntentPattern {
  patterns: RegExp[];
  keywords: string[];
  weight: number;
  requiresImage?: boolean;
  excludePatterns?: RegExp[];
}

/**
 * Intent Recognition System
 * Analyzes user messages to determine appropriate image tool and parameters
 */
export class IntentRecognizer {
  private intentPatterns: Record<string, IntentPattern> = {
    analyze_image: {
      patterns: [
        /what.*in.*image/i,
        /describe.*picture/i,
        /analyze.*image/i,
        /tell.*about.*image/i,
        /what.*see/i,
        /identify.*objects/i,
        /what.*this.*photo/i,
        /explain.*image/i,
        /详细.*描述/i,
        /分析.*图片/i,
        /这.*图.*什么/i,
        /看到.*什么/i,
        /识别.*对象/i,
        /图片.*内容/i
      ],
      keywords: ['analyze', 'describe', 'identify', 'explain', 'what', 'see', 'objects', 'colors', 'scene', '分析', '描述', '识别', '解释', '什么', '看到', '对象', '颜色', '场景'],
      weight: 1.0,
      requiresImage: true
    },

    find_similar_images_by_image: {
      patterns: [
        /find.*similar.*image/i,
        /search.*like.*this/i,
        /images.*similar.*to/i,
        /find.*images.*like/i,
        /search.*similar/i,
        /more.*like.*this/i,
        /similar.*pictures/i,
        /找.*相似.*图片/i,
        /搜索.*类似/i,
        /相似.*图像/i,
        /像.*这样.*图片/i,
        /以图搜图/i,
        /找.*类似/i
      ],
      keywords: ['similar', 'like', 'find', 'search', 'same', 'match', '相似', '类似', '搜索', '查找', '匹配', '相同'],
      weight: 1.0,
      requiresImage: true
    },

    find_similar_images_by_description: {
      patterns: [
        /find.*images.*of/i,
        /search.*for.*pictures/i,
        /show.*me.*photos/i,
        /images.*with/i,
        /pictures.*containing/i,
        /find.*photos.*of/i,
        /search.*images.*that/i,
        /找.*图片.*包含/i,
        /搜索.*照片/i,
        /显示.*图像/i,
        /查找.*图片/i,
        /包含.*的.*图片/i,
        /有.*的.*照片/i
      ],
      keywords: ['find', 'search', 'show', 'photos', 'pictures', 'images', 'containing', 'with', 'of', '找', '搜索', '显示', '照片', '图片', '包含', '有'],
      weight: 0.9,
      requiresImage: false,
      excludePatterns: [/similar/i, /like.*this/i, /相似/i, /类似/i]
    },

    find_images_by_tags: {
      patterns: [
        /tagged.*with/i,
        /images.*with.*tag/i,
        /filter.*by.*tag/i,
        /tag.*search/i,
        /images.*tagged/i,
        /find.*by.*tag/i,
        /标签.*搜索/i,
        /带.*标签.*图片/i,
        /按.*标签.*筛选/i,
        /标记.*为/i,
        /有.*标签/i
      ],
      keywords: ['tag', 'tagged', 'filter', 'label', 'category', '标签', '标记', '筛选', '分类', '类别'],
      weight: 0.8,
      requiresImage: false
    }
  };

  /**
   * Recognize user intent from message and context
   */
  async recognizeIntent(message: string, hasImage: boolean = false): Promise<ImageToolIntent> {
    const normalizedMessage = message.toLowerCase().trim();
    const scores: Record<string, number> = {};
    const reasonings: Record<string, string[]> = {};

    // Calculate scores for each intent
    for (const [intentName, pattern] of Object.entries(this.intentPatterns)) {
      let score = 0;
      const reasons: string[] = [];

      // Check if image requirement is met
      if (pattern.requiresImage && !hasImage) {
        score -= 0.5;
        reasons.push(`需要图片但未提供`);
      } else if (pattern.requiresImage && hasImage) {
        score += 0.3;
        reasons.push(`提供了所需的图片`);
      }

      // Check exclude patterns first
      if (pattern.excludePatterns) {
        for (const excludePattern of pattern.excludePatterns) {
          if (excludePattern.test(normalizedMessage)) {
            score -= 0.4;
            reasons.push(`匹配排除模式: ${excludePattern.source}`);
          }
        }
      }

      // Check regex patterns
      let patternMatches = 0;
      for (const regex of pattern.patterns) {
        if (regex.test(normalizedMessage)) {
          patternMatches++;
          score += pattern.weight * 0.4;
          reasons.push(`匹配模式: ${regex.source}`);
        }
      }

      // Check keyword matches
      let keywordMatches = 0;
      for (const keyword of pattern.keywords) {
        if (normalizedMessage.includes(keyword.toLowerCase())) {
          keywordMatches++;
          score += pattern.weight * 0.1;
        }
      }

      if (keywordMatches > 0) {
        reasons.push(`匹配关键词: ${keywordMatches}个`);
      }

      // Boost score for multiple matches
      if (patternMatches > 1) {
        score += 0.2;
        reasons.push(`多个模式匹配加成`);
      }

      scores[intentName] = Math.max(0, score);
      reasonings[intentName] = reasons;
    }

    // Find the best match
    const sortedIntents = Object.entries(scores)
      .sort(([, a], [, b]) => b - a)
      .filter(([, score]) => score > 0);

    if (sortedIntents.length === 0) {
      // No clear intent, provide fallback
      return this.createFallbackIntent(message, hasImage);
    }

    const [bestIntent, bestScore] = sortedIntents[0];
    const confidence = Math.min(0.95, bestScore);

    // Extract parameters for the recognized intent
    const parameters = await this.extractParameters(message, bestIntent as any);

    // Generate fallback options if confidence is low
    const fallbackOptions = confidence < 0.6 
      ? sortedIntents.slice(1, 3).map(([intent]) => intent)
      : undefined;

    return {
      toolName: bestIntent as any,
      confidence,
      parameters,
      reasoning: reasonings[bestIntent].join('; '),
      fallbackOptions
    };
  }

  /**
   * Extract parameters from natural language for specific tool
   */
  async extractParameters(message: string, toolName: string): Promise<Record<string, any>> {
    const parameterExtractor = new ParameterExtractor();
    const baseParams: Record<string, any> = {};

    switch (toolName) {
      case 'analyze_image':
        baseParams.detailLevel = parameterExtractor.extractDetailLevel(message);
        baseParams.focusAreas = parameterExtractor.extractFocusAreas(message);
        break;

      case 'find_similar_images_by_image':
      case 'find_similar_images_by_description':
        baseParams.limit = parameterExtractor.extractQuantity(message);
        baseParams.threshold = parameterExtractor.extractSimilarityLevel(message);
        break;

      case 'find_images_by_tags':
        baseParams.tags = parameterExtractor.extractTags(message);
        baseParams.matchMode = parameterExtractor.extractMatchMode(message);
        baseParams.limit = parameterExtractor.extractQuantity(message);
        break;
    }

    // Remove undefined values
    return Object.fromEntries(
      Object.entries(baseParams).filter(([, value]) => value !== undefined)
    );
  }

  /**
   * Handle ambiguous requests by asking for clarification
   */
  async handleAmbiguousIntent(message: string, hasImage: boolean): Promise<string> {
    const possibleIntents = await this.recognizeIntent(message, hasImage);
    
    if (possibleIntents.confidence < 0.4) {
      if (hasImage) {
        return "我看到您上传了图片。您希望我：\n1. 分析图片内容\n2. 搜索相似图片\n3. 其他操作？\n请告诉我您的具体需求。";
      } else {
        return "请告诉我您想要：\n1. 通过描述搜索图片\n2. 通过标签搜索图片\n3. 上传图片进行分析\n请提供更多详细信息。";
      }
    }

    if (possibleIntents.fallbackOptions && possibleIntents.fallbackOptions.length > 0) {
      const alternatives = possibleIntents.fallbackOptions
        .map((intent: any) => this.getIntentDescription(intent))
        .join('、');
      
      return `我理解您想要${this.getIntentDescription(possibleIntents.toolName)}，但也可能是想要${alternatives}。请确认您的具体需求。`;
    }

    return "请提供更多详细信息，以便我更好地理解您的需求。";
  }

  /**
   * Create fallback intent for unclear requests
   */
  private createFallbackIntent(message: string, hasImage: boolean): ImageToolIntent {
    // Default to analyze_image if image is provided, otherwise search by description
    const defaultTool = hasImage ? 'analyze_image' : 'find_similar_images_by_description';
    
    return {
      toolName: defaultTool,
      confidence: 0.3,
      parameters: {},
      reasoning: '无法确定明确意图，使用默认工具',
      fallbackOptions: hasImage 
        ? ['find_similar_images_by_image']
        : ['find_images_by_tags']
    };
  }

  /**
   * Get human-readable description of intent
   */
  private getIntentDescription(toolName: string): string {
    const descriptions: Record<string, string> = {
      'analyze_image': '分析图片内容',
      'find_similar_images_by_image': '搜索相似图片',
      'find_similar_images_by_description': '通过描述搜索图片',
      'find_images_by_tags': '通过标签搜索图片'
    };
    return descriptions[toolName] || toolName;
  }

  /**
   * Update intent patterns (for learning and improvement)
   */
  updateIntentPatterns(toolName: string, newPatterns: Partial<IntentPattern>): void {
    if (this.intentPatterns[toolName]) {
      this.intentPatterns[toolName] = {
        ...this.intentPatterns[toolName],
        ...newPatterns
      };
    }
  }

  /**
   * Get confidence threshold for auto-execution
   */
  getConfidenceThreshold(): number {
    return 0.6;
  }

  /**
   * Check if intent should be auto-executed based on confidence
   */
  shouldAutoExecute(intent: ImageToolIntent): boolean {
    return intent.confidence >= this.getConfidenceThreshold();
  }
}

/**
 * Parameter Extraction Engine
 * Extracts specific parameters from natural language
 */
export class ParameterExtractor {
  private quantityMap: Record<string, number> = {
    // English
    'one': 1, 'two': 2, 'three': 3, 'four': 4, 'five': 5,
    'six': 6, 'seven': 7, 'eight': 8, 'nine': 9, 'ten': 10,
    'few': 8, 'several': 12, 'some': 15, 'many': 30, 'lots': 50,
    'dozen': 12, 'couple': 2,
    
    // Chinese
    '一': 1, '二': 2, '三': 3, '四': 4, '五': 5,
    '六': 6, '七': 7, '八': 8, '九': 9, '十': 10,
    '几': 8, '一些': 15, '很多': 30, '大量': 50,
    '少量': 5, '若干': 12, '多个': 20
  };

  private similarityMap: Record<string, number> = {
    // English
    'very similar': 0.8, 'quite similar': 0.7, 'similar': 0.6,
    'somewhat similar': 0.5, 'loosely related': 0.4, 'related': 0.3,
    'exactly like': 0.9, 'almost identical': 0.85, 'close match': 0.75,
    'rough match': 0.45, 'vaguely similar': 0.35,
    
    // Chinese
    '非常相似': 0.8, '很相似': 0.7, '相似': 0.6, '比较相似': 0.65,
    '有点相似': 0.5, '略微相似': 0.4, '相关': 0.3,
    '完全一样': 0.9, '几乎相同': 0.85, '接近': 0.75,
    '大致相似': 0.55, '模糊相似': 0.35
  };

  private detailLevelMap: Record<string, string> = {
    // English
    'basic': 'basic', 'simple': 'basic', 'brief': 'basic',
    'detailed': 'detailed', 'thorough': 'detailed', 'complete': 'detailed',
    'comprehensive': 'comprehensive', 'full': 'comprehensive', 'extensive': 'comprehensive',
    
    // Chinese
    '简单': 'basic', '基本': 'basic', '简要': 'basic',
    '详细': 'detailed', '完整': 'detailed', '全面': 'comprehensive',
    '深入': 'comprehensive', '彻底': 'comprehensive'
  };

  private focusAreaMap: Record<string, string> = {
    // English
    'objects': 'objects', 'things': 'objects', 'items': 'objects',
    'colors': 'colors', 'colour': 'colors', 'hues': 'colors',
    'people': 'people', 'person': 'people', 'humans': 'people',
    'emotions': 'emotions', 'feelings': 'emotions', 'mood': 'emotions',
    'text': 'text', 'words': 'text', 'writing': 'text',
    'scene': 'scene', 'setting': 'scene', 'location': 'scene',
    'activities': 'activities', 'actions': 'activities', 'doing': 'activities',
    'style': 'style', 'artistic': 'style', 'composition': 'style',
    
    // Chinese
    '对象': 'objects', '物体': 'objects', '东西': 'objects',
    '颜色': 'colors', '色彩': 'colors', '色调': 'colors',
    '人': 'people', '人物': 'people', '人员': 'people',
    '情感': 'emotions', '情绪': 'emotions', '心情': 'emotions',
    '文字': 'text', '文本': 'text', '字': 'text',
    '场景': 'scene', '环境': 'scene', '地点': 'scene',
    '活动': 'activities', '动作': 'activities', '行为': 'activities',
    '风格': 'style', '艺术': 'style', '构图': 'style'
  };

  /**
   * Extract quantity from natural language
   */
  extractQuantity(message: string): number | undefined {
    const normalizedMessage = message.toLowerCase();
    
    // Check for explicit numbers
    const numberMatch = normalizedMessage.match(/(\d+)\s*(?:images?|pictures?|photos?|张|个|幅)/);
    if (numberMatch) {
      const num = parseInt(numberMatch[1]);
      return Math.min(100, Math.max(1, num)); // Clamp between 1-100
    }

    // Check for quantity words
    for (const [word, quantity] of Object.entries(this.quantityMap)) {
      if (normalizedMessage.includes(word)) {
        return quantity;
      }
    }

    return undefined; // Use default
  }

  /**
   * Extract similarity level from natural language
   */
  extractSimilarityLevel(message: string): number | undefined {
    const normalizedMessage = message.toLowerCase();
    
    // Check for explicit percentages
    const percentMatch = normalizedMessage.match(/(\d+)%/);
    if (percentMatch) {
      const percent = parseInt(percentMatch[1]);
      return Math.min(1, Math.max(0, percent / 100));
    }

    // Check for similarity phrases (longer phrases first)
    const sortedSimilarityKeys = Object.keys(this.similarityMap)
      .sort((a, b) => b.length - a.length);

    for (const phrase of sortedSimilarityKeys) {
      if (normalizedMessage.includes(phrase)) {
        return this.similarityMap[phrase];
      }
    }

    return undefined; // Use default
  }

  /**
   * Extract tags from natural language
   */
  extractTags(message: string): string[] | undefined {
    const tags: string[] = [];
    
    // Look for explicit tag patterns
    const tagPatterns = [
      /tagged?\s+(?:with|as)\s+['""]([^'""]+)['""]?/gi,
      /tags?\s*[:：]\s*([^,，\n]+)/gi,
      /标签\s*[:：]\s*([^,，\n]+)/gi,
      /带有\s*['""]([^'""]+)['""]?\s*标签/gi
    ];

    for (const pattern of tagPatterns) {
      let match;
      while ((match = pattern.exec(message)) !== null) {
        const tagString = match[1].trim();
        const extractedTags = tagString.split(/[,，\s]+/).filter((tag: any) => tag.length > 0);
        tags.push(...extractedTags);
      }
    }

    // Look for quoted strings as potential tags
    const quotedMatches = message.match(/['""]([^'""]+)['""]?/g);
    if (quotedMatches) {
      for (const match of quotedMatches) {
        const tag = match.replace(/['""]?/g, '').trim();
        if (tag.length > 0 && !tags.includes(tag)) {
          tags.push(tag);
        }
      }
    }

    return tags.length > 0 ? tags : undefined;
  }

  /**
   * Extract match mode (AND/OR logic) from natural language
   */
  extractMatchMode(message: string): 'any' | 'all' | undefined {
    const normalizedMessage = message.toLowerCase();
    
    // AND logic indicators
    const andPatterns = [
      /all.*tags?/i, /every.*tag/i, /both.*tags?/i,
      /所有.*标签/i, /全部.*标签/i, /每个.*标签/i,
      /and/i, /与/i, /并且/i
    ];

    // OR logic indicators  
    const orPatterns = [
      /any.*tags?/i, /either.*tag/i, /one.*of.*tags?/i,
      /任何.*标签/i, /任一.*标签/i, /其中.*标签/i,
      /or/i, /或/i, /或者/i
    ];

    for (const pattern of andPatterns) {
      if (pattern.test(normalizedMessage)) {
        return 'all';
      }
    }

    for (const pattern of orPatterns) {
      if (pattern.test(normalizedMessage)) {
        return 'any';
      }
    }

    return undefined; // Use default
  }

  /**
   * Extract detail level for image analysis
   */
  extractDetailLevel(message: string): 'basic' | 'detailed' | 'comprehensive' | undefined {
    const normalizedMessage = message.toLowerCase();
    
    // Check for detail level indicators (longer phrases first)
    const sortedDetailKeys = Object.keys(this.detailLevelMap)
      .sort((a, b) => b.length - a.length);

    for (const phrase of sortedDetailKeys) {
      if (normalizedMessage.includes(phrase)) {
        return this.detailLevelMap[phrase] as any;
      }
    }

    return undefined; // Use default
  }

  /**
   * Extract focus areas for image analysis
   */
  extractFocusAreas(message: string): string[] | undefined {
    const normalizedMessage = message.toLowerCase();
    const focusAreas: string[] = [];
    
    // Check for focus area indicators
    for (const [phrase, area] of Object.entries(this.focusAreaMap)) {
      if (normalizedMessage.includes(phrase) && !focusAreas.includes(area)) {
        focusAreas.push(area);
      }
    }

    return focusAreas.length > 0 ? focusAreas : undefined;
  }

  /**
   * Extract language preference
   */
  extractLanguage(message: string): 'zh' | 'en' | undefined {
    // Simple heuristic: if message contains Chinese characters, prefer Chinese
    const hasChinese = /[\u4e00-\u9fff]/.test(message);
    const hasEnglish = /[a-zA-Z]/.test(message);
    
    if (hasChinese && !hasEnglish) {
      return 'zh';
    } else if (hasEnglish && !hasChinese) {
      return 'en';
    }
    
    return undefined; // Use default
  }

  /**
   * Extract all parameters at once
   */
  extractAllParameters(message: string, toolName: string): SearchParameters {
    return {
      limit: this.extractQuantity(message),
      threshold: this.extractSimilarityLevel(message),
      tags: this.extractTags(message),
      matchMode: this.extractMatchMode(message),
      detailLevel: this.extractDetailLevel(message),
      focusAreas: this.extractFocusAreas(message)
    };
  }
}

/**
 * Intent Recognition Factory
 * Provides configured instances of intent recognition components
 */
export class IntentRecognitionFactory {
  private static instance: IntentRecognizer;
  private static parameterExtractor: ParameterExtractor;

  /**
   * Get singleton intent recognizer instance
   */
  static getIntentRecognizer(): IntentRecognizer {
    if (!this.instance) {
      this.instance = new IntentRecognizer();
    }
    return this.instance;
  }

  /**
   * Get singleton parameter extractor instance
   */
  static getParameterExtractor(): ParameterExtractor {
    if (!this.parameterExtractor) {
      this.parameterExtractor = new ParameterExtractor();
    }
    return this.parameterExtractor;
  }

  /**
   * Create configured intent recognizer with custom patterns
   */
  static createCustomIntentRecognizer(customPatterns?: Record<string, Partial<IntentPattern>>): IntentRecognizer {
    const recognizer = new IntentRecognizer();
    
    if (customPatterns) {
      for (const [toolName, patterns] of Object.entries(customPatterns)) {
        recognizer.updateIntentPatterns(toolName, patterns);
      }
    }
    
    return recognizer;
  }

  /**
   * Test intent recognition with sample messages
   */
  static async testIntentRecognition(messages: Array<{text: string; hasImage?: boolean}>): Promise<Array<{message: string; intent: ImageToolIntent}>> {
    const recognizer = this.getIntentRecognizer();
    const results = [];

    for (const {text, hasImage = false} of messages) {
      const intent = await recognizer.recognizeIntent(text, hasImage);
      results.push({message: text, intent});
    }

    return results;
  }
}