import { ToolCall, ToolCallResponse } from '../types/tools';

export type ToolCallStatus = 'pending' | 'executing' | 'completed' | 'failed';

export interface ToolCallState {
  id: string;
  toolCall: ToolCall;
  status: ToolCallStatus;
  startTime?: Date;
  endTime?: Date;
  result?: ToolCallResponse;
  error?: string;
  progress?: number; // 0-100
}

export interface ToolCallSession {
  id: string;
  messageId: string;
  toolCalls: ToolCallState[];
  status: 'pending' | 'executing' | 'completed' | 'failed';
  startTime: Date;
  endTime?: Date;
  totalTools: number;
  completedTools: number;
  failedTools: number;
}

export interface ToolCallHistory {
  sessionId: string;
  messageId: string;
  toolName: string;
  parameters: any;
  result?: any;
  executionTime: number;
  status: ToolCallStatus;
  timestamp: Date;
}

/**
 * Tool call state manager for tracking execution progress and history
 */
export class ToolCallStateManager {
  private sessions: Map<string, ToolCallSession> = new Map();
  private history: ToolCallHistory[] = [];
  private listeners: ((session: ToolCallSession) => void)[] = [];

  /**
   * Create a new tool call session
   */
  createSession(messageId: string, toolCalls: ToolCall[]): ToolCallSession {
    const sessionId = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    const session: ToolCallSession = {
      id: sessionId,
      messageId,
      toolCalls: toolCalls.map((toolCall: any) => ({
        id: toolCall.id,
        toolCall,
        status: 'pending',
        startTime: new Date()
      })),
      status: 'pending',
      startTime: new Date(),
      totalTools: toolCalls.length,
      completedTools: 0,
      failedTools: 0
    };

    this.sessions.set(sessionId, session);
    this.notifyListeners(session);
    
    return session;
  }

  /**
   * Update tool call status
   */
  updateToolCallStatus(sessionId: string, toolCallId: string, status: ToolCallStatus, result?: ToolCallResponse, error?: string): void {
    const session = this.sessions.get(sessionId);
    if (!session) return;

    const toolCallState = session.toolCalls.find((tc: any) => tc.id === toolCallId);
    if (!toolCallState) return;

    toolCallState.status = status;
    toolCallState.result = result;
    toolCallState.error = error;

    if (status === 'executing') {
      toolCallState.startTime = new Date();
    } else if (status === 'completed' || status === 'failed') {
      toolCallState.endTime = new Date();
      
      if (status === 'completed') {
        session.completedTools++;
      } else {
        session.failedTools++;
      }
    }

    // Update session status
    this.updateSessionStatus(session);
    
    // Add to history
    if (status === 'completed' || status === 'failed') {
      this.addToHistory(session, toolCallState);
    }

    this.notifyListeners(session);
  }

  /**
   * Update tool call progress
   */
  updateToolCallProgress(sessionId: string, toolCallId: string, progress: number): void {
    const session = this.sessions.get(sessionId);
    if (!session) return;

    const toolCallState = session.toolCalls.find((tc: any) => tc.id === toolCallId);
    if (!toolCallState) return;

    toolCallState.progress = Math.max(0, Math.min(100, progress));
    this.notifyListeners(session);
  }

  /**
   * Update session status based on tool call states
   */
  private updateSessionStatus(session: ToolCallSession): void {
    const { toolCalls } = session;
    
    if (toolCalls.every((tc: any) => tc.status === 'completed')) {
      session.status = 'completed';
      session.endTime = new Date();
    } else if (toolCalls.some((tc: any) => tc.status === 'failed') && toolCalls.every((tc: any) => tc.status === 'completed' || tc.status === 'failed')) {
      session.status = 'failed';
      session.endTime = new Date();
    } else if (toolCalls.some((tc: any) => tc.status === 'executing')) {
      session.status = 'executing';
    }
  }

  /**
   * Add tool call to history
   */
  private addToHistory(session: ToolCallSession, toolCallState: ToolCallState): void {
    const executionTime = toolCallState.endTime && toolCallState.startTime 
      ? toolCallState.endTime.getTime() - toolCallState.startTime.getTime()
      : 0;

    const historyEntry: ToolCallHistory = {
      sessionId: session.id,
      messageId: session.messageId,
      toolName: toolCallState.toolCall.name,
      parameters: toolCallState.toolCall.parameters,
      result: toolCallState.result?.result,
      executionTime,
      status: toolCallState.status,
      timestamp: new Date()
    };

    this.history.push(historyEntry);
    
    // Keep only last 100 history entries
    if (this.history.length > 100) {
      this.history = this.history.slice(-100);
    }
  }

  /**
   * Get session by ID
   */
  getSession(sessionId: string): ToolCallSession | undefined {
    return this.sessions.get(sessionId);
  }

  /**
   * Get all active sessions
   */
  getActiveSessions(): ToolCallSession[] {
    return Array.from(this.sessions.values()).filter((session: any) => 
      session.status === 'pending' || session.status === 'executing'
    );
  }

  /**
   * Get completed sessions
   */
  getCompletedSessions(): ToolCallSession[] {
    return Array.from(this.sessions.values()).filter((session: any) => 
      session.status === 'completed' || session.status === 'failed'
    );
  }

  /**
   * Get tool call history
   */
  getHistory(limit: number = 50): ToolCallHistory[] {
    return this.history.slice(-limit).reverse();
  }

  /**
   * Get tool call statistics
   */
  getStatistics(): {
    totalCalls: number;
    successfulCalls: number;
    failedCalls: number;
    averageExecutionTime: number;
    mostUsedTools: { toolName: string; count: number }[];
  } {
    const totalCalls = this.history.length;
    const successfulCalls = this.history.filter((h: any) => h.status === 'completed').length;
    const failedCalls = this.history.filter((h: any) => h.status === 'failed').length;
    
    const executionTimes = this.history.filter((h: any) => h.executionTime > 0).map((h: any) => h.executionTime);
    const averageExecutionTime = executionTimes.length > 0 
      ? executionTimes.reduce((sum, time) => sum + time, 0) / executionTimes.length 
      : 0;

    const toolUsage = this.history.reduce((acc, h) => {
      acc[h.toolName] = (acc[h.toolName] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const mostUsedTools = Object.entries(toolUsage)
      .map(([toolName, count]) => ({ toolName, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 5);

    return {
      totalCalls,
      successfulCalls,
      failedCalls,
      averageExecutionTime,
      mostUsedTools
    };
  }

  /**
   * Clear old sessions (keep only last 20)
   */
  clearOldSessions(): void {
    const completedSessions = this.getCompletedSessions();
    if (completedSessions.length > 20) {
      const sessionsToRemove = completedSessions
        .sort((a, b) => a.startTime.getTime() - b.startTime.getTime())
        .slice(0, completedSessions.length - 20);

      for (const session of sessionsToRemove) {
        this.sessions.delete(session.id);
      }
    }
  }

  /**
   * Subscribe to session updates
   */
  subscribe(listener: (session: ToolCallSession) => void): () => void {
    this.listeners.push(listener);
    return () => {
      const index = this.listeners.indexOf(listener);
      if (index > -1) {
        this.listeners.splice(index, 1);
      }
    };
  }

  /**
   * Notify all listeners
   */
  private notifyListeners(session: ToolCallSession): void {
    for (const listener of this.listeners) {
      try {
        listener(session);
      } catch (error) {
        console.error('Error in tool call state listener:', error);
      }
    }
  }

  /**
   * Clear all data
   */
  clear(): void {
    this.sessions.clear();
    this.history = [];
    this.listeners = [];
  }
}

// Global instance
export const toolCallStateManager = new ToolCallStateManager();