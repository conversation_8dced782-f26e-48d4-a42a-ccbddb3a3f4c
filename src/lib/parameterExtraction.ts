/**
 * Advanced Parameter Extraction System
 * 
 * This module provides sophisticated natural language parameter extraction
 * capabilities for image tool operations, supporting multiple languages
 * and complex parameter combinations.
 */

export interface ExtractedParameters {
  // Quantity parameters
  limit?: number;
  
  // Similarity parameters
  threshold?: number;
  similarityLevel?: 'very_high' | 'high' | 'medium' | 'low' | 'very_low';
  
  // Tag parameters
  tags?: string[];
  matchMode?: 'any' | 'all';
  
  // Analysis parameters
  detailLevel?: 'basic' | 'detailed' | 'comprehensive';
  focusAreas?: string[];
  
  // Language and format parameters
  language?: 'zh' | 'en';
  format?: 'json' | 'text' | 'structured';
  
  // Confidence and metadata
  extractionConfidence?: number;
  extractedFrom?: string[];
}

export interface ParameterExtractionRule {
  patterns: RegExp[];
  keywords: string[];
  values: Record<string, unknown>;
  weight: number;
  language?: 'zh' | 'en' | 'both';
  context?: string[];
}

/**
 * Advanced Parameter Extraction Engine
 */
export class AdvancedParameterExtractor {
  private quantityRules: ParameterExtractionRule[] = [
    {
      patterns: [/(\d+)\s*(?:images?|pictures?|photos?|pics?)/gi],
      keywords: [],
      values: {} as Record<string, number>,
      weight: 1.0,
      language: 'en'
    },
    {
      patterns: [/(\d+)\s*(?:张|个|幅|份)/gi],
      keywords: [],
      values: {} as Record<string, number>,
      weight: 1.0,
      language: 'zh'
    },
    {
      patterns: [/(?:a\s+)?few/gi, /several/gi, /some/gi, /many/gi, /lots?\s+of/gi],
      keywords: ['few', 'several', 'some', 'many', 'lots'],
      values: {
        'few': 8,
        'several': 12,
        'some': 15,
        'many': 30,
        'lots': 50,
        'lot': 50
      } as Record<string, number>,
      weight: 0.8,
      language: 'en'
    },
    {
      patterns: [/几个?/gi, /一些/gi, /若干/gi, /很多/gi, /大量/gi, /少量/gi],
      keywords: ['几', '一些', '若干', '很多', '大量', '少量'],
      values: {
        '几': 8,
        '一些': 15,
        '若干': 12,
        '很多': 30,
        '大量': 50,
        '少量': 5
      } as Record<string, number>,
      weight: 0.8,
      language: 'zh'
    }
  ];

  private similarityRules: ParameterExtractionRule[] = [
    {
      patterns: [
        /very\s+similar/gi,
        /quite\s+similar/gi,
        /somewhat\s+similar/gi,
        /loosely\s+related/gi,
        /exactly\s+like/gi,
        /almost\s+identical/gi
      ],
      keywords: ['similar', 'like', 'identical', 'related'],
      values: {
        'very similar': 0.8,
        'quite similar': 0.7,
        'similar': 0.6,
        'somewhat similar': 0.5,
        'loosely related': 0.4,
        'related': 0.3,
        'exactly like': 0.9,
        'almost identical': 0.85
      },
      weight: 1.0,
      language: 'en'
    },
    {
      patterns: [
        /非常相似/gi,
        /很相似/gi,
        /比较相似/gi,
        /有点相似/gi,
        /略微相似/gi,
        /完全一样/gi,
        /几乎相同/gi
      ],
      keywords: ['相似', '相同', '一样', '类似'],
      values: {
        '非常相似': 0.8,
        '很相似': 0.7,
        '比较相似': 0.65,
        '相似': 0.6,
        '有点相似': 0.5,
        '略微相似': 0.4,
        '完全一样': 0.9,
        '几乎相同': 0.85,
        '类似': 0.55
      },
      weight: 1.0,
      language: 'zh'
    },
    {
      patterns: [/(\d+)%\s*(?:similar|match|相似|匹配)/gi],
      keywords: [],
      values: {},
      weight: 1.0,
      language: 'both'
    }
  ];

  private detailLevelRules: ParameterExtractionRule[] = [
    {
      patterns: [
        /basic\s+(?:analysis|description)/gi,
        /simple\s+(?:analysis|description)/gi,
        /brief\s+(?:analysis|description)/gi,
        /quick\s+(?:analysis|description)/gi
      ],
      keywords: ['basic', 'simple', 'brief', 'quick'],
      values: {
        'basic': 'basic',
        'simple': 'basic',
        'brief': 'basic',
        'quick': 'basic'
      },
      weight: 1.0,
      language: 'en'
    },
    {
      patterns: [
        /detailed\s+(?:analysis|description)/gi,
        /thorough\s+(?:analysis|description)/gi,
        /complete\s+(?:analysis|description)/gi
      ],
      keywords: ['detailed', 'thorough', 'complete'],
      values: {
        'detailed': 'detailed',
        'thorough': 'detailed',
        'complete': 'detailed'
      },
      weight: 1.0,
      language: 'en'
    },
    {
      patterns: [
        /comprehensive\s+(?:analysis|description)/gi,
        /full\s+(?:analysis|description)/gi,
        /extensive\s+(?:analysis|description)/gi,
        /in-depth\s+(?:analysis|description)/gi
      ],
      keywords: ['comprehensive', 'full', 'extensive', 'in-depth'],
      values: {
        'comprehensive': 'comprehensive',
        'full': 'comprehensive',
        'extensive': 'comprehensive',
        'in-depth': 'comprehensive'
      },
      weight: 1.0,
      language: 'en'
    },
    {
      patterns: [
        /简单.*(?:分析|描述)/gi,
        /基本.*(?:分析|描述)/gi,
        /简要.*(?:分析|描述)/gi
      ],
      keywords: ['简单', '基本', '简要'],
      values: {
        '简单': 'basic',
        '基本': 'basic',
        '简要': 'basic'
      },
      weight: 1.0,
      language: 'zh'
    },
    {
      patterns: [
        /详细.*(?:分析|描述)/gi,
        /完整.*(?:分析|描述)/gi,
        /全面.*(?:分析|描述)/gi
      ],
      keywords: ['详细', '完整', '全面'],
      values: {
        '详细': 'detailed',
        '完整': 'detailed',
        '全面': 'comprehensive'
      },
      weight: 1.0,
      language: 'zh'
    }
  ];

  private focusAreaRules: ParameterExtractionRule[] = [
    {
      patterns: [
        /(?:identify|find|detect|analyze)\s+(?:the\s+)?objects?/gi,
        /what\s+(?:objects?|things?|items?)/gi,
        /objects?\s+in\s+(?:the\s+)?(?:image|picture|photo)/gi
      ],
      keywords: ['objects', 'things', 'items'],
      values: { 'match': 'objects' },
      weight: 1.0,
      language: 'en'
    },
    {
      patterns: [
        /(?:identify|find|detect|analyze)\s+(?:the\s+)?colou?rs?/gi,
        /what\s+colou?rs?/gi,
        /colou?rs?\s+in\s+(?:the\s+)?(?:image|picture|photo)/gi
      ],
      keywords: ['colors', 'colours', 'hues'],
      values: { 'match': 'colors' },
      weight: 1.0,
      language: 'en'
    },
    {
      patterns: [
        /(?:identify|find|detect|analyze)\s+(?:the\s+)?(?:people|persons?|humans?)/gi,
        /who\s+(?:is|are)\s+in/gi,
        /people\s+in\s+(?:the\s+)?(?:image|picture|photo)/gi
      ],
      keywords: ['people', 'person', 'humans'],
      values: { 'match': 'people' },
      weight: 1.0,
      language: 'en'
    },
    {
      patterns: [
        /(?:emotions?|feelings?|mood)/gi,
        /how\s+(?:does|do)\s+(?:they|he|she)\s+feel/gi,
        /emotional\s+(?:state|expression)/gi
      ],
      keywords: ['emotions', 'feelings', 'mood', 'emotional'],
      values: { 'match': 'emotions' },
      weight: 1.0,
      language: 'en'
    },
    {
      patterns: [
        /(?:text|words?|writing|letters?)/gi,
        /what\s+(?:does\s+it\s+)?say/gi,
        /any\s+text/gi
      ],
      keywords: ['text', 'words', 'writing'],
      values: { 'match': 'text' },
      weight: 1.0,
      language: 'en'
    },
    {
      patterns: [
        /(?:scene|setting|location|place|environment)/gi,
        /where\s+(?:is\s+this|was\s+this)/gi,
        /what\s+(?:place|location)/gi
      ],
      keywords: ['scene', 'setting', 'location', 'place', 'environment'],
      values: { 'match': 'scene' },
      weight: 1.0,
      language: 'en'
    },
    {
      patterns: [
        /(?:activities?|actions?|doing)/gi,
        /what\s+(?:are\s+they|is\s+he|is\s+she)\s+doing/gi,
        /what\s+(?:activity|action)/gi
      ],
      keywords: ['activities', 'actions', 'doing'],
      values: { 'match': 'activities' },
      weight: 1.0,
      language: 'en'
    },
    {
      patterns: [
        /(?:style|artistic|composition|art)/gi,
        /what\s+style/gi,
        /artistic\s+(?:style|approach)/gi
      ],
      keywords: ['style', 'artistic', 'composition', 'art'],
      values: { 'match': 'style' },
      weight: 1.0,
      language: 'en'
    },
    // Chinese patterns
    {
      patterns: [
        /(?:识别|找到|检测|分析).*(?:对象|物体|东西)/gi,
        /(?:有|是).*什么.*(?:对象|物体|东西)/gi,
        /图.*(?:中|里).*(?:对象|物体|东西)/gi
      ],
      keywords: ['对象', '物体', '东西'],
      values: { 'match': 'objects' },
      weight: 1.0,
      language: 'zh'
    },
    {
      patterns: [
        /(?:识别|找到|检测|分析).*(?:颜色|色彩|色调)/gi,
        /什么.*颜色/gi,
        /图.*(?:中|里).*(?:颜色|色彩)/gi
      ],
      keywords: ['颜色', '色彩', '色调'],
      values: { 'match': 'colors' },
      weight: 1.0,
      language: 'zh'
    },
    {
      patterns: [
        /(?:识别|找到|检测|分析).*(?:人|人物|人员)/gi,
        /(?:有|是).*什么.*人/gi,
        /图.*(?:中|里).*人/gi
      ],
      keywords: ['人', '人物', '人员'],
      values: { 'match': 'people' },
      weight: 1.0,
      language: 'zh'
    },
    {
      patterns: [
        /(?:情感|情绪|心情|感受)/gi,
        /(?:感觉|表情).*怎么样/gi,
        /情感.*(?:状态|表达)/gi
      ],
      keywords: ['情感', '情绪', '心情', '感受'],
      values: { 'match': 'emotions' },
      weight: 1.0,
      language: 'zh'
    },
    {
      patterns: [
        /(?:文字|文本|字|写)/gi,
        /(?:写|说).*什么/gi,
        /(?:有|包含).*文字/gi
      ],
      keywords: ['文字', '文本', '字'],
      values: { 'match': 'text' },
      weight: 1.0,
      language: 'zh'
    },
    {
      patterns: [
        /(?:场景|环境|地点|位置|背景)/gi,
        /(?:在|是).*什么.*(?:地方|地点)/gi,
        /什么.*(?:场景|环境)/gi
      ],
      keywords: ['场景', '环境', '地点', '位置', '背景'],
      values: { 'match': 'scene' },
      weight: 1.0,
      language: 'zh'
    },
    {
      patterns: [
        /(?:活动|动作|行为|在做)/gi,
        /(?:在|正在).*(?:做|干).*什么/gi,
        /什么.*(?:活动|动作)/gi
      ],
      keywords: ['活动', '动作', '行为'],
      values: { 'match': 'activities' },
      weight: 1.0,
      language: 'zh'
    },
    {
      patterns: [
        /(?:风格|艺术|构图|样式)/gi,
        /什么.*风格/gi,
        /艺术.*(?:风格|样式)/gi
      ],
      keywords: ['风格', '艺术', '构图', '样式'],
      values: { 'match': 'style' },
      weight: 1.0,
      language: 'zh'
    }
  ];

  private tagRules: ParameterExtractionRule[] = [
    {
      patterns: [
        /tagged?\s+(?:with|as)\s+['""]([^'""]+)['""]?/gi,
        /tags?\s*[:：]\s*([^,，\n]+)/gi,
        /(?:has|have|with)\s+tags?\s+([^,，\n]+)/gi
      ],
      keywords: ['tagged', 'tags', 'tag'],
      values: {},
      weight: 1.0,
      language: 'en'
    },
    {
      patterns: [
        /标签\s*[:：]\s*([^,，\n]+)/gi,
        /带有\s*['""]([^'""]+)['""]?\s*标签/gi,
        /(?:有|包含)\s*([^,，\n]+)\s*标签/gi
      ],
      keywords: ['标签', '标记'],
      values: {},
      weight: 1.0,
      language: 'zh'
    },
    {
      patterns: [/['""]([^'""]+)['""]?/g],
      keywords: [],
      values: {},
      weight: 0.6,
      language: 'both',
      context: ['tag', 'tagged', 'label', '标签']
    }
  ];

  private matchModeRules: ParameterExtractionRule[] = [
    {
      patterns: [
        /all\s+(?:of\s+)?(?:the\s+)?tags?/gi,
        /every\s+tag/gi,
        /both\s+tags?/gi,
        /all\s+(?:these\s+)?(?:tags?|labels?)/gi
      ],
      keywords: ['all', 'every', 'both'],
      values: { 'match': 'all' },
      weight: 1.0,
      language: 'en'
    },
    {
      patterns: [
        /any\s+(?:of\s+)?(?:the\s+)?tags?/gi,
        /either\s+tag/gi,
        /one\s+of\s+(?:the\s+)?tags?/gi,
        /any\s+(?:these\s+)?(?:tags?|labels?)/gi
      ],
      keywords: ['any', 'either', 'one'],
      values: { 'match': 'any' },
      weight: 1.0,
      language: 'en'
    },
    {
      patterns: [
        /所有.*标签/gi,
        /全部.*标签/gi,
        /每个.*标签/gi,
        /都.*(?:有|包含)/gi
      ],
      keywords: ['所有', '全部', '每个', '都'],
      values: { 'match': 'all' },
      weight: 1.0,
      language: 'zh'
    },
    {
      patterns: [
        /任何.*标签/gi,
        /任一.*标签/gi,
        /其中.*标签/gi,
        /(?:有|包含).*(?:任何|任一)/gi
      ],
      keywords: ['任何', '任一', '其中'],
      values: { 'match': 'any' },
      weight: 1.0,
      language: 'zh'
    }
  ];

  /**
   * Extract quantity parameters from text
   */
  extractQuantity(text: string): { value?: number; confidence: number; source: string[] } {
    const normalizedText = text.toLowerCase();
    let bestValue: number | undefined;
    let bestConfidence = 0;
    const sources: string[] = [];

    for (const rule of this.quantityRules) {
      // Check patterns
      for (const pattern of rule.patterns) {
        const matches = normalizedText.match(pattern);
        if (matches) {
          if (pattern.source.includes('\\d+')) {
            // Extract numeric value
            const numMatch = matches[0].match(/\d+/);
            if (numMatch) {
              const value = parseInt(numMatch[0]);
              if (value > 0 && value <= 100) {
                const confidence = rule.weight;
                if (confidence > bestConfidence) {
                  bestValue = value;
                  bestConfidence = confidence;
                  sources.push(`numeric: ${matches[0]}`);
                }
              }
            }
          } else {
            // Use predefined values
            for (const [keyword, value] of Object.entries(rule.values)) {
              if (normalizedText.includes(keyword)) {
                const confidence = rule.weight * 0.8;
                if (confidence > bestConfidence) {
                  bestValue = value as number;
                  bestConfidence = confidence;
                  sources.push(`keyword: ${keyword}`);
                }
              }
            }
          }
        }
      }
    }

    return {
      value: bestValue,
      confidence: bestConfidence,
      source: sources
    };
  }

  /**
   * Extract similarity threshold from text
   */
  extractSimilarityThreshold(text: string): { value?: number; level?: string; confidence: number; source: string[] } {
    const normalizedText = text.toLowerCase();
    let bestValue: number | undefined;
    let bestLevel: string | undefined;
    let bestConfidence = 0;
    const sources: string[] = [];

    for (const rule of this.similarityRules) {
      // Check for percentage patterns
      if (rule.patterns.some((p: any) => p.source.includes('\\d+'))) {
        const percentMatch = normalizedText.match(/(\d+)%/);
        if (percentMatch) {
          const percent = parseInt(percentMatch[1]);
          if (percent >= 0 && percent <= 100) {
            const value = percent / 100;
            const confidence = rule.weight;
            if (confidence > bestConfidence) {
              bestValue = value;
              bestLevel = this.getThresholdLevel(value);
              bestConfidence = confidence;
              sources.push(`percentage: ${percent}%`);
            }
          }
        }
      }

      // Check similarity phrases
      for (const [phrase, value] of Object.entries(rule.values)) {
        if (normalizedText.includes(phrase.toLowerCase())) {
          const confidence = rule.weight * 0.9;
          if (confidence > bestConfidence) {
            bestValue = value as number;
            bestLevel = this.getThresholdLevel(value as number);
            bestConfidence = confidence;
            sources.push(`phrase: ${phrase}`);
          }
        }
      }
    }

    return {
      value: bestValue,
      level: bestLevel,
      confidence: bestConfidence,
      source: sources
    };
  }

  /**
   * Extract detail level from text
   */
  extractDetailLevel(text: string): { value?: string; confidence: number; source: string[] } {
    const normalizedText = text.toLowerCase();
    let bestValue: string | undefined;
    let bestConfidence = 0;
    const sources: string[] = [];

    for (const rule of this.detailLevelRules) {
      for (const pattern of rule.patterns) {
        if (pattern.test(normalizedText)) {
          for (const [keyword, value] of Object.entries(rule.values)) {
            if (normalizedText.includes(keyword.toLowerCase())) {
              const confidence = rule.weight;
              if (confidence > bestConfidence) {
                bestValue = value as string;
                bestConfidence = confidence;
                sources.push(`pattern: ${keyword}`);
              }
            }
          }
        }
      }
    }

    return {
      value: bestValue,
      confidence: bestConfidence,
      source: sources
    };
  }

  /**
   * Extract focus areas from text
   */
  extractFocusAreas(text: string): { values: string[]; confidence: number; source: string[] } {
    const normalizedText = text.toLowerCase();
    const focusAreas: string[] = [];
    const sources: string[] = [];
    let totalConfidence = 0;
    let matchCount = 0;

    for (const rule of this.focusAreaRules) {
      let ruleMatched = false;

      for (const pattern of rule.patterns) {
        if (pattern.test(normalizedText)) {
          const area = rule.values.match as string;
          if (area && !focusAreas.includes(area)) {
            focusAreas.push(area);
            sources.push(`pattern: ${area}`);
            totalConfidence += rule.weight;
            matchCount++;
            ruleMatched = true;
            break;
          }
        }
      }

      if (!ruleMatched) {
        for (const keyword of rule.keywords) {
          if (normalizedText.includes(keyword.toLowerCase())) {
            const area = rule.values.match as string;
            if (area && !focusAreas.includes(area)) {
              focusAreas.push(area);
              sources.push(`keyword: ${keyword}`);
              totalConfidence += rule.weight * 0.7;
              matchCount++;
              break;
            }
          }
        }
      }
    }

    const averageConfidence = matchCount > 0 ? totalConfidence / matchCount : 0;

    return {
      values: focusAreas,
      confidence: averageConfidence,
      source: sources
    };
  }

  /**
   * Extract tags from text
   */
  extractTags(text: string): { values: string[]; confidence: number; source: string[] } {
    const tags: string[] = [];
    const sources: string[] = [];
    let totalConfidence = 0;
    let matchCount = 0;

    for (const rule of this.tagRules) {
      for (const pattern of rule.patterns) {
        let match;
        while ((match = pattern.exec(text)) !== null) {
          if (match[1]) {
            const tagString = match[1].trim();
            const extractedTags = tagString.split(/[,，\s]+/).filter((tag: any) => tag.length > 0);
            
            for (const tag of extractedTags) {
              if (!tags.includes(tag)) {
                tags.push(tag);
                sources.push(`pattern: ${tag}`);
                totalConfidence += rule.weight;
                matchCount++;
              }
            }
          }
        }
      }
    }

    const averageConfidence = matchCount > 0 ? totalConfidence / matchCount : 0;

    return {
      values: tags,
      confidence: averageConfidence,
      source: sources
    };
  }

  /**
   * Extract match mode from text
   */
  extractMatchMode(text: string): { value?: 'any' | 'all'; confidence: number; source: string[] } {
    const normalizedText = text.toLowerCase();
    let bestValue: 'any' | 'all' | undefined;
    let bestConfidence = 0;
    const sources: string[] = [];

    for (const rule of this.matchModeRules) {
      for (const pattern of rule.patterns) {
        if (pattern.test(normalizedText)) {
          const value = rule.values.match as 'any' | 'all';
          const confidence = rule.weight;
          if (confidence > bestConfidence) {
            bestValue = value;
            bestConfidence = confidence;
            sources.push(`pattern: ${value}`);
          }
        }
      }
    }

    return {
      value: bestValue,
      confidence: bestConfidence,
      source: sources
    };
  }

  /**
   * Extract all parameters from text
   */
  extractAllParameters(text: string, toolName?: string): ExtractedParameters {
    const quantity = this.extractQuantity(text);
    const similarity = this.extractSimilarityThreshold(text);
    const detailLevel = this.extractDetailLevel(text);
    const focusAreas = this.extractFocusAreas(text);
    const tags = this.extractTags(text);
    const matchMode = this.extractMatchMode(text);
    const language = this.detectLanguage(text);

    const extractedFrom: string[] = [];
    let totalConfidence = 0;
    let parameterCount = 0;

    const result: ExtractedParameters = {};

    if (quantity.value !== undefined) {
      result.limit = quantity.value;
      extractedFrom.push(...quantity.source);
      totalConfidence += quantity.confidence;
      parameterCount++;
    }

    if (similarity.value !== undefined) {
      result.threshold = similarity.value;
      result.similarityLevel = similarity.level as 'very_high' | 'high' | 'medium' | 'low' | 'very_low';
      extractedFrom.push(...similarity.source);
      totalConfidence += similarity.confidence;
      parameterCount++;
    }

    if (detailLevel.value !== undefined) {
      result.detailLevel = detailLevel.value as 'basic' | 'detailed' | 'comprehensive';
      extractedFrom.push(...detailLevel.source);
      totalConfidence += detailLevel.confidence;
      parameterCount++;
    }

    if (focusAreas.values.length > 0) {
      result.focusAreas = focusAreas.values;
      extractedFrom.push(...focusAreas.source);
      totalConfidence += focusAreas.confidence;
      parameterCount++;
    }

    if (tags.values.length > 0) {
      result.tags = tags.values;
      extractedFrom.push(...tags.source);
      totalConfidence += tags.confidence;
      parameterCount++;
    }

    if (matchMode.value !== undefined) {
      result.matchMode = matchMode.value;
      extractedFrom.push(...matchMode.source);
      totalConfidence += matchMode.confidence;
      parameterCount++;
    }

    if (language) {
      result.language = language;
      extractedFrom.push(`language: ${language}`);
    }

    result.extractionConfidence = parameterCount > 0 ? totalConfidence / parameterCount : 0;
    result.extractedFrom = extractedFrom;

    return result;
  }

  /**
   * Get threshold level from numeric value
   */
  private getThresholdLevel(value: number): 'very_high' | 'high' | 'medium' | 'low' | 'very_low' {
    if (value >= 0.8) return 'very_high';
    if (value >= 0.65) return 'high';
    if (value >= 0.45) return 'medium';
    if (value >= 0.25) return 'low';
    return 'very_low';
  }

  /**
   * Detect language from text
   */
  private detectLanguage(text: string): 'zh' | 'en' | undefined {
    const hasChinese = /[\u4e00-\u9fff]/.test(text);
    const hasEnglish = /[a-zA-Z]/.test(text);
    
    if (hasChinese && !hasEnglish) {
      return 'zh';
    } else if (hasEnglish && !hasChinese) {
      return 'en';
    }
    
    // Mixed or unclear
    return undefined;
  }

  /**
   * Validate extracted parameters
   */
  validateParameters(params: ExtractedParameters, toolName: string): { valid: boolean; errors: string[]; warnings: string[] } {
    const errors: string[] = [];
    const warnings: string[] = [];

    // Validate limit
    if (params.limit !== undefined) {
      if (params.limit < 1 || params.limit > 100) {
        errors.push(`Limit must be between 1 and 100, got ${params.limit}`);
      }
    }

    // Validate threshold
    if (params.threshold !== undefined) {
      if (params.threshold < 0 || params.threshold > 1) {
        errors.push(`Threshold must be between 0 and 1, got ${params.threshold}`);
      }
    }

    // Validate detail level
    if (params.detailLevel !== undefined) {
      const validLevels = ['basic', 'detailed', 'comprehensive'];
      if (!validLevels.includes(params.detailLevel)) {
        errors.push(`Invalid detail level: ${params.detailLevel}`);
      }
    }

    // Validate focus areas
    if (params.focusAreas !== undefined) {
      const validAreas = ['objects', 'colors', 'people', 'emotions', 'text', 'scene', 'activities', 'style'];
      const invalidAreas = params.focusAreas.filter((area: any) => !validAreas.includes(area));
      if (invalidAreas.length > 0) {
        warnings.push(`Unknown focus areas: ${invalidAreas.join(', ')}`);
      }
    }

    // Validate tags
    if (params.tags !== undefined) {
      if (params.tags.length === 0) {
        warnings.push('Empty tags array provided');
      }
      const longTags = params.tags.filter((tag: any) => tag.length > 50);
      if (longTags.length > 0) {
        warnings.push(`Very long tags detected: ${longTags.join(', ')}`);
      }
    }

    // Tool-specific validations
    switch (toolName) {
      case 'analyze_image':
        if (params.tags !== undefined || params.matchMode !== undefined) {
          warnings.push('Tags and match mode are not used for image analysis');
        }
        break;
      
      case 'find_images_by_tags':
        if (params.tags === undefined || params.tags.length === 0) {
          errors.push('Tags are required for tag-based search');
        }
        break;
      
      case 'find_similar_images_by_image':
      case 'find_similar_images_by_description':
        if (params.detailLevel !== undefined || params.focusAreas !== undefined) {
          warnings.push('Detail level and focus areas are not used for similarity search');
        }
        break;
    }

    return {
      valid: errors.length === 0,
      errors,
      warnings
    };
  }

  /**
   * Get parameter extraction statistics
   */
  getExtractionStats(params: ExtractedParameters): {
    totalParameters: number;
    extractedParameters: number;
    extractionRate: number;
    averageConfidence: number;
    sourceBreakdown: Record<string, number>;
  } {
    const totalPossible = 8; // limit, threshold, detailLevel, focusAreas, tags, matchMode, language, format
    const extracted = Object.keys(params).filter((key: any) => 
      key !== 'extractionConfidence' && key !== 'extractedFrom' && params[key as keyof ExtractedParameters] !== undefined
    ).length;

    const sourceBreakdown: Record<string, number> = {};
    if (params.extractedFrom) {
      for (const source of params.extractedFrom) {
        const type = source.split(':')[0];
        sourceBreakdown[type] = (sourceBreakdown[type] || 0) + 1;
      }
    }

    return {
      totalParameters: totalPossible,
      extractedParameters: extracted,
      extractionRate: extracted / totalPossible,
      averageConfidence: params.extractionConfidence || 0,
      sourceBreakdown
    };
  }
}

/**
 * Parameter Extraction Factory
 */
export class ParameterExtractionFactory {
  private static instance: AdvancedParameterExtractor;

  /**
   * Get singleton parameter extractor instance
   */
  static getInstance(): AdvancedParameterExtractor {
    if (!this.instance) {
      this.instance = new AdvancedParameterExtractor();
    }
    return this.instance;
  }

  /**
   * Create parameter extractor with custom rules
   */
  static createCustomExtractor(customRules?: {
    quantityRules?: ParameterExtractionRule[];
    similarityRules?: ParameterExtractionRule[];
    detailLevelRules?: ParameterExtractionRule[];
    focusAreaRules?: ParameterExtractionRule[];
    tagRules?: ParameterExtractionRule[];
    matchModeRules?: ParameterExtractionRule[];
  }): AdvancedParameterExtractor {
    const extractor = new AdvancedParameterExtractor();
    
    // Custom rules would be applied here if needed
    // This is a placeholder for future extensibility
    
    return extractor;
  }

  /**
   * Test parameter extraction with sample texts
   */
  static async testParameterExtraction(texts: string[]): Promise<Array<{text: string; parameters: ExtractedParameters; stats: any}>> {
    const extractor = this.getInstance();
    const results = [];

    for (const text of texts) {
      const parameters = extractor.extractAllParameters(text);
      const stats = extractor.getExtractionStats(parameters);
      results.push({ text, parameters, stats });
    }

    return results;
  }
}