/**
 * Intelligent Response Formatting System
 * Provides conversational result presentation for image analysis and search results
 */

export interface ImageAnalysisResult {
  success: boolean;
  description: string;
  tags: string[];
  structuredData?: {
    objects: Array<{name: string; confidence: number; attributes: string[]}>;
    colors: string[];
    scene: string;
    mood: string;
    textContent?: string;
  };
  confidence: number;
  analysisTime?: number;
  model?: string;
  error?: string;
}

export interface ImageSearchResult {
  success: boolean;
  results: Array<{
    id: string;
    path: string;
    similarity?: number;
    tags?: string[];
    description?: string;
    metadata?: any;
  }>;
  total: number;
  queryInfo?: any;
  searchInfo?: any;
  tagStats?: any;
  error?: string;
}

export interface ToolExecutionContext {
  toolName: string;
  parameters: any;
  userQuery: string;
  hasImage: boolean;
  previousResults?: any[];
}

export class ResponseFormatter {
  
  /**
   * Format image analysis results in a conversational manner
   */
  static formatAnalysisResult(result: ImageAnalysisResult, context: ToolExecutionContext): string {
    if (!result.success) {
      return this.formatAnalysisError(result, context);
    }

    const parts: string[] = [];
    
    // Main description with confidence indicator
    const confidenceLevel = this.getConfidenceLevel(result.confidence);
    parts.push(`I can see that this image shows: **${result.description}**`);
    
    if (confidenceLevel !== 'high') {
      parts.push(`_(${confidenceLevel} confidence)_`);
    }

    // Key objects and elements
    if (result.structuredData?.objects && result.structuredData.objects.length > 0) {
      const topObjects = result.structuredData.objects
        .filter((obj: any) => obj.confidence > 0.5)
        .slice(0, 5)
        .map((obj: any) => {
          const attrs = obj.attributes.length > 0 ? ` (${obj.attributes.slice(0, 2).join(', ')})` : '';
          return `${obj.name}${attrs}`;
        });
      
      if (topObjects.length > 0) {
        parts.push(`\n🔍 **Key elements I identified:** ${topObjects.join(', ')}`);
      }
    }

    // Scene and mood
    if (result.structuredData?.scene || result.structuredData?.mood) {
      const sceneInfo: string[] = [];
      if (result.structuredData.scene) {
        sceneInfo.push(`**Scene:** ${result.structuredData.scene}`);
      }
      if (result.structuredData.mood) {
        sceneInfo.push(`**Mood:** ${result.structuredData.mood}`);
      }
      parts.push(`\n🎨 ${sceneInfo.join(' • ')}`);
    }

    // Colors
    if (result.structuredData?.colors && result.structuredData.colors.length > 0) {
      const colorList = result.structuredData.colors.slice(0, 5).join(', ');
      parts.push(`\n🌈 **Dominant colors:** ${colorList}`);
    }

    // Text content
    if (result.structuredData?.textContent) {
      parts.push(`\n📝 **Text found:** "${result.structuredData.textContent}"`);
    }

    // Tags
    if (result.tags && result.tags.length > 0) {
      const tagList = result.tags.slice(0, 8).join(', ');
      parts.push(`\n🏷️ **Tags:** ${tagList}`);
    }

    // Performance info
    if (result.analysisTime) {
      parts.push(`\n_Analysis completed in ${result.analysisTime}ms_`);
    }

    // Suggestions for next actions
    parts.push(this.generateAnalysisActionSuggestions(result, context));

    return parts.join('');
  }

  /**
   * Format image search results with natural language presentation
   */
  static formatSearchResult(result: ImageSearchResult, context: ToolExecutionContext): string {
    if (!result.success) {
      return this.formatSearchError(result, context);
    }

    if (result.total === 0) {
      return this.formatNoResultsFound(context);
    }

    const parts: string[] = [];
    
    // Main result summary
    const searchMethod = this.getSearchMethodDescription(context.toolName);
    parts.push(`🔍 **Found ${result.total} image${result.total === 1 ? '' : 's'}** using ${searchMethod}`);

    // Quality indicators
    if (result.results.length > 0) {
      const qualityInfo = this.analyzeResultQuality(result.results, context);
      if (qualityInfo) {
        parts.push(`\n${qualityInfo}`);
      }
    }

    // Result highlights
    if (result.results.length > 0) {
      const highlights = this.generateResultHighlights(result.results, context);
      if (highlights) {
        parts.push(`\n${highlights}`);
      }
    }

    // Search insights
    const insights = this.generateSearchInsights(result, context);
    if (insights) {
      parts.push(`\n${insights}`);
    }

    // Action suggestions
    parts.push(this.generateSearchActionSuggestions(result, context));

    return parts.join('');
  }

  /**
   * Format multiple tool results in a cohesive narrative
   */
  static formatMultipleResults(results: Array<{toolName: string; result: any}>, context: ToolExecutionContext): string {
    if (results.length === 0) {
      return "I wasn't able to process your request. Please try again.";
    }

    if (results.length === 1) {
      const {toolName, result} = results[0];
      const singleContext = {...context, toolName};
      
      if (toolName === 'analyze_image') {
        return this.formatAnalysisResult(result, singleContext);
      } else {
        return this.formatSearchResult(result, singleContext);
      }
    }

    // Multiple results - create a cohesive narrative
    const parts: string[] = [];
    let hasAnalysis = false;
    let hasSearch = false;

    for (const {toolName, result} of results) {
      if (toolName === 'analyze_image') {
        hasAnalysis = true;
        parts.push("## 🔍 Image Analysis");
        parts.push(this.formatAnalysisResult(result, {...context, toolName}));
      } else {
        hasSearch = true;
        parts.push(`## 🔎 Search Results`);
        parts.push(this.formatSearchResult(result, {...context, toolName}));
      }
    }

    // Add connecting narrative if both analysis and search were performed
    if (hasAnalysis && hasSearch) {
      parts.unshift("I've analyzed your image and searched for related content:");
    }

    return parts.join('\n\n');
  }

  /**
   * Generate suggestions for failed searches or no results
   */
  static generateFailureSuggestions(error: string, context: ToolExecutionContext): string {
    const suggestions: string[] = [];
    
    // Tool-specific suggestions
    switch (context.toolName) {
      case 'analyze_image':
        suggestions.push("💡 **Try these alternatives:**");
        suggestions.push("• Make sure the image is clear and well-lit");
        suggestions.push("• Try a different image format (JPEG, PNG, WebP)");
        suggestions.push("• Check if the image file size is reasonable");
        break;
        
      case 'find_similar_images_by_image':
        suggestions.push("💡 **Try these alternatives:**");
        suggestions.push("• Use a different reference image");
        suggestions.push("• Try describing what you're looking for instead");
        suggestions.push("• Search by tags if you know specific categories");
        break;
        
      case 'find_similar_images_by_description':
        suggestions.push("💡 **Try these alternatives:**");
        suggestions.push("• Use more specific or different keywords");
        suggestions.push("• Try broader terms if your search was too specific");
        suggestions.push("• Search by tags for category-based results");
        break;
        
      case 'find_images_by_tags':
        suggestions.push("💡 **Try these alternatives:**");
        suggestions.push("• Use different or more general tags");
        suggestions.push("• Try 'any' matching instead of 'all' for broader results");
        suggestions.push("• Search by description for more flexible matching");
        break;
    }

    // Error-specific suggestions
    if (error.includes('format')) {
      suggestions.push("• Ensure your image is in JPEG, PNG, or WebP format");
    }
    
    if (error.includes('size') || error.includes('large')) {
      suggestions.push("• Try reducing the image file size");
    }
    
    if (error.includes('network') || error.includes('connection')) {
      suggestions.push("• Check your internet connection and try again");
    }

    return suggestions.join('\n');
  }

  /**
   * Get confidence level description
   */
  private static getConfidenceLevel(confidence: number): string {
    if (confidence >= 0.8) return 'high';
    if (confidence >= 0.6) return 'good';
    if (confidence >= 0.4) return 'moderate';
    return 'low';
  }

  /**
   * Get search method description
   */
  private static getSearchMethodDescription(toolName: string): string {
    switch (toolName) {
      case 'find_similar_images_by_image':
        return 'visual similarity matching';
      case 'find_similar_images_by_description':
        return 'AI-powered description matching';
      case 'find_images_by_tags':
        return 'tag-based filtering';
      case 'get_image_analysis':
        return 'detailed image analysis';
      default:
        return 'image search';
    }
  }

  /**
   * Analyze result quality and provide insights
   */
  private static analyzeResultQuality(results: any[], context: ToolExecutionContext): string | null {
    if (results.length === 0) return null;

    const hasHighSimilarity = results.some((r: any) => r.similarity && r.similarity > 0.8);
    const hasModerateResults = results.some((r: any) => r.similarity && r.similarity > 0.5);
    const avgSimilarity = results
      .filter((r: any) => r.similarity)
      .reduce((sum, r) => sum + r.similarity, 0) / results.filter((r: any) => r.similarity).length;

    if (hasHighSimilarity) {
      return "✨ **Excellent matches found** - several images are very similar to your query";
    } else if (hasModerateResults) {
      return "👍 **Good matches found** - found several relevant images";
    } else if (avgSimilarity > 0.3) {
      return "📋 **Related images found** - results may be broadly related to your query";
    } else {
      return "🔍 **Exploratory results** - showing loosely related images that might interest you";
    }
  }

  /**
   * Generate highlights from search results
   */
  private static generateResultHighlights(results: any[], context: ToolExecutionContext): string | null {
    if (results.length === 0) return null;

    const highlights: string[] = [];
    
    // Top similarity scores
    const topResults = results
      .filter((r: any) => r.similarity)
      .sort((a, b) => b.similarity - a.similarity)
      .slice(0, 3);
    
    if (topResults.length > 0) {
      const topScore = Math.round(topResults[0].similarity * 100);
      highlights.push(`🎯 **Best match:** ${topScore}% similarity`);
    }

    // Common tags
    const allTags = results
      .flatMap(r => r.tags || [])
      .filter((tag: any) => tag);
    
    if (allTags.length > 0) {
      const tagCounts = allTags.reduce((acc, tag) => {
        acc[tag] = (acc[tag] || 0) + 1;
        return acc;
      }, {} as Record<string, number>);
      
      const commonTags = Object.entries(tagCounts)
        .sort(([,a], [,b]) => (b as number) - (a as number))
        .slice(0, 3)
        .map(([tag]) => tag);
      
      if (commonTags.length > 0) {
        highlights.push(`🏷️ **Common themes:** ${commonTags.join(', ')}`);
      }
    }

    return highlights.length > 0 ? highlights.join(' • ') : null;
  }

  /**
   * Generate search insights
   */
  private static generateSearchInsights(result: ImageSearchResult, context: ToolExecutionContext): string | null {
    const insights: string[] = [];

    // Parameter insights
    if (context.parameters?.threshold) {
      const threshold = context.parameters.threshold;
      if (threshold > 0.7) {
        insights.push("🎯 Used strict similarity matching for precise results");
      } else if (threshold < 0.4) {
        insights.push("🌐 Used broad similarity matching for diverse results");
      }
    }

    // Tag statistics
    if (result.tagStats && context.toolName === 'find_images_by_tags') {
      const matchedTags = Object.keys(result.tagStats).length;
      insights.push(`📊 Matched ${matchedTags} tag${matchedTags === 1 ? '' : 's'} in your search`);
    }

    // Search performance
    if (result.total > 100) {
      insights.push("📈 Large result set - showing most relevant matches first");
    }

    return insights.length > 0 ? insights.join(' • ') : null;
  }

  /**
   * Generate action suggestions for analysis results
   */
  private static generateAnalysisActionSuggestions(result: ImageAnalysisResult, context: ToolExecutionContext): string {
    const suggestions: string[] = [];
    
    suggestions.push("\n💡 **What would you like to do next?**");
    suggestions.push("• Find similar images to this one");
    
    if (result.tags && result.tags.length > 0) {
      const topTags = result.tags.slice(0, 3).join(', ');
      suggestions.push(`• Search for more images with tags: ${topTags}`);
    }
    
    if (result.structuredData?.objects && result.structuredData.objects.length > 0) {
      const topObject = result.structuredData.objects[0].name;
      suggestions.push(`• Find more images of ${topObject}`);
    }
    
    suggestions.push("• Ask me specific questions about what you see");

    return suggestions.join('\n');
  }

  /**
   * Generate action suggestions for search results
   */
  private static generateSearchActionSuggestions(result: ImageSearchResult, context: ToolExecutionContext): string {
    const suggestions: string[] = [];
    
    suggestions.push("\n💡 **What would you like to do next?**");
    
    if (result.total > result.results.length) {
      suggestions.push("• Ask for more results from this search");
    }
    
    suggestions.push("• Refine your search with different parameters");
    suggestions.push("• Analyze any of these images in detail");
    
    if (context.toolName !== 'find_similar_images_by_description') {
      suggestions.push("• Try describing what you're looking for in words");
    }
    
    if (context.toolName !== 'find_images_by_tags') {
      suggestions.push("• Search by specific tags or categories");
    }

    return suggestions.join('\n');
  }

  /**
   * Format analysis errors
   */
  private static formatAnalysisError(result: ImageAnalysisResult, context: ToolExecutionContext): string {
    const parts: string[] = [];
    
    parts.push("❌ **I couldn't analyze this image**");
    
    if (result.error) {
      parts.push(`\n**Issue:** ${result.error}`);
    }
    
    parts.push(`\n${this.generateFailureSuggestions(result.error || '', context)}`);
    
    return parts.join('');
  }

  /**
   * Format search errors
   */
  private static formatSearchError(result: ImageSearchResult, context: ToolExecutionContext): string {
    const parts: string[] = [];
    
    const searchMethod = this.getSearchMethodDescription(context.toolName);
    parts.push(`❌ **Search failed using ${searchMethod}**`);
    
    if (result.error) {
      parts.push(`\n**Issue:** ${result.error}`);
    }
    
    parts.push(`\n${this.generateFailureSuggestions(result.error || '', context)}`);
    
    return parts.join('');
  }

  /**
   * Format no results found scenario
   */
  static formatNoResultsFound(context: ToolExecutionContext): string {
    const parts: string[] = [];
    
    const searchMethod = this.getSearchMethodDescription(context.toolName);
    parts.push(`🔍 **No images found** using ${searchMethod}`);
    
    parts.push("\n💡 **Try these alternatives:**");
    
    switch (context.toolName) {
      case 'find_similar_images_by_image':
        parts.push("• Try a different reference image");
        parts.push("• Use a lower similarity threshold for broader results");
        parts.push("• Describe what you're looking for instead");
        break;
        
      case 'find_similar_images_by_description':
        parts.push("• Try different or more general keywords");
        parts.push("• Use simpler terms or broader descriptions");
        parts.push("• Search by specific tags instead");
        break;
        
      case 'find_images_by_tags':
        parts.push("• Try different or more common tags");
        parts.push("• Use 'any' matching instead of 'all' for broader results");
        parts.push("• Search by description for more flexible matching");
        break;
        
      default:
        parts.push("• Try different search terms or parameters");
        parts.push("• Use a broader search approach");
        break;
    }
    
    parts.push("• Ask me to help you refine your search strategy");
    
    return parts.join('\n');
  }
}