import { toolConfigManager, ToolConfig } from './toolConfiguration';

/**
 * Tool execution metrics
 */
export interface ToolExecutionMetrics {
  toolName: string;
  executionId: string;
  startTime: Date;
  endTime?: Date;
  duration?: number;
  success: boolean;
  parameters: any;
  result?: any;
  error?: string;
  // 内存使用情况记录已简化移除
  performanceMarks?: {
    validation: number;
    execution: number;
    formatting: number;
  };
}

/**
 * Tool performance statistics
 */
export interface ToolPerformanceStats {
  toolName: string;
  totalExecutions: number;
  successfulExecutions: number;
  failedExecutions: number;
  averageResponseTime: number;
  minResponseTime: number;
  maxResponseTime: number;
  errorRate: number;
  successRate: number;
  lastExecution?: Date;
  recentExecutions: ToolExecutionMetrics[];
  alertsTriggered: number;
  performanceTrend: 'improving' | 'stable' | 'degrading';
}

/**
 * Performance alert
 */
export interface PerformanceAlert {
  id: string;
  toolName: string;
  type: 'response_time' | 'error_rate' | 'success_rate';
  severity: 'low' | 'medium' | 'high' | 'critical';
  message: string;
  threshold: number;
  actualValue: number;
  timestamp: Date;
  resolved: boolean;
  resolvedAt?: Date;
}

/**
 * Log entry
 */
export interface ToolLogEntry {
  id: string;
  timestamp: Date;
  level: 'debug' | 'info' | 'warn' | 'error';
  toolName: string;
  message: string;
  data?: any;
  executionId?: string;
  duration?: number;
  error?: string;
}

/**
 * Tool monitoring and logging system
 */
export class ToolMonitoringSystem {
  private static instance: ToolMonitoringSystem | null = null;
  private metrics: Map<string, ToolExecutionMetrics[]> = new Map();
  private performanceStats: Map<string, ToolPerformanceStats> = new Map();
  private alerts: Map<string, PerformanceAlert[]> = new Map();
  private logs: ToolLogEntry[] = [];
  private activeExecutions: Map<string, ToolExecutionMetrics> = new Map();
  private alertIdCounter: number = 1;
  private logIdCounter: number = 1;
  private cleanupInterval: NodeJS.Timeout | null = null;

  private constructor() {
    this.startCleanupProcess();
  }

  /**
   * Get singleton instance
   */
  static getInstance(): ToolMonitoringSystem {
    if (!ToolMonitoringSystem.instance) {
      ToolMonitoringSystem.instance = new ToolMonitoringSystem();
    }
    return ToolMonitoringSystem.instance;
  }

  /**
   * Start tool execution monitoring
   */
  startExecution(toolName: string, parameters: any): string {
    const executionId = `${toolName}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    const metrics: ToolExecutionMetrics = {
      toolName,
      executionId,
      startTime: new Date(),
      success: false,
      parameters: this.sanitizeParameters(parameters),
      performanceMarks: {
        validation: 0,
        execution: 0,
        formatting: 0
      }
    };

    this.activeExecutions.set(executionId, metrics);
    this.log('debug', toolName, `Started execution: ${executionId}`, { parameters }, executionId);

    return executionId;
  }

  /**
   * Mark performance checkpoint
   */
  markPerformanceCheckpoint(executionId: string, checkpoint: 'validation' | 'execution' | 'formatting'): void {
    const metrics = this.activeExecutions.get(executionId);
    if (metrics && metrics.performanceMarks) {
      const elapsed = Date.now() - metrics.startTime.getTime();
      metrics.performanceMarks[checkpoint] = elapsed;
    }
  }

  /**
   * End tool execution monitoring
   */
  endExecution(executionId: string, success: boolean, result?: any, error?: string): void {
    const metrics = this.activeExecutions.get(executionId);
    if (!metrics) {
      console.warn(`No active execution found for ID: ${executionId}`);
      return;
    }

    // Complete metrics
    metrics.endTime = new Date();
    metrics.duration = metrics.endTime.getTime() - metrics.startTime.getTime();
    metrics.success = success;
    metrics.result = success ? this.sanitizeResult(result) : undefined;
    metrics.error = error;

    // Store metrics
    const toolMetrics = this.metrics.get(metrics.toolName) || [];
    toolMetrics.push(metrics);
    this.metrics.set(metrics.toolName, toolMetrics);

    // Update performance statistics
    this.updatePerformanceStats(metrics);

    // Check for performance alerts
    this.checkPerformanceAlerts(metrics);

    // Log completion
    const logLevel = success ? 'info' : 'error';
    const message = success 
      ? `Execution completed successfully in ${metrics.duration}ms`
      : `Execution failed: ${error}`;
    
    this.log(logLevel, metrics.toolName, message, { 
      duration: metrics.duration,
      success,
      error 
    }, executionId, metrics.duration);

    // Remove from active executions
    this.activeExecutions.delete(executionId);
  }

  /**
   * Update performance statistics
   */
  private updatePerformanceStats(metrics: ToolExecutionMetrics): void {
    const toolName = metrics.toolName;
    let stats = this.performanceStats.get(toolName);

    if (!stats) {
      stats = {
        toolName,
        totalExecutions: 0,
        successfulExecutions: 0,
        failedExecutions: 0,
        averageResponseTime: 0,
        minResponseTime: Infinity,
        maxResponseTime: 0,
        errorRate: 0,
        successRate: 0,
        recentExecutions: [],
        alertsTriggered: 0,
        performanceTrend: 'stable'
      };
    }

    // Update basic counters
    stats.totalExecutions++;
    if (metrics.success) {
      stats.successfulExecutions++;
    } else {
      stats.failedExecutions++;
    }

    // Update response time statistics
    if (metrics.duration !== undefined) {
      stats.minResponseTime = Math.min(stats.minResponseTime, metrics.duration);
      stats.maxResponseTime = Math.max(stats.maxResponseTime, metrics.duration);
      
      // Update average response time
      const totalTime = stats.averageResponseTime * (stats.totalExecutions - 1) + metrics.duration;
      stats.averageResponseTime = totalTime / stats.totalExecutions;
    }

    // Update rates
    stats.errorRate = (stats.failedExecutions / stats.totalExecutions) * 100;
    stats.successRate = (stats.successfulExecutions / stats.totalExecutions) * 100;
    stats.lastExecution = metrics.endTime;

    // Update recent executions (keep last 50)
    stats.recentExecutions.push(metrics);
    if (stats.recentExecutions.length > 50) {
      stats.recentExecutions = stats.recentExecutions.slice(-50);
    }

    // Calculate performance trend
    stats.performanceTrend = this.calculatePerformanceTrend(stats.recentExecutions);

    this.performanceStats.set(toolName, stats);
  }

  /**
   * Calculate performance trend
   */
  private calculatePerformanceTrend(recentExecutions: ToolExecutionMetrics[]): 'improving' | 'stable' | 'degrading' {
    if (recentExecutions.length < 10) {
      return 'stable';
    }

    const recent = recentExecutions.slice(-10);
    const older = recentExecutions.slice(-20, -10);

    if (older.length === 0) {
      return 'stable';
    }

    const recentAvgTime = recent.reduce((sum, m) => sum + (m.duration || 0), 0) / recent.length;
    const olderAvgTime = older.reduce((sum, m) => sum + (m.duration || 0), 0) / older.length;

    const recentSuccessRate = (recent.filter((m: any) => m.success).length / recent.length) * 100;
    const olderSuccessRate = (older.filter((m: any) => m.success).length / older.length) * 100;

    // Consider both response time and success rate
    const timeImprovement = (olderAvgTime - recentAvgTime) / olderAvgTime;
    const successImprovement = recentSuccessRate - olderSuccessRate;

    if (timeImprovement > 0.1 || successImprovement > 5) {
      return 'improving';
    } else if (timeImprovement < -0.1 || successImprovement < -5) {
      return 'degrading';
    } else {
      return 'stable';
    }
  }

  /**
   * Check for performance alerts
   */
  private checkPerformanceAlerts(metrics: ToolExecutionMetrics): void {
    const config = toolConfigManager.getToolConfig(metrics.toolName);
    const thresholds = config.performance.alertThresholds;

    if (!config.performance.monitoring) {
      return;
    }

    const stats = this.performanceStats.get(metrics.toolName);
    if (!stats) {
      return;
    }

    // Check response time alert
    if (metrics.duration && metrics.duration > thresholds.responseTime) {
      this.createAlert(
        metrics.toolName,
        'response_time',
        'high',
        `Response time exceeded threshold: ${metrics.duration}ms > ${thresholds.responseTime}ms`,
        thresholds.responseTime,
        metrics.duration
      );
    }

    // Check error rate alert
    if (stats.errorRate > thresholds.errorRate) {
      this.createAlert(
        metrics.toolName,
        'error_rate',
        stats.errorRate > thresholds.errorRate * 2 ? 'critical' : 'medium',
        `Error rate exceeded threshold: ${stats.errorRate.toFixed(1)}% > ${thresholds.errorRate}%`,
        thresholds.errorRate,
        stats.errorRate
      );
    }

    // Check success rate alert
    if (stats.successRate < thresholds.successRate) {
      this.createAlert(
        metrics.toolName,
        'success_rate',
        stats.successRate < thresholds.successRate * 0.8 ? 'critical' : 'medium',
        `Success rate below threshold: ${stats.successRate.toFixed(1)}% < ${thresholds.successRate}%`,
        thresholds.successRate,
        stats.successRate
      );
    }
  }

  /**
   * Create performance alert
   */
  private createAlert(
    toolName: string,
    type: PerformanceAlert['type'],
    severity: PerformanceAlert['severity'],
    message: string,
    threshold: number,
    actualValue: number
  ): void {
    const alert: PerformanceAlert = {
      id: `alert_${this.alertIdCounter++}`,
      toolName,
      type,
      severity,
      message,
      threshold,
      actualValue,
      timestamp: new Date(),
      resolved: false
    };

    const toolAlerts = this.alerts.get(toolName) || [];
    toolAlerts.push(alert);
    this.alerts.set(toolName, toolAlerts);

    // Update alert counter in stats
    const stats = this.performanceStats.get(toolName);
    if (stats) {
      stats.alertsTriggered++;
    }

    // Log the alert
    this.log('warn', toolName, `Performance alert: ${message}`, { alert });

    console.warn(`[PERFORMANCE ALERT] ${toolName}: ${message}`);
  }

  /**
   * Log message
   */
  log(
    level: 'debug' | 'info' | 'warn' | 'error',
    toolName: string,
    message: string,
    data?: any,
    executionId?: string,
    duration?: number
  ): void {
    const config = toolConfigManager.getToolConfig(toolName);
    
    if (!config.logging.enabled) {
      return;
    }

    // Check log level
    const logLevels = { debug: 0, info: 1, warn: 2, error: 3 };
    const configLevel = logLevels[config.logging.level];
    const messageLevel = logLevels[level];

    if (messageLevel < configLevel) {
      return;
    }

    const logEntry: ToolLogEntry = {
      id: `log_${this.logIdCounter++}`,
      timestamp: new Date(),
      level,
      toolName,
      message,
      data: config.logging.includeParameters ? data : undefined,
      executionId,
      duration
    };

    this.logs.push(logEntry);

    // Keep only recent logs (based on global config)
    const globalConfig = toolConfigManager.getGlobalConfig();
    const maxLogs = Math.floor(globalConfig.logging.maxLogSize / 1000); // Rough estimate
    if (this.logs.length > maxLogs) {
      this.logs = this.logs.slice(-maxLogs);
    }

    // Console output based on global config
    if (globalConfig.logging.destination === 'console' || globalConfig.logging.destination === 'both') {
      const timestamp = logEntry.timestamp.toISOString();
      const prefix = `[${timestamp}] [${level.toUpperCase()}] [${toolName}]`;
      
      switch (level) {
        case 'debug':
          console.debug(`${prefix} ${message}`, data || '');
          break;
        case 'info':
          console.info(`${prefix} ${message}`, data || '');
          break;
        case 'warn':
          console.warn(`${prefix} ${message}`, data || '');
          break;
        case 'error':
          console.error(`${prefix} ${message}`, data || '');
          break;
      }
    }
  }

  /**
   * Get tool performance statistics
   */
  getPerformanceStats(toolName?: string): ToolPerformanceStats[] {
    if (toolName) {
      const stats = this.performanceStats.get(toolName);
      return stats ? [stats] : [];
    }

    return Array.from(this.performanceStats.values());
  }

  /**
   * Get tool execution metrics
   */
  getExecutionMetrics(toolName?: string, limit?: number): ToolExecutionMetrics[] {
    if (toolName) {
      const metrics = this.metrics.get(toolName) || [];
      return limit ? metrics.slice(-limit) : metrics;
    }

    const allMetrics: ToolExecutionMetrics[] = [];
    this.metrics.forEach((metrics: any) => {
      allMetrics.push(...metrics);
    });

    // Sort by start time
    allMetrics.sort((a, b) => b.startTime.getTime() - a.startTime.getTime());
    
    return limit ? allMetrics.slice(0, limit) : allMetrics;
  }

  /**
   * Get performance alerts
   */
  getAlerts(toolName?: string, unresolved?: boolean): PerformanceAlert[] {
    let alerts: PerformanceAlert[] = [];

    if (toolName) {
      alerts = this.alerts.get(toolName) || [];
    } else {
      this.alerts.forEach((toolAlerts: any) => {
        alerts.push(...toolAlerts);
      });
    }

    if (unresolved) {
      alerts = alerts.filter((alert: any) => !alert.resolved);
    }

    // Sort by timestamp (newest first)
    return alerts.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());
  }

  /**
   * Resolve alert
   */
  resolveAlert(alertId: string): boolean {
    let alertFound = false;
    this.alerts.forEach((toolAlerts: any) => {
      if (!alertFound) {
        const alert = toolAlerts.find((a: any) => a.id === alertId);
        if (alert) {
          alert.resolved = true;
          alert.resolvedAt = new Date();
          this.log('info', alert.toolName, `Alert resolved: ${alert.message}`, { alertId });
          alertFound = true;
        }
      }
    });
    return alertFound;
  }

  /**
   * Get logs
   */
  getLogs(
    toolName?: string,
    level?: 'debug' | 'info' | 'warn' | 'error',
    limit?: number
  ): ToolLogEntry[] {
    let logs = [...this.logs];

    if (toolName) {
      logs = logs.filter((log: any) => log.toolName === toolName);
    }

    if (level) {
      logs = logs.filter((log: any) => log.level === level);
    }

    // Sort by timestamp (newest first)
    logs.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());

    return limit ? logs.slice(0, limit) : logs;
  }

  /**
   * Clear logs
   */
  clearLogs(toolName?: string): void {
    if (toolName) {
      this.logs = this.logs.filter((log: any) => log.toolName !== toolName);
    } else {
      this.logs = [];
    }
    
    console.log(`Logs cleared${toolName ? ` for tool: ${toolName}` : ''}`);
  }

  /**
   * Get system overview
   */
  getSystemOverview(): {
    totalExecutions: number;
    activeExecutions: number;
    totalAlerts: number;
    unresolvedAlerts: number;
    averageResponseTime: number;
    overallSuccessRate: number;
    toolsWithIssues: string[];
    performanceSummary: Record<string, {
      executions: number;
      successRate: number;
      avgResponseTime: number;
      trend: string;
    }>;
  } {
    const stats = Array.from(this.performanceStats.values());
    const totalExecutions = stats.reduce((sum, s) => sum + s.totalExecutions, 0);
    const totalSuccessful = stats.reduce((sum, s) => sum + s.successfulExecutions, 0);
    const totalResponseTime = stats.reduce((sum, s) => sum + (s.averageResponseTime * s.totalExecutions), 0);
    
    const allAlerts = this.getAlerts();
    const unresolvedAlerts = allAlerts.filter((a: any) => !a.resolved);
    
    const toolsWithIssues = stats
      .filter((s: any) => s.errorRate > 5 || s.alertsTriggered > 0)
      .map((s: any) => s.toolName);

    const performanceSummary: Record<string, any> = {};
    stats.forEach((s: any) => {
      performanceSummary[s.toolName] = {
        executions: s.totalExecutions,
        successRate: Math.round(s.successRate * 100) / 100,
        avgResponseTime: Math.round(s.averageResponseTime),
        trend: s.performanceTrend
      };
    });

    return {
      totalExecutions,
      activeExecutions: this.activeExecutions.size,
      totalAlerts: allAlerts.length,
      unresolvedAlerts: unresolvedAlerts.length,
      averageResponseTime: totalExecutions > 0 ? Math.round(totalResponseTime / totalExecutions) : 0,
      overallSuccessRate: totalExecutions > 0 ? Math.round((totalSuccessful / totalExecutions) * 10000) / 100 : 0,
      toolsWithIssues,
      performanceSummary
    };
  }

  /**
   * Start cleanup process
   */
  private startCleanupProcess(): void {
    // Clean up old data every hour
    this.cleanupInterval = setInterval(() => {
      this.cleanupOldData();
    }, 3600000); // 1 hour
  }

  /**
   * Clean up old data
   */
  private cleanupOldData(): void {
    const globalConfig = toolConfigManager.getGlobalConfig();
    const retentionTime = globalConfig.performance.metricsRetention;
    const cutoffTime = new Date(Date.now() - retentionTime);

    // Clean up old metrics
    this.metrics.forEach((metrics, toolName) => {
      const filteredMetrics = metrics.filter((m: any) => m.startTime > cutoffTime);
      this.metrics.set(toolName, filteredMetrics);
    });

    // Clean up old logs
    this.logs = this.logs.filter((log: any) => log.timestamp > cutoffTime);

    // Clean up resolved alerts older than 7 days
    const alertCutoffTime = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
    this.alerts.forEach((alerts, toolName) => {
      const filteredAlerts = alerts.filter((a: any) => 
        !a.resolved || (a.resolvedAt && a.resolvedAt > alertCutoffTime)
      );
      this.alerts.set(toolName, filteredAlerts);
    });

    console.log('Monitoring data cleanup completed');
  }

  /**
   * Sanitize parameters for logging
   */
  private sanitizeParameters(parameters: any): any {
    if (!parameters || typeof parameters !== 'object') {
      return parameters;
    }

    const sanitized = { ...parameters };
    
    // Remove or truncate large data
    if (sanitized.imageBase64) {
      sanitized.imageBase64 = `[IMAGE_DATA:${sanitized.imageBase64.length}_bytes]`;
    }

    return sanitized;
  }

  /**
   * Sanitize result for logging
   */
  private sanitizeResult(result: any): any {
    if (!result || typeof result !== 'object') {
      return result;
    }

    const sanitized = { ...result };
    
    // Remove large arrays or truncate them
    if (Array.isArray(sanitized.results) && sanitized.results.length > 10) {
      sanitized.results = `[ARRAY:${sanitized.results.length}_items]`;
    }

    return sanitized;
  }

  // 内存使用情况监控已简化移除

  /**
   * Cleanup resources
   */
  cleanup(): void {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
      this.cleanupInterval = null;
    }
    
    this.metrics.clear();
    this.performanceStats.clear();
    this.alerts.clear();
    this.logs = [];
    this.activeExecutions.clear();
    
    console.log('Tool monitoring system cleanup completed');
  }
}

/**
 * Global monitoring system instance
 */
export const toolMonitoring = ToolMonitoringSystem.getInstance();

/**
 * Convenience functions
 */
export function startToolExecution(toolName: string, parameters: any): string {
  return toolMonitoring.startExecution(toolName, parameters);
}

export function endToolExecution(executionId: string, success: boolean, result?: any, error?: string): void {
  toolMonitoring.endExecution(executionId, success, result, error);
}

export function logToolMessage(
  level: 'debug' | 'info' | 'warn' | 'error',
  toolName: string,
  message: string,
  data?: any
): void {
  toolMonitoring.log(level, toolName, message, data);
}

export function getToolStats(toolName?: string): ToolPerformanceStats[] {
  return toolMonitoring.getPerformanceStats(toolName);
}

export function getToolAlerts(toolName?: string, unresolved?: boolean): PerformanceAlert[] {
  return toolMonitoring.getAlerts(toolName, unresolved);
}