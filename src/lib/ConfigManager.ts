/**
 * 前端配置管理器
 * 统一管理应用的各种配置信息，包括环境变量、API配置、应用设置等
 */

export interface AIConfig {
  vlBaseUrl: string
  vlApiKey: string
  vlModel: string
  chatBaseUrl: string
  chatApiKey: string
  chatModel: string
  embeddingBaseUrl: string
  embeddingApiKey: string
  embeddingModel: string
  embeddingDimension: number
}

export interface AppConfig {
  version: string
  name: string
  description: string
  isDevelopment: boolean
}

export interface UIConfig {
  defaultTheme: 'light' | 'dark' | 'auto'
  enableAnimations: boolean
  compactMode: boolean
  language: 'zh-CN' | 'en-US'
}

export interface DatabaseConfig {
  enableVectorSearch: boolean
  cacheSize: number
  autoBackup: boolean
}

export interface DeveloperConfig {
  enableConsoleLog: boolean
  enablePerformanceMonitoring: boolean
  enableApiDebug: boolean
  enableExperimentalFeatures: boolean
}

class ConfigManager {
  private static instance: ConfigManager
  private _aiConfig: AIConfig
  private _appConfig: AppConfig
  private _uiConfig: UIConfig
  private _databaseConfig: DatabaseConfig
  private _developerConfig: DeveloperConfig

  private constructor() {
    this._aiConfig = this.loadAIConfig()
    this._appConfig = this.loadAppConfig()
    this._uiConfig = this.loadUIConfig()
    this._databaseConfig = this.loadDatabaseConfig()
    this._developerConfig = this.loadDeveloperConfig()
  }

  public static getInstance(): ConfigManager {
    if (!ConfigManager.instance) {
      ConfigManager.instance = new ConfigManager()
    }
    return ConfigManager.instance
  }

  // AI 配置
  private loadAIConfig(): AIConfig {
    return {
      vlBaseUrl: import.meta.env.VITE_AI_VL_BASE_URL || 'https://api.siliconflow.cn/v1',
      vlApiKey: import.meta.env.VITE_AI_VL_API_KEY || '',
      vlModel: import.meta.env.VITE_AI_VL_MODEL || 'Qwen/Qwen2.5-VL-32B-Instruct',
      chatBaseUrl: import.meta.env.VITE_AI_CHAT_BASE_URL || 'https://api.siliconflow.cn/v1',
      chatApiKey: import.meta.env.VITE_AI_CHAT_API_KEY || '',
      chatModel: import.meta.env.VITE_AI_CHAT_MODEL || 'Qwen/Qwen3-8B',
      embeddingBaseUrl: import.meta.env.VITE_AI_EMBEDDING_BASE_URL || 'https://api.siliconflow.cn/v1',
      embeddingApiKey: import.meta.env.VITE_AI_EMBEDDING_KEY || '',
      embeddingModel: 'BAAI/bge-large-zh-v1.5',
      embeddingDimension: 1024
    }
  }

  // 应用配置
  private loadAppConfig(): AppConfig {
    return {
      version: '1.0.0',
      name: 'PicMind',
      description: '智能图片管理应用',
      isDevelopment: import.meta.env.DEV
    }
  }

  // UI 配置
  private loadUIConfig(): UIConfig {
    const saved = localStorage.getItem('uiConfig')
    const defaults: UIConfig = {
      defaultTheme: 'auto',
      enableAnimations: true,
      compactMode: false,
      language: 'zh-CN'
    }
    
    if (saved) {
      try {
        return { ...defaults, ...JSON.parse(saved) }
      } catch {
        return defaults
      }
    }
    return defaults
  }

  // 数据库配置
  private loadDatabaseConfig(): DatabaseConfig {
    const saved = localStorage.getItem('databaseConfig')
    const defaults: DatabaseConfig = {
      enableVectorSearch: true,
      cacheSize: 100,
      autoBackup: true
    }
    
    if (saved) {
      try {
        return { ...defaults, ...JSON.parse(saved) }
      } catch {
        return defaults
      }
    }
    return defaults
  }

  // 开发者配置
  private loadDeveloperConfig(): DeveloperConfig {
    const saved = localStorage.getItem('developerConfig')
    const defaults: DeveloperConfig = {
      enableConsoleLog: true,
      enablePerformanceMonitoring: false,
      enableApiDebug: false,
      enableExperimentalFeatures: false
    }
    
    if (saved) {
      try {
        return { ...defaults, ...JSON.parse(saved) }
      } catch {
        return defaults
      }
    }
    return defaults
  }

  // Getters
  get aiConfig(): AIConfig {
    return { ...this._aiConfig }
  }

  get appConfig(): AppConfig {
    return { ...this._appConfig }
  }

  get uiConfig(): UIConfig {
    return { ...this._uiConfig }
  }

  get databaseConfig(): DatabaseConfig {
    return { ...this._databaseConfig }
  }

  get developerConfig(): DeveloperConfig {
    return { ...this._developerConfig }
  }

  // 更新配置方法
  updateUIConfig(config: Partial<UIConfig>): void {
    this._uiConfig = { ...this._uiConfig, ...config }
    localStorage.setItem('uiConfig', JSON.stringify(this._uiConfig))
    this.notifyConfigChange('ui', this._uiConfig)
  }

  updateDatabaseConfig(config: Partial<DatabaseConfig>): void {
    this._databaseConfig = { ...this._databaseConfig, ...config }
    localStorage.setItem('databaseConfig', JSON.stringify(this._databaseConfig))
    this.notifyConfigChange('database', this._databaseConfig)
  }

  updateDeveloperConfig(config: Partial<DeveloperConfig>): void {
    this._developerConfig = { ...this._developerConfig, ...config }
    localStorage.setItem('developerConfig', JSON.stringify(this._developerConfig))
    this.notifyConfigChange('developer', this._developerConfig)
  }

  // 配置变更通知
  private configChangeListeners: Array<(type: string, config: any) => void> = []

  onConfigChange(listener: (type: string, config: any) => void): () => void {
    this.configChangeListeners.push(listener)
    return () => {
      const index = this.configChangeListeners.indexOf(listener)
      if (index > -1) {
        this.configChangeListeners.splice(index, 1)
      }
    }
  }

  private notifyConfigChange(type: string, config: any): void {
    this.configChangeListeners.forEach(listener => {
      try {
        listener(type, config)
      } catch (error) {
        console.error('配置变更监听器执行失败:', error)
      }
    })
  }

  // 重置配置
  resetUIConfig(): void {
    localStorage.removeItem('uiConfig')
    this._uiConfig = this.loadUIConfig()
    this.notifyConfigChange('ui', this._uiConfig)
  }

  resetDatabaseConfig(): void {
    localStorage.removeItem('databaseConfig')
    this._databaseConfig = this.loadDatabaseConfig()
    this.notifyConfigChange('database', this._databaseConfig)
  }

  resetDeveloperConfig(): void {
    localStorage.removeItem('developerConfig')
    this._developerConfig = this.loadDeveloperConfig()
    this.notifyConfigChange('developer', this._developerConfig)
  }

  resetAllConfig(): void {
    this.resetUIConfig()
    this.resetDatabaseConfig()
    this.resetDeveloperConfig()
  }

  // 导出配置
  exportConfig(): string {
    return JSON.stringify({
      ui: this._uiConfig,
      database: this._databaseConfig,
      developer: this._developerConfig,
      exportTime: new Date().toISOString()
    }, null, 2)
  }

  // 导入配置
  importConfig(configJson: string): boolean {
    try {
      const config = JSON.parse(configJson)
      
      if (config.ui) {
        this.updateUIConfig(config.ui)
      }
      if (config.database) {
        this.updateDatabaseConfig(config.database)
      }
      if (config.developer) {
        this.updateDeveloperConfig(config.developer)
      }
      
      return true
    } catch (error) {
      console.error('导入配置失败:', error)
      return false
    }
  }

  // 获取配置摘要
  getConfigSummary(): Record<string, any> {
    return {
      app: {
        name: this._appConfig.name,
        version: this._appConfig.version,
        isDevelopment: this._appConfig.isDevelopment
      },
      ai: {
        vlBaseUrl: this._aiConfig.vlBaseUrl,
        chatBaseUrl: this._aiConfig.chatBaseUrl,
        vlModel: this._aiConfig.vlModel,
        chatModel: this._aiConfig.chatModel,
        embeddingModel: this._aiConfig.embeddingModel,
        hasVlApiKey: !!this._aiConfig.vlApiKey,
        hasChatApiKey: !!this._aiConfig.chatApiKey
      },
      ui: this._uiConfig,
      database: this._databaseConfig,
      developer: this._developerConfig
    }
  }

  // 验证配置
  validateConfig(): { isValid: boolean; errors: string[] } {
    const errors: string[] = []

    // 验证 AI 配置
    if (!this._aiConfig.vlBaseUrl) {
      errors.push('VL 服务地址未配置')
    }
    if (!this._aiConfig.vlApiKey) {
      errors.push('VL API 密钥未配置')
    }
    if (!this._aiConfig.chatBaseUrl) {
      errors.push('Chat 服务地址未配置')
    }
    if (!this._aiConfig.chatApiKey) {
      errors.push('Chat API 密钥未配置')
    }
    if (!this._aiConfig.vlModel) {
      errors.push('视觉模型未配置')
    }
    if (!this._aiConfig.chatModel) {
      errors.push('对话模型未配置')
    }

    return {
      isValid: errors.length === 0,
      errors
    }
  }
}

// 导出单例实例
export const configManager = ConfigManager.getInstance()

// 导出类型
export type { ConfigManager }

// 默认导出
export default configManager