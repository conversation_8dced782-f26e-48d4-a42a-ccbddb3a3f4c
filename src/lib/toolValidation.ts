import { ToolParameter, ToolParameters, ValidationResult } from '../types/tools';

/**
 * Validates tool parameters against their schema
 */
export class ToolParameterValidator {
  /**
   * Validates parameters against a tool's parameter schema
   */
  static validate(schema: ToolParameters, params: any): ValidationResult {
    const errors: string[] = [];
    
    if (!params || typeof params !== 'object') {
      return {
        valid: false,
        errors: ['Parameters must be an object']
      };
    }

    // Check required parameters
    if (schema.required) {
      for (const requiredParam of schema.required) {
        if (!(requiredParam in params)) {
          errors.push(`Required parameter '${requiredParam}' is missing`);
        }
      }
    }

    // Validate each parameter
    for (const [paramName, paramValue] of Object.entries(params)) {
      const paramSchema = schema.properties[paramName];
      if (!paramSchema) {
        errors.push(`Unknown parameter '${paramName}'`);
        continue;
      }

      const paramErrors = this.validateParameter(paramName, paramValue, paramSchema);
      errors.push(...paramErrors);
    }

    return {
      valid: errors.length === 0,
      errors
    };
  }

  /**
   * Validates a single parameter against its schema
   */
  private static validateParameter(name: string, value: any, schema: ToolParameter): string[] {
    const errors: string[] = [];

    // Check type
    if (!this.validateType(value, schema.type)) {
      errors.push(`Parameter '${name}' must be of type ${schema.type}`);
      return errors; // Return early if type is wrong
    }

    // Type-specific validations
    switch (schema.type) {
      case 'string':
        if (typeof value === 'string') {
          if (schema.minLength !== undefined && value.length < schema.minLength) {
            errors.push(`Parameter '${name}' must be at least ${schema.minLength} characters long`);
          }
          if (schema.maxLength !== undefined && value.length > schema.maxLength) {
            errors.push(`Parameter '${name}' must be at most ${schema.maxLength} characters long`);
          }
          if (schema.enum && !schema.enum.includes(value)) {
            errors.push(`Parameter '${name}' must be one of: ${schema.enum.join(', ')}`);
          }
        }
        break;

      case 'number':
        if (typeof value === 'number') {
          if (schema.minimum !== undefined && value < schema.minimum) {
            errors.push(`Parameter '${name}' must be at least ${schema.minimum}`);
          }
          if (schema.maximum !== undefined && value > schema.maximum) {
            errors.push(`Parameter '${name}' must be at most ${schema.maximum}`);
          }
        }
        break;

      case 'array':
        if (Array.isArray(value) && schema.items) {
          const itemSchema = schema.items; // Extract to ensure TypeScript knows it's not undefined
          value.forEach((item, index) => {
            const itemErrors = this.validateParameter(`${name}[${index}]`, item, itemSchema);
            errors.push(...itemErrors);
          });
        }
        break;

      case 'object':
        if (typeof value === 'object' && value !== null && schema.properties) {
          for (const [propName, propValue] of Object.entries(value)) {
            const propSchema = schema.properties[propName];
            if (propSchema) {
              const propErrors = this.validateParameter(`${name}.${propName}`, propValue, propSchema);
              errors.push(...propErrors);
            }
          }
        }
        break;
    }

    return errors;
  }

  /**
   * Validates the basic type of a value
   */
  private static validateType(value: any, expectedType: string): boolean {
    switch (expectedType) {
      case 'string':
        return typeof value === 'string';
      case 'number':
        return typeof value === 'number' && !isNaN(value);
      case 'boolean':
        return typeof value === 'boolean';
      case 'array':
        return Array.isArray(value);
      case 'object':
        return typeof value === 'object' && value !== null && !Array.isArray(value);
      default:
        return false;
    }
  }
}