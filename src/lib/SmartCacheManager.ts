/**
 * 智能缓存管理器
 * 
 * 提供基于文件修改时间和访问频率的智能缓存策略：
 * - 基于LRU（最近最少使用）和访问频率的混合策略
 * - 支持文件修改时间验证，确保缓存一致性
 * - 自动过期和清理机制
 * - 缓存统计和性能监控
 */

import { logger } from './LoggerService'

// 缓存条目接口
export interface CacheEntry<T> {
  data: T
  size: number
  timestamp: number
  modifiedTime?: number
  accessCount: number
  lastAccessed: number
  metadata?: Record<string, any>
}

// 缓存配置
export interface SmartCacheConfig {
  maxSize: number        // 最大缓存大小（字节）
  maxEntries: number     // 最大条目数
  ttl: number           // 生存时间（毫秒）
  cleanupInterval: number // 清理间隔（毫秒）
  priorityFunction?: (entry: CacheEntry<any>) => number // 优先级计算函数
}

// 缓存统计
export interface CacheStats {
  size: number          // 当前大小（字节）
  count: number         // 当前条目数
  hitRate: number       // 命中率（0-1）
  hits: number          // 命中次数
  misses: number        // 未命中次数
  evictions: number     // 驱逐次数
  expirations: number   // 过期次数
}

/**
 * 智能缓存管理器类
 */
export class SmartCacheManager<T> {
  private cache = new Map<string, CacheEntry<T>>()
  private config: SmartCacheConfig
  private stats = {
    hits: 0,
    misses: 0,
    evictions: 0,
    expirations: 0
  }
  private totalSize = 0
  private cleanupTimer?: NodeJS.Timeout

  constructor(config?: Partial<SmartCacheConfig>) {
    // 默认配置
    this.config = {
      maxSize: 100 * 1024 * 1024, // 100MB
      maxEntries: 1000,
      ttl: 30 * 60 * 1000,        // 30分钟
      cleanupInterval: 5 * 60 * 1000, // 5分钟
      priorityFunction: this.defaultPriorityFunction,
      ...config
    }

    // 启动定期清理
    this.startCleanup()
  }

  /**
   * 获取缓存项
   * @param key 缓存键
   * @param modifiedTime 可选的文件修改时间，用于验证缓存是否过期
   */
  async get(key: string, modifiedTime?: number): Promise<T | null> {
    const entry = this.cache.get(key)
    
    if (!entry) {
      this.stats.misses++
      return null
    }

    // 检查是否过期
    const now = Date.now()
    if (now - entry.timestamp > this.config.ttl) {
      this.cache.delete(key)
      this.totalSize -= entry.size
      this.stats.expirations++
      return null
    }

    // 检查文件是否已修改（如果提供了修改时间）
    if (modifiedTime !== undefined && 
        entry.modifiedTime !== undefined && 
        modifiedTime > entry.modifiedTime) {
      this.cache.delete(key)
      this.totalSize -= entry.size
      this.stats.expirations++
      logger.debug(`缓存过期（文件已修改）: ${key}`, 'SmartCacheManager')
      return null
    }

    // 更新访问统计
    entry.accessCount++
    entry.lastAccessed = now
    this.stats.hits++
    
    return entry.data
  }

  /**
   * 设置缓存项
   * @param key 缓存键
   * @param data 缓存数据
   * @param size 数据大小（字节）
   * @param modifiedTime 可选的文件修改时间
   * @param metadata 可选的元数据
   */
  async set(
    key: string, 
    data: T, 
    size: number,
    modifiedTime?: number,
    metadata?: Record<string, any>
  ): Promise<void> {
    // 检查单个项是否超过最大缓存大小
    if (size > this.config.maxSize) {
      logger.warn(`缓存项过大，无法缓存: ${key}`, 'SmartCacheManager', {
        size,
        maxSize: this.config.maxSize
      })
      return
    }

    // 确保有足够的空间
    await this.ensureSpace(size)

    const now = Date.now()
    const entry: CacheEntry<T> = {
      data,
      size,
      timestamp: now,
      modifiedTime,
      accessCount: 1,
      lastAccessed: now,
      metadata
    }

    // 如果键已存在，先移除旧条目
    const existingEntry = this.cache.get(key)
    if (existingEntry) {
      this.totalSize -= existingEntry.size
    }

    // 添加新条目
    this.cache.set(key, entry)
    this.totalSize += size
  }

  /**
   * 检查键是否存在
   */
  has(key: string): boolean {
    return this.cache.has(key)
  }

  /**
   * 删除缓存项
   */
  delete(key: string): boolean {
    const entry = this.cache.get(key)
    if (entry) {
      this.cache.delete(key)
      this.totalSize -= entry.size
      return true
    }
    return false
  }

  /**
   * 清空缓存
   */
  clear(): void {
    this.cache.clear()
    this.totalSize = 0
  }

  /**
   * 获取缓存统计
   */
  getStats(): CacheStats {
    const totalRequests = this.stats.hits + this.stats.misses
    const hitRate = totalRequests > 0 ? this.stats.hits / totalRequests : 0

    return {
      size: this.totalSize,
      count: this.cache.size,
      hitRate,
      hits: this.stats.hits,
      misses: this.stats.misses,
      evictions: this.stats.evictions,
      expirations: this.stats.expirations
    }
  }

  /**
   * 获取所有缓存键
   */
  getKeys(): string[] {
    return Array.from(this.cache.keys())
  }

  /**
   * 获取缓存条目（包括元数据）
   */
  getEntry(key: string): CacheEntry<T> | null {
    return this.cache.get(key) || null
  }

  /**
   * 更新配置
   */
  updateConfig(config: Partial<SmartCacheConfig>): void {
    this.config = { ...this.config, ...config }
  }

  /**
   * 销毁缓存管理器
   */
  destroy(): void {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer)
    }
    this.clear()
  }

  /**
   * 确保有足够的缓存空间
   */
  private async ensureSpace(newSize: number): Promise<void> {
    // 检查是否需要清理空间
    if (this.totalSize + newSize <= this.config.maxSize && 
        this.cache.size < this.config.maxEntries) {
      return
    }

    // 计算需要释放的空间
    const sizeToFree = Math.max(
      0,
      (this.totalSize + newSize) - this.config.maxSize + 1024 * 1024 // 额外释放1MB
    )

    // 如果缓存项太多，也需要清理
    const needToFreeEntries = Math.max(
      0,
      this.cache.size + 1 - this.config.maxEntries
    )

    if (sizeToFree <= 0 && needToFreeEntries <= 0) {
      return
    }

    // 收集所有条目并计算优先级
    const entries = Array.from(this.cache.entries()).map(([key, entry]) => ({
      key,
      entry,
      priority: this.config.priorityFunction!(entry)
    }))

    // 按优先级排序（升序，优先级低的先删除）
    entries.sort((a, b) => a.priority - b.priority)

    // 删除条目直到释放足够空间
    let freedSize = 0
    let freedEntries = 0
    const removedKeys: string[] = []

    for (const { key, entry } of entries) {
      if (freedSize >= sizeToFree && freedEntries >= needToFreeEntries) {
        break
      }

      this.cache.delete(key)
      freedSize += entry.size
      freedEntries++
      this.stats.evictions++
      removedKeys.push(key)
    }

    // 更新总大小
    this.totalSize -= freedSize

    if (removedKeys.length > 0) {
      logger.debug(`缓存驱逐: 删除了 ${removedKeys.length} 个条目，释放了 ${freedSize} 字节`, 
        'SmartCacheManager')
    }
  }

  /**
   * 默认优先级计算函数
   * 结合访问频率、最近访问时间和缓存时间计算优先级
   */
  private defaultPriorityFunction(entry: CacheEntry<any>): number {
    const now = Date.now()
    
    // 计算每天的访问频率
    const ageInDays = Math.max(1, (now - entry.timestamp) / (24 * 60 * 60 * 1000))
    const accessFrequency = entry.accessCount / ageInDays
    
    // 最近访问时间（小时）
    const recency = (now - entry.lastAccessed) / (60 * 60 * 1000)
    
    // 优先级计算：访问频率越高、越近期访问过，优先级越高（不易被删除）
    // 返回负值，因为排序是按优先级升序（低优先级先删除）
    return -(accessFrequency * 10 - recency * 0.5)
  }

  /**
   * 启动定期清理
   */
  private startCleanup(): void {
    this.cleanupTimer = setInterval(() => {
      this.cleanupExpired()
    }, this.config.cleanupInterval)
  }

  /**
   * 清理过期条目
   */
  private cleanupExpired(): void {
    const now = Date.now()
    const expiredKeys: string[] = []
    
    for (const [key, entry] of this.cache.entries()) {
      if (now - entry.timestamp > this.config.ttl) {
        expiredKeys.push(key)
      }
    }
    
    if (expiredKeys.length > 0) {
      let freedSize = 0
      
      for (const key of expiredKeys) {
        const entry = this.cache.get(key)
        if (entry) {
          freedSize += entry.size
          this.cache.delete(key)
          this.stats.expirations++
        }
      }
      
      this.totalSize -= freedSize
      
      logger.debug(`缓存清理: 删除了 ${expiredKeys.length} 个过期条目，释放了 ${freedSize} 字节`, 
        'SmartCacheManager')
    }
  }
}

// 导出类型