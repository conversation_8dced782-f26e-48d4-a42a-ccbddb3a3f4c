import { trpcClient } from './trpcClient'

/**
 * 图片库接口
 */
export interface ImageLibrary {
  id: string
  name: string
  rootPath: string
  type: 'local' | 'cloud' | 'network'
  status: 'active' | 'offline' | 'removed'
  description?: string
  createdAt: string
  updatedAt: string
  // 统计信息
  totalImages?: number
  totalSize?: number
  imagesByFormat?: Record<string, number>
  averageFileSize?: number
  newestImageDate?: string
  oldestImageDate?: string
  // 扫描进度
  scanProgress?: {
    total: number
    processed: number
    failed: number
    status: string
    lastScannedPath?: string
    estimatedTimeRemaining?: number
  }
}

/**
 * 图片库查询参数
 */
export interface LibraryQueryParams {
  name?: string
  status?: 'active' | 'offline' | 'removed'
  type?: 'local' | 'cloud' | 'network'
  limit?: number
  offset?: number
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
}

/**
 * 图片库创建参数
 */
export interface CreateLibraryParams {
  name: string
  rootPath: string
  type?: 'local' | 'cloud' | 'network'
  description?: string
  settings?: {
    recursive?: boolean
    includeHidden?: boolean
    maxDepth?: number
    supportedFormats?: string[]
    excludePatterns?: string[]
    autoScanInterval?: number
  }
}

/**
 * 图片库更新参数
 */
export interface UpdateLibraryParams {
  name?: string
  rootPath?: string
  type?: 'local' | 'cloud' | 'network'
  status?: 'active' | 'offline' | 'removed'
  description?: string
  settings?: string
  updatedAt?: string
}

/**
 * 图片库服务类 - 使用 tRPC client
 */
export class ImageLibraryService {
  private static instance: ImageLibraryService | null = null

  private constructor() {
    // 私有构造函数，确保单例
  }

  /**
   * 获取图片库服务实例
   */
  static getInstance(): ImageLibraryService {
    if (!this.instance) {
      this.instance = new ImageLibraryService()
    }
    return this.instance
  }

  /**
   * 获取所有图片库
   */
  async getAllLibraries(): Promise<{ success: boolean; libraries: ImageLibrary[]; error?: string }> {
    try {
      console.log('ImageLibraryService: 获取所有图片库')
      const result = await trpcClient.library.getAllLibraries.query()
      
      if (result.success && result.libraries) {
        console.log(`ImageLibraryService: 成功获取 ${result.libraries.length} 个图片库`)
        return {
          success: true,
          libraries: result.libraries.map(this.transformLibraryData)
        }
      } else {
        console.error('ImageLibraryService: 获取图片库失败:', result.error)
        return {
          success: false,
          libraries: [],
          error: result.error
        }
      }
    } catch (error) {
      console.error('ImageLibraryService: 获取图片库异常:', error)
      return {
        success: false,
        libraries: [],
        error: error instanceof Error ? error.message : String(error)
      }
    }
  }

  /**
   * 获取单个图片库
   */
  async getLibrary(id: string): Promise<{ success: boolean; library: ImageLibrary | null; error?: string }> {
    try {
      console.log(`ImageLibraryService: 获取图片库 ${id}`)
      const result = await trpcClient.library.getLibrary.query(id)
      
      if (result.success && result.library) {
        return {
          success: true,
          library: this.transformLibraryData(result.library)
        }
      } else {
        return {
          success: false,
          library: null,
          error: result.error
        }
      }
    } catch (error) {
      console.error('ImageLibraryService: 获取图片库异常:', error)
      return {
        success: false,
        library: null,
        error: error instanceof Error ? error.message : String(error)
      }
    }
  }

  /**
   * 创建图片库
   */
  async createLibrary(params: CreateLibraryParams): Promise<{ success: boolean; libraryId: string | null; error?: string }> {
    try {
      console.log('ImageLibraryService: 创建图片库:', params)
      const result = await trpcClient.library.createLibrary.mutate({
        name: params.name,
        rootPath: params.rootPath,
        type: params.type || 'local',
        description: params.description,
        settings: params.settings
      })
      
      if (result.success && result.libraryId) {
        console.log(`ImageLibraryService: 成功创建图片库 ${result.libraryId}`)
        return {
          success: true,
          libraryId: result.libraryId
        }
      } else {
        console.error('ImageLibraryService: 创建图片库失败:', result.error)
        return {
          success: false,
          libraryId: null,
          error: result.error
        }
      }
    } catch (error) {
      console.error('ImageLibraryService: 创建图片库异常:', error)
      return {
        success: false,
        libraryId: null,
        error: error instanceof Error ? error.message : String(error)
      }
    }
  }

  /**
   * 更新图片库
   */
  async updateLibrary(id: string, updates: UpdateLibraryParams): Promise<{ success: boolean; error?: string }> {
    try {
      console.log(`ImageLibraryService: 更新图片库 ${id}:`, updates)
      const result = await trpcClient.library.updateLibrary.mutate({
        id,
        updates
      })
      
      if (result.success) {
        console.log(`ImageLibraryService: 成功更新图片库 ${id}`)
        return { success: true }
      } else {
        console.error('ImageLibraryService: 更新图片库失败:', result.error)
        return {
          success: false,
          error: result.error
        }
      }
    } catch (error) {
      console.error('ImageLibraryService: 更新图片库异常:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error)
      }
    }
  }

  /**
   * 删除图片库
   */
  async deleteLibrary(id: string): Promise<{ success: boolean; error?: string }> {
    try {
      console.log(`ImageLibraryService: 删除图片库 ${id}`)
      const result = await trpcClient.library.deleteLibrary.mutate(id)
      
      if (result.success) {
        console.log(`ImageLibraryService: 成功删除图片库 ${id}`)
        return { success: true }
      } else {
        console.error('ImageLibraryService: 删除图片库失败:', result.error)
        return {
          success: false,
          error: result.error
        }
      }
    } catch (error) {
      console.error('ImageLibraryService: 删除图片库异常:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error)
      }
    }
  }

  /**
   * 开始扫描图片库
   */
  async startScan(id: string): Promise<{ success: boolean; error?: string }> {
    try {
      console.log(`ImageLibraryService: 开始扫描图片库 ${id}`)
      const result = await trpcClient.library.startScan.mutate(id)
      
      if (result.success) {
        console.log(`ImageLibraryService: 成功开始扫描图片库 ${id}`)
        return { success: true }
      } else {
        console.error('ImageLibraryService: 开始扫描失败:', result.error)
        return {
          success: false,
          error: result.error
        }
      }
    } catch (error) {
      console.error('ImageLibraryService: 开始扫描异常:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error)
      }
    }
  }

  /**
   * 查询图片库
   */
  async queryLibraries(params: LibraryQueryParams = {}): Promise<{ 
    success: boolean; 
    libraries: ImageLibrary[]; 
    total: number; 
    error?: string 
  }> {
    try {
      console.log('ImageLibraryService: 查询图片库:', params)
      const result = await trpcClient.library.queryLibraries.query(params)
      
      if (result.success && result.libraries) {
        return {
          success: true,
          libraries: result.libraries.map(this.transformLibraryData),
          total: result.total || result.libraries.length
        }
      } else {
        return {
          success: false,
          libraries: [],
          total: 0,
          error: result.error
        }
      }
    } catch (error) {
      console.error('ImageLibraryService: 查询图片库异常:', error)
      return {
        success: false,
        libraries: [],
        total: 0,
        error: error instanceof Error ? error.message : String(error)
      }
    }
  }

  /**
   * 获取活跃的图片库
   */
  async getActiveLibraries(): Promise<{ success: boolean; libraries: ImageLibrary[]; error?: string }> {
    try {
      console.log('ImageLibraryService: 获取活跃图片库')
      const result = await trpcClient.library.getActiveLibraries.query()
      
      if (result.success && result.libraries) {
        return {
          success: true,
          libraries: result.libraries.map(this.transformLibraryData)
        }
      } else {
        return {
          success: false,
          libraries: [],
          error: result.error
        }
      }
    } catch (error) {
      console.error('ImageLibraryService: 获取活跃图片库异常:', error)
      return {
        success: false,
        libraries: [],
        error: error instanceof Error ? error.message : String(error)
      }
    }
  }

  /**
   * 验证图片库路径
   */
  async validateLibraryPath(rootPath: string): Promise<{ 
    success: boolean; 
    valid: boolean; 
    exists: boolean; 
    accessible: boolean; 
    error?: string 
  }> {
    try {
      console.log(`ImageLibraryService: 验证图片库路径 ${rootPath}`)
      const result = await trpcClient.library.validateLibraryPath.query(rootPath)
      
      return {
        success: result.success,
        valid: result.valid || false,
        exists: result.exists || false,
        accessible: result.accessible || false,
        error: result.error
      }
    } catch (error) {
      console.error('ImageLibraryService: 验证路径异常:', error)
      return {
        success: false,
        valid: false,
        exists: false,
        accessible: false,
        error: error instanceof Error ? error.message : String(error)
      }
    }
  }

  /**
   * 获取统计摘要
   */
  async getStatisticsSummary(): Promise<{ 
    success: boolean; 
    summary: any; 
    error?: string 
  }> {
    try {
      console.log('ImageLibraryService: 获取统计摘要')
      const result = await trpcClient.library.getStatisticsSummary.query()
      
      return {
        success: result.success,
        summary: result.summary,
        error: result.error
      }
    } catch (error) {
      console.error('ImageLibraryService: 获取统计摘要异常:', error)
      return {
        success: false,
        summary: null,
        error: error instanceof Error ? error.message : String(error)
      }
    }
  }

  /**
   * 测试连接
   */
  async testConnection(): Promise<{ success: boolean; connected: boolean; error?: string }> {
    try {
      console.log('ImageLibraryService: 测试连接')
      const result = await trpcClient.library.testConnection.query()
      
      return {
        success: result.success,
        connected: result.connected || false,
        error: result.error
      }
    } catch (error) {
      console.error('ImageLibraryService: 测试连接异常:', error)
      return {
        success: false,
        connected: false,
        error: error instanceof Error ? error.message : String(error)
      }
    }
  }

  /**
   * 转换库数据格式
   */
  private transformLibraryData(library: any): ImageLibrary {
    return {
      id: library.id,
      name: library.name,
      rootPath: library.rootPath || library.path, // 兼容不同的字段名
      type: library.type || 'local',
      status: library.status || 'active',
      description: library.description,
      createdAt: library.createdAt,
      updatedAt: library.updatedAt,
      totalImages: library.totalImages || 0,
      totalSize: library.totalSize || 0,
      imagesByFormat: library.imagesByFormat,
      averageFileSize: library.averageFileSize,
      newestImageDate: library.newestImageDate,
      oldestImageDate: library.oldestImageDate,
      scanProgress: library.scanProgress
    }
  }
}

// 导出单例实例
export const imageLibraryService = ImageLibraryService.getInstance()