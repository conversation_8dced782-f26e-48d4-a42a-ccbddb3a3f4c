import { ToolDefinition } from '../../electron/ai/VLChatService';
import { VLImageToolsBridge } from './vlImageToolsBridge';

/**
 * Tool discovery configuration
 */
export interface ToolDiscoveryConfig {
  toolsDirectory?: string;
  enableValidation?: boolean;
  enableCompatibilityCheck?: boolean;
  autoRegister?: boolean;
  retryAttempts?: number;
  retryDelay?: number;
}

/**
 * Tool discovery result
 */
export interface ToolDiscoveryResult {
  discovered: ToolDefinition[];
  validated: ToolDefinition[];
  compatible: ToolDefinition[];
  registered: ToolDefinition[];
  errors: Array<{
    toolName: string;
    stage: 'discovery' | 'validation' | 'compatibility' | 'registration';
    error: string;
  }>;
  discoveryTime: number;
}

/**
 * Tool compatibility check result
 */
export interface ToolCompatibilityResult {
  compatible: boolean;
  issues: string[];
  warnings: string[];
}

/**
 * Tool discovery and registration system
 * Automatically discovers available image tools and registers them with VL Chat Service
 */
export class ToolDiscovery {
  private config: Required<ToolDiscoveryConfig>;
  private discoveredTools: Map<string, ToolDefinition> = new Map();
  private validatedTools: Map<string, ToolDefinition> = new Map();
  private compatibleTools: Map<string, ToolDefinition> = new Map();

  constructor(config: ToolDiscoveryConfig = {}) {
    this.config = {
      toolsDirectory: config.toolsDirectory || 'tools',
      enableValidation: config.enableValidation !== false,
      enableCompatibilityCheck: config.enableCompatibilityCheck !== false,
      autoRegister: config.autoRegister !== false,
      retryAttempts: config.retryAttempts || 3,
      retryDelay: config.retryDelay || 1000
    };
  }

  /**
   * Discover all available image tools
   */
  async discoverTools(): Promise<ToolDiscoveryResult> {
    const startTime = Date.now();
    const result: ToolDiscoveryResult = {
      discovered: [],
      validated: [],
      compatible: [],
      registered: [],
      errors: [],
      discoveryTime: 0
    };

    try {
      console.log('Starting tool discovery process...');

      // Step 1: Discover tools from VLImageToolsBridge
      await this.discoverFromBridge(result);

      // Step 2: Validate discovered tools
      if (this.config.enableValidation) {
        await this.validateDiscoveredTools(result);
      } else {
        result.validated = [...result.discovered];
        this.validatedTools = new Map(this.discoveredTools);
      }

      // Step 3: Check compatibility
      if (this.config.enableCompatibilityCheck) {
        await this.checkToolCompatibility(result);
      } else {
        result.compatible = [...result.validated];
        this.compatibleTools = new Map(this.validatedTools);
      }

      // Step 4: Auto-register if enabled
      if (this.config.autoRegister) {
        await this.registerCompatibleTools(result);
      } else {
        result.registered = [...result.compatible];
      }

      result.discoveryTime = Date.now() - startTime;
      this.logDiscoveryResults(result);

      return result;

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      console.error('Tool discovery failed:', error);
      
      result.errors.push({
        toolName: 'system',
        stage: 'discovery',
        error: errorMessage
      });
      
      result.discoveryTime = Date.now() - startTime;
      return result;
    }
  }

  /**
   * Discover tools from VLImageToolsBridge
   */
  private async discoverFromBridge(result: ToolDiscoveryResult): Promise<void> {
    try {
      console.log('Discovering tools from VLImageToolsBridge...');
      
      // Get tool definitions from the bridge
      const toolDefinitions = VLImageToolsBridge.getToolDefinitions();
      
      for (const toolDef of toolDefinitions) {
        try {
          this.discoveredTools.set(toolDef.function.name, toolDef);
          result.discovered.push(toolDef);
          console.log(`✓ Discovered tool: ${toolDef.function.name}`);
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : String(error);
          result.errors.push({
            toolName: toolDef.function?.name || 'unknown',
            stage: 'discovery',
            error: errorMessage
          });
          console.error(`✗ Failed to discover tool ${toolDef.function?.name}:`, error);
        }
      }

      console.log(`Discovered ${result.discovered.length} tools from VLImageToolsBridge`);

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      result.errors.push({
        toolName: 'VLImageToolsBridge',
        stage: 'discovery',
        error: errorMessage
      });
      console.error('Failed to discover tools from VLImageToolsBridge:', error);
    }
  }

  /**
   * Validate discovered tools
   */
  private async validateDiscoveredTools(result: ToolDiscoveryResult): Promise<void> {
    console.log('Validating discovered tools...');

    this.discoveredTools.forEach((toolDef, toolName) => {
      try {
        const validation = this.validateToolDefinition(toolDef);
        
        if (validation.valid) {
          this.validatedTools.set(toolName, toolDef);
          result.validated.push(toolDef);
          console.log(`✓ Validated tool: ${toolName}`);
        } else {
          result.errors.push({
            toolName,
            stage: 'validation',
            error: validation.error || 'Validation failed'
          });
          console.error(`✗ Tool validation failed for ${toolName}:`, validation.error);
        }
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        result.errors.push({
          toolName,
          stage: 'validation',
          error: errorMessage
        });
        console.error(`✗ Tool validation error for ${toolName}:`, error);
      }
    });

    console.log(`Validated ${result.validated.length} tools`);
  }

  /**
   * Check tool compatibility with VL Chat Service
   */
  private async checkToolCompatibility(result: ToolDiscoveryResult): Promise<void> {
    console.log('Checking tool compatibility...');

    const compatibilityPromises: Promise<void>[] = [];
    
    this.validatedTools.forEach((toolDef, toolName) => {
      const compatibilityPromise = (async () => {
        try {
          const compatibility = await this.checkToolCompatibilityInternal(toolDef);
          
          if (compatibility.compatible) {
            this.compatibleTools.set(toolName, toolDef);
            result.compatible.push(toolDef);
            console.log(`✓ Tool compatible: ${toolName}`);
            
            if (compatibility.warnings.length > 0) {
              console.warn(`⚠ Compatibility warnings for ${toolName}:`, compatibility.warnings);
            }
          } else {
            result.errors.push({
              toolName,
              stage: 'compatibility',
              error: `Compatibility issues: ${compatibility.issues.join(', ')}`
            });
            console.error(`✗ Tool compatibility failed for ${toolName}:`, compatibility.issues);
          }
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : String(error);
          result.errors.push({
            toolName,
            stage: 'compatibility',
            error: errorMessage
          });
          console.error(`✗ Tool compatibility check error for ${toolName}:`, error);
        }
      })();
      
      compatibilityPromises.push(compatibilityPromise);
    });
    
    await Promise.all(compatibilityPromises);

    console.log(`${result.compatible.length} tools are compatible`);
  }

  /**
   * Register compatible tools with VL Chat Service
   */
  private async registerCompatibleTools(result: ToolDiscoveryResult): Promise<void> {
    console.log('Registering compatible tools...');

    const compatibleToolsArray = Array.from(this.compatibleTools.entries());
    for (const [toolName, toolDef] of compatibleToolsArray) {
      let attempts = 0;
      let registered = false;

      while (attempts < this.config.retryAttempts && !registered) {
        try {
          // Note: Actual registration will be handled by the VL Chat Service
          // This is just marking them as ready for registration
          result.registered.push(toolDef);
          registered = true;
          console.log(`✓ Tool ready for registration: ${toolName}`);
        } catch (error) {
          attempts++;
          const errorMessage = error instanceof Error ? error.message : String(error);
          
          if (attempts >= this.config.retryAttempts) {
            result.errors.push({
              toolName,
              stage: 'registration',
              error: `Registration failed after ${this.config.retryAttempts} attempts: ${errorMessage}`
            });
            console.error(`✗ Tool registration failed for ${toolName}:`, error);
          } else {
            console.warn(`⚠ Tool registration attempt ${attempts} failed for ${toolName}, retrying...`);
            await this.delay(this.config.retryDelay);
          }
        }
      }
    }

    console.log(`${result.registered.length} tools ready for registration`);
  }

  /**
   * Validate tool definition structure
   */
  private validateToolDefinition(toolDef: ToolDefinition): { valid: boolean; error?: string } {
    try {
      // Check basic structure
      if (!toolDef || typeof toolDef !== 'object') {
        return { valid: false, error: 'Tool definition must be an object' };
      }

      // Check type field
      if (toolDef.type !== 'function') {
        return { valid: false, error: 'Tool type must be "function"' };
      }

      // Check function field
      if (!toolDef.function || typeof toolDef.function !== 'object') {
        return { valid: false, error: 'Tool must contain function object' };
      }

      // Check name field
      if (!toolDef.function.name || typeof toolDef.function.name !== 'string') {
        return { valid: false, error: 'Tool must have valid name' };
      }

      // Check name format (only allow letters, numbers, underscores, hyphens)
      if (!/^[a-zA-Z0-9_-]+$/.test(toolDef.function.name)) {
        return { valid: false, error: 'Tool name can only contain letters, numbers, underscores, and hyphens' };
      }

      // Check description field
      if (!toolDef.function.description || typeof toolDef.function.description !== 'string') {
        return { valid: false, error: 'Tool must have valid description' };
      }

      // Check parameters field
      if (!toolDef.function.parameters || typeof toolDef.function.parameters !== 'object') {
        return { valid: false, error: 'Tool must have valid parameters definition' };
      }

      // Validate parameters structure (JSON Schema format)
      if (toolDef.function.parameters.type !== 'object') {
        return { valid: false, error: 'Tool parameters type must be "object"' };
      }

      return { valid: true };

    } catch (error) {
      return { 
        valid: false, 
        error: error instanceof Error ? error.message : String(error) 
      };
    }
  }

  /**
   * Check tool compatibility with VL Chat Service
   */
  private async checkToolCompatibilityInternal(toolDef: ToolDefinition): Promise<ToolCompatibilityResult> {
    const result: ToolCompatibilityResult = {
      compatible: true,
      issues: [],
      warnings: []
    };

    try {
      // Check if tool is supported by VLImageToolsBridge
      const supportedTools = ['analyze_image', 'find_similar_images_by_image', 'find_similar_images_by_description', 'find_images_by_tags', 'get_image_analysis'];
      
      if (!supportedTools.includes(toolDef.function.name)) {
        result.compatible = false;
        result.issues.push(`Tool ${toolDef.function.name} is not supported by VLImageToolsBridge`);
        return result;
      }

      // Test parameter validation
      try {
        const validation = VLImageToolsBridge.validateToolParameters(toolDef.function.name, {});
        // If validation passes or fails with expected parameter errors, it's compatible
        if (!validation.valid && !validation.error?.includes('parameter is required')) {
          result.warnings.push(`Parameter validation may have issues: ${validation.error}`);
        }
      } catch (error) {
        result.warnings.push(`Parameter validation test failed: ${error instanceof Error ? error.message : String(error)}`);
      }

      // Check parameter schema complexity
      const params = toolDef.function.parameters;
      if (params.properties && Object.keys(params.properties).length > 10) {
        result.warnings.push('Tool has many parameters, may be complex for AI model to use');
      }

      // Check for required parameters
      if (params.required && params.required.length === 0) {
        result.warnings.push('Tool has no required parameters, may lead to ambiguous calls');
      }

      return result;

    } catch (error) {
      result.compatible = false;
      result.issues.push(`Compatibility check failed: ${error instanceof Error ? error.message : String(error)}`);
      return result;
    }
  }

  /**
   * Get discovered tools
   */
  getDiscoveredTools(): ToolDefinition[] {
    return Array.from(this.discoveredTools.values());
  }

  /**
   * Get validated tools
   */
  getValidatedTools(): ToolDefinition[] {
    return Array.from(this.validatedTools.values());
  }

  /**
   * Get compatible tools
   */
  getCompatibleTools(): ToolDefinition[] {
    return Array.from(this.compatibleTools.values());
  }

  /**
   * Get tool by name
   */
  getTool(name: string): ToolDefinition | undefined {
    return this.compatibleTools.get(name) || this.validatedTools.get(name) || this.discoveredTools.get(name);
  }

  /**
   * Check if tool is available
   */
  hasCompatibleTool(name: string): boolean {
    return this.compatibleTools.has(name);
  }

  /**
   * Get tool discovery statistics
   */
  getDiscoveryStats(): {
    discovered: number;
    validated: number;
    compatible: number;
    errorCount: number;
    successRate: number;
  } {
    const discovered = this.discoveredTools.size;
    const validated = this.validatedTools.size;
    const compatible = this.compatibleTools.size;
    const errorCount = discovered - compatible;
    const successRate = discovered > 0 ? (compatible / discovered) * 100 : 0;

    return {
      discovered,
      validated,
      compatible,
      errorCount,
      successRate
    };
  }

  /**
   * Delay utility for retry mechanism
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Log discovery results
   */
  private logDiscoveryResults(result: ToolDiscoveryResult): void {
    console.log('\n=== Tool Discovery Results ===');
    console.log(`Discovered: ${result.discovered.length} tools`);
    console.log(`Validated: ${result.validated.length} tools`);
    console.log(`Compatible: ${result.compatible.length} tools`);
    console.log(`Ready for registration: ${result.registered.length} tools`);
    console.log(`Errors: ${result.errors.length}`);
    console.log(`Discovery time: ${result.discoveryTime}ms`);

    if (result.errors.length > 0) {
      console.log('\nErrors encountered:');
      result.errors.forEach((error: any) => {
        console.log(`  - ${error.toolName} (${error.stage}): ${error.error}`);
      });
    }

    if (result.registered.length > 0) {
      console.log('\nTools ready for registration:');
      result.registered.forEach((tool: any) => {
        console.log(`  - ${tool.function.name}: ${tool.function.description}`);
      });
    }

    console.log('==============================\n');
  }
}

/**
 * Auto-discovery and registration system
 * Integrates with VL Chat Service initialization
 */
export class ToolAutoRegistration {
  private discovery: ToolDiscovery;
  private registeredTools: Map<string, ToolDefinition> = new Map();

  constructor(config: ToolDiscoveryConfig = {}) {
    this.discovery = new ToolDiscovery(config);
  }

  /**
   * Discover and register tools automatically
   */
  async discoverAndRegister(): Promise<{
    discoveryResult: ToolDiscoveryResult;
    registrationSuccess: boolean;
    registeredTools: ToolDefinition[];
  }> {
    try {
      console.log('Starting automatic tool discovery and registration...');

      // Discover tools
      const discoveryResult = await this.discovery.discoverTools();

      // Mark tools as registered (actual registration handled by VL Chat Service)
      const registeredTools = discoveryResult.registered;
      registeredTools.forEach((tool: any) => {
        this.registeredTools.set(tool.function.name, tool);
      });

      const registrationSuccess = registeredTools.length > 0;

      console.log(`Auto-registration completed: ${registeredTools.length} tools ready`);

      return {
        discoveryResult,
        registrationSuccess,
        registeredTools
      };

    } catch (error) {
      console.error('Auto-registration failed:', error);
      throw error;
    }
  }

  /**
   * Get tools ready for VL Chat Service registration
   */
  getToolsForRegistration(): ToolDefinition[] {
    return Array.from(this.registeredTools.values());
  }

  /**
   * Check if auto-registration was successful
   */
  isRegistrationSuccessful(): boolean {
    return this.registeredTools.size > 0;
  }

  /**
   * Get registration statistics
   */
  getRegistrationStats(): {
    totalRegistered: number;
    toolNames: string[];
    categories: Record<string, string[]>;
  } {
    const toolNames = Array.from(this.registeredTools.keys());
    
    // Categorize tools by prefix
    const categories: Record<string, string[]> = {
      analyze: toolNames.filter((name: any) => name.startsWith('analyze_')),
      find: toolNames.filter((name: any) => name.startsWith('find_')),
      get: toolNames.filter((name: any) => name.startsWith('get_')),
      other: toolNames.filter((name: any) => !name.startsWith('analyze_') && !name.startsWith('find_') && !name.startsWith('get_'))
    };

    return {
      totalRegistered: toolNames.length,
      toolNames,
      categories
    };
  }
}

/**
 * Convenience function for quick tool discovery
 */
export async function discoverImageTools(config?: ToolDiscoveryConfig): Promise<ToolDiscoveryResult> {
  const discovery = new ToolDiscovery(config);
  return await discovery.discoverTools();
}

/**
 * Convenience function for auto-registration
 */
export async function autoRegisterImageTools(config?: ToolDiscoveryConfig): Promise<ToolDefinition[]> {
  const autoReg = new ToolAutoRegistration(config);
  const result = await autoReg.discoverAndRegister();
  return result.registeredTools;
}