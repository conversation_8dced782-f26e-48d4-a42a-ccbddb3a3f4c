# Tool Registry System

This directory contains the tool registration and management infrastructure for the chat-image-tools feature.

## Overview

The Tool Registry System provides a centralized way to register, validate, and execute tools that can be called by AI models through the tool use functionality.

## Core Components

### 1. Type Definitions (`src/types/tools.ts`)

Defines the core interfaces and types for the tool system:

- `Tool`: Main tool interface with name, description, parameters, and execute function
- `ToolParameters`: JSON Schema-like parameter definitions
- `ToolResult`: Standardized result format for tool execution
- `ToolExecutionError`: Custom error class for tool-related errors

### 2. Tool Registry (`src/lib/toolRegistry.ts`)

Central registry for managing tools:

- **Singleton Pattern**: Ensures single instance across the application
- **Tool Registration**: Register new tools with validation
- **Tool Execution**: Execute tools with parameter validation and error handling
- **Tool Discovery**: Get available tools and their documentation

Key methods:
- `registerTool(tool: Tool)`: Register a new tool
- `executeTool(name: string, params: any)`: Execute a tool safely
- `getTool(name: string)`: Get a specific tool
- `getAllTools()`: Get all registered tools
- `getToolDocumentation()`: Get AI-consumable tool documentation

### 3. Parameter Validation (`src/lib/toolValidation.ts`)

Validates tool parameters against their schemas:

- **Type Validation**: Ensures parameters match expected types
- **Constraint Validation**: Validates string length, number ranges, enum values
- **Required Parameter Checking**: Ensures all required parameters are provided
- **Detailed Error Messages**: Provides clear validation error messages

### 4. Tool Utilities (`src/lib/toolUtils.ts`)

Helper functions for creating tools and handling common patterns:

- **Tool Creation**: `createTool()` with standardized error handling
- **Parameter Helpers**: `stringParam()`, `numberParam()`, `booleanParam()`, etc.
- **Execution Utilities**: Timeout handling, retry logic, execution time measurement

## Usage Examples

### Basic Tool Creation

```typescript
import { ToolUtils, toolRegistry } from './lib/tools';

// Create a simple tool
const echoTool = ToolUtils.createTool(
  'echo',
  'Echoes the input message',
  ToolUtils.createParameters({
    message: ToolUtils.stringParam('Message to echo', true),
    prefix: ToolUtils.stringParam('Optional prefix', false)
  }, ['message']),
  async (params) => {
    return { echo: `${params.prefix || ''}${params.message}` };
  }
);

// Register the tool
toolRegistry.registerTool(echoTool);
```

### Tool Execution

```typescript
// Execute a tool
const result = await toolRegistry.executeTool('echo', {
  message: 'Hello World',
  prefix: 'Echo: '
});

if (result.success) {
  console.log('Result:', result.data);
  console.log('Execution time:', result.metadata?.executionTime, 'ms');
} else {
  console.error('Error:', result.error);
}
```

### Parameter Validation

```typescript
// Create parameters with validation
const parameters = ToolUtils.createParameters({
  count: ToolUtils.numberParam('Number of items', true, { 
    minimum: 1, 
    maximum: 100 
  }),
  category: ToolUtils.stringParam('Category', true, { 
    enum: ['images', 'documents', 'videos'] 
  }),
  tags: ToolUtils.arrayParam('Tags', ToolUtils.stringParam('Tag'), false)
}, ['count', 'category']);
```

## Error Handling

The system provides comprehensive error handling:

1. **Tool Not Found**: When trying to execute a non-existent tool
2. **Parameter Validation**: When parameters don't match the schema
3. **Execution Errors**: When tool execution fails
4. **Service Unavailable**: When dependent services are not available

All errors are wrapped in standardized `ToolResult` objects with detailed error messages.

## Integration with AI Models

The tool registry provides AI-consumable documentation through `getToolDocumentation()`, which returns tool definitions in a format suitable for AI model consumption:

```typescript
const docs = toolRegistry.getToolDocumentation();
// Returns array of { name, description, parameters } objects
```

## Testing

Run the example component to test the tool registry functionality:

```typescript
import { ToolRegistryExample } from '../examples/ToolRegistryExample';
```

## Best Practices

1. **Use ToolUtils**: Leverage the utility functions for consistent tool creation
2. **Validate Parameters**: Always define proper parameter schemas
3. **Handle Errors**: Use try-catch blocks in tool implementations
4. **Document Tools**: Provide clear descriptions for AI model understanding
5. **Test Tools**: Verify tool functionality before registration

## Architecture

```
Tool Registry System
├── Types (tools.ts)
├── Registry (toolRegistry.ts)
├── Validation (toolValidation.ts)
├── Utilities (toolUtils.ts)
└── Examples (ToolRegistryExample.tsx)
```

The system follows a modular architecture with clear separation of concerns, making it easy to extend and maintain.