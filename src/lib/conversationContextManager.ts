/**
 * Conversation Context Management System
 * Tracks multi-step image workflows and maintains conversation history
 */

export interface ImageWorkflowStep {
  stepId: string;
  toolName: string;
  parameters: any;
  result: any;
  timestamp: Date;
  userQuery: string;
  success: boolean;
}

export interface ConversationContext {
  conversationId: string;
  startTime: Date;
  lastActivity: Date;
  currentWorkflow?: ImageWorkflow;
  workflowHistory: ImageWorkflow[];
  userPreferences: UserPreferences;
  sessionStats: SessionStats;
}

export interface ImageWorkflow {
  workflowId: string;
  type: 'analysis' | 'search' | 'mixed';
  startTime: Date;
  steps: ImageWorkflowStep[];
  currentImage?: {
    imageBase64?: string;
    imageId?: string;
    analysisResult?: any;
  };
  searchContext?: {
    lastQuery?: string;
    lastResults?: any[];
    searchParameters?: any;
  };
  status: 'active' | 'completed' | 'abandoned';
}

export interface UserPreferences {
  preferredResultCount: number;
  preferredSimilarityThreshold: number;
  preferredDetailLevel: 'basic' | 'detailed' | 'comprehensive';
  preferredLanguage: string;
  searchHistory: string[];
}

export interface SessionStats {
  totalQueries: number;
  successfulOperations: number;
  failedOperations: number;
  mostUsedTools: Record<string, number>;
  averageResponseTime: number;
}

export interface ContextualSuggestion {
  type: 'follow_up' | 'refinement' | 'alternative' | 'related';
  text: string;
  action?: {
    toolName: string;
    parameters: any;
  };
  priority: number;
}

export class ConversationContextManager {
  private contexts: Map<string, ConversationContext> = new Map();
  private readonly maxContextAge = 24 * 60 * 60 * 1000; // 24 hours
  private readonly maxWorkflowSteps = 20;
  private readonly maxWorkflowHistory = 10;

  /**
   * Initialize or get conversation context
   */
  getOrCreateContext(conversationId: string): ConversationContext {
    let context = this.contexts.get(conversationId);
    
    if (!context) {
      context = {
        conversationId,
        startTime: new Date(),
        lastActivity: new Date(),
        workflowHistory: [],
        userPreferences: this.getDefaultPreferences(),
        sessionStats: this.getDefaultStats()
      };
      this.contexts.set(conversationId, context);
    } else {
      context.lastActivity = new Date();
    }
    
    // Clean up old contexts
    this.cleanupOldContexts();
    
    return context;
  }

  /**
   * Start a new image workflow
   */
  startWorkflow(
    conversationId: string, 
    type: 'analysis' | 'search' | 'mixed',
    initialImage?: { imageBase64?: string; imageId?: string }
  ): string {
    const context = this.getOrCreateContext(conversationId);
    
    // Complete current workflow if exists
    if (context.currentWorkflow && context.currentWorkflow.status === 'active') {
      this.completeWorkflow(conversationId, 'abandoned');
    }
    
    const workflowId = `workflow_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    const workflow: ImageWorkflow = {
      workflowId,
      type,
      startTime: new Date(),
      steps: [],
      currentImage: initialImage,
      status: 'active'
    };
    
    context.currentWorkflow = workflow;
    
    console.log(`Started new ${type} workflow: ${workflowId}`);
    return workflowId;
  }

  /**
   * Add step to current workflow
   */
  addWorkflowStep(
    conversationId: string,
    toolName: string,
    parameters: any,
    result: any,
    userQuery: string,
    success: boolean
  ): void {
    const context = this.getOrCreateContext(conversationId);
    
    if (!context.currentWorkflow) {
      // Auto-start workflow based on tool type
      const workflowType = toolName === 'analyze_image' ? 'analysis' : 'search';
      this.startWorkflow(conversationId, workflowType, 
        parameters.imageBase64 ? { imageBase64: parameters.imageBase64 } : 
        parameters.imageId ? { imageId: parameters.imageId } : undefined
      );
    }
    
    const workflow = context.currentWorkflow!;
    
    const step: ImageWorkflowStep = {
      stepId: `step_${Date.now()}_${Math.random().toString(36).substr(2, 6)}`,
      toolName,
      parameters,
      result,
      timestamp: new Date(),
      userQuery,
      success
    };
    
    workflow.steps.push(step);
    
    // Update workflow context based on step
    this.updateWorkflowContext(workflow, step);
    
    // Update session stats
    context.sessionStats.totalQueries++;
    if (success) {
      context.sessionStats.successfulOperations++;
    } else {
      context.sessionStats.failedOperations++;
    }
    
    context.sessionStats.mostUsedTools[toolName] = (context.sessionStats.mostUsedTools[toolName] || 0) + 1;
    
    // Limit workflow steps
    if (workflow.steps.length > this.maxWorkflowSteps) {
      workflow.steps = workflow.steps.slice(-this.maxWorkflowSteps);
    }
    
    console.log(`Added step to workflow ${workflow.workflowId}: ${toolName}`);
  }

  /**
   * Complete current workflow
   */
  completeWorkflow(conversationId: string, status: 'completed' | 'abandoned' = 'completed'): void {
    const context = this.contexts.get(conversationId);
    if (!context || !context.currentWorkflow) return;
    
    context.currentWorkflow.status = status;
    context.workflowHistory.push(context.currentWorkflow);
    context.currentWorkflow = undefined;
    
    // Limit workflow history
    if (context.workflowHistory.length > this.maxWorkflowHistory) {
      context.workflowHistory = context.workflowHistory.slice(-this.maxWorkflowHistory);
    }
    
    console.log(`Completed workflow with status: ${status}`);
  }

  /**
   * Get contextual suggestions based on current workflow
   */
  getContextualSuggestions(conversationId: string): ContextualSuggestion[] {
    const context = this.contexts.get(conversationId);
    if (!context) return [];
    
    const suggestions: ContextualSuggestion[] = [];
    
    // Current workflow suggestions
    if (context.currentWorkflow) {
      suggestions.push(...this.generateWorkflowSuggestions(context.currentWorkflow));
    }
    
    // Historical suggestions
    if (context.workflowHistory.length > 0) {
      suggestions.push(...this.generateHistoricalSuggestions(context.workflowHistory));
    }
    
    // User preference suggestions
    suggestions.push(...this.generatePreferenceSuggestions(context.userPreferences));
    
    // Sort by priority and return top suggestions
    return suggestions
      .sort((a, b) => b.priority - a.priority)
      .slice(0, 8);
  }

  /**
   * Check if user query is a follow-up to previous results
   */
  isFollowUpQuery(conversationId: string, userQuery: string): {
    isFollowUp: boolean;
    relatedStep?: ImageWorkflowStep;
    context?: any;
  } {
    const context = this.contexts.get(conversationId);
    if (!context || !context.currentWorkflow) {
      return { isFollowUp: false };
    }
    
    const workflow = context.currentWorkflow;
    const lastStep = workflow.steps[workflow.steps.length - 1];
    
    if (!lastStep) {
      return { isFollowUp: false };
    }
    
    // Check for follow-up patterns
    const followUpPatterns = [
      /show me more/i,
      /find more like/i,
      /what about/i,
      /tell me about/i,
      /analyze (this|that|these|those)/i,
      /similar to (this|that|these|those)/i,
      /more details/i,
      /refine/i,
      /adjust/i,
      /different/i,
      /better/i
    ];
    
    const isFollowUp = followUpPatterns.some((pattern: any) => pattern.test(userQuery));
    
    if (isFollowUp) {
      return {
        isFollowUp: true,
        relatedStep: lastStep,
        context: {
          lastTool: lastStep.toolName,
          lastResult: lastStep.result,
          lastParameters: lastStep.parameters,
          searchContext: workflow.searchContext,
          currentImage: workflow.currentImage
        }
      };
    }
    
    return { isFollowUp: false };
  }

  /**
   * Get conversation summary for context
   */
  getConversationSummary(conversationId: string): {
    totalSteps: number;
    recentActivity: string[];
    currentFocus?: string;
    suggestedActions: string[];
  } {
    const context = this.contexts.get(conversationId);
    if (!context) {
      return {
        totalSteps: 0,
        recentActivity: [],
        suggestedActions: ['Start by uploading an image or describing what you\'re looking for']
      };
    }
    
    const totalSteps = context.workflowHistory.reduce((sum, wf) => sum + wf.steps.length, 0) +
                     (context.currentWorkflow?.steps.length || 0);
    
    const recentActivity: string[] = [];
    
    // Add recent workflow steps
    if (context.currentWorkflow) {
      const recentSteps = context.currentWorkflow.steps.slice(-3);
      recentSteps.forEach((step: any) => {
        const action = this.getStepDescription(step);
        recentActivity.push(action);
      });
    }
    
    // Determine current focus
    let currentFocus: string | undefined;
    if (context.currentWorkflow) {
      const workflow = context.currentWorkflow;
      if (workflow.currentImage) {
        currentFocus = 'Working with uploaded image';
      } else if (workflow.searchContext?.lastQuery) {
        currentFocus = `Searching for: ${workflow.searchContext.lastQuery}`;
      } else {
        currentFocus = `${workflow.type} workflow in progress`;
      }
    }
    
    // Generate suggested actions
    const suggestedActions = this.getContextualSuggestions(conversationId)
      .slice(0, 3)
      .map((suggestion: any) => suggestion.text);
    
    return {
      totalSteps,
      recentActivity,
      currentFocus,
      suggestedActions
    };
  }

  /**
   * Update user preferences based on usage patterns
   */
  updateUserPreferences(conversationId: string, parameters: any): void {
    const context = this.contexts.get(conversationId);
    if (!context) return;
    
    const prefs = context.userPreferences;
    
    // Update preferred result count
    if (parameters.limit && typeof parameters.limit === 'number') {
      prefs.preferredResultCount = Math.round((prefs.preferredResultCount + parameters.limit) / 2);
    }
    
    // Update preferred similarity threshold
    if (parameters.threshold && typeof parameters.threshold === 'number') {
      prefs.preferredSimilarityThreshold = (prefs.preferredSimilarityThreshold + parameters.threshold) / 2;
    }
    
    // Update preferred detail level
    if (parameters.detailLevel) {
      prefs.preferredDetailLevel = parameters.detailLevel;
    }
    
    // Add to search history
    if (parameters.description) {
      prefs.searchHistory.unshift(parameters.description);
      prefs.searchHistory = prefs.searchHistory.slice(0, 10); // Keep last 10
    }
  }

  /**
   * Clear conversation context
   */
  clearContext(conversationId: string): void {
    this.contexts.delete(conversationId);
    console.log(`Cleared context for conversation: ${conversationId}`);
  }

  /**
   * Get all active conversations
   */
  getActiveConversations(): string[] {
    return Array.from(this.contexts.keys());
  }

  /**
   * Private helper methods
   */
  private updateWorkflowContext(workflow: ImageWorkflow, step: ImageWorkflowStep): void {
    // Update current image if step involves image analysis
    if (step.toolName === 'analyze_image' && step.success) {
      if (step.parameters.imageBase64) {
        workflow.currentImage = {
          imageBase64: step.parameters.imageBase64,
          analysisResult: step.result
        };
      } else if (step.parameters.imageId) {
        workflow.currentImage = {
          imageId: step.parameters.imageId,
          analysisResult: step.result
        };
      }
    }
    
    // Update search context for search operations
    if (step.toolName.includes('find_') && step.success) {
      if (!workflow.searchContext) {
        workflow.searchContext = {};
      }
      
      workflow.searchContext.lastResults = step.result.results;
      workflow.searchContext.searchParameters = step.parameters;
      
      if (step.parameters.description) {
        workflow.searchContext.lastQuery = step.parameters.description;
      }
    }
  }

  private generateWorkflowSuggestions(workflow: ImageWorkflow): ContextualSuggestion[] {
    const suggestions: ContextualSuggestion[] = [];
    const lastStep = workflow.steps[workflow.steps.length - 1];
    
    if (!lastStep) return suggestions;
    
    // Suggestions based on last successful step
    if (lastStep.success) {
      switch (lastStep.toolName) {
        case 'analyze_image':
          suggestions.push({
            type: 'follow_up',
            text: 'Find similar images to this one',
            action: {
              toolName: 'find_similar_images_by_image',
              parameters: {
                imageBase64: workflow.currentImage?.imageBase64,
                imageId: workflow.currentImage?.imageId
              }
            },
            priority: 8
          });
          
          if (lastStep.result.tags && lastStep.result.tags.length > 0) {
            suggestions.push({
              type: 'follow_up',
              text: `Search for images with similar tags: ${lastStep.result.tags.slice(0, 3).join(', ')}`,
              action: {
                toolName: 'find_images_by_tags',
                parameters: {
                  tags: lastStep.result.tags.slice(0, 3)
                }
              },
              priority: 7
            });
          }
          break;
          
        case 'find_similar_images_by_image':
        case 'find_similar_images_by_description':
        case 'find_images_by_tags':
          if (lastStep.result.total > 0) {
            suggestions.push({
              type: 'refinement',
              text: 'Refine search with different parameters',
              priority: 6
            });
            
            suggestions.push({
              type: 'follow_up',
              text: 'Analyze one of the found images',
              priority: 5
            });
          } else {
            suggestions.push({
              type: 'alternative',
              text: 'Try a different search approach',
              priority: 7
            });
          }
          break;
      }
    } else {
      // Suggestions for failed steps
      suggestions.push({
        type: 'alternative',
        text: 'Try a different approach',
        priority: 6
      });
    }
    
    return suggestions;
  }

  private generateHistoricalSuggestions(workflowHistory: ImageWorkflow[]): ContextualSuggestion[] {
    const suggestions: ContextualSuggestion[] = [];
    
    // Find successful patterns from history
    const successfulSearches = workflowHistory
      .flatMap(wf => wf.steps)
      .filter((step: any) => step.success && step.toolName.includes('find_'))
      .slice(-3);
    
    successfulSearches.forEach((step: any) => {
      if (step.parameters.description) {
        suggestions.push({
          type: 'related',
          text: `Search again for: "${step.parameters.description}"`,
          action: {
            toolName: step.toolName,
            parameters: step.parameters
          },
          priority: 3
        });
      }
    });
    
    return suggestions;
  }

  private generatePreferenceSuggestions(preferences: UserPreferences): ContextualSuggestion[] {
    const suggestions: ContextualSuggestion[] = [];
    
    // Suggestions based on search history
    preferences.searchHistory.slice(0, 2).forEach((query: any) => {
      suggestions.push({
        type: 'related',
        text: `Search again for: "${query}"`,
        action: {
          toolName: 'find_similar_images_by_description',
          parameters: { description: query }
        },
        priority: 2
      });
    });
    
    return suggestions;
  }

  private getStepDescription(step: ImageWorkflowStep): string {
    switch (step.toolName) {
      case 'analyze_image':
        return step.success ? 'Analyzed image successfully' : 'Image analysis failed';
      case 'find_similar_images_by_image':
        return step.success ? `Found ${step.result.total || 0} similar images` : 'Similar image search failed';
      case 'find_similar_images_by_description':
        return step.success ? `Found ${step.result.total || 0} images matching description` : 'Description search failed';
      case 'find_images_by_tags':
        return step.success ? `Found ${step.result.total || 0} images with tags` : 'Tag search failed';
      default:
        return `Executed ${step.toolName}`;
    }
  }

  private getDefaultPreferences(): UserPreferences {
    return {
      preferredResultCount: 20,
      preferredSimilarityThreshold: 0.5,
      preferredDetailLevel: 'detailed',
      preferredLanguage: 'zh',
      searchHistory: []
    };
  }

  private getDefaultStats(): SessionStats {
    return {
      totalQueries: 0,
      successfulOperations: 0,
      failedOperations: 0,
      mostUsedTools: {},
      averageResponseTime: 0
    };
  }

  private cleanupOldContexts(): void {
    const now = Date.now();
    const toDelete: string[] = [];
    
    this.contexts.forEach((context, id) => {
      if (now - context.lastActivity.getTime() > this.maxContextAge) {
        toDelete.push(id);
      }
    });
    
    toDelete.forEach((id: any) => {
      this.contexts.delete(id);
      console.log(`Cleaned up old context: ${id}`);
    });
  }
}

// Singleton instance
export const conversationContextManager = new ConversationContextManager();