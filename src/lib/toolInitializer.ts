// import { ToolRegistry, toolRegistry } from './toolRegistry';
// import { imageTools, registerImageTools } from './imageTools-clean';
// import { Tool } from '../types/tools';
//
// /**
//  * Tool initialization configuration
//  */
// interface ToolInitConfig {
//   enableLazyLoading?: boolean;
//   enableErrorRecovery?: boolean;
//   retryAttempts?: number;
//   retryDelay?: number;
//   enableProgressTracking?: boolean;
//   enableEventNotifications?: boolean;
// }
//
// /**
//  * Default configuration for tool initialization
//  */
// const DEFAULT_CONFIG: Required<ToolInitConfig> = {
//   enableLazyLoading: true,
//   enableErrorRecovery: true,
//   retryAttempts: 3,
//   retryDelay: 1000,
//   enableProgressTracking: true,
//   enableEventNotifications: true
// };
//
// /**
//  * Tool initialization status
//  */
// interface ToolInitStatus {
//   initialized: boolean;
//   totalTools: number;
//   successfulTools: number;
//   failedTools: string[];
//   initializationTime: number;
//   loadedTools: string[];
//   isLoading: boolean;
//   progress: number; // 0-100
// }
//
// /**
//  * Tool loading progress event
//  */
// interface ToolLoadingProgressEvent {
//   toolName: string;
//   status: 'loading' | 'success' | 'failed';
//   progress: number;
//   totalTools: number;
//   loadedTools: number;
//   error?: string;
// }
//
// /**
//  * Tool system ready event
//  */
// interface ToolSystemReadyEvent {
//   totalTools: number;
//   successfulTools: number;
//   failedTools: string[];
//   loadedTools: string[];
//   initializationTime: number;
// }
//
// /**
//  * Tool initializer class for managing tool registration and initialization
//  */
// export class ToolInitializer {
//   private static instance: ToolInitializer | null = null;
//   private config: Required<ToolInitConfig>;
//   private initStatus: ToolInitStatus;
//   private registry: ToolRegistry;
//
//   private constructor(config: ToolInitConfig = {}) {
//     this.config = { ...DEFAULT_CONFIG, ...config };
//     this.registry = toolRegistry;
//     this.initStatus = {
//       initialized: false,
//       totalTools: 0,
//       successfulTools: 0,
//       failedTools: [],
//       loadedTools: [],
//       initializationTime: 0,
//       isLoading: false,
//       progress: 0
//     };
//   }
//
//   /**
//    * Get singleton instance
//    */
//   static getInstance(config?: ToolInitConfig): ToolInitializer {
//     if (!ToolInitializer.instance) {
//       ToolInitializer.instance = new ToolInitializer(config);
//     }
//     return ToolInitializer.instance;
//   }
//
//   /**
//    * Initialize all tools with error recovery and lazy loading support
//    */
//   async initializeTools(): Promise<ToolInitStatus> {
//     const startTime = Date.now();
//     console.log('开始异步初始化工具系统...');
//
//     try {
//       // Reset status
//       this.initStatus = {
//         initialized: false,
//         totalTools: 0,
//         successfulTools: 0,
//         failedTools: [],
//         loadedTools: [],
//         initializationTime: 0,
//         isLoading: true,
//         progress: 0
//       };
//
//       // Emit loading started event
//       if (this.config.enableEventNotifications) {
//         this.emit('loadingStarted', { totalTools: imageTools.length });
//       }
//
//       // Initialize image tools
//       await this.initializeImageTools();
//
//       // Calculate final status
//       this.initStatus.initializationTime = Date.now() - startTime;
//       this.initStatus.initialized = this.initStatus.successfulTools > 0; // Allow partial success
//       this.initStatus.isLoading = false;
//       this.initStatus.progress = 100;
//
//       // Emit system ready event
//       if (this.config.enableEventNotifications) {
//         const readyEvent: ToolSystemReadyEvent = {
//           totalTools: this.initStatus.totalTools,
//           successfulTools: this.initStatus.successfulTools,
//           failedTools: this.initStatus.failedTools,
//           loadedTools: this.initStatus.loadedTools,
//           initializationTime: this.initStatus.initializationTime
//         };
//         this.emit('systemReady', readyEvent);
//       }
//
//       // Log results
//       this.logInitializationResults();
//
//       return this.initStatus;
//
//     } catch (error) {
//       console.error('工具系统初始化失败:', error);
//       this.initStatus.initializationTime = Date.now() - startTime;
//       this.initStatus.isLoading = false;
//
//       // Emit error event
//       if (this.config.enableEventNotifications) {
//         this.emit('loadingError', { error: error instanceof Error ? error.message : String(error) });
//       }
//
//       throw error;
//     }
//   }
//
//   /**
//    * Initialize image tools with error recovery and progress tracking
//    */
//   private async initializeImageTools(): Promise<void> {
//     console.log('正在异步初始化图片工具...');
//
//     const toolsToRegister = imageTools;
//     this.initStatus.totalTools = toolsToRegister.length;
//
//     for (let i = 0; i < toolsToRegister.length; i++) {
//       const tool = toolsToRegister[i];
//
//       // Emit progress event for current tool
//       if (this.config.enableProgressTracking && this.config.enableEventNotifications) {
//         const progressEvent: ToolLoadingProgressEvent = {
//           toolName: tool.name,
//           status: 'loading',
//           progress: Math.round((i / toolsToRegister.length) * 100),
//           totalTools: toolsToRegister.length,
//           loadedTools: this.initStatus.successfulTools,
//           error: undefined
//         };
//         this.emit('toolProgress', progressEvent);
//       }
//
//       await this.registerToolWithRetry(tool);
//
//       // Update progress after tool registration attempt
//       if (this.config.enableProgressTracking) {
//         this.initStatus.progress = Math.round(((i + 1) / toolsToRegister.length) * 100);
//       }
//     }
//   }
//
//   /**
//    * Register a single tool with retry mechanism
//    */
//   private async registerToolWithRetry(tool: Tool): Promise<void> {
//     let attempts = 0;
//     let lastError: Error | null = null;
//
//     while (attempts < this.config.retryAttempts) {
//       try {
//         // Validate tool before registration
//         this.validateTool(tool);
//
//         // Register the tool
//         this.registry.registerTool(tool);
//
//         // Test tool execution if lazy loading is disabled
//         if (!this.config.enableLazyLoading) {
//           await this.testToolExecution(tool);
//         }
//
//         this.initStatus.successfulTools++;
//         this.initStatus.loadedTools.push(tool.name);
//
//         // Emit success event
//         if (this.config.enableEventNotifications) {
//           const progressEvent: ToolLoadingProgressEvent = {
//             toolName: tool.name,
//             status: 'success',
//             progress: this.initStatus.progress,
//             totalTools: this.initStatus.totalTools,
//             loadedTools: this.initStatus.successfulTools,
//             error: undefined
//           };
//           this.emit('toolProgress', progressEvent);
//         }
//
//         console.log(`✓ 工具 '${tool.name}' 注册成功`);
//         return;
//
//       } catch (error) {
//         lastError = error instanceof Error ? error : new Error(String(error));
//         attempts++;
//
//         if (attempts < this.config.retryAttempts) {
//           console.warn(`⚠ 工具 '${tool.name}' 注册失败，正在重试 (${attempts}/${this.config.retryAttempts}):`, lastError.message);
//           await this.delay(this.config.retryDelay);
//         }
//       }
//     }
//
//     // All retry attempts failed
//     const errorMessage = `工具 '${tool.name}' 注册失败: ${lastError?.message || '未知错误'}`;
//     console.error(`✗ ${errorMessage}`);
//     this.initStatus.failedTools.push(tool.name);
//
//     // Emit failure event
//     if (this.config.enableEventNotifications) {
//       const progressEvent: ToolLoadingProgressEvent = {
//         toolName: tool.name,
//         status: 'failed',
//         progress: this.initStatus.progress,
//         totalTools: this.initStatus.totalTools,
//         loadedTools: this.initStatus.successfulTools,
//         error: errorMessage
//       };
//       this.emit('toolProgress', progressEvent);
//     }
//
//     if (!this.config.enableErrorRecovery) {
//       throw new Error(errorMessage);
//     }
//   }
//
//   /**
//    * Validate tool structure and requirements
//    */
//   private validateTool(tool: Tool): void {
//     if (!tool.name || typeof tool.name !== 'string') {
//       throw new Error('工具名称无效');
//     }
//
//     if (!tool.description || typeof tool.description !== 'string') {
//       throw new Error('工具描述无效');
//     }
//
//     if (!tool.parameters || typeof tool.parameters !== 'object') {
//       throw new Error('工具参数定义无效');
//     }
//
//     if (!tool.execute || typeof tool.execute !== 'function') {
//       throw new Error('工具执行函数无效');
//     }
//   }
//
//   /**
//    * Test tool execution with minimal parameters
//    */
//   private async testToolExecution(tool: Tool): Promise<void> {
//     // Skip execution test for now to avoid side effects
//     // This could be enhanced to run with mock parameters
//     console.log(`跳过工具 '${tool.name}' 的执行测试`);
//   }
//
//   /**
//    * Delay utility for retry mechanism
//    */
//   private delay(ms: number): Promise<void> {
//     return new Promise(resolve => setTimeout(resolve, ms));
//   }
//
//   /**
//    * Log initialization results
//    */
//   private logInitializationResults(): void {
//     const { totalTools, successfulTools, failedTools, initializationTime, initialized } = this.initStatus;
//
//     console.log('\n=== 工具系统初始化完成 ===');
//     console.log(`总工具数: ${totalTools}`);
//     console.log(`成功注册: ${successfulTools}`);
//     console.log(`失败工具: ${failedTools.length}`);
//     console.log(`初始化时间: ${initializationTime}ms`);
//     console.log(`初始化状态: ${initialized ? '成功' : '部分失败'}`);
//
//     if (failedTools.length > 0) {
//       console.log(`失败的工具: ${failedTools.join(', ')}`);
//     }
//
//     console.log('========================\n');
//   }
//
//   /**
//    * Get initialization status
//    */
//   getInitializationStatus(): ToolInitStatus {
//     return { ...this.initStatus };
//   }
//
//   /**
//    * Re-initialize failed tools
//    */
//   async reinitializeFailedTools(): Promise<void> {
//     if (this.initStatus.failedTools.length === 0) {
//       console.log('没有失败的工具需要重新初始化');
//       return;
//     }
//
//     console.log(`正在重新初始化 ${this.initStatus.failedTools.length} 个失败的工具...`);
//
//     const failedToolNames = [...this.initStatus.failedTools];
//     this.initStatus.failedTools = [];
//
//     // Emit retry started event
//     if (this.config.enableEventNotifications) {
//       this.emit('retryStarted', { failedTools: failedToolNames });
//     }
//
//     for (const toolName of failedToolNames) {
//       const tool = imageTools.find((t: any) => t.name === toolName);
//       if (tool) {
//         await this.registerToolWithRetry(tool);
//       }
//     }
//
//     this.initStatus.initialized = this.initStatus.successfulTools > 0;
//
//     // Emit retry completed event
//     if (this.config.enableEventNotifications) {
//       this.emit('retryCompleted', {
//         retriedTools: failedToolNames.length,
//         stillFailedTools: this.initStatus.failedTools.length,
//         successfulRetries: failedToolNames.length - this.initStatus.failedTools.length
//       });
//     }
//
//     console.log(`重新初始化完成，剩余失败工具: ${this.initStatus.failedTools.length}`);
//   }
//
//   /**
//    * Get available tools summary
//    */
//   getToolsSummary(): {
//     totalRegistered: number;
//     toolNames: string[];
//     categories: Record<string, string[]>;
//   } {
//     const allTools = this.registry.getAllTools();
//     const toolNames = allTools.map((tool: any) => tool.name);
//
//     // Categorize tools by prefix
//     const categories: Record<string, string[]> = {
//       image: toolNames.filter((name: any) => name.startsWith('find_') || name.startsWith('get_image_'))
//     };
//
//     return {
//       totalRegistered: allTools.length,
//       toolNames,
//       categories
//     };
//   }
//
//   /**
//    * Check if tools are ready for use
//    */
//   isReady(): boolean {
//     return this.initStatus.initialized && this.initStatus.successfulTools > 0;
//   }
//
//   /**
//    * Start asynchronous tool initialization (non-blocking)
//    * Returns immediately while tools load in the background
//    */
//   startAsyncInitialization(): void {
//     if (this.initStatus.isLoading) {
//       console.log('工具系统已在初始化中...');
//       return;
//     }
//
//     console.log('🚀 启动异步工具初始化...');
//
//     // Start initialization in background without blocking
//     this.initializeTools().catch(error => {
//       console.error('异步工具初始化失败:', error);
//     });
//   }
//
//   /**
//    * Get current loading progress (0-100)
//    */
//   getProgress(): number {
//     return this.initStatus.progress;
//   }
//
//   /**
//    * Check if currently loading
//    */
//   isLoading(): boolean {
//     return this.initStatus.isLoading;
//   }
//
//   private emit(loadingStarted: string, param2: any) {
//     // do nothing
//   }
// }
//
// /**
//  * Initialize tools with default configuration
//  */
// export async function initializeAllTools(config?: ToolInitConfig): Promise<ToolInitStatus> {
//   const initializer = ToolInitializer.getInstance(config);
//   return await initializer.initializeTools();
// }
//
// /**
//  * Get tool initializer instance
//  */
// export function getToolInitializer(): ToolInitializer {
//   return ToolInitializer.getInstance();
// }
//
// /**
//  * Quick initialization function for common use cases
//  */
// export async function quickInitializeTools(): Promise<boolean> {
//   try {
//     const status = await initializeAllTools({
//       enableLazyLoading: true,
//       enableErrorRecovery: true,
//       retryAttempts: 2,
//       retryDelay: 500
//     });
//
//     return status.initialized;
//   } catch (error) {
//     console.error('快速初始化工具失败:', error);
//     return false;
//   }
// }