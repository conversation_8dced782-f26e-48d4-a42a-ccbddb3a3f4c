/**
 * 日志服务
 * 提供统一的日志记录和性能监控功能
 */

// 日志级别
export enum LogLevel {
  DEBUG = 'debug',
  INFO = 'info',
  WARN = 'warn',
  ERROR = 'error'
}

// 性能指标类型
export interface PerformanceMetric {
  operation: string
  duration: number
  timestamp: number
  metadata?: Record<string, any>
}

// 日志条目
export interface LogEntry {
  level: LogLevel
  message: string
  timestamp: number
  context?: string
  data?: Record<string, any>
}

// 日志配置
export interface LoggerConfig {
  minLevel: LogLevel
  enableConsole: boolean
  maxLogEntries: number
  enablePerformanceMonitoring: boolean
}

/**
 * 日志服务类
 */
export class LoggerService {
  private logs: LogEntry[] = []
  private performanceMetrics: PerformanceMetric[] = []
  private config: LoggerConfig
  private performanceMarks: Map<string, number> = new Map()

  constructor(config?: Partial<LoggerConfig>) {
    this.config = {
      minLevel: LogLevel.INFO,
      enableConsole: true,
      maxLogEntries: 1000,
      enablePerformanceMonitoring: true,
      ...config
    }
  }

  /**
   * 记录调试日志
   */
  debug(message: string, context?: string, data?: Record<string, any>): void {
    this.log(LogLevel.DEBUG, message, context, data)
  }

  /**
   * 记录信息日志
   */
  info(message: string, context?: string, data?: Record<string, any>): void {
    this.log(LogLevel.INFO, message, context, data)
  }

  /**
   * 记录警告日志
   */
  warn(message: string, context?: string, data?: Record<string, any>): void {
    this.log(LogLevel.WARN, message, context, data)
  }

  /**
   * 记录错误日志
   */
  error(message: string, context?: string, data?: Record<string, any>): void {
    this.log(LogLevel.ERROR, message, context, data)
  }

  /**
   * 记录异常
   */
  logException(error: Error, context?: string, additionalData?: Record<string, any>): void {
    const data = {
      name: error.name,
      message: error.message,
      stack: error.stack,
      ...additionalData
    }
    this.error(`Exception: ${error.message}`, context, data)
  }

  /**
   * 开始性能计时
   */
  startPerformanceTimer(markId: string): void {
    if (!this.config.enablePerformanceMonitoring) return
    this.performanceMarks.set(markId, performance.now())
  }

  /**
   * 结束性能计时并记录
   */
  endPerformanceTimer(markId: string, operation: string, metadata?: Record<string, any>): number | null {
    if (!this.config.enablePerformanceMonitoring) return null
    
    const startTime = this.performanceMarks.get(markId)
    if (startTime === undefined) {
      this.warn(`Performance mark '${markId}' not found`, 'Performance')
      return null
    }
    
    const endTime = performance.now()
    const duration = endTime - startTime
    
    this.recordPerformanceMetric({
      operation,
      duration,
      timestamp: Date.now(),
      metadata
    })
    
    this.performanceMarks.delete(markId)
    return duration
  }

  /**
   * 获取所有日志
   */
  getLogs(level?: LogLevel): LogEntry[] {
    if (!level) return [...this.logs]
    return this.logs.filter(log => log.level === level)
  }

  /**
   * 获取性能指标
   */
  getPerformanceMetrics(operation?: string): PerformanceMetric[] {
    if (!operation) return [...this.performanceMetrics]
    return this.performanceMetrics.filter(metric => metric.operation === operation)
  }

  /**
   * 获取性能统计
   */
  getPerformanceStats(operation?: string): Record<string, any> {
    const metrics = operation 
      ? this.performanceMetrics.filter(m => m.operation === operation)
      : this.performanceMetrics
    
    if (metrics.length === 0) {
      return { count: 0 }
    }
    
    const durations = metrics.map(m => m.duration)
    const total = durations.reduce((sum, val) => sum + val, 0)
    const avg = total / durations.length
    const min = Math.min(...durations)
    const max = Math.max(...durations)
    
    // 计算中位数
    const sorted = [...durations].sort((a, b) => a - b)
    const mid = Math.floor(sorted.length / 2)
    const median = sorted.length % 2 === 0
      ? (sorted[mid - 1] + sorted[mid]) / 2
      : sorted[mid]
    
    return {
      count: metrics.length,
      total,
      avg,
      min,
      max,
      median
    }
  }

  /**
   * 清除日志
   */
  clearLogs(): void {
    this.logs = []
  }

  /**
   * 清除性能指标
   */
  clearPerformanceMetrics(): void {
    this.performanceMetrics = []
  }

  /**
   * 更新配置
   */
  updateConfig(config: Partial<LoggerConfig>): void {
    this.config = { ...this.config, ...config }
  }

  /**
   * 导出日志
   */
  exportLogs(): string {
    return JSON.stringify({
      logs: this.logs,
      performanceMetrics: this.performanceMetrics
    }, null, 2)
  }

  // 私有方法

  /**
   * 记录日志
   */
  private log(level: LogLevel, message: string, context?: string, data?: Record<string, any>): void {
    // 检查日志级别
    if (this.shouldLogLevel(level)) {
      const entry: LogEntry = {
        level,
        message,
        timestamp: Date.now(),
        context,
        data
      }
      
      // 添加到日志数组
      this.logs.push(entry)
      
      // 如果超过最大日志数，删除最旧的
      if (this.logs.length > this.config.maxLogEntries) {
        this.logs.shift()
      }
      
      // 输出到控制台
      if (this.config.enableConsole) {
        this.logToConsole(entry)
      }
    }
  }

  /**
   * 记录性能指标
   */
  private recordPerformanceMetric(metric: PerformanceMetric): void {
    this.performanceMetrics.push(metric)
    
    // 如果启用了控制台日志，输出性能信息
    if (this.config.enableConsole && this.shouldLogLevel(LogLevel.DEBUG)) {
      console.debug(
        `%c⏱️ Performance: ${metric.operation} - ${metric.duration.toFixed(2)}ms`,
        'color: #8c8c8c'
      )
    }
  }

  /**
   * 输出到控制台
   */
  private logToConsole(entry: LogEntry): void {
    const timestamp = new Date(entry.timestamp).toISOString()
    const contextStr = entry.context ? `[${entry.context}]` : ''
    const message = `${timestamp} ${contextStr} ${entry.message}`
    
    switch (entry.level) {
      case LogLevel.DEBUG:
        console.debug('%c' + message, 'color: #8c8c8c', entry.data || '')
        break
      case LogLevel.INFO:
        console.info('%c' + message, 'color: #0077cc', entry.data || '')
        break
      case LogLevel.WARN:
        console.warn('%c' + message, 'color: #ff9900', entry.data || '')
        break
      case LogLevel.ERROR:
        console.error('%c' + message, 'color: #cc0000', entry.data || '')
        break
    }
  }

  /**
   * 检查是否应该记录该级别的日志
   */
  private shouldLogLevel(level: LogLevel): boolean {
    const levels = [LogLevel.DEBUG, LogLevel.INFO, LogLevel.WARN, LogLevel.ERROR]
    const configLevelIndex = levels.indexOf(this.config.minLevel)
    const currentLevelIndex = levels.indexOf(level)
    
    return currentLevelIndex >= configLevelIndex
  }
}

// 导出单例实例
export const logger = new LoggerService()