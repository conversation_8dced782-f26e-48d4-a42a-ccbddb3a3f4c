/**
 * Test suite for comprehensive image tools error handling system
 */

import { ImageToolsErrorHandler, ImageToolsRecoverySystem } from './imageToolsErrorHandler';

/**
 * Test the error handling system with various scenarios
 */
export class ErrorHandlingTestSuite {
  
  /**
   * Test analyze_image error handling
   */
  static testAnalyzeImageErrors() {
    console.log('Testing analyze_image error handling...');
    
    // Test missing parameter error
    const missingParamError = new Error('必须提供imageBase64参数');
    const missingParamResult = ImageToolsErrorHandler.handleToolError(missingParamError, {
      toolName: 'analyze_image',
      parameters: {},
      userQuery: 'analyze this image'
    });
    
    console.log('Missing parameter error:', {
      userMessage: missingParamResult.userMessage,
      recoverable: missingParamResult.recoverable,
      suggestionsCount: missingParamResult.suggestions.length
    });
    
    // Test invalid format error
    const formatError = new Error('图片格式不支持，请使用JPEG、PNG或WebP格式');
    const formatResult = ImageToolsErrorHandler.handleToolError(formatError, {
      toolName: 'analyze_image',
      parameters: { imageBase64: 'invalid_format' },
      userQuery: 'analyze this image'
    });
    
    console.log('Format error:', {
      userMessage: formatResult.userMessage,
      recoverable: formatResult.recoverable,
      suggestionsCount: formatResult.suggestions.length
    });
    
    // Test AI service unavailable error
    const serviceError = new Error('AI服务不可用或返回空结果');
    const serviceResult = ImageToolsErrorHandler.handleToolError(serviceError, {
      toolName: 'analyze_image',
      parameters: { imageBase64: 'data:image/jpeg;base64,valid_data' },
      userQuery: 'analyze this image'
    });
    
    console.log('Service unavailable error:', {
      userMessage: serviceResult.userMessage,
      recoverable: serviceResult.recoverable,
      suggestionsCount: serviceResult.suggestions.length
    });
  }
  
  /**
   * Test search tool error handling
   */
  static testSearchToolErrors() {
    console.log('Testing search tool error handling...');
    
    // Test find_similar_images_by_image errors
    const imageNotFoundError = new Error('图片ID test123 不存在');
    const imageNotFoundResult = ImageToolsErrorHandler.handleToolError(imageNotFoundError, {
      toolName: 'find_similar_images_by_image',
      parameters: { imageId: 'test123', limit: 20 },
      userQuery: 'find similar images'
    });
    
    console.log('Image not found error:', {
      userMessage: imageNotFoundResult.userMessage,
      recoverable: imageNotFoundResult.recoverable,
      suggestionsCount: imageNotFoundResult.suggestions.length
    });
    
    // Test find_images_by_tags errors
    const emptyTagsError = new Error('标签列表不能为空');
    const emptyTagsResult = ImageToolsErrorHandler.handleToolError(emptyTagsError, {
      toolName: 'find_images_by_tags',
      parameters: { tags: [] },
      userQuery: 'find images with tags'
    });
    
    console.log('Empty tags error:', {
      userMessage: emptyTagsResult.userMessage,
      recoverable: emptyTagsResult.recoverable,
      suggestionsCount: emptyTagsResult.suggestions.length
    });
    
    // Test find_similar_images_by_description errors
    const emptyDescriptionError = new Error('描述文本不能为空');
    const emptyDescriptionResult = ImageToolsErrorHandler.handleToolError(emptyDescriptionError, {
      toolName: 'find_similar_images_by_description',
      parameters: { description: '' },
      userQuery: 'find images'
    });
    
    console.log('Empty description error:', {
      userMessage: emptyDescriptionResult.userMessage,
      recoverable: emptyDescriptionResult.recoverable,
      suggestionsCount: emptyDescriptionResult.suggestions.length
    });
  }
  
  /**
   * Test parameter validation error handling
   */
  static testParameterValidation() {
    console.log('Testing parameter validation...');
    
    // Test analyze_image parameter validation
    const imageBase64Validation = ImageToolsErrorHandler.handleParameterValidationError(
      'analyze_image',
      'imageBase64',
      'invalid_format',
      'format'
    );
    
    console.log('Image base64 validation:', {
      message: imageBase64Validation.message,
      suggestion: imageBase64Validation.suggestion
    });
    
    // Test threshold validation
    const thresholdValidation = ImageToolsErrorHandler.handleParameterValidationError(
      'find_similar_images_by_image',
      'threshold',
      1.5,
      'range'
    );
    
    console.log('Threshold validation:', {
      message: thresholdValidation.message,
      suggestion: thresholdValidation.suggestion
    });
    
    // Test tags validation
    const tagsValidation = ImageToolsErrorHandler.handleParameterValidationError(
      'find_images_by_tags',
      'tags',
      [],
      'required'
    );
    
    console.log('Tags validation:', {
      message: tagsValidation.message,
      suggestion: tagsValidation.suggestion
    });
  }
  
  /**
   * Test tool result validation
   */
  static testResultValidation() {
    console.log('Testing result validation...');
    
    // Test analyze_image result validation
    const analyzeImageResult = {
      success: true,
      description: 'A beautiful landscape',
      tags: ['landscape', 'nature', 'outdoor'],
      confidence: 0.9,
      structuredData: {
        objects: [{ name: 'tree', confidence: 0.8, attributes: ['green'] }],
        colors: ['green', 'blue'],
        scene: 'outdoor'
      }
    };
    
    const analyzeValidation = ImageToolsErrorHandler.validateToolResult('analyze_image', analyzeImageResult);
    console.log('Analyze image validation:', {
      isValid: analyzeValidation.isValid,
      errorsCount: analyzeValidation.errors.length,
      warningsCount: analyzeValidation.warnings.length
    });
    
    // Test search result validation
    const searchResult = {
      success: true,
      results: [
        { id: '1', url: '/image/1.jpg', title: 'Image 1', description: 'Test image', tags: ['test'] },
        { id: '2', url: '/image/2.jpg', title: 'Image 2', description: 'Another test', tags: ['test'] }
      ],
      total: 2
    };
    
    const searchValidation = ImageToolsErrorHandler.validateToolResult('find_similar_images_by_description', searchResult);
    console.log('Search result validation:', {
      isValid: searchValidation.isValid,
      errorsCount: searchValidation.errors.length,
      warningsCount: searchValidation.warnings.length
    });
    
    // Test invalid result
    const invalidResult = null;
    const invalidValidation = ImageToolsErrorHandler.validateToolResult('analyze_image', invalidResult);
    console.log('Invalid result validation:', {
      isValid: invalidValidation.isValid,
      errorsCount: invalidValidation.errors.length,
      warningsCount: invalidValidation.warnings.length
    });
  }
  
  /**
   * Test alternative tool suggestions
   */
  static testAlternativeToolSuggestions() {
    console.log('Testing alternative tool suggestions...');
    
    // Test analyze_image failure alternatives
    const analyzeAlternatives = ImageToolsRecoverySystem.suggestAlternativeTools(
      'analyze_image',
      { imageBase64: 'data:image/jpeg;base64,test', detailLevel: 'comprehensive' },
      'ANALYSIS_FAILED',
      'analyze this image in detail'
    );
    
    console.log('Analyze image alternatives:', {
      count: analyzeAlternatives.length,
      topSuggestion: analyzeAlternatives[0]?.toolName,
      topConfidence: analyzeAlternatives[0]?.confidence
    });
    
    // Test search failure alternatives
    const searchAlternatives = ImageToolsRecoverySystem.suggestAlternativeTools(
      'find_similar_images_by_description',
      { description: 'beautiful landscape with mountains', threshold: 0.7 },
      'NO_RESULTS',
      'find landscape images'
    );
    
    console.log('Search alternatives:', {
      count: searchAlternatives.length,
      alternatives: searchAlternatives.map((alt: any) => ({ tool: alt.toolName, confidence: alt.confidence }))
    });
    
    // Test tag search failure alternatives
    const tagSearchAlternatives = ImageToolsRecoverySystem.suggestAlternativeTools(
      'find_images_by_tags',
      { tags: ['landscape', 'mountain', 'nature'], matchMode: 'all' },
      'NO_RESULTS',
      'find nature images'
    );
    
    console.log('Tag search alternatives:', {
      count: tagSearchAlternatives.length,
      alternatives: tagSearchAlternatives.map((alt: any) => ({ tool: alt.toolName, confidence: alt.confidence }))
    });
  }
  
  /**
   * Test parameter adjustment recommendations
   */
  static testParameterAdjustments() {
    console.log('Testing parameter adjustments...');
    
    // Test similarity search with no results
    const similarityAdjustments = ImageToolsRecoverySystem.generateParameterAdjustments(
      'find_similar_images_by_image',
      { threshold: 0.8, limit: 20 },
      'NO_RESULTS',
      0
    );
    
    console.log('Similarity adjustments:', {
      count: similarityAdjustments.length,
      adjustments: similarityAdjustments.map((adj: any) => ({
        parameter: adj.parameter,
        from: adj.originalValue,
        to: adj.suggestedValue,
        impact: adj.impact
      }))
    });
    
    // Test tag search adjustments
    const tagAdjustments = ImageToolsRecoverySystem.generateParameterAdjustments(
      'find_images_by_tags',
      { tags: ['landscape', 'mountain', 'nature', 'outdoor', 'scenic'], matchMode: 'all' },
      'NO_RESULTS',
      0
    );
    
    console.log('Tag search adjustments:', {
      count: tagAdjustments.length,
      adjustments: tagAdjustments.map((adj: any) => ({
        parameter: adj.parameter,
        from: adj.originalValue,
        to: adj.suggestedValue,
        impact: adj.impact
      }))
    });
    
    // Test analyze image adjustments
    const analyzeAdjustments = ImageToolsRecoverySystem.generateParameterAdjustments(
      'analyze_image',
      { detailLevel: 'comprehensive', focusAreas: ['objects', 'colors', 'emotions', 'text', 'style'] },
      'TIMEOUT',
      0
    );
    
    console.log('Analyze image adjustments:', {
      count: analyzeAdjustments.length,
      adjustments: analyzeAdjustments.map((adj: any) => ({
        parameter: adj.parameter,
        from: adj.originalValue,
        to: adj.suggestedValue,
        impact: adj.impact
      }))
    });
  }
  
  /**
   * Test troubleshooting guide generation
   */
  static testTroubleshootingGuides() {
    console.log('Testing troubleshooting guides...');
    
    // Test service unavailable guide
    const serviceGuide = ImageToolsRecoverySystem.generateTroubleshootingGuide(
      'analyze_image',
      'SERVICE_UNAVAILABLE',
      'AI service is not available',
      {}
    );
    
    console.log('Service unavailable guide:', {
      stepsCount: serviceGuide.steps.length,
      preventionTipsCount: serviceGuide.preventionTips.length,
      relatedIssuesCount: serviceGuide.relatedIssues.length,
      firstStep: serviceGuide.steps[0]?.action
    });
    
    // Test no results guide
    const noResultsGuide = ImageToolsRecoverySystem.generateTroubleshootingGuide(
      'find_similar_images_by_description',
      'NO_RESULTS',
      'No matching images found',
      { description: 'complex technical description' }
    );
    
    console.log('No results guide:', {
      stepsCount: noResultsGuide.steps.length,
      preventionTipsCount: noResultsGuide.preventionTips.length,
      relatedIssuesCount: noResultsGuide.relatedIssues.length,
      firstStep: noResultsGuide.steps[0]?.action
    });
    
    // Test invalid format guide
    const formatGuide = ImageToolsRecoverySystem.generateTroubleshootingGuide(
      'analyze_image',
      'INVALID_FORMAT',
      'Image format not supported',
      {}
    );
    
    console.log('Invalid format guide:', {
      stepsCount: formatGuide.steps.length,
      preventionTipsCount: formatGuide.preventionTips.length,
      relatedIssuesCount: formatGuide.relatedIssues.length,
      firstStep: formatGuide.steps[0]?.action
    });
  }
  
  /**
   * Test contextual recovery options
   */
  static testContextualRecovery() {
    console.log('Testing contextual recovery options...');
    
    // Test first attempt recovery
    const firstAttempt = ImageToolsErrorHandler.generateContextualRecoveryOptions(
      'analyze_image',
      'ANALYSIS_FAILED',
      { imageBase64: 'data:image/jpeg;base64,test' },
      0
    );
    
    console.log('First attempt recovery:', {
      count: firstAttempt.length,
      suggestions: firstAttempt.map((s: any) => s.action)
    });
    
    // Test second attempt recovery
    const secondAttempt = ImageToolsErrorHandler.generateContextualRecoveryOptions(
      'find_similar_images_by_description',
      'NO_RESULTS',
      { description: 'complex description' },
      1
    );
    
    console.log('Second attempt recovery:', {
      count: secondAttempt.length,
      suggestions: secondAttempt.map((s: any) => s.action)
    });
    
    // Test multiple attempts recovery
    const multipleAttempts = ImageToolsErrorHandler.generateContextualRecoveryOptions(
      'find_images_by_tags',
      'NO_RESULTS',
      { tags: ['rare', 'specific', 'tags'] },
      3
    );
    
    console.log('Multiple attempts recovery:', {
      count: multipleAttempts.length,
      suggestions: multipleAttempts.map((s: any) => s.action)
    });
  }
  
  /**
   * Test environment validation
   */
  static testEnvironmentValidation() {
    console.log('Testing environment validation...');
    
    // Test analyze_image environment
    const analyzeEnv = ImageToolsErrorHandler.validateToolEnvironment('analyze_image');
    console.log('Analyze image environment:', {
      isValid: analyzeEnv.isValid,
      errorsCount: analyzeEnv.errors.length,
      warningsCount: analyzeEnv.warnings.length,
      suggestionsCount: analyzeEnv.suggestions.length
    });
    
    // Test search tool environment
    const searchEnv = ImageToolsErrorHandler.validateToolEnvironment('find_similar_images_by_image');
    console.log('Search tool environment:', {
      isValid: searchEnv.isValid,
      errorsCount: searchEnv.errors.length,
      warningsCount: searchEnv.warnings.length,
      suggestionsCount: searchEnv.suggestions.length
    });
  }
  
  /**
   * Run all tests
   */
  static runAllTests() {
    console.log('=== Running Image Tools Error Handling Test Suite ===\n');
    
    try {
      this.testAnalyzeImageErrors();
      console.log('✓ Analyze image error tests passed\n');
      
      this.testSearchToolErrors();
      console.log('✓ Search tool error tests passed\n');
      
      this.testParameterValidation();
      console.log('✓ Parameter validation tests passed\n');
      
      this.testResultValidation();
      console.log('✓ Result validation tests passed\n');
      
      this.testAlternativeToolSuggestions();
      console.log('✓ Alternative tool suggestion tests passed\n');
      
      this.testParameterAdjustments();
      console.log('✓ Parameter adjustment tests passed\n');
      
      this.testTroubleshootingGuides();
      console.log('✓ Troubleshooting guide tests passed\n');
      
      this.testContextualRecovery();
      console.log('✓ Contextual recovery tests passed\n');
      
      this.testEnvironmentValidation();
      console.log('✓ Environment validation tests passed\n');
      
      console.log('=== All Error Handling Tests Completed Successfully ===');
      
    } catch (error) {
      console.error('❌ Test failed:', error);
      throw error;
    }
  }
}

// Export for use in other modules