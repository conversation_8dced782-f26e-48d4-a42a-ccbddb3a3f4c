/**
 * 图片URL处理工具函数
 * 统一处理本地文件路径转换，参考SimpleImage组件的实现
 */

/**
 * 将本地文件路径转换为正确的文件URL格式
 * @param imagePath 图片路径
 * @param fallbackUrl 备用URL，默认为placeholder
 * @returns 处理后的图片URL
 */
export function getImageUrl(imagePath: string | undefined, fallbackUrl?: string): string {
  if (!imagePath) {
    return fallbackUrl || generateImagePlaceholder();
  }

  // 如果已经是HTTP、HTTPS、data URL或自定义协议URL，直接使用
  if (imagePath.startsWith('http') ||
      imagePath.startsWith('data:') ||
      imagePath.startsWith('app://')) {
    return imagePath;
  }

  // 如果是file://协议，需要转换为app://协议
  if (imagePath.startsWith('file://')) {
    // 移除file://前缀，然后按照本地路径处理
    const localPath = imagePath.replace('file://', '');
    const normalizedPath = localPath
      .replace(/\\/g, '/') // 将反斜杠转换为正斜杠
      .replace(/^([A-Za-z]):/, '$1'); // 移除Windows驱动器的冒号
    return `app://${normalizedPath}`;
  }

  // 将本地文件路径转换为app:// URL格式
  // 例如: C:\Users\<USER>\image.jpg -> app://C/Users/<USER>/image.jpg
  //      /home/<USER>/image.jpg -> app:///home/<USER>/image.jpg
  const normalizedPath = imagePath
    .replace(/\\/g, '/') // 将反斜杠转换为正斜杠
    .replace(/^([A-Za-z]):/, '$1'); // 移除Windows驱动器的冒号

  return `app://${normalizedPath}`;
}

/**
 * 获取缩略图URL（用于列表视图等小尺寸显示）
 * @param imagePath 图片路径
 * @returns 缩略图URL
 */
export function getThumbnailUrl(imagePath: string | undefined): string {
  return getImageUrl(imagePath, generateImagePlaceholder(80, 80));
}

/**
 * 处理图片加载错误的统一函数
 * @param imageUrl 当前图片URL
 * @param originalPath 原始路径
 * @param fallbackUrl 备用URL
 */
export function handleImageError(
  imageUrl: string,
  originalPath: string | undefined,
  fallbackUrl?: string
) {
  console.error('❌ [ImageUrlUtils] 图片加载失败:', imageUrl);
  console.error('❌ [ImageUrlUtils] 原始路径:', originalPath);
  return fallbackUrl || generateImagePlaceholder();
}

/**
 * 处理图片加载成功的统一函数
 * @param imageUrl 成功加载的图片URL
 */
export function handleImageLoad(imageUrl: string) {
  console.log('✅ [ImageUrlUtils] 图片加载成功:', imageUrl);
}

/**
 * 验证图片URL是否有效
 * @param imageUrl 图片URL
 * @returns 是否有效
 */
export function isValidImageUrl(imageUrl: string): boolean {
  if (!imageUrl) return false;
  
  // 检查是否为有效的URL格式
  try {
    new URL(imageUrl);
    return true;
  } catch {
    // 检查是否为本地路径格式
    return /^[A-Za-z]:[\\\/]/.test(imageUrl) || imageUrl.startsWith('/');
  }
}

/**
 * 从图片数据对象中提取最合适的图片路径
 * @param imageData 图片数据对象
 * @returns 图片路径
 */
export function extractImagePath(imageData: any): string | undefined {
  // 尝试多个可能的路径字段
  return imageData?.url ||
         imageData?.imagePath ||
         imageData?.path ||
         imageData?.src ||
         imageData?.thumbnail ||
         undefined;
}

/**
 * 生成图片占位符URL
 * @param width 宽度
 * @param height 高度
 * @returns SVG占位符URL
 */
export function generateImagePlaceholder(width: number = 400, height: number = 400): string {
  const svg = `
    <svg width="${width}" height="${height}" viewBox="0 0 ${width} ${height}" fill="none" xmlns="http://www.w3.org/2000/svg">
      <rect width="${width}" height="${height}" fill="#F3F4F6"/>
      <circle cx="${width/2}" cy="${height*0.4}" r="${Math.min(width, height)*0.1}" fill="#9CA3AF"/>
      <path d="M${width*0.3} ${height*0.6}L${width*0.5} ${height*0.75}L${width*0.7} ${height*0.6}V${height*0.8}H${width*0.3}V${height*0.6}Z" fill="#9CA3AF"/>
    </svg>
  `

  return `data:image/svg+xml;base64,${btoa(svg)}`
}

/**
 * 处理时间戳转换
 * @param timestamp Unix时间戳（可能是秒或毫秒）
 * @param fallbackTimestamp 备用时间戳
 * @returns Date对象
 */
export function convertTimestampToDate(
  timestamp?: number,
  fallbackTimestamp?: number
): Date {
  let targetTimestamp = timestamp || fallbackTimestamp || Date.now()

  // 如果时间戳小于10位数，认为是秒级时间戳，需要转换为毫秒
  if (targetTimestamp < 1e10) {
    targetTimestamp = targetTimestamp * 1000
  }

  const date = new Date(targetTimestamp)

  // 检查日期是否有效
  if (isNaN(date.getTime())) {
    return new Date() // 返回当前时间作为fallback
  }

  return date
}