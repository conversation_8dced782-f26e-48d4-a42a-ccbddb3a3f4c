import { VLChatService, ToolDefinition } from '../../electron/ai/VLChatService';
import { ToolDiscovery, ToolAutoRegistration, ToolDiscoveryConfig, ToolDiscoveryResult } from './toolDiscovery';
import { VLImageToolsBridge } from './vlImageToolsBridge';
import { toolConfigManager, ToolConfig } from './toolConfiguration';
import { toolMonitoring, ToolPerformanceStats, PerformanceAlert } from './toolMonitoring';

/**
 * Tool loader configuration
 */
export interface ToolLoaderConfig extends ToolDiscoveryConfig {
  enableAutoInitialization?: boolean;
  enablePerformanceMonitoring?: boolean;
  enableHealthChecks?: boolean;
  healthCheckInterval?: number;
  maxRetryAttempts?: number;
  retryBackoffMultiplier?: number;
}

/**
 * Tool loading result
 */
export interface ToolLoadingResult {
  discoveryResult: ToolDiscoveryResult;
  loadedTools: ToolDefinition[];
  registeredTools: ToolDefinition[];
  failedTools: string[];
  loadingTime: number;
  success: boolean;
}

/**
 * Tool health status
 */
export interface ToolHealthStatus {
  toolName: string;
  healthy: boolean;
  lastCheck: Date;
  responseTime?: number;
  error?: string;
}

/**
 * Tool performance metrics
 */
export interface ToolPerformanceMetrics {
  toolName: string;
  totalCalls: number;
  successfulCalls: number;
  failedCalls: number;
  averageResponseTime: number;
  lastCallTime?: Date;
  errorRate: number;
}

/**
 * Tool loader class for automatic tool discovery and registration with VL Chat Service
 * Provides comprehensive tool management including health monitoring and performance tracking
 */
export class ToolLoader {
  private config: Required<ToolLoaderConfig>;
  private vlChatService: VLChatService | null = null;
  private autoRegistration: ToolAutoRegistration;
  private loadedTools: Map<string, ToolDefinition> = new Map();
  private toolHealth: Map<string, ToolHealthStatus> = new Map();
  private performanceMetrics: Map<string, ToolPerformanceMetrics> = new Map();
  private healthCheckInterval: NodeJS.Timeout | null = null;
  private initialized: boolean = false;

  constructor(config: ToolLoaderConfig = {}) {
    this.config = {
      toolsDirectory: config.toolsDirectory || 'tools',
      enableValidation: config.enableValidation !== false,
      enableCompatibilityCheck: config.enableCompatibilityCheck !== false,
      autoRegister: config.autoRegister !== false,
      retryAttempts: config.retryAttempts || 3,
      retryDelay: config.retryDelay || 1000,
      enableAutoInitialization: config.enableAutoInitialization !== false,
      enablePerformanceMonitoring: config.enablePerformanceMonitoring !== false,
      enableHealthChecks: config.enableHealthChecks !== false,
      healthCheckInterval: config.healthCheckInterval || 60000, // 1 minute
      maxRetryAttempts: config.maxRetryAttempts || 5,
      retryBackoffMultiplier: config.retryBackoffMultiplier || 1.5
    };

    this.autoRegistration = new ToolAutoRegistration(this.config);
  }

  /**
   * Initialize tool loader with VL Chat Service
   */
  async initialize(vlChatService: VLChatService): Promise<ToolLoadingResult> {
    const startTime = Date.now();
    console.log('Initializing ToolLoader with VL Chat Service...');

    try {
      this.vlChatService = vlChatService;

      // Discover and prepare tools for registration
      const autoRegResult = await this.autoRegistration.discoverAndRegister();
      const toolsToRegister = autoRegResult.registeredTools;

      // Register tools with VL Chat Service
      const registrationResult = await this.registerToolsWithService(toolsToRegister);

      // Update loaded tools
      registrationResult.successful.forEach((tool: any) => {
        this.loadedTools.set(tool.function.name, tool);
        this.initializeToolMetrics(tool.function.name);
      });

      // Start health checks if enabled
      if (this.config.enableHealthChecks) {
        this.startHealthChecks();
      }

      const loadingTime = Date.now() - startTime;
      const success = registrationResult.successful.length > 0;
      this.initialized = success;

      const result: ToolLoadingResult = {
        discoveryResult: autoRegResult.discoveryResult,
        loadedTools: registrationResult.successful,
        registeredTools: registrationResult.successful,
        failedTools: registrationResult.failed.map((f: any) => f.toolName),
        loadingTime,
        success
      };

      this.logLoadingResults(result);
      return result;

    } catch (error) {
      console.error('ToolLoader initialization failed:', error);
      
      const loadingTime = Date.now() - startTime;
      return {
        discoveryResult: {
          discovered: [],
          validated: [],
          compatible: [],
          registered: [],
          errors: [{
            toolName: 'system',
            stage: 'registration',
            error: error instanceof Error ? error.message : String(error)
          }],
          discoveryTime: loadingTime
        },
        loadedTools: [],
        registeredTools: [],
        failedTools: [],
        loadingTime,
        success: false
      };
    }
  }

  /**
   * Register tools with VL Chat Service
   */
  private async registerToolsWithService(tools: ToolDefinition[]): Promise<{
    successful: ToolDefinition[];
    failed: Array<{ toolName: string; error: string }>;
  }> {
    const successful: ToolDefinition[] = [];
    const failed: Array<{ toolName: string; error: string }> = [];

    if (!this.vlChatService) {
      throw new Error('VL Chat Service not initialized');
    }

    console.log(`Registering ${tools.length} tools with VL Chat Service...`);

    for (const tool of tools) {
      const toolName = tool.function.name;
      
      // Check if tool is enabled in configuration
      if (!toolConfigManager.isToolEnabled(toolName)) {
        console.log(`⚠ Tool ${toolName} is disabled in configuration, skipping registration`);
        continue;
      }

      let attempts = 0;
      let registered = false;
      let lastError: Error | null = null;

      while (attempts < this.config.maxRetryAttempts && !registered) {
        try {
          // Log registration attempt
          toolMonitoring.log('info', toolName, `Attempting registration (attempt ${attempts + 1})`);

          // Register tool with VL Chat Service
          this.vlChatService.registerTool(tool);
          successful.push(tool);
          registered = true;
          
          // Log successful registration
          toolMonitoring.log('info', toolName, 'Tool registered successfully');
          console.log(`✓ Registered tool: ${toolName}`);

        } catch (error) {
          lastError = error instanceof Error ? error : new Error(String(error));
          attempts++;

          // Log registration failure
          toolMonitoring.log('warn', toolName, `Registration attempt ${attempts} failed: ${lastError.message}`);

          if (attempts < this.config.maxRetryAttempts) {
            const delay = this.config.retryDelay * Math.pow(this.config.retryBackoffMultiplier, attempts - 1);
            console.warn(`⚠ Tool registration attempt ${attempts} failed for ${toolName}, retrying in ${delay}ms...`);
            await this.delay(delay);
          }
        }
      }

      if (!registered) {
        const errorMessage = lastError?.message || 'Unknown registration error';
        failed.push({
          toolName,
          error: errorMessage
        });
        
        // Log final failure
        toolMonitoring.log('error', toolName, `Registration failed after ${this.config.maxRetryAttempts} attempts: ${errorMessage}`);
        console.error(`✗ Failed to register tool ${toolName} after ${this.config.maxRetryAttempts} attempts:`, errorMessage);
      }
    }

    console.log(`Registration completed: ${successful.length} successful, ${failed.length} failed`);
    return { successful, failed };
  }

  /**
   * Initialize performance metrics for a tool
   */
  private initializeToolMetrics(toolName: string): void {
    if (!this.performanceMetrics.has(toolName)) {
      this.performanceMetrics.set(toolName, {
        toolName,
        totalCalls: 0,
        successfulCalls: 0,
        failedCalls: 0,
        averageResponseTime: 0,
        errorRate: 0
      });
    }

    if (!this.toolHealth.has(toolName)) {
      this.toolHealth.set(toolName, {
        toolName,
        healthy: true,
        lastCheck: new Date()
      });
    }
  }

  /**
   * Start health checks for loaded tools
   */
  private startHealthChecks(): void {
    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval);
    }

    this.healthCheckInterval = setInterval(async () => {
      await this.performHealthChecks();
    }, this.config.healthCheckInterval);

    console.log(`Started health checks with ${this.config.healthCheckInterval}ms interval`);
  }

  /**
   * Perform health checks on all loaded tools
   */
  private async performHealthChecks(): Promise<void> {
    if (!this.config.enableHealthChecks || this.loadedTools.size === 0) {
      return;
    }

    console.log('Performing tool health checks...');

    this.loadedTools.forEach((_, toolName) => {
      try {
        const startTime = Date.now();
        
        // Perform basic validation check
        const validation = VLImageToolsBridge.validateToolParameters(toolName, {});
        const responseTime = Date.now() - startTime;
        
        // Update health status
        this.toolHealth.set(toolName, {
          toolName,
          healthy: true, // Basic validation passing means tool is accessible
          lastCheck: new Date(),
          responseTime
        });

      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        
        this.toolHealth.set(toolName, {
          toolName,
          healthy: false,
          lastCheck: new Date(),
          error: errorMessage
        });

        console.warn(`⚠ Health check failed for tool ${toolName}:`, errorMessage);
      }
    });
  }

  /**
   * Stop health checks
   */
  private stopHealthChecks(): void {
    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval);
      this.healthCheckInterval = null;
      console.log('Stopped health checks');
    }
  }

  /**
   * Update performance metrics for a tool call
   */
  updateToolMetrics(toolName: string, success: boolean, responseTime: number): void {
    if (!this.config.enablePerformanceMonitoring) {
      return;
    }

    const metrics = this.performanceMetrics.get(toolName);
    if (!metrics) {
      return;
    }

    // Update metrics
    metrics.totalCalls++;
    metrics.lastCallTime = new Date();

    if (success) {
      metrics.successfulCalls++;
    } else {
      metrics.failedCalls++;
    }

    // Update average response time
    const totalResponseTime = metrics.averageResponseTime * (metrics.totalCalls - 1) + responseTime;
    metrics.averageResponseTime = totalResponseTime / metrics.totalCalls;

    // Update error rate
    metrics.errorRate = (metrics.failedCalls / metrics.totalCalls) * 100;

    this.performanceMetrics.set(toolName, metrics);
  }

  /**
   * Get tool health status
   */
  getToolHealth(toolName?: string): ToolHealthStatus[] {
    if (toolName) {
      const health = this.toolHealth.get(toolName);
      return health ? [health] : [];
    }

    return Array.from(this.toolHealth.values());
  }

  /**
   * Get tool performance metrics
   */
  getToolMetrics(toolName?: string): ToolPerformanceMetrics[] {
    if (toolName) {
      const metrics = this.performanceMetrics.get(toolName);
      return metrics ? [metrics] : [];
    }

    return Array.from(this.performanceMetrics.values());
  }

  /**
   * Get overall system health
   */
  getSystemHealth(): {
    totalTools: number;
    healthyTools: number;
    unhealthyTools: number;
    healthPercentage: number;
    lastHealthCheck: Date | null;
  } {
    const healthStatuses = Array.from(this.toolHealth.values());
    const totalTools = healthStatuses.length;
    const healthyTools = healthStatuses.filter((h: any) => h.healthy).length;
    const unhealthyTools = totalTools - healthyTools;
    const healthPercentage = totalTools > 0 ? (healthyTools / totalTools) * 100 : 0;
    
    const lastHealthCheck = healthStatuses.length > 0 
      ? new Date(Math.max(...healthStatuses.map((h: any) => h.lastCheck.getTime())))
      : null;

    return {
      totalTools,
      healthyTools,
      unhealthyTools,
      healthPercentage,
      lastHealthCheck
    };
  }

  /**
   * Get loaded tools
   */
  getLoadedTools(): ToolDefinition[] {
    return Array.from(this.loadedTools.values());
  }

  /**
   * Check if a tool is loaded
   */
  isToolLoaded(toolName: string): boolean {
    return this.loadedTools.has(toolName);
  }

  /**
   * Get tool by name
   */
  getTool(toolName: string): ToolDefinition | undefined {
    return this.loadedTools.get(toolName);
  }

  /**
   * Reload a specific tool
   */
  async reloadTool(toolName: string): Promise<boolean> {
    if (!this.vlChatService) {
      console.error('VL Chat Service not available for tool reload');
      return false;
    }

    try {
      console.log(`Reloading tool: ${toolName}`);

      // Get fresh tool definition
      const toolDefinitions = VLImageToolsBridge.getToolDefinitions();
      const toolDef = toolDefinitions.find((t: any) => t.function.name === toolName);

      if (!toolDef) {
        console.error(`Tool ${toolName} not found in available tools`);
        return false;
      }

      // Unregister existing tool
      this.vlChatService.unregisterTool(toolName);
      this.loadedTools.delete(toolName);

      // Re-register tool
      this.vlChatService.registerTool(toolDef);
      this.loadedTools.set(toolName, toolDef);
      this.initializeToolMetrics(toolName);

      console.log(`✓ Tool ${toolName} reloaded successfully`);
      return true;

    } catch (error) {
      console.error(`Failed to reload tool ${toolName}:`, error);
      return false;
    }
  }

  /**
   * Reload all tools
   */
  async reloadAllTools(): Promise<{ successful: string[]; failed: string[] }> {
    const successful: string[] = [];
    const failed: string[] = [];

    const toolNames = Array.from(this.loadedTools.keys());

    for (const toolName of toolNames) {
      const reloaded = await this.reloadTool(toolName);
      if (reloaded) {
        successful.push(toolName);
      } else {
        failed.push(toolName);
      }
    }

    console.log(`Tool reload completed: ${successful.length} successful, ${failed.length} failed`);
    return { successful, failed };
  }

  /**
   * Check if tool loader is initialized
   */
  isInitialized(): boolean {
    return this.initialized;
  }

  /**
   * Get initialization status
   */
  getStatus(): {
    initialized: boolean;
    toolsLoaded: number;
    healthChecksEnabled: boolean;
    performanceMonitoringEnabled: boolean;
    lastHealthCheck: Date | null;
  } {
    const systemHealth = this.getSystemHealth();

    return {
      initialized: this.initialized,
      toolsLoaded: this.loadedTools.size,
      healthChecksEnabled: this.config.enableHealthChecks,
      performanceMonitoringEnabled: this.config.enablePerformanceMonitoring,
      lastHealthCheck: systemHealth.lastHealthCheck
    };
  }

  /**
   * Cleanup resources
   */
  cleanup(): void {
    this.stopHealthChecks();
    this.loadedTools.clear();
    this.toolHealth.clear();
    this.performanceMetrics.clear();
    this.initialized = false;
    console.log('ToolLoader cleanup completed');
  }

  /**
   * Delay utility
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Log loading results
   */
  private logLoadingResults(result: ToolLoadingResult): void {
    console.log('\n=== Tool Loading Results ===');
    console.log(`Success: ${result.success}`);
    console.log(`Loaded tools: ${result.loadedTools.length}`);
    console.log(`Registered tools: ${result.registeredTools.length}`);
    console.log(`Failed tools: ${result.failedTools.length}`);
    console.log(`Loading time: ${result.loadingTime}ms`);

    if (result.failedTools.length > 0) {
      console.log(`Failed tools: ${result.failedTools.join(', ')}`);
    }

    if (result.loadedTools.length > 0) {
      console.log('\nLoaded tools:');
      result.loadedTools.forEach((tool: any) => {
        console.log(`  - ${tool.function.name}: ${tool.function.description}`);
      });
    }

    console.log('============================\n');
  }
}

/**
 * Global tool loader instance
 */
let globalToolLoader: ToolLoader | null = null;

/**
 * Get or create global tool loader instance
 */
export function getToolLoader(config?: ToolLoaderConfig): ToolLoader {
  if (!globalToolLoader) {
    globalToolLoader = new ToolLoader(config);
  }
  return globalToolLoader;
}

/**
 * Initialize tools with VL Chat Service
 */
export async function initializeToolsWithVLChatService(
  vlChatService: VLChatService,
  config?: ToolLoaderConfig
): Promise<ToolLoadingResult> {
  const toolLoader = getToolLoader(config);
  return await toolLoader.initialize(vlChatService);
}

/**
 * Quick initialization function for common use cases
 */
export async function quickInitializeTools(
  vlChatService: VLChatService
): Promise<boolean> {
  try {
    const result = await initializeToolsWithVLChatService(vlChatService, {
      enableValidation: true,
      enableCompatibilityCheck: true,
      autoRegister: true,
      enableHealthChecks: false, // Disable for quick init
      enablePerformanceMonitoring: false,
      retryAttempts: 2,
      retryDelay: 500
    });

    return result.success;
  } catch (error) {
    console.error('Quick tool initialization failed:', error);
    return false;
  }
}

/**
 * Advanced initialization with full monitoring
 */
export async function advancedInitializeTools(
  vlChatService: VLChatService,
  config?: ToolLoaderConfig
): Promise<ToolLoadingResult> {
  const fullConfig: ToolLoaderConfig = {
    enableValidation: true,
    enableCompatibilityCheck: true,
    autoRegister: true,
    enableHealthChecks: true,
    enablePerformanceMonitoring: true,
    healthCheckInterval: 30000, // 30 seconds
    retryAttempts: 3,
    retryDelay: 1000,
    maxRetryAttempts: 5,
    retryBackoffMultiplier: 1.5,
    ...config
  };

  return await initializeToolsWithVLChatService(vlChatService, fullConfig);
}