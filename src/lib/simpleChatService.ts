// import OpenAI from 'openai';
// import { PathBasedImageToolsBridge } from './pathBasedImageToolsBridge';
// import { ToolCallSession } from './toolCallStateManager';
// import { Tool, ToolCall, ToolCallResponse } from '../types/tools';
//
// // ReAct步骤接口
// export interface ReActStep {
//   id: string;
//   type: 'thought' | 'action' | 'observation' | 'final';
//   content: string;
//   timestamp: Date;
//   status: 'pending' | 'active' | 'completed' | 'error';
//   toolName?: string;
//   toolParams?: Record<string, unknown>;
//   toolResult?: any;
//   executionTime?: number;
// }
//
// export interface SimpleChatConfig {
//   baseURL: string;
//   apiKey: string;
//   chatModel: string; // 专用聊天模型
//   timeout?: number;
//   maxRetries?: number;
// }
//
// export interface ChatMessage {
//   role: 'user' | 'assistant' | 'system' | 'tool';
//   content: string;
//   tool_call_id?: string;
//   tool_calls?: OpenAI.Chat.Completions.ChatCompletionMessageToolCall[];
// }
//
// export interface SimpleChatResponse {
//   content: string;
//   toolCalls?: ToolCall[];
//   toolResults?: ToolCallResponse[];
//   toolCallSession?: ToolCallSession;
//   reactSteps?: ReActStep[]; // 新增：ReAct步骤
//   error?: string;
// }
//
// // 新增流式响应接口
// export interface StreamingChatResponse {
//   content: string;
//   isComplete: boolean;
//   toolCalls?: any[]; // 改为any[]以避免类型冲突
//   toolResults?: ToolCallResponse[];
//   toolCallSession?: ToolCallSession;
//   reactSteps?: ReActStep[]; // 新增：ReAct步骤
//   currentReActStep?: ReActStep; // 新增：当前ReAct步骤
//   error?: string;
// }
//
// // ReAct流式回调函数类型
// export type ReActStepCallback = (step: ReActStep) => void;
//
// // 流式回调函数类型
// export type StreamingCallback = (response: StreamingChatResponse) => void;
//
// /**
//  * 简单聊天服务 - 支持纯文本对话和工具调用
//  */
// export class SimpleChatService {
//   private chatClient: OpenAI;
//   private config: SimpleChatConfig;
//   private conversationHistory: ChatMessage[] = [];
//   private reActStepCallback?: ReActStepCallback; // 新增：ReAct步骤回调
//
//   constructor(config: SimpleChatConfig) {
//     this.config = config;
//
//     console.log('SimpleChatService 配置信息:');
//     console.log('Base URL:', config.baseURL);
//     console.log('Chat Model:', config.chatModel);
//
//     // 聊天客户端
//     this.chatClient = new OpenAI({
//       baseURL: config.baseURL,
//       apiKey: config.apiKey,
//       timeout: config.timeout || 60000,
//       maxRetries: config.maxRetries || 3,
//       dangerouslyAllowBrowser: true,
//     });
//
//     this.initializeSystemPrompt();
//   }
//
//   /**
//    * 设置ReAct步骤回调
//    */
//   setReActStepCallback(callback: ReActStepCallback): void {
//     this.reActStepCallback = callback;
//   }
//
//   /**
//    * 触发ReAct步骤回调
//    */
//   private triggerReActStep(step: ReActStep): void {
//     if (this.reActStepCallback) {
//       this.reActStepCallback(step);
//     }
//   }
//
//   /**
//    * 发送图片路径消息
//    */
//   async sendImageMessageByPath(imagePath: string, message?: string): Promise<SimpleChatResponse> {
//     try {
//       const enhancedMessage = message || '请分析这张图片，并提供详细的描述和分析。';
//
//       const userMessage = `我有一张图片需要分析，图片路径是: ${imagePath}
//
// 用户的问题或要求: ${enhancedMessage}
//
// 请使用ReAct格式思考并分析这张图片。`;
//
//       this.conversationHistory.push({
//         role: 'user',
//         content: userMessage
//       });
//
//       const toolResults: ToolCallResponse[] = [];
//       const reactSteps: ReActStep[] = [];
//       const maxIterations = 5;
//       let iteration = 0;
//
//       while (iteration < maxIterations) {
//         iteration++;
//         console.log(`🔄 sendImageMessageByPath - 第${iteration}轮迭代开始`);
//
//         const requestOptions: OpenAI.Chat.Completions.ChatCompletionCreateParams = {
//           model: this.config.chatModel,
//           messages: this.conversationHistory as any,
//           stream: false,
//           temperature: 0.7,
//           max_tokens: 1000,
//         };
//
//         const response = await this.chatClient.chat.completions.create(requestOptions);
//         const choice = response.choices[0];
//
//         if (!choice) {
//           throw new Error('聊天模型返回空结果');
//         }
//
//         const assistantMessage = choice.message.content || '';
//         console.log('🔍 sendImageMessageByPath - 模型回复:', assistantMessage);
//
//         // 解析ReAct输出
//         const parsed = this.parseReActOutput(assistantMessage);
//         console.log('📊 sendImageMessageByPath - 解析结果:', {
//           hasAction: parsed.hasAction,
//           actionTool: parsed.action?.tool,
//           actionParams: parsed.action?.parameters,
//           finalAnswer: parsed.finalAnswer ? parsed.finalAnswer.substring(0, 100) + '...' : undefined,
//           thoughtsCount: parsed.thoughts.length
//         });
//
//         // 处理思考步骤
//         if (parsed.thoughts.length > 0) {
//           for (const thought of parsed.thoughts) {
//             const thoughtStep: ReActStep = {
//               id: `thought_${Date.now()}_${Math.random()}`,
//               type: 'thought',
//               content: thought,
//               timestamp: new Date(),
//               status: 'completed'
//             };
//             reactSteps.push(thoughtStep);
//             this.triggerReActStep(thoughtStep);
//           }
//         }
//
//         // 添加助手消息到历史
//         this.conversationHistory.push({
//           role: 'assistant',
//           content: assistantMessage
//         });
//
//         // 如果有最终答案，返回结果
//         if (parsed.finalAnswer) {
//           console.log('✅ sendImageMessageByPath - 找到Final Answer，返回结果');
//           const finalStep: ReActStep = {
//             id: `final_${Date.now()}`,
//             type: 'final',
//             content: parsed.finalAnswer,
//             timestamp: new Date(),
//             status: 'completed'
//           };
//           reactSteps.push(finalStep);
//           this.triggerReActStep(finalStep);
//
//           return {
//             content: parsed.finalAnswer,
//             toolResults: toolResults.length > 0 ? toolResults : undefined,
//             reactSteps
//           };
//         }
//
//         // 如果有动作需要执行
//         if (parsed.hasAction && parsed.action) {
//           console.log('🛠️ sendImageMessageByPath - 准备执行工具调用');
//           console.log('工具名:', parsed.action.tool);
//           console.log('参数:', parsed.action.parameters);
//
//           // 创建动作步骤
//           const actionStep: ReActStep = {
//             id: `action_${Date.now()}_${iteration}`,
//             type: 'action',
//             content: `调用工具: ${parsed.action.tool}`,
//             timestamp: new Date(),
//             status: 'active',
//             toolName: parsed.action.tool,
//             toolParams: parsed.action.parameters
//           };
//           reactSteps.push(actionStep);
//           this.triggerReActStep(actionStep);
//
//           try {
//             console.log('🚀 sendImageMessageByPath - 开始执行工具...');
//             const startTime = Date.now();
//
//             // 执行工具
//             const result = await PathBasedImageToolsBridge.executeTool(
//               parsed.action.tool,
//               parsed.action.parameters
//             );
//
//             const executionTime = Date.now() - startTime;
//             console.log('✅ sendImageMessageByPath - 工具执行成功，结果:', result);
//
//             // 更新动作步骤状态
//             actionStep.status = 'completed';
//             actionStep.executionTime = executionTime;
//             actionStep.toolResult = result.data || result;
//             this.triggerReActStep(actionStep);
//
//             const toolResponse: ToolCallResponse = {
//               toolCallId: `react_${Date.now()}_${iteration}`,
//               result: result.data || result
//             };
//
//             toolResults.push(toolResponse);
//
//             // 创建观察步骤
//             const observationStep: ReActStep = {
//               id: `observation_${Date.now()}_${iteration}`,
//               type: 'observation',
//               content: `观察到的结果: ${typeof result.data === 'string' ? result.data : JSON.stringify(result.data || result)}`,
//               timestamp: new Date(),
//               status: 'completed',
//               toolResult: result.data || result
//             };
//             reactSteps.push(observationStep);
//             this.triggerReActStep(observationStep);
//
//             // 添加Observation到对话历史
//             const observation = `Observation: ${JSON.stringify(result.data || result)}`;
//             console.log('📝 sendImageMessageByPath - 添加Observation到历史:', observation.substring(0, 200) + '...');
//
//             this.conversationHistory.push({
//               role: 'assistant',
//               content: observation
//             });
//
//           } catch (error) {
//             console.error('❌ sendImageMessageByPath - 工具执行失败:', error);
//
//             // 更新动作步骤状态为错误
//             actionStep.status = 'error';
//             this.triggerReActStep(actionStep);
//
//             // 工具执行失败，添加错误信息
//             const errorObservation = `Observation: 工具执行失败: ${error instanceof Error ? error.message : String(error)}`;
//             this.conversationHistory.push({
//               role: 'assistant',
//               content: errorObservation
//             });
//
//             // 创建错误观察步骤
//             const errorObservationStep: ReActStep = {
//               id: `error_observation_${Date.now()}_${iteration}`,
//               type: 'observation',
//               content: `错误: ${error instanceof Error ? error.message : String(error)}`,
//               timestamp: new Date(),
//               status: 'error'
//             };
//             reactSteps.push(errorObservationStep);
//             this.triggerReActStep(errorObservationStep);
//           }
//
//           // 继续下一轮循环
//           console.log('🔄 sendImageMessageByPath - 继续下一轮ReAct循环');
//           continue;
//         }
//
//         // 如果既没有Final Answer也没有Action，返回原始内容
//         console.log('⚠️ sendImageMessageByPath - 既没有Final Answer也没有Action，返回原始内容');
//         return {
//           content: assistantMessage,
//           toolResults: toolResults.length > 0 ? toolResults : undefined,
//           reactSteps
//         };
//       }
//
//       // 达到最大迭代次数
//       console.log('⏰ sendImageMessageByPath - 达到最大迭代次数');
//       return {
//         content: '抱歉，处理过程中达到了最大迭代次数限制。',
//         error: '达到最大迭代次数限制',
//         reactSteps
//       };
//
//     } catch (error) {
//       console.error('SimpleChatService: 图片路径消息处理失败:', error);
//       return {
//         content: '抱歉，图片分析失败，请稍后再试。',
//         error: error instanceof Error ? error.message : String(error)
//       };
//     }
//   }
//
//   /**
//    * 初始化系统提示
//    */
//   private initializeSystemPrompt(): void {
//     this.conversationHistory = [{
//       role: 'system',
//       content: `你是一个AI助理，可以分析图片和搜索相似图片。
//
// 使用工具的格式：
// Thought: [你的思考]
// Action: 工具名{"参数": "值"}
//
// 重要：
// - Action后立即停止输出，不要写任何其他内容
// - 不要写Observation，系统会自动提供
// - 不要写Final Answer，等系统给出结果后再写
//
// 工具列表：
// - analyze_image_by_path: 分析图片 {"imagePath": "路径", "includeStructuredData": true}
// - find_similar_images_by_image: 找相似图片 {"imagePath": "路径", "limit": 10}
// - find_similar_images_by_description: 按描述找图片 {"description": "描述", "limit": 10}
// - find_images_by_tags: 按标签找图片 {"tags": ["标签"], "limit": 10}
// - get_image_analysis: 获取图片分析 {"imagePath": "路径"}`
//     }];
//   }
//
//   /**
//    * 发送消息 - 支持ReAct模式
//    */
//   async sendMessage(message: string): Promise<SimpleChatResponse> {
//     try {
//       // 添加用户消息到历史
//       this.conversationHistory.push({
//         role: 'user',
//         content: message
//       });
//
//       const toolResults: ToolCallResponse[] = [];
//       const maxIterations = 5; // 防止无限循环
//       let iteration = 0;
//
//       while (iteration < maxIterations) {
//         iteration++;
//
//         // 获取模型回复
//         const response = await this.chatClient.chat.completions.create({
//           model: this.config.chatModel,
//           messages: this.conversationHistory as any,
//           stream: false,
//           temperature: 0.7,
//           max_tokens: 1000,
//         });
//
//         const choice = response.choices[0];
//         if (!choice) {
//           throw new Error('聊天模型返回空结果');
//         }
//
//         const assistantContent = choice.message.content || '';
//
//         // 解析ReAct输出
//         const parsed = this.parseReActOutput(assistantContent);
//
//         // 调试日志
//         console.log('ReAct解析结果:', {
//           hasAction: parsed.hasAction,
//           action: parsed.action,
//           finalAnswer: parsed.finalAnswer,
//           assistantContent: assistantContent.substring(0, 200) + '...'
//         });
//
//         // 添加助手消息到历史
//         this.conversationHistory.push({
//           role: 'assistant',
//           content: assistantContent
//         });
//
//         // 如果有最终答案，返回结果
//         if (parsed.finalAnswer) {
//           return {
//             content: parsed.finalAnswer,
//             toolResults: toolResults.length > 0 ? toolResults : undefined
//           };
//         }
//
//         // 如果有动作需要执行
//         if (parsed.hasAction && parsed.action) {
//           try {
//             // 执行工具
//             const result = await PathBasedImageToolsBridge.executeTool(
//               parsed.action.tool,
//               parsed.action.parameters
//             );
//
//             const toolResponse: ToolCallResponse = {
//               toolCallId: `react_${Date.now()}_${iteration}`,
//               result: result.data || result
//             };
//
//             toolResults.push(toolResponse);
//
//             // 添加Observation到对话历史
//             const observation = `Observation: ${JSON.stringify(result.data || result)}`;
//             this.conversationHistory.push({
//               role: 'assistant',  // 保持为assistant角色以符合对话流
//               content: observation
//             });
//
//           } catch (error) {
//             // 工具执行失败，添加错误信息
//             const errorObservation = `Observation: 工具执行失败: ${error instanceof Error ? error.message : String(error)}`;
//             this.conversationHistory.push({
//               role: 'assistant',
//               content: errorObservation
//             });
//           }
//
//           // 继续下一轮循环
//           continue;
//         }
//
//         // 如果既没有Final Answer也没有Action，返回原始内容
//         return {
//           content: assistantContent,
//           toolResults: toolResults.length > 0 ? toolResults : undefined
//         };
//       }
//
//       // 达到最大迭代次数
//       return {
//         content: '抱歉，处理过程中达到了最大迭代次数限制。',
//         error: '达到最大迭代次数限制'
//       };
//
//     } catch (error) {
//       console.error('SimpleChatService: ReAct模式发送消息失败:', error);
//       return {
//         content: '抱歉，我遇到了一些问题，请稍后再试。',
//         error: error instanceof Error ? error.message : String(error)
//       };
//     }
//   }
//
//   /**
//    * 发送图片消息
//    */
//   async sendImageMessage(imageBase64: string, message?: string): Promise<SimpleChatResponse> {
//     try {
//       // 图片消息处理
//       const enhancedMessage = message || '请分析这张图片，并提供详细的描述和分析。';
//
//       // 首先保存图片到临时文件以便工具使用
//       const tempFileName = `temp_chat_${Date.now()}.jpg`;
//       const saveResult = await window.electronAPI?.fileSystem.saveImage({
//         base64Data: imageBase64,
//         filename: tempFileName
//       });
//
//       if (!saveResult?.success || !saveResult.filePath) {
//         throw new Error('保存图片到临时文件失败');
//       }
//
//       // 使用analyze_image_by_path工具分析图片
//       const analyzeResult = await PathBasedImageToolsBridge.executeTool('analyze_image_by_path', {
//         imagePath: saveResult.filePath,
//         includeStructuredData: true
//       });
//
//       if (analyzeResult.success) {
//         // 构建分析结果的回复
//         const analysisData = analyzeResult.data;
//         const responseContent = `我已经分析了这张图片。以下是详细分析结果：
//
// **图片描述**: ${analysisData.description || '无法获取描述'}
//
// **检测到的标签**: ${analysisData.tags?.join(', ') || '无标签'}
//
// **分析置信度**: ${(analysisData.confidence * 100).toFixed(1)}%
//
// ${enhancedMessage !== '请分析这张图片，并提供详细的描述和分析。' ? `\n**关于您的问题"${enhancedMessage}"**: 根据图片分析结果，${analysisData.description}` : ''}
//
// 您还可以问我关于这张图片的其他问题，比如寻找相似图片或者更详细的分析。`;
//
//         // 添加到对话历史
//         this.conversationHistory.push({
//           role: 'user',
//           content: `[图片] ${enhancedMessage}`
//         });
//
//         this.conversationHistory.push({
//           role: 'assistant',
//           content: responseContent
//         });
//
//         return {
//           content: responseContent,
//           toolResults: [{
//             toolCallId: 'analyze_image_' + Date.now(),
//             result: analysisData
//           }]
//         };
//       } else {
//         throw new Error(analyzeResult.error || '图片分析失败');
//       }
//     } catch (error) {
//       console.error('SimpleChatService: 图片消息处理失败:', error);
//       return {
//         content: '抱歉，图片分析失败，请稍后再试。',
//         error: error instanceof Error ? error.message : String(error)
//       };
//     }
//   }
//
//   /**
//    * 解析ReAct格式的输出
//    */
//   private parseReActOutput(output: string): {
//     thoughts: string[];
//     action?: { tool: string; parameters: any };
//     finalAnswer?: string;
//     hasAction: boolean;
//   } {
//     const thoughts: string[] = [];
//     let action: { tool: string; parameters: any } | undefined;
//     let finalAnswer: string | undefined;
//
//     console.log('解析ReAct输出，原始内容:', output);
//
//     // 先尝试解析新格式：{Thought: ... Action: tool{"param": "value"} ... Final Answer: ...}
//     const newFormatMatch = this.parseNewReActFormat(output);
//     if (newFormatMatch.hasAction || newFormatMatch.finalAnswer) {
//       console.log('使用新格式解析结果:', newFormatMatch);
//       return newFormatMatch;
//     }
//
//     // 如果新格式解析失败，继续使用原有的逐行解析方式
//     const lines = output.split('\n');
//     let currentSection = '';
//     let buffer = '';
//     let foundAction = false;
//
//     for (const line of lines) {
//       const trimmedLine = line.trim();
//
//       if (trimmedLine.startsWith('Thought:')) {
//         if (buffer && currentSection === 'thought') {
//           thoughts.push(buffer.trim());
//         }
//         currentSection = 'thought';
//         buffer = trimmedLine.substring(8).trim();
//       } else if (trimmedLine.startsWith('Action:')) {
//         if (buffer && currentSection === 'thought') {
//           thoughts.push(buffer.trim());
//         }
//         currentSection = 'action';
//         const actionLine = trimmedLine.substring(7).trim();
//
//         console.log('找到Action行:', actionLine);
//
//         // 解析工具名和参数 - 支持有空格或无空格的格式
//         const spaceIndex = actionLine.indexOf(' ');
//         const braceIndex = actionLine.indexOf('{');
//
//         console.log('解析Action - spaceIndex:', spaceIndex, 'braceIndex:', braceIndex);
//
//         if (spaceIndex > 0 && (braceIndex === -1 || spaceIndex < braceIndex)) {
//           // 有空格分隔: "tool_name {params}"
//           const tool = actionLine.substring(0, spaceIndex);
//           const paramStr = actionLine.substring(spaceIndex + 1);
//           console.log('尝试解析(有空格) - tool:', tool, 'paramStr:', paramStr);
//           try {
//             const parameters = JSON.parse(paramStr);
//             action = { tool, parameters };
//             foundAction = true;
//             console.log('解析成功(有空格):', action);
//           } catch (error) {
//             console.warn('解析Action参数失败(有空格):', paramStr, error);
//           }
//         } else if (braceIndex > 0) {
//           // 无空格分隔: "tool_name{params}"
//           const tool = actionLine.substring(0, braceIndex);
//           const paramStr = actionLine.substring(braceIndex);
//           console.log('尝试解析(无空格) - tool:', tool, 'paramStr:', paramStr);
//           try {
//             const parameters = JSON.parse(paramStr);
//             action = { tool, parameters };
//             foundAction = true;
//             console.log('解析成功(无空格):', action);
//           } catch (error) {
//             console.warn('解析Action参数失败(无空格):', paramStr, error);
//           }
//         } else {
//           console.warn('无法解析Action格式 - actionLine:', actionLine, 'spaceIndex:', spaceIndex, 'braceIndex:', braceIndex);
//         }
//         buffer = '';
//
//         // 找到Action后，忽略后续的虚假内容（如模型自己编造的Observation）
//         if (foundAction) {
//           console.log('已找到Action，停止解析后续内容');
//           break;
//         }
//       } else if (trimmedLine.startsWith('Final Answer:') && !foundAction) {
//         // 只有在没找到Action时才处理Final Answer
//         if (buffer && currentSection === 'thought') {
//           thoughts.push(buffer.trim());
//         }
//         currentSection = 'final';
//         finalAnswer = trimmedLine.substring(13).trim();
//         buffer = '';
//       } else if (trimmedLine && !trimmedLine.startsWith('Observation:') && !foundAction) {
//         // 只有在没找到Action时才继续处理其他内容
//         if (buffer) buffer += '\n';
//         buffer += line;
//       }
//     }
//
//     // 处理最后的buffer（只有在没找到Action时）
//     if (!foundAction) {
//       if (buffer && currentSection === 'thought') {
//         thoughts.push(buffer.trim());
//       } else if (buffer && currentSection === 'final') {
//         finalAnswer = (finalAnswer || '') + '\n' + buffer.trim();
//       }
//     }
//
//     const result = {
//       thoughts,
//       action,
//       finalAnswer,
//       hasAction: !!action
//     };
//
//     console.log('ReAct解析最终结果:', result);
//     return result;
//   }
//
//   /**
//    * 解析新的ReAct格式：严格模式，Action后的所有内容都被忽略
//    */
//   private parseNewReActFormat(output: string): {
//     thoughts: string[];
//     action?: { tool: string; parameters: any };
//     finalAnswer?: string;
//     hasAction: boolean;
//   } {
//     const thoughts: string[] = [];
//     let action: { tool: string; parameters: any } | undefined;
//     let finalAnswer: string | undefined;
//
//     console.log('尝试解析新格式ReAct输出');
//
//     // 首先检查是否包含Action - 支持直接的Action输出
//     const actionRegex = /Action:\s*([a-zA-Z_][a-zA-Z0-9_]*)\s*(\{[^}]*\})/;
//     const actionMatch = output.match(actionRegex);
//
//     if (actionMatch) {
//       const tool = actionMatch[1].trim();
//       const paramStr = actionMatch[2].trim();
//
//       console.log('直接Action解析 - tool:', tool, 'paramStr:', paramStr);
//
//       try {
//         const parameters = JSON.parse(paramStr);
//         action = { tool, parameters };
//         console.log('直接Action解析成功:', action);
//
//         // 提取Action之前的Thought（如果有的话）
//         const actionIndex = output.indexOf(actionMatch[0]);
//         if (actionIndex > 0) {
//           const beforeAction = output.substring(0, actionIndex);
//           const thoughtRegex = /Thought:\s*(.*?)$/s;
//           const thoughtMatch = beforeAction.match(thoughtRegex);
//           if (thoughtMatch && thoughtMatch[1]) {
//             const thoughtText = thoughtMatch[1].trim();
//             if (thoughtText) {
//               thoughts.push(thoughtText);
//             }
//           }
//         }
//
//         // 找到Action后立即返回，忽略所有后续内容
//         return {
//           thoughts,
//           action,
//           finalAnswer: undefined,
//           hasAction: true
//         };
//       } catch (error) {
//         console.warn('直接Action参数解析失败:', paramStr, error);
//       }
//     }
//
//     // 如果没有Action，才处理Final Answer
//     // 但要严格过滤虚构内容
//     if (!action) {
//       // 首先提取Thought
//       const thoughtRegex = /Thought:\s*(.*?)(?=Final\s+Answer:|$)/s;
//       const thoughtMatch = output.match(thoughtRegex);
//       if (thoughtMatch && thoughtMatch[1]) {
//         const thoughtText = thoughtMatch[1].trim();
//         if (thoughtText) {
//           thoughts.push(thoughtText);
//         }
//       }
//
//       // 提取Final Answer，但要过滤虚构内容
//       const finalAnswerRegex = /Final\s+Answer:\s*([\s\S]*?)(?:\s*$)/;
//       const finalAnswerMatch = output.match(finalAnswerRegex);
//
//       if (finalAnswerMatch && finalAnswerMatch[1]) {
//         let answerText = finalAnswerMatch[1].trim();
//
//         // 严格过滤虚构内容
//         const filterPatterns = [
//           /Observation:\s*\[.*?\]/g,         // 过滤虚构的Observation
//           /等待获取.*?结果\.{3}/g,
//           /请稍等片刻\.{3}/g,
//           /（假设获取了.*?）/g,
//           /假设.*?结果.*?\)/g,
//           /由于当前环境无法直接查看/g,
//           /\[分析结果\]/g,                   // 过滤占位符
//           /现在可以了解图片内容了/g
//         ];
//
//         filterPatterns.forEach(pattern => {
//           answerText = answerText.replace(pattern, '').trim();
//         });
//
//         // 清理多余的空行和标点
//         answerText = answerText.replace(/\n\s*\n/g, '\n').trim();
//
//         if (answerText && answerText.length > 10) { // 确保不是空洞的回答
//           finalAnswer = answerText;
//           console.log('严格模式Final Answer解析成功:', finalAnswer.substring(0, 100) + '...');
//         }
//       }
//     }
//
//     return {
//       thoughts,
//       action,
//       finalAnswer,
//       hasAction: !!action
//     };
//   }
// }