// import { Tool, ToolResult } from '../types/tools';
// import { ToolUtils } from './toolUtils';
// import { databaseService } from './database';
// import { aiService } from './ai';
// import { ImageData } from '../data/mockData';
// import { ImageRecord } from '../types/database';
//
// /**
//  * Image search result interface
//  */
// export interface ImageSearchResult {
//   images: Array<{
//     id: string;
//     url: string;
//     title: string;
//     description: string;
//     tags: string[];
//     similarity?: number;
//     thumbnail?: string;
//   }>;
//   total: number;
//   query: string;
//   executionTime: number;
// }
//
// /**
//  * Image analysis result interface
//  */
// export interface ImageAnalysisResult {
//   description: string;
//   tags: string[];
//   objects: Array<{
//     name: string;
//     confidence: number;
//     attributes: string[];
//   }>;
//   colors: string[];
//   scene: string;
//   mood: string;
//   structuredMetadata?: Record<string, unknown>;
// }
//
// /**
//  * Convert ImageData to search result format
//  */
// function convertToSearchResult(images: ImageData[], query: string, executionTime: number): ImageSearchResult {
//   return {
//     images: images.map((img: any) => ({
//       id: img.id,
//       url: img.url,
//       title: img.title,
//       description: img.description,
//       tags: img.tags,
//       similarity: img.similarity,
//       thumbnail: img.url // Using the same URL as thumbnail for now
//     })),
//     total: images.length,
//     query,
//     executionTime
//   };
// }
//
// /**
//  * Tool: Analyze image
//  * Provides comprehensive image analysis using VL model capabilities
//  */
// export const analyzeImageTool: Tool = ToolUtils.createTool(
//   'analyze_image',
//   '分析图片内容，提供详细的描述、标签和结构化数据',
//   ToolUtils.createParameters({
//     imageBase64: ToolUtils.stringParam('图片的base64数据（必须包含data:image/前缀）', true, {
//       pattern: '^data:image\\/(jpeg|jpg|png|webp);base64,[A-Za-z0-9+/]+=*$'
//     }),
//     includeStructuredData: ToolUtils.booleanParam('是否包含结构化数据', false, { default: true })
//   }),
//   async (params: {
//     imageBase64: string;
//     includeStructuredData?: boolean;
//   }) => {
//     const startTime = Date.now();
//     const { imageBase64, includeStructuredData = true } = params;
//
//     try {
//       console.log(`开始分析图片...`);
//
//       // 调用现有的图片分析接口
//       const analysisResult = await aiService.analyzeImage(imageBase64);
//
//       if (!analysisResult) {
//         throw new Error('AI服务不可用或返回空结果');
//       }
//
//       const executionTime = Date.now() - startTime;
//
//       const result = {
//         success: true,
//         description: analysisResult.description,
//         tags: analysisResult.tags_flat || analysisResult.tags,
//         structuredData: includeStructuredData ? analysisResult.structured_data : undefined,
//         executionTime
//       };
//
//       console.log(`图片分析完成: 耗时${executionTime}ms`);
//
//       return result;
//
//     } catch (error) {
//       console.error('analyze_image工具执行失败:', error);
//       throw error;
//     }
//   }
// );
//
// /**
//  * Find similar images by image tool
//  */
// export const findSimilarImagesByImageTool: Tool = ToolUtils.createTool(
//   'find_similar_images_by_image',
//   '根据图片查找相似图片',
//   ToolUtils.createParameters({
//     imageId: ToolUtils.stringParam('图片ID或图片路径', false),
//     imageBase64: ToolUtils.stringParam('图片的base64数据', false),
//     limit: ToolUtils.numberParam('返回结果数量限制', false, { default: 10, minimum: 1, maximum: 50 }),
//     threshold: ToolUtils.numberParam('相似度阈值', false, { default: 0.7, minimum: 0, maximum: 1 })
//   }),
//   async (params: {
//     imageId?: string;
//     imageBase64?: string;
//     limit?: number;
//     threshold?: number;
//   }) => {
//     const startTime = Date.now();
//     const { imageId, imageBase64, limit = 10, threshold = 0.7 } = params;
//
//     try {
//       if (!imageId && !imageBase64) {
//         throw new Error('必须提供imageId或imageBase64参数');
//       }
//
//       console.log(`开始搜索相似图片: imageId=${imageId}, threshold=${threshold}, limit=${limit}`);
//
//       let searchResults;
//
//       if (imageId) {
//         // 使用图片ID/路径搜索相似图片
//         searchResults = await databaseService.searchSimilarImagesByImageId(imageId, threshold, limit);
//       } else if (imageBase64) {
//         // 如果提供的是base64数据，先分析图片得到描述，再生成向量
//         console.log('分析图片内容...');
//         const analysisResult = await aiService.analyzeImage(imageBase64);
//
//         if ('error' in analysisResult && analysisResult.error) {
//           throw new Error(`图片分析失败: ${analysisResult.error}`);
//         }
//
//         if (!analysisResult?.description) {
//           throw new Error('图片分析结果为空');
//         }
//
//         console.log('生成图片描述向量用于相似度搜索...');
//         const embeddingResult = await aiService.generateEmbedding(analysisResult.description);
//
//         if ('error' in embeddingResult && embeddingResult.error) {
//           throw new Error(`向量生成失败: ${embeddingResult.error}`);
//         }
//
//         if (!embeddingResult?.embedding) {
//           throw new Error('向量生成结果为空');
//         }
//
//         // 使用向量进行相似度搜索
//         searchResults = await databaseService.searchSimilarImages(
//           embeddingResult.embedding,
//           limit
//         );
//
//         // 根据阈值过滤结果
//         if (searchResults.results) {
//           searchResults.results = searchResults.results.filter((record: any) => {
//             const score = record.score || 0;
//             return score >= threshold;
//           });
//           searchResults.total = searchResults.results.length;
//         }
//       } else {
//         throw new Error('未提供有效的搜索参数');
//       }
//
//       if (searchResults.error) {
//         throw new Error(searchResults.error);
//       }
//
//       // 转换结果格式
//       const convertedResults = searchResults.results.map((record: any) => ({
//         id: record.id,
//         url: record.imagePath,
//         title: record.metadata?.filename || record.imagePath.split('/').pop() || '未知图片',
//         description: record.description || '相似图片',
//         tags: record.tags || [],
//         similarity: Math.round((record.score || 0) * 100),
//         thumbnail: record.imagePath,
//         metadata: record.metadata
//       }));
//
//       const executionTime = Date.now() - startTime;
//
//       console.log(`相似图片搜索完成: 找到${convertedResults.length}张图片, 耗时${executionTime}ms`);
//
//       return {
//         success: true,
//         results: convertedResults,
//         total: convertedResults.length,
//         queryInfo: {
//           sourceImageId: imageId,
//           searchMethod: imageId ? 'image_vector_similarity' : 'base64_vector_similarity',
//           threshold,
//           executionTime
//         }
//       };
//
//     } catch (error) {
//       console.error('find_similar_images_by_image工具执行失败:', error);
//       const executionTime = Date.now() - startTime;
//
//       return {
//         success: false,
//         results: [],
//         total: 0,
//         error: error instanceof Error ? error.message : String(error),
//         queryInfo: {
//           sourceImageId: imageId,
//           searchMethod: 'failed',
//           threshold,
//           executionTime
//         }
//       };
//     }
//   }
// );
//
// /**
//  * Find similar images by description tool
//  */
// export const findSimilarImagesByDescriptionTool: Tool = ToolUtils.createTool(
//   'find_similar_images_by_description',
//   '根据文本描述搜索图片',
//   ToolUtils.createParameters({
//     description: ToolUtils.stringParam('描述文本', true),
//     limit: ToolUtils.numberParam('返回结果数量限制', false, { default: 10, minimum: 1, maximum: 50 }),
//     threshold: ToolUtils.numberParam('相似度阈值', false, { default: 0.6, minimum: 0, maximum: 1 }),
//     enableKeywordExpansion: ToolUtils.booleanParam('启用关键词扩展', false, { default: true })
//   }),
//   async (params: {
//     description: string;
//     limit?: number;
//     threshold?: number;
//     enableKeywordExpansion?: boolean;
//   }) => {
//     try {
//       const {
//         description,
//         limit = 10,
//         threshold = 0.6,
//         enableKeywordExpansion = true
//       } = params;
//
//       if (!description || description.trim().length === 0) {
//         throw new Error('描述文本不能为空');
//       }
//
//       // 使用galleryService的智能搜索功能
//       const searchResult = await galleryService.searchImages(description.trim(), limit);
//
//       if (searchResult.error) {
//         throw new Error(searchResult.error);
//       }
//
//       // 应用相似度阈值过滤
//       const filteredImages = searchResult.images.filter((image: any) => {
//         return !image.similarity || image.similarity >= (threshold * 100);
//       });
//
//       // 提取关键词（如果AI服务可用）
//       let extractedKeywords: string[] = [];
//       let expandedTags: string[] = [];
//
//       try {
//         const parseResult = await window.electronAPI?.ai.parseSearchQuery(description);
//         if (parseResult && !parseResult.error) {
//           extractedKeywords = parseResult.keywords || [];
//         }
//
//         // 如果启用关键词扩展，查找相似标签
//         if (enableKeywordExpansion && extractedKeywords.length > 0) {
//           const similarTagsResult = await databaseService.findSimilarTagsBatch(
//             extractedKeywords,
//             { limit: 3, threshold: 0.6 }
//           );
//
//           if (similarTagsResult && similarTagsResult instanceof Map) {
//             expandedTags = Array.from(similarTagsResult.values())
//               .flat()
//               .map((tag: any) => tag.tag);
//           }
//         }
//       } catch (error) {
//         console.warn('关键词提取失败:', error);
//       }
//
//       // 为结果添加匹配的关键词信息
//       const resultsWithKeywords = filteredImages.map((image: any) => ({
//         ...image,
//         matchedKeywords: image.tags.filter((tag: any) =>
//           extractedKeywords.some((keyword: any) =>
//             tag.toLowerCase().includes(keyword.toLowerCase()) ||
//             keyword.toLowerCase().includes(tag.toLowerCase())
//           )
//         )
//       }));
//
//       return {
//         success: true,
//         results: resultsWithKeywords,
//         total: resultsWithKeywords.length,
//         searchInfo: {
//           query: description,
//           extractedKeywords,
//           expandedTags,
//           searchMethod: 'enhanced_hybrid_search',
//           threshold
//         }
//       };
//
//     } catch (error) {
//       console.error('find_similar_images_by_description工具执行失败:', error);
//       throw error;
//     }
//   }
// );
//
// /**
//  * Find images by tags tool
//  */
// export const findImagesByTagsTool: Tool = ToolUtils.createTool(
//   'find_images_by_tags',
//   '根据标签搜索图片',
//   ToolUtils.createParameters({
//     tags: ToolUtils.arrayParam('标签列表', ToolUtils.stringParam('标签'), true),
//     matchMode: ToolUtils.stringParam('匹配模式', false, { enum: ['all', 'any'], default: 'any' }),
//     logic: ToolUtils.stringParam('逻辑关系', false, { enum: ['AND', 'OR'], default: 'OR' }),
//     limit: ToolUtils.numberParam('返回结果数量限制', false, { default: 20, minimum: 1, maximum: 100 }),
//     includeSimilarTags: ToolUtils.booleanParam('包含相似标签', false, { default: false }),
//     sortBy: ToolUtils.stringParam('排序方式', false, {
//       enum: ['relevance', 'uploadTime', 'title', 'fileSize'],
//       default: 'relevance'
//     }),
//     sortOrder: ToolUtils.stringParam('排序顺序', false, { enum: ['asc', 'desc'], default: 'desc' })
//   }),
//   async (params: {
//     tags: string[];
//     matchMode?: 'all' | 'any';
//     logic?: 'AND' | 'OR';
//     limit?: number;
//     includeSimilarTags?: boolean;
//     sortBy?: 'relevance' | 'uploadTime' | 'title' | 'fileSize';
//     sortOrder?: 'asc' | 'desc';
//   }) => {
//     try {
//       const {
//         tags,
//         matchMode = 'any',
//         logic = 'OR',
//         limit = 20,
//         includeSimilarTags = false,
//         sortBy = 'relevance',
//         sortOrder = 'desc'
//       } = params;
//
//       // 统一处理匹配模式：matchMode 优先，如果没有则使用 logic
//       const finalMatchMode = matchMode || (logic === 'AND' ? 'all' : 'any');
//
//       if (!tags || tags.length === 0) {
//         throw new Error('标签列表不能为空');
//       }
//
//       console.log(`开始搜索标签: ${tags.join(', ')}, 匹配模式: ${finalMatchMode}, 限制: ${limit}`);
//
//       // 使用真实的数据库服务查询
//       let searchResult;
//
//       if (finalMatchMode === 'all') {
//         // AND 逻辑：需要匹配所有标签
//         // 注意：目前数据库服务可能不直接支持AND逻辑，我们需要在结果中进行筛选
//         searchResult = await galleryService.getImagesByTags(tags, limit * 2); // 获取更多结果用于筛选
//
//         if (searchResult.error) {
//           throw new Error(searchResult.error);
//         }
//
//         // 筛选出包含所有指定标签的图片
//         const filteredImages = searchResult.images.filter(image => {
//           const imageTags = Array.isArray(image.tags) ? image.tags : [];
//           return tags.every(tag =>
//             imageTags.some(imageTag =>
//               imageTag.toLowerCase().includes(tag.toLowerCase()) ||
//               tag.toLowerCase().includes(imageTag.toLowerCase())
//             )
//           );
//         });
//
//         searchResult.images = filteredImages.slice(0, limit);
//         searchResult.total = filteredImages.length;
//       } else {
//         // OR 逻辑：匹配任意标签
//         searchResult = await galleryService.getImagesByTags(tags, limit);
//
//         if (searchResult.error) {
//           throw new Error(searchResult.error);
//         }
//       }
//
//       // 转换为工具期望的格式并计算匹配信息
//       const results = searchResult.images.map(image => {
//         const imageTags = Array.isArray(image.tags) ? image.tags : [];
//         const matchedTags = imageTags.filter(imageTag =>
//           tags.some(queryTag =>
//             imageTag.toLowerCase().includes(queryTag.toLowerCase()) ||
//             queryTag.toLowerCase().includes(imageTag.toLowerCase())
//           )
//         );
//
//         return {
//           id: image.id,
//           title: image.title || image.url.split('/').pop() || '未知图片',
//           url: image.url,
//           tags: imageTags,
//           matchedTags,
//           tagMatchCount: matchedTags.length,
//           uploadTime: image.uploadTime || new Date().toISOString(),
//           thumbnail: (image as any).thumbnail || image.url,
//           metadata: (image as any).metadata
//         };
//       });
//
//       // 获取扩展标签（如果需要）
//       let expandedTags: string[] = [];
//       if (includeSimilarTags) {
//         try {
//           // 获取相似标签
//           const similarTagsResult = await databaseService.findSimilarTagsBatch(
//             tags,
//             { limit: 5, threshold: 0.6 }
//           );
//
//           if (similarTagsResult && similarTagsResult instanceof Map) {
//             expandedTags = Array.from(similarTagsResult.values())
//               .flat()
//               .map((tag: any) => tag.tag)
//               .filter((tag: string) => !tags.includes(tag))
//               .slice(0, 10);
//           }
//         } catch (error) {
//           console.warn('获取相似标签失败:', error);
//         }
//       }
//
//       // 获取标签统计信息
//       let tagStats;
//       try {
//         const allTags = await galleryService.getAllTags();
//         const tagFrequency: Record<string, number> = {};
//
//         // 计算查询标签的频率 - 使用更精确的匹配逻辑
//         for (const tag of tags) {
//           const count = results.reduce((acc, result) => {
//             // 精确匹配或部分匹配（忽略大小写）
//             const hasMatch = result.tags.some(resultTag =>
//               resultTag.toLowerCase() === tag.toLowerCase() ||
//               resultTag.toLowerCase().includes(tag.toLowerCase()) ||
//               tag.toLowerCase().includes(resultTag.toLowerCase())
//             );
//             return acc + (hasMatch ? 1 : 0);
//           }, 0);
//           tagFrequency[tag] = count;
//         }
//
//         // 智能推荐相关标签：优先显示与查询标签语义相似的标签
//         let relevantTags: string[] = [];
//
//         // 如果没有搜索到结果，显示可能相关的标签
//         if (results.length === 0) {
//           const queryTagsLower = tags.map(t => t.toLowerCase());
//           relevantTags = allTags.filter(tag => {
//             const tagLower = tag.toLowerCase();
//             return queryTagsLower.some(queryTag =>
//               tagLower.includes(queryTag) ||
//               queryTag.includes(tagLower) ||
//               // 添加一些常见的语义相关词汇匹配
//               (queryTag === '动物' && ['猫', '狗', '鸟', '宠物', '动物'].some(related => tagLower.includes(related))) ||
//               (queryTag === '宠物' && ['猫', '狗', '动物', '萌宠'].some(related => tagLower.includes(related)))
//             );
//           }).slice(0, 10);
//
//           // 如果还是没有相关标签，显示最常用的标签
//           if (relevantTags.length === 0) {
//             relevantTags = allTags.slice(0, 10);
//           }
//         } else {
//           // 如果有结果，显示结果中出现的所有标签
//           const resultTags = new Set<string>();
//           results.forEach(result => {
//             result.tags.forEach(tag => resultTags.add(tag));
//           });
//           relevantTags = Array.from(resultTags).slice(0, 20);
//         }
//
//         tagStats = {
//           availableTags: relevantTags,
//           tagFrequency,
//           searchSuggestions: results.length === 0 ? {
//             message: `未找到包含标签 "${tags.join('", "')}" 的图片`,
//             suggestedTags: relevantTags,
//             totalAvailableTags: allTags.length
//           } : undefined
//         };
//       } catch (error) {
//         console.warn('获取标签统计失败:', error);
//         tagStats = {
//           availableTags: tags,
//           tagFrequency: tags.reduce((acc: any, tag: string) => {
//             acc[tag] = results.filter(r => r.tags.includes(tag)).length;
//             return acc;
//           }, {}),
//           searchSuggestions: {
//             message: '获取标签信息失败，请稍后重试',
//             suggestedTags: [],
//             totalAvailableTags: 0
//           }
//         };
//       }
//
//       return {
//         success: true,
//         results,
//         total: results.length,
//         searchInfo: {
//           queryTags: tags,
//           expandedTags,
//           matchMode: finalMatchMode,
//           logic: finalMatchMode === 'all' ? 'AND' : 'OR',
//           sortBy,
//           includedSimilarTags: includeSimilarTags
//         },
//         tagStats
//       };
//
//     } catch (error) {
//       console.error('find_images_by_tags工具执行失败:', error);
//       throw error;
//     }
//   }
// );
//
//
//
// /**
//  * Export all image tools
//  */
// export const imageTools = [
//   analyzeImageTool,
//   findSimilarImagesByImageTool,
//   findSimilarImagesByDescriptionTool,
//   findImagesByTagsTool
// ];
//
// /**
//  * Register image tools to the tool registry
//  */
// export function registerImageTools(registry: any) {
//   imageTools.forEach((tool: any) => {
//     registry.registerTool(tool);
//   });
//   console.log(`已注册 ${imageTools.length} 个图片工具`);
// }