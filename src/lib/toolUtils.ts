import { Tool, ToolParameters, ToolResult, ToolExecutionError } from '../types/tools';

/**
 * Utility functions for working with tools
 */
export class ToolUtils {
  /**
   * Create a tool with standardized error handling
   */
  static createTool(
    name: string,
    description: string,
    parameters: ToolParameters,
    executeFunction: (params: any) => Promise<any>
  ): Tool {
    return {
      name,
      description,
      parameters,
      execute: async (params: any): Promise<ToolResult> => {
        try {
          const data = await executeFunction(params);
          return {
            success: true,
            data
          };
        } catch (error) {
          console.error(`Error in tool '${name}':`, error);
          
          // Determine error type based on error characteristics
          let errorType: 'EXECUTION_ERROR' | 'SERVICE_UNAVAILABLE' = 'EXECUTION_ERROR';
          if (error instanceof Error) {
            // Check for common service unavailable indicators
            if (error.message.includes('ECONNREFUSED') || 
                error.message.includes('timeout') ||
                error.message.includes('service unavailable')) {
              errorType = 'SERVICE_UNAVAILABLE';
            }
          }

          throw new ToolExecutionError(
            name,
            errorType,
            error instanceof Error ? error.message : 'Unknown execution error',
            error instanceof Error ? error : undefined
          );
        }
      }
    };
  }

  /**
   * Create a simple string parameter schema
   */
  static stringParam(description: string, required = false, options?: {
    minLength?: number;
    maxLength?: number;
    enum?: string[];
    pattern?: string;
    default?: string;
  }) {
    return {
      type: 'string' as const,
      description,
      required,
      ...options
    };
  }

  /**
   * Create a simple number parameter schema
   */
  static numberParam(description: string, required = false, options?: {
    minimum?: number;
    maximum?: number;
    default?: number;
  }) {
    return {
      type: 'number' as const,
      description,
      required,
      ...options
    };
  }

  /**
   * Create a simple boolean parameter schema
   */
  static booleanParam(description: string, required = false, options?: {
      default?: boolean;
  }) {
    return {
      type: 'boolean' as const,
      description,
      required,
      ...options
    };
  }

  /**
   * Create an array parameter schema
   */
  static arrayParam(description: string, itemType: any, required = false) {
    return {
      type: 'array' as const,
      description,
      required,
      items: itemType
    };
  }

  /**
   * Create an object parameter schema
   */
  static objectParam(description: string, properties: Record<string, any>, required = false, requiredProps?: string[]) {
    return {
      type: 'object' as const,
      description,
      required,
      properties,
      ...(requiredProps && { required: requiredProps })
    };
  }

  /**
   * Create a standardized tool parameters object
   */
  static createParameters(properties: Record<string, any>, required?: string[]): ToolParameters {
    return {
      type: 'object',
      properties,
      ...(required && { required })
    };
  }

  /**
   * Measure execution time of a function
   */
  static async measureExecutionTime<T>(fn: () => Promise<T>): Promise<{ result: T; executionTime: number }> {
    const startTime = Date.now();
    const result = await fn();
    const executionTime = Date.now() - startTime;
    return { result, executionTime };
  }

  /**
   * Create a timeout wrapper for tool execution
   */
  static withTimeout<T>(promise: Promise<T>, timeoutMs: number): Promise<T> {
    return Promise.race([
      promise,
      new Promise<never>((_, reject) => {
        setTimeout(() => {
          reject(new Error(`Operation timed out after ${timeoutMs}ms`));
        }, timeoutMs);
      })
    ]);
  }

  /**
   * Retry a function with exponential backoff
   */
  static async retry<T>(
    fn: () => Promise<T>,
    maxRetries = 3,
    baseDelayMs = 1000
  ): Promise<T> {
    let lastError: Error;
    
    for (let attempt = 0; attempt <= maxRetries; attempt++) {
      try {
        return await fn();
      } catch (error) {
        lastError = error instanceof Error ? error : new Error('Unknown error');
        
        if (attempt === maxRetries) {
          break;
        }
        
        // Exponential backoff
        const delay = baseDelayMs * Math.pow(2, attempt);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
    
    throw lastError!;
  }
}