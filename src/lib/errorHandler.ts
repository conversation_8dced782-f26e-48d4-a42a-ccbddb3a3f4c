import { toast } from 'sonner'

/**
 * 全局错误处理函数
 * 根据开发/生产模式显示不同详情级别的错误信息
 * 
 * @param error - 错误对象或错误信息
 * @param userMessage - 用户友好的错误描述
 * @param options - 可选配置
 */
interface ErrorHandlerOptions {
  /** 是否在开发模式下也只显示用户友好信息 */
  forceUserFriendly?: boolean
  /** 是否跳过toast显示，只记录日志 */
  skipToast?: boolean
  /** 自定义日志前缀 */
  logPrefix?: string
}

export const handleError = (
  error: any, 
  userMessage: string, 
  options: ErrorHandlerOptions = {}
) => {
  const isDev = import.meta.env.DEV
  const { forceUserFriendly = false, skipToast = false, logPrefix = '错误' } = options
  
  // 提取错误详情
  const getErrorDetails = (err: any): string => {
    if (typeof err === 'string') return err
    if (err?.message) return err.message
    if (err?.toString && typeof err.toString === 'function') return err.toString()
    return '未知错误'
  }
  
  const errorDetails = getErrorDetails(error)
  
  // 控制台日志记录
  if (isDev && !forceUserFriendly) {
    console.error(`${logPrefix} - 详细信息:`, error)
  } else {
    console.error(`${logPrefix}:`, userMessage, error)
  }
  
  // Toast 消息显示
  if (!skipToast) {
    if (isDev && !forceUserFriendly) {
      // 开发模式：显示详细错误信息
      toast.error(`${userMessage}: ${errorDetails}`)
    } else {
      // 生产模式或强制用户友好模式：只显示用户友好的错误信息
      toast.error(userMessage)
    }
  }
}

/**
 * 处理API响应错误的便捷函数
 * @param response - API响应对象
 * @param userMessage - 用户友好的错误描述
 * @param options - 可选配置
 */
export const handleApiError = (
  response: { success: boolean; error?: any }, 
  userMessage: string,
  options: ErrorHandlerOptions = {}
) => {
  if (!response.success && response.error) {
    handleError(response.error, userMessage, options)
  }
}

/**
 * Promise 错误处理包装器
 * @param promise - 要包装的Promise
 * @param userMessage - 用户友好的错误描述
 * @param options - 可选配置
 */
export const withErrorHandler = async <T>(
  promise: Promise<T>,
  userMessage: string,
  options: ErrorHandlerOptions = {}
): Promise<T | null> => {
  try {
    return await promise
  } catch (error) {
    handleError(error, userMessage, options)
    return null
  }
}

/**
 * 创建带有预设用户消息的错误处理函数
 * @param defaultMessage - 默认的用户友好消息
 * @param defaultOptions - 默认选项
 */
export const createErrorHandler = (
  defaultMessage: string,
  defaultOptions: ErrorHandlerOptions = {}
) => {
  return (error: any, userMessage?: string, options?: ErrorHandlerOptions) => {
    handleError(
      error,
      userMessage || defaultMessage,
      { ...defaultOptions, ...options }
    )
  }
}

// 预设的错误处理器
export const networkErrorHandler = createErrorHandler('网络连接失败，请检查网络状态')
export const fileSystemErrorHandler = createErrorHandler('文件系统操作失败')
export const databaseErrorHandler = createErrorHandler('数据库操作失败')
export const authErrorHandler = createErrorHandler('身份验证失败')