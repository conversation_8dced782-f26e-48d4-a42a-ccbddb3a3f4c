/**
 * Test script for Intent Recognition and Parameter Extraction
 * 
 * This script tests the implemented intent recognition and parameter extraction
 * functionality to ensure it works correctly according to the requirements.
 */

import { IntentRecognitionFactory } from './intentRecognition';
import { ParameterExtractionFactory } from './parameterExtraction';

/**
 * Test intent recognition with various user messages
 */
async function testIntentRecognition() {
    console.log('🧠 Testing Intent Recognition System...\n');

    const recognizer = IntentRecognitionFactory.getIntentRecognizer();

    const testCases = [
        // Analyze image intents
        { text: "What's in this image?", hasImage: true, expected: 'analyze_image' },
        { text: "Describe this picture", hasImage: true, expected: 'analyze_image' },
        { text: "分析这张图片", hasImage: true, expected: 'analyze_image' },
        { text: "Tell me about the objects in this photo", hasImage: true, expected: 'analyze_image' },

        // Find similar images by image
        { text: "Find similar images", hasImage: true, expected: 'find_similar_images_by_image' },
        { text: "Search for images like this", hasImage: true, expected: 'find_similar_images_by_image' },
        { text: "找相似的图片", hasImage: true, expected: 'find_similar_images_by_image' },
        { text: "More pictures like this one", hasImage: true, expected: 'find_similar_images_by_image' },

        // Find similar images by description
        { text: "Find images of cats", hasImage: false, expected: 'find_similar_images_by_description' },
        { text: "Show me photos of sunset", hasImage: false, expected: 'find_similar_images_by_description' },
        { text: "搜索包含花朵的图片", hasImage: false, expected: 'find_similar_images_by_description' },
        { text: "Pictures containing mountains", hasImage: false, expected: 'find_similar_images_by_description' },

        // Find images by tags
        { text: "Images tagged with nature", hasImage: false, expected: 'find_images_by_tags' },
        { text: "Find photos with tag 'landscape'", hasImage: false, expected: 'find_images_by_tags' },
        { text: "标签搜索：动物", hasImage: false, expected: 'find_images_by_tags' },
        { text: "Filter by tag: portrait", hasImage: false, expected: 'find_images_by_tags' }
    ];

    let correctPredictions = 0;

    for (const testCase of testCases) {
        const intent = await recognizer.recognizeIntent(testCase.text, testCase.hasImage);
        const isCorrect = intent.toolName === testCase.expected;

        console.log(`📝 "${testCase.text}"`);
        console.log(`   Expected: ${testCase.expected}`);
        console.log(`   Predicted: ${intent.toolName} (confidence: ${(intent.confidence * 100).toFixed(1)}%)`);
        console.log(`   ${isCorrect ? '✅ Correct' : '❌ Incorrect'}`);
        console.log(`   Reasoning: ${intent.reasoning}`);

        if (intent.parameters && Object.keys(intent.parameters).length > 0) {
            console.log(`   Parameters: ${JSON.stringify(intent.parameters)}`);
        }

        console.log('');

        if (isCorrect) correctPredictions++;
    }

    const accuracy = (correctPredictions / testCases.length) * 100;
    console.log(`🎯 Intent Recognition Accuracy: ${accuracy.toFixed(1)}% (${correctPredictions}/${testCases.length})\n`);

    return accuracy;
}

/**
 * Test parameter extraction with various user messages
 */
async function testParameterExtraction() {
    console.log('🔧 Testing Parameter Extraction System...\n');

    const extractor = ParameterExtractionFactory.getInstance();

    const testCases = [
        // Quantity extraction
        { text: "Find 10 similar images", expected: { limit: 10 } },
        { text: "Show me a few pictures", expected: { limit: 8 } },
        { text: "Get many photos", expected: { limit: 30 } },
        { text: "找几张相似的图片", expected: { limit: 8 } },
        { text: "显示很多照片", expected: { limit: 30 } },

        // Similarity extraction
        { text: "Find very similar images", expected: { threshold: 0.8 } },
        { text: "Show somewhat similar pictures", expected: { threshold: 0.5 } },
        { text: "Get 80% similar photos", expected: { threshold: 0.8 } },
        { text: "找非常相似的图片", expected: { threshold: 0.8 } },
        { text: "显示比较相似的照片", expected: { threshold: 0.65 } },

        // Detail level extraction
        { text: "Give me a basic analysis", expected: { detailLevel: 'basic' } },
        { text: "Provide detailed description", expected: { detailLevel: 'detailed' } },
        { text: "I want comprehensive analysis", expected: { detailLevel: 'comprehensive' } },
        { text: "简单分析一下", expected: { detailLevel: 'basic' } },
        { text: "详细描述图片", expected: { detailLevel: 'detailed' } },

        // Focus areas extraction
        { text: "Identify the objects and colors", expected: { focusAreas: ['objects', 'colors'] } },
        { text: "What people and emotions are in this?", expected: { focusAreas: ['people', 'emotions'] } },
        { text: "Analyze the scene and style", expected: { focusAreas: ['scene', 'style'] } },
        { text: "识别对象和颜色", expected: { focusAreas: ['objects', 'colors'] } },
        { text: "分析人物和情感", expected: { focusAreas: ['people', 'emotions'] } },

        // Tag extraction
        { text: 'Find images tagged with "nature" and "landscape"', expected: { tags: ['nature', 'landscape'] } },
        { text: "Photos with tags: sunset, beach", expected: { tags: ['sunset', 'beach'] } },
        { text: '标签：动物，可爱', expected: { tags: ['动物', '可爱'] } },

        // Match mode extraction
        { text: "Images with all these tags", expected: { matchMode: 'all' } },
        { text: "Photos with any of the tags", expected: { matchMode: 'any' } },
        { text: "包含所有标签的图片", expected: { matchMode: 'all' } },
        { text: "有任何标签的照片", expected: { matchMode: 'any' } },

        // Complex combinations
        {
            text: "Find 15 very similar images with detailed analysis focusing on objects and colors",
            expected: { limit: 15, threshold: 0.8, detailLevel: 'detailed', focusAreas: ['objects', 'colors'] }
        },
        {
            text: "Show me a few somewhat similar photos tagged with nature, analyzing all tags",
            expected: { limit: 8, threshold: 0.5, tags: ['nature'], matchMode: 'all' }
        }
    ];

    let totalTests = 0;
    let passedTests = 0;

    for (const testCase of testCases) {
        const extracted = extractor.extractAllParameters(testCase.text);
        const stats = extractor.getExtractionStats(extracted);

        console.log(`📝 "${testCase.text}"`);
        console.log(`   Expected: ${JSON.stringify(testCase.expected)}`);
        console.log(`   Extracted: ${JSON.stringify(filterRelevantParams(extracted))}`);
        console.log(`   Confidence: ${(extracted.extractionConfidence || 0 * 100).toFixed(1)}%`);
        console.log(`   Extraction Rate: ${(stats.extractionRate * 100).toFixed(1)}%`);

        // Check if extracted parameters match expected
        const matches = checkParameterMatch(extracted, testCase.expected);
        console.log(`   ${matches ? '✅ Match' : '❌ No Match'}`);

        if (extracted.extractedFrom && extracted.extractedFrom.length > 0) {
            console.log(`   Sources: ${extracted.extractedFrom.slice(0, 3).join(', ')}${extracted.extractedFrom.length > 3 ? '...' : ''}`);
        }

        console.log('');

        totalTests++;
        if (matches) passedTests++;
    }

    const accuracy = (passedTests / totalTests) * 100;
    console.log(`🎯 Parameter Extraction Accuracy: ${accuracy.toFixed(1)}% (${passedTests}/${totalTests})\n`);

    return accuracy;
}

/**
 * Filter out metadata from extracted parameters for comparison
 */
function filterRelevantParams(params: any): any {
    const { extractionConfidence, extractedFrom, ...relevant } = params;
    return relevant;
}

/**
 * Check if extracted parameters match expected parameters
 */
function checkParameterMatch(extracted: any, expected: any): boolean {
    for (const [key, expectedValue] of Object.entries(expected)) {
        const extractedValue = extracted[key];

        if (Array.isArray(expectedValue)) {
            if (!Array.isArray(extractedValue)) return false;
            if (expectedValue.length !== extractedValue.length) return false;
            for (const item of expectedValue) {
                if (!extractedValue.includes(item)) return false;
            }
        } else {
            if (extractedValue !== expectedValue) return false;
        }
    }
    return true;
}

/**
 * Test ambiguous intent handling
 */
async function testAmbiguousIntentHandling() {
    console.log('🤔 Testing Ambiguous Intent Handling...\n');

    const recognizer = IntentRecognitionFactory.getIntentRecognizer();

    const ambiguousMessages = [
        { text: "Help me with images", hasImage: false },
        { text: "I need something", hasImage: true },
        { text: "Find stuff", hasImage: false },
        { text: "图片相关", hasImage: true }
    ];

    for (const message of ambiguousMessages) {
        const intent = await recognizer.recognizeIntent(message.text, message.hasImage);
        const clarification = await recognizer.handleAmbiguousIntent(message.text, message.hasImage);

        console.log(`📝 "${message.text}" (hasImage: ${message.hasImage})`);
        console.log(`   Intent: ${intent.toolName} (confidence: ${(intent.confidence * 100).toFixed(1)}%)`);
        console.log(`   Clarification: ${clarification}`);
        console.log('');
    }
}

/**
 * Run all tests
 */
async function runAllTests() {
    console.log('🚀 Starting Intent Recognition and Parameter Extraction Tests\n');
    console.log('='.repeat(80) + '\n');

    try {
        const intentAccuracy = await testIntentRecognition();
        const parameterAccuracy = await testParameterExtraction();
        await testAmbiguousIntentHandling();

        console.log('='.repeat(80));
        console.log('📊 Test Summary:');
        console.log(`   Intent Recognition Accuracy: ${intentAccuracy.toFixed(1)}%`);
        console.log(`   Parameter Extraction Accuracy: ${parameterAccuracy.toFixed(1)}%`);
        console.log(`   Overall System Performance: ${((intentAccuracy + parameterAccuracy) / 2).toFixed(1)}%`);

        if (intentAccuracy >= 80 && parameterAccuracy >= 70) {
            console.log('✅ All tests passed! System is ready for integration.');
        } else {
            console.log('⚠️  Some tests need improvement. Consider tuning the patterns and rules.');
        }

    } catch (error) {
        console.error('❌ Test execution failed:', error);
    }
}

// Export for use in other modules
export {
    testIntentRecognition,
    testParameterExtraction,
    testAmbiguousIntentHandling,
    runAllTests
};

// Run tests if this file is executed directly
// Check if this is the main module in ES module context
if (import.meta.url === `file://${process.argv[1]}`) {
    runAllTests();
}