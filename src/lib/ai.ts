// import { ImageAnalysisResult, EmbeddingResult, ProcessImageRequest, ProcessImageResult, AIServiceStatus } from '../types/ai'
//
// /**
//  * 前端AI服务封装类
//  * 提供了对AI服务的统一访问接口
//  */
// export class FrontendAIService {
//   private static instance: FrontendAIService | null = null
//
//   private constructor() {
//     // 私有构造函数，确保单例
//   }
//
//   /**
//    * 获取AI服务实例
//    */
//   static getInstance(): FrontendAIService {
//     if (!this.instance) {
//       this.instance = new FrontendAIService()
//     }
//     return this.instance
//   }
//
//   /**
//    * 检查 Electron API 是否可用
//    */
//   private checkElectronAPI(): boolean {
//     if (typeof window === 'undefined' || !window.electronAPI) {
//       console.error('ElectronAPI 不可用，请确保在 Electron 环境中运行')
//       return false
//     }
//     return true
//   }
//
//   /**
//    * 测试AI服务连接
//    */
//   async testConnection(): Promise<boolean> {
//     if (!this.checkElectronAPI()) return false
//
//     try {
//       return await window.electronAPI.ai.testConnection()
//     } catch (error) {
//       console.error('测试AI服务连接失败:', error)
//       return false
//     }
//   }
//
//   /**
//    * 分析图片，生成描述和标签
//    * @param imageBase64 图片的Base64数据
//    */
//   async analyzeImage(imageBase64: string): Promise<ImageAnalysisResult> {
//     if (!this.checkElectronAPI()) {
//       throw new Error('ElectronAPI 不可用')
//     }
//
//     try {
//       const result = await window.electronAPI.ai.analyzeImage(imageBase64)
//       if (result.error) {
//         throw new Error(result.error)
//       }
//       return {
//         description: result.description,
//         tags: result.tags,
//         tags_flat: result.tags_flat || result.tags, // 使用扁平化标签，如果不存在则使用原始标签
//         structured_data: result.structured_data || {} // 使用结构化数据，如果不存在则使用空对象
//       }
//     } catch (error) {
//       console.error('图片分析失败:', error)
//       throw error
//     }
//   }
//
//   /**
//    * 生成文本向量
//    * @param text 要向量化的文本
//    */
//   async generateEmbedding(text: string): Promise<EmbeddingResult> {
//     if (!this.checkElectronAPI()) {
//       throw new Error('ElectronAPI 不可用')
//     }
//
//     try {
//       const result = await window.electronAPI.ai.generateEmbedding(text)
//       if (result.error) {
//         throw new Error(result.error)
//       }
//       return {
//         embedding: result.embedding,
//         dimensions: result.dimensions
//       }
//     } catch (error) {
//       console.error('向量生成失败:', error)
//       throw error
//     }
//   }
//
//   /**
//    * 完整的图片处理流程（分析 + 向量化）
//    * @param imageBase64 图片的Base64数据
//    * @param filename 可选的文件名
//    */
//   async processImage(imageBase64: string, filename?: string): Promise<ProcessImageResult> {
//     if (!this.checkElectronAPI()) {
//       throw new Error('ElectronAPI 不可用')
//     }
//
//     try {
//       const request: ProcessImageRequest = {
//         imageBase64,
//         filename
//       }
//
//       const result = await window.electronAPI.ai.processImage(request)
//       if (result.error) {
//         throw new Error(result.error)
//       }
//
//       return result
//     } catch (error) {
//       console.error('图片处理失败:', error)
//       throw error
//     }
//   }
//
//   /**
//    * 获取AI服务状态
//    */
//   async getStatus(): Promise<AIServiceStatus> {
//     if (!this.checkElectronAPI()) {
//       return {
//         connected: false,
//         vlModel: 'N/A',
//         embeddingModel: 'N/A',
//         error: 'ElectronAPI 不可用'
//       }
//     }
//
//     try {
//       return await window.electronAPI.ai.getStatus()
//     } catch (error) {
//       console.error('获取AI服务状态失败:', error)
//       return {
//         connected: false,
//         vlModel: 'N/A',
//         embeddingModel: 'N/A',
//         error: error instanceof Error ? error.message : String(error)
//       }
//     }
//   }
//
//   /**
//    * 手动测试AI服务连接
//    */
//   async manualTestConnection(): Promise<{ success: boolean; connected?: boolean; error?: string }> {
//     if (!this.checkElectronAPI()) {
//       return { success: false, error: 'ElectronAPI 不可用' }
//     }
//
//     try {
//       return await window.electronAPI.ai.manualTestConnection()
//     } catch (error) {
//       console.error('手动测试AI服务连接失败:', error)
//       return {
//         success: false,
//         error: error instanceof Error ? error.message : String(error)
//       }
//     }
//   }
//
//   /**
//    * 重置AI服务连接状态
//    */
//   async resetConnection(): Promise<{ success: boolean; error?: string }> {
//     if (!this.checkElectronAPI()) {
//       return { success: false, error: 'ElectronAPI 不可用' }
//     }
//
//     try {
//       return await window.electronAPI.ai.resetConnection()
//     } catch (error) {
//       console.error('重置AI服务连接失败:', error)
//       return {
//         success: false,
//         error: error instanceof Error ? error.message : String(error)
//       }
//     }
//   }
//
//   /**
//    * 获取AI服务连接状态
//    */
//   async getConnectionStatus(): Promise<{ tested: boolean; connected: boolean; error?: string; success?: boolean }> {
//     if (!this.checkElectronAPI()) {
//       return { tested: false, connected: false, error: 'ElectronAPI 不可用', success: false }
//     }
//
//     try {
//       return await window.electronAPI.ai.getConnectionStatus()
//     } catch (error) {
//       console.error('获取AI服务连接状态失败:', error)
//       return {
//         tested: false,
//         connected: false,
//         error: error instanceof Error ? error.message : String(error),
//         success: false
//       }
//     }
//   }
// }
//
// // 导出单例实例
// export const aiService = FrontendAIService.getInstance()
//
// /**
//  * 工具函数：将文件转换为Base64
//  */
// export function fileToBase64(file: File): Promise<string> {
//   return new Promise((resolve, reject) => {
//     const reader = new FileReader()
//     reader.onload = () => {
//       const result = reader.result as string
//       resolve(result)
//     }
//     reader.onerror = reject
//     reader.readAsDataURL(file)
//   })
// }
//
// /**
//  * 工具函数：从Base64中提取纯数据部分
//  */
// export function extractBase64Data(base64String: string): string {
//   const commaIndex = base64String.indexOf(',')
//   return commaIndex !== -1 ? base64String.substring(commaIndex + 1) : base64String
// }
//
// /**
//  * 工具函数：验证图片格式
//  */
// export function isValidImageFormat(file: File): boolean {
//   const validTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp']
//   return validTypes.includes(file.type.toLowerCase())
// }
//
// /**
//  * 工具函数：获取图片尺寸
//  */
// export function getImageDimensions(file: File): Promise<{ width: number; height: number }> {
//   return new Promise((resolve, reject) => {
//     const img = new Image()
//     const url = URL.createObjectURL(file)
//
//     img.onload = () => {
//       URL.revokeObjectURL(url)
//       resolve({
//         width: img.naturalWidth,
//         height: img.naturalHeight
//       })
//     }
//
//     img.onerror = () => {
//       URL.revokeObjectURL(url)
//       reject(new Error('无法读取图片尺寸'))
//     }
//
//     img.src = url
//   })
// }