import { ToolDefinition } from '../../electron/ai/VLChatService';
import { ImageTools } from '../../tools/imageTools';
import { toolMonitoring, startToolExecution, endToolExecution } from './toolMonitoring';
import { toolConfigManager } from './toolConfiguration';

/**
 * Tool execution result interface
 */
export interface ToolExecutionResult {
  success: boolean;
  data?: any;
  error?: string;
  executionTime?: number;
  toolName?: string;
}

/**
 * Tool execution bridge for VL Chat Service
 * Connects VL Chat Service to existing image tools with proper error handling and result formatting
 */
export class VLImageToolsBridge {

  /**
   * Execute a tool by name with parameters
   */
  static async executeTool(toolName: string, parameters: any): Promise<ToolExecutionResult> {
    // Check if tool is enabled
    if (!toolConfigManager.isToolEnabled(toolName)) {
      return {
        success: false,
        error: `Tool ${toolName} is disabled`,
        executionTime: 0,
        toolName
      };
    }

    // Start monitoring
    const executionId = startToolExecution(toolName, parameters);
    const startTime = Date.now();

    try {
      // Get tool configuration
      const config = toolConfigManager.getToolConfig(toolName);

      // Apply parameter defaults and overrides
      const mergedParameters = {
        ...toolConfigManager.getToolDefaults(toolName),
        ...parameters
      };

      toolMonitoring.log('debug', toolName, `Executing tool with merged parameters`, {
        originalParams: parameters,
        mergedParams: mergedParameters
      }, executionId);

      // Mark validation checkpoint
      toolMonitoring.markPerformanceCheckpoint(executionId, 'validation');

      let result: any;

      // Execute with timeout
      const executeWithTimeout = async (): Promise<any> => {
        switch (toolName) {
          case 'analyze_image':
            return await this.executeAnalyzeImage(mergedParameters);

          case 'find_similar_images_by_image':
            return await this.executeFindSimilarImagesByImage(mergedParameters);

          case 'find_similar_images_by_description':
            return await this.executeFindSimilarImagesByDescription(mergedParameters);

          case 'find_images_by_tags':
            return await this.executeFindImagesByTags(mergedParameters);

          default:
            throw new Error(`Unknown tool: ${toolName}`);
        }
      };

      // Mark execution checkpoint
      toolMonitoring.markPerformanceCheckpoint(executionId, 'execution');

      // Execute with timeout
      // eslint-disable-next-line prefer-const
      result = await Promise.race([
        executeWithTimeout(),
        new Promise((_, reject) =>
          setTimeout(() => reject(new Error(`Tool execution timeout after ${config.timeout}ms`)), config.timeout)
        )
      ]);

      // Mark formatting checkpoint
      toolMonitoring.markPerformanceCheckpoint(executionId, 'formatting');

      const executionTime = Date.now() - startTime;

      // End monitoring with success
      endToolExecution(executionId, true, result);

      toolMonitoring.log('info', toolName, `Tool execution completed successfully`, {
        executionTime,
        resultSize: JSON.stringify(result).length
      }, executionId, executionTime);

      return {
        success: true,
        data: result,
        executionTime,
        toolName
      };

    } catch (error) {
      const executionTime = Date.now() - startTime;
      const errorMessage = error instanceof Error ? error.message : String(error);

      // End monitoring with failure
      endToolExecution(executionId, false, undefined, errorMessage);

      toolMonitoring.log('error', toolName, `Tool execution failed: ${errorMessage}`, {
        executionTime,
        error: errorMessage
      }, executionId, executionTime);

      return {
        success: false,
        error: errorMessage,
        executionTime,
        toolName
      };
    }
  }

  /**
   * Execute analyze_image tool
   */
  private static async executeAnalyzeImage(parameters: any): Promise<any> {
    // Validate required parameters
    if (!parameters.imageBase64) {
      throw new Error('imageBase64 parameter is required');
    }

    // Validate image format
    if (!parameters.imageBase64.match(/^data:image\/(jpeg|jpg|png|webp);base64,/)) {
      throw new Error('Invalid image format. Please use JPEG, PNG, or WebP format with proper data URL prefix');
    }

    // Save base64 image to temporary file
    const tempFileName = `temp_analyze_${Date.now()}.jpg`;
    const saveResult = await window.electronAPI?.fileSystem.saveImage({
      base64Data: parameters.imageBase64,
      filename: tempFileName
    });

    if (!saveResult?.success || !saveResult.filePath) {
      throw new Error(saveResult?.error || 'Failed to save image to temporary file');
    }

    // Map VL model parameters to tool parameters
    const toolParams = {
      imagePath: saveResult.filePath,
      includeStructuredData: parameters.includeStructuredData !== false
    };

    const result = await ImageTools.analyzeImage(toolParams);

    if (!result.success) {
      throw new Error(result.error || 'Image analysis failed');
    }

    // Format result for VL model consumption
    return {
      success: true,
      description: result.description,
      tags: result.tags,
      structuredData: result.structuredData,
      confidence: result.confidence,
      analysisTime: result.analysisTime,
      model: result.model
    };
  }

  /**
   * Execute find_similar_images_by_image tool
   */
  private static async executeFindSimilarImagesByImage(parameters: any): Promise<any> {
    // Validate that either imageId or imageBase64 is provided
    if (!parameters.imageId && !parameters.imageBase64) {
      throw new Error('Either imageId or imageBase64 parameter is required');
    }

    if (parameters.imageId && parameters.imageBase64) {
      throw new Error('Cannot provide both imageId and imageBase64 parameters');
    }

    // Validate image format if imageBase64 is provided
    if (parameters.imageBase64 && !parameters.imageBase64.match(/^data:image\/(jpeg|jpg|png|webp);base64,/)) {
      throw new Error('Invalid image format. Please use JPEG, PNG, or WebP format with proper data URL prefix');
    }

    // Map parameters with intelligent defaults
    const toolParams = {
      imageId: parameters.imageId,
      imageBase64: parameters.imageBase64,
      limit: this.extractLimit(parameters.limit),
      threshold: this.extractThreshold(parameters.threshold)
    };

    const result = await ImageTools.findSimilarImagesByImage(toolParams);

    if (!result.success) {
      throw new Error(result.error || 'Similar image search failed');
    }

    // Format result for VL model consumption
    return {
      success: true,
      results: result.results,
      total: result.total,
      queryInfo: result.queryInfo
    };
  }

  /**
   * Execute find_similar_images_by_description tool
   */
  private static async executeFindSimilarImagesByDescription(parameters: any): Promise<any> {
    // Validate required parameters
    if (!parameters.description || typeof parameters.description !== 'string') {
      throw new Error('description parameter is required and must be a string');
    }

    if (parameters.description.trim().length === 0) {
      throw new Error('description cannot be empty');
    }

    if (parameters.description.length > 500) {
      throw new Error('description is too long (maximum 500 characters)');
    }

    // Map parameters with intelligent defaults
    const toolParams = {
      description: parameters.description.trim(),
      limit: this.extractLimit(parameters.limit),
      threshold: this.extractThreshold(parameters.threshold),
      enableKeywordExpansion: true // Always enable for better results
    };

    const result = await ImageTools.findSimilarImagesByDescription(toolParams);

    if (!result.success) {
      throw new Error(result.error || 'Description-based image search failed');
    }

    // Format result for VL model consumption
    return {
      success: true,
      results: result.results,
      total: result.total,
      searchInfo: result.searchInfo
    };
  }

  /**
   * Execute find_images_by_tags tool
   */
  private static async executeFindImagesByTags(parameters: any): Promise<any> {
    // Validate required parameters
    if (!parameters.tags || !Array.isArray(parameters.tags)) {
      throw new Error('tags parameter is required and must be an array');
    }

    if (parameters.tags.length === 0) {
      throw new Error('tags array cannot be empty');
    }

    if (parameters.tags.length > 10) {
      throw new Error('too many tags (maximum 10 tags allowed)');
    }

    // Validate individual tags
    for (const tag of parameters.tags) {
      if (typeof tag !== 'string' || tag.trim().length === 0) {
        throw new Error('all tags must be non-empty strings');
      }
      if (tag.length > 50) {
        throw new Error('tag is too long (maximum 50 characters per tag)');
      }
    }

    // Map parameters with intelligent defaults
    const toolParams = {
      tags: parameters.tags.map((tag: string) => tag.trim()),
      logic: this.extractMatchMode(parameters.matchMode),
      limit: this.extractLimit(parameters.limit),
      includeSimilarTags: true, // Enable for better results
      sortBy: 'relevance' as 'title' | 'uploadTime' | 'fileSize' | 'relevance',
      sortOrder: 'desc' as const
    };

    const result = await ImageTools.findImagesByTags(toolParams);

    if (!result.success) {
      throw new Error(result.error || 'Tag-based image search failed');
    }

    // Format result for VL model consumption
    return {
      success: true,
      results: result.results,
      total: result.total,
      searchInfo: result.searchInfo,
      tagStats: result.tagStats
    };
  }



  /**
   * Extract and validate limit parameter with intelligent defaults
   */
  private static extractLimit(limit?: number): number {
    if (limit === undefined || limit === null) {
      return 20; // Default limit
    }

    if (typeof limit !== 'number' || !Number.isInteger(limit)) {
      throw new Error('limit must be an integer');
    }

    if (limit < 1) {
      throw new Error('limit must be at least 1');
    }

    if (limit > 100) {
      throw new Error('limit cannot exceed 100');
    }

    return limit;
  }

  /**
   * Extract and validate threshold parameter with intelligent defaults
   */
  private static extractThreshold(threshold?: number): number {
    if (threshold === undefined || threshold === null) {
      return 0.5; // Default threshold for balanced results
    }

    if (typeof threshold !== 'number') {
      throw new Error('threshold must be a number');
    }

    if (threshold < 0 || threshold > 1) {
      throw new Error('threshold must be between 0 and 1');
    }

    return threshold;
  }

  /**
   * Extract and validate match mode parameter
   */
  private static extractMatchMode(matchMode?: string): 'AND' | 'OR' {
    if (!matchMode) {
      return 'OR'; // Default to OR logic
    }

    if (matchMode === 'any') {
      return 'OR';
    }

    if (matchMode === 'all') {
      return 'AND';
    }

    throw new Error('matchMode must be either "any" or "all"');
  }

  /**
   * Format tool execution error for user-friendly display
   */
  static formatToolError(toolName: string, error: string): string {
    const errorMappings: Record<string, Record<string, string>> = {
      'analyze_image': {
        'imageBase64 parameter is required': 'Please provide an image to analyze.',
        'Invalid image format': 'The image format is not supported. Please use JPEG, PNG, or WebP format.',
        'AI服务不可用': 'The AI service is currently unavailable. Please try again later.',
        'Image analysis failed': 'Failed to analyze the image. Please try with a different image.'
      },
      'find_similar_images_by_image': {
        'Either imageId or imageBase64 parameter is required': 'Please provide either an image ID or upload an image.',
        'Cannot provide both imageId and imageBase64 parameters': 'Please provide either an image ID or an image file, not both.',
        'Invalid image format': 'The image format is not supported. Please use JPEG, PNG, or WebP format.',
        'Similar image search failed': 'Failed to find similar images. Please try again.'
      },
      'find_similar_images_by_description': {
        'description parameter is required': 'Please provide a description of what you\'re looking for.',
        'description cannot be empty': 'Please provide a meaningful description.',
        'description is too long': 'The description is too long. Please keep it under 500 characters.',
        'Description-based image search failed': 'Failed to search for images. Please try with a different description.'
      },
      'find_images_by_tags': {
        'tags parameter is required': 'Please provide at least one tag to search for.',
        'tags array cannot be empty': 'Please provide at least one tag.',
        'too many tags': 'Too many tags provided. Please use at most 10 tags.',
        'all tags must be non-empty strings': 'All tags must be valid text.',
        'Tag-based image search failed': 'Failed to search for images by tags. Please try again.'
      },
      'get_image_analysis': {
        'Either imageId or imageBase64 parameter is required': 'Please provide either an image ID or upload an image.',
        'Cannot provide both imageId and imageBase64 parameters': 'Please provide either an image ID or an image file, not both.',
        'Image analysis retrieval failed': 'Failed to retrieve image analysis. Please try again.'
      }
    };

    const toolErrors = errorMappings[toolName];
    if (toolErrors) {
      // Try to find a matching error message
      for (const [key, value] of Object.entries(toolErrors)) {
        if (error.includes(key)) {
          return value;
        }
      }
    }

    // Fallback to generic error message
    return `An error occurred while executing ${toolName}: ${error}`;
  }

  /**
   * Validate tool parameters before execution
   */
  static validateToolParameters(toolName: string, parameters: any): { valid: boolean; error?: string } {
    try {
      switch (toolName) {
        case 'analyze_image':
          if (!parameters.imageBase64) {
            return { valid: false, error: 'imageBase64 parameter is required' };
          }
          if (!parameters.imageBase64.match(/^data:image\/(jpeg|jpg|png|webp);base64,/)) {
            return { valid: false, error: 'Invalid image format' };
          }
          break;

        case 'find_similar_images_by_image':
          if (!parameters.imageId && !parameters.imageBase64) {
            return { valid: false, error: 'Either imageId or imageBase64 parameter is required' };
          }
          if (parameters.imageId && parameters.imageBase64) {
            return { valid: false, error: 'Cannot provide both imageId and imageBase64 parameters' };
          }
          if (parameters.imageBase64 && !parameters.imageBase64.match(/^data:image\/(jpeg|jpg|png|webp);base64,/)) {
            return { valid: false, error: 'Invalid image format' };
          }
          break;

        case 'find_similar_images_by_description':
          if (!parameters.description || typeof parameters.description !== 'string') {
            return { valid: false, error: 'description parameter is required and must be a string' };
          }
          if (parameters.description.trim().length === 0) {
            return { valid: false, error: 'description cannot be empty' };
          }
          if (parameters.description.length > 500) {
            return { valid: false, error: 'description is too long (maximum 500 characters)' };
          }
          break;

        case 'find_images_by_tags':
          if (!parameters.tags || !Array.isArray(parameters.tags)) {
            return { valid: false, error: 'tags parameter is required and must be an array' };
          }
          if (parameters.tags.length === 0) {
            return { valid: false, error: 'tags array cannot be empty' };
          }
          if (parameters.tags.length > 10) {
            return { valid: false, error: 'too many tags (maximum 10 tags allowed)' };
          }
          for (const tag of parameters.tags) {
            if (typeof tag !== 'string' || tag.trim().length === 0) {
              return { valid: false, error: 'all tags must be non-empty strings' };
            }
            if (tag.length > 50) {
              return { valid: false, error: 'tag is too long (maximum 50 characters per tag)' };
            }
          }
          break;

        default:
          return { valid: false, error: `Unknown tool: ${toolName}` };
      }

      return { valid: true };

    } catch (error) {
      return {
        valid: false,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  /**
   * Get tool definition for VL Chat Service registration
   */
  static getToolDefinitions(): ToolDefinition[] {
    return [
      {
        type: 'function',
        function: {
          name: 'analyze_image',
          description: 'Analyze an uploaded image to extract detailed information including description, objects, colors, scene, mood, and other visual elements. Use this when users want to understand what\'s in an image.',
          parameters: {
            type: 'object',
            properties: {
              imageBase64: {
                type: 'string',
                description: 'Base64 encoded image data with data URL prefix (data:image/...)'
              },
              includeStructuredData: {
                type: 'boolean',
                default: true,
                description: 'Whether to include structured data in the analysis'
              }
            },
            required: ['imageBase64']
          }
        }
      },
      {
        type: 'function',
        function: {
          name: 'find_similar_images_by_image',
          description: 'Find images that are visually similar to a provided image using computer vision and vector similarity algorithms. Use this tool when users upload an image and want to find similar ones, or when they say things like \'find images like this\', \'search for similar pictures\', \'show me more like this image\', or \'find visually similar images\'.',
          parameters: {
            type: 'object',
            properties: {
              imageId: {
                type: 'string',
                description: 'ID of an existing image in the database to find similar images. Either imageId or imageBase64 must be provided.'
              },
              imageBase64: {
                type: 'string',
                description: 'Base64 encoded image data (must include data:image/ prefix) for real-time analysis and similarity search. Either imageId or imageBase64 must be provided.'
              },
              limit: {
                type: 'number',
                description: 'Maximum number of results to return. Extract from user language: \'a few\'=5-8, \'some\'=10-15, \'many\'=20-30, \'lots\'=40-50. Default is 20 if not specified.',
                default: 20,
                minimum: 1,
                maximum: 100
              },
              threshold: {
                type: 'number',
                description: 'Similarity threshold (0-1). Extract from user language: \'very similar\'=0.7-0.8, \'similar\'=0.5-0.6, \'somewhat similar\'=0.4-0.5, \'loosely related\'=0.2-0.4. Default is 0.5 for balanced results.',
                default: 0.5,
                minimum: 0,
                maximum: 1
              }
            },
            oneOf: [
              { required: ['imageId'] },
              { required: ['imageBase64'] }
            ]
          }
        }
      },
      {
        type: 'function',
        function: {
          name: 'find_similar_images_by_description',
          description: 'Search for images based on natural language descriptions using AI semantic understanding and vector search. Use this tool when users describe what they\'re looking for without providing an image, such as \'find images of cats playing\', \'show me sunset photos\', \'search for pictures of mountains\', or \'find images with happy people\'.',
          parameters: {
            type: 'object',
            properties: {
              description: {
                type: 'string',
                description: 'Natural language description of what to search for. Can describe objects, scenes, emotions, colors, actions, etc. More detailed descriptions yield more precise results.',
                minLength: 2,
                maxLength: 500
              },
              limit: {
                type: 'number',
                description: 'Maximum number of results to return. Extract from user language: \'a few\'=5-8, \'some\'=10-15, \'many\'=20-30, \'lots\'=40-50. Default is 20 if not specified.',
                default: 20,
                minimum: 1,
                maximum: 100
              },
              threshold: {
                type: 'number',
                description: 'Similarity threshold (0-1). Extract from user language: \'very similar\'=0.7-0.8, \'similar\'=0.5-0.6, \'somewhat similar\'=0.4-0.5, \'loosely related\'=0.2-0.4, \'broadly related\'=0.1-0.3. Default is 0.4 for balanced results.',
                default: 0.4,
                minimum: 0,
                maximum: 1
              }
            },
            required: ['description']
          }
        }
      },
      {
        type: 'function',
        function: {
          name: 'find_images_by_tags',
          description: 'Search for images using specific tags with AND/OR logic for precise or broad matching. Use this tool when users mention specific tags, categories, or when they say things like \'find images tagged with...\', \'show me pictures with tags...\', \'filter by tags...\', or \'images categorized as...\'.',
          parameters: {
            type: 'object',
            properties: {
              tags: {
                type: 'array',
                description: 'List of tags to search for. Extract relevant keywords and tags from user\'s request.',
                items: {
                  type: 'string',
                  minLength: 1,
                  maxLength: 50
                },
                minItems: 1,
                maxItems: 10,
                uniqueItems: true
              },
              limit: {
                type: 'number',
                description: 'Maximum number of results to return. Extract from user language: \'a few\'=5-8, \'some\'=10-15, \'many\'=20-30, \'lots\'=40-50. Default is 20 if not specified.',
                default: 20,
                minimum: 1,
                maximum: 100
              },
              matchMode: {
                type: 'string',
                description: 'Tag matching mode. Extract from user language: \'any\'=OR logic (matches any tag, more results), \'all\'=AND logic (matches all tags, more precise). Use \'all\' when user says \'with both\', \'having all\', \'must have all\'. Use \'any\' when user says \'or\', \'either\', \'any of\'. Default is \'any\'.',
                enum: ['any', 'all'],
                default: 'any'
              }
            },
            required: ['tags']
          }
        }
      }
    ];
  }

  /**
   * Create a tool executor function for VL Chat Service
   */
  static createToolExecutor() {
    return async (toolName: string, parameters: any): Promise<any> => {
      const result = await this.executeTool(toolName, parameters);

      if (!result.success) {
        throw new Error(result.error || `Tool execution failed: ${toolName}`);
      }

      return result.data;
    };
  }
}