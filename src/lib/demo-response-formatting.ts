/**
 * Demo script to showcase response formatting and conversation management features
 */

import { ResponseFormatter, ToolExecutionContext, ImageAnalysisResult, ImageSearchResult } from './responseFormatter';
import { conversationContextManager } from './conversationContextManager';

console.log('🎯 Response Formatting and Conversation Management Demo\n');

// Demo 1: Image Analysis Result Formatting
console.log('=== Demo 1: Image Analysis Result Formatting ===');

const mockAnalysisResult: ImageAnalysisResult = {
  success: true,
  description: "一只橙色的猫咪坐在阳光明媚的窗台上，背景是绿色的植物",
  tags: ["猫", "橙色", "窗台", "阳光", "植物", "室内", "宠物"],
  structuredData: {
    objects: [
      { name: "猫", confidence: 0.95, attributes: ["橙色", "坐着"] },
      { name: "窗台", confidence: 0.88, attributes: ["木制", "宽敞"] },
      { name: "植物", confidence: 0.82, attributes: ["绿色", "茂盛"] }
    ],
    colors: ["橙色", "绿色", "棕色", "白色"],
    scene: "室内家居环境",
    mood: "温馨平静"
  },
  confidence: 0.92,
  analysisTime: 1450
};

const analysisContext: ToolExecutionContext = {
  toolName: 'analyze_image',
  parameters: { 
    imageBase64: 'data:image/jpeg;base64,...',
    includeStructuredData: true
  },
  userQuery: '这张图片里有什么？',
  hasImage: true
};

const formattedAnalysis = ResponseFormatter.formatAnalysisResult(mockAnalysisResult, analysisContext);
console.log(formattedAnalysis);
console.log('\n');

// Demo 2: Search Result Formatting
console.log('=== Demo 2: Search Result Formatting ===');

const mockSearchResult: ImageSearchResult = {
  success: true,
  results: [
    {
      id: 'img_001',
      path: '/images/cat_window_001.jpg',
      similarity: 0.89,
      tags: ['猫', '窗台', '橙色'],
      description: '橙色猫咪在窗边'
    },
    {
      id: 'img_002', 
      path: '/images/cat_sunny_002.jpg',
      similarity: 0.76,
      tags: ['猫', '阳光', '室内'],
      description: '阳光下的猫咪'
    },
    {
      id: 'img_003',
      path: '/images/window_plants_003.jpg', 
      similarity: 0.68,
      tags: ['窗台', '植物', '绿色'],
      description: '窗台上的绿植'
    }
  ],
  total: 12
};

const searchContext: ToolExecutionContext = {
  toolName: 'find_similar_images_by_description',
  parameters: {
    description: '橙色猫咪在窗台上',
    limit: 15,
    threshold: 0.6
  },
  userQuery: '找一些类似的猫咪照片',
  hasImage: false
};

const formattedSearch = ResponseFormatter.formatSearchResult(mockSearchResult, searchContext);
console.log(formattedSearch);
console.log('\n');

// Demo 3: Conversation Context Management
console.log('=== Demo 3: Conversation Context Management ===');

const conversationId = 'demo-conversation-456';

// 开始一个分析工作流
console.log('📝 Starting analysis workflow...');
const workflowId = conversationContextManager.startWorkflow(
  conversationId, 
  'analysis',
  { imageBase64: 'data:image/jpeg;base64,demo-cat-image' }
);
console.log(`Started workflow: ${workflowId}`);

// 添加分析步骤
conversationContextManager.addWorkflowStep(
  conversationId,
  'analyze_image',
  { imageBase64: 'data:image/jpeg;base64,demo-cat-image', includeStructuredData: true },
  mockAnalysisResult,
  '分析这张猫咪的照片',
  true
);

// 添加搜索步骤
conversationContextManager.addWorkflowStep(
  conversationId,
  'find_similar_images_by_description',
  { description: '橙色猫咪在窗台', limit: 15, threshold: 0.6 },
  mockSearchResult,
  '找类似的猫咪图片',
  true
);

// 测试后续查询检测
console.log('\n🔍 Testing follow-up query detection...');
const followUpTests = [
  '显示更多类似的图片',
  '告诉我更多关于这些结果的信息',
  '今天天气怎么样？' // 非后续查询
];

followUpTests.forEach((query: any) => {
  const result = conversationContextManager.isFollowUpQuery(conversationId, query);
  console.log(`"${query}" -> Follow-up: ${result.isFollowUp}`);
});

// 获取上下文建议
console.log('\n💡 Getting contextual suggestions...');
const suggestions = conversationContextManager.getContextualSuggestions(conversationId);
suggestions.slice(0, 5).forEach((suggestion, index) => {
  console.log(`${index + 1}. [${suggestion.type}] ${suggestion.text} (优先级: ${suggestion.priority})`);
});

// 获取对话摘要
console.log('\n📊 Conversation summary:');
const summary = conversationContextManager.getConversationSummary(conversationId);
console.log(`总步骤数: ${summary.totalSteps}`);
console.log(`当前焦点: ${summary.currentFocus}`);
console.log('最近活动:', summary.recentActivity);
console.log('建议操作:', summary.suggestedActions.slice(0, 3));

// Demo 4: Error Handling
console.log('\n=== Demo 4: Error Handling ===');

const errorContext: ToolExecutionContext = {
  toolName: 'find_images_by_tags',
  parameters: { tags: ['独角兽', '彩虹'], matchMode: 'all' },
  userQuery: '找有独角兽和彩虹的图片',
  hasImage: false
};

const errorSuggestions = ResponseFormatter.generateFailureSuggestions(
  '没有找到匹配的图片',
  errorContext
);
console.log('错误处理建议:');
console.log(errorSuggestions);

// Demo 5: Multiple Results Formatting
console.log('\n=== Demo 5: Multiple Results Formatting ===');

const multipleResults = [
  { toolName: 'analyze_image', result: mockAnalysisResult },
  { toolName: 'find_similar_images_by_description', result: mockSearchResult }
];

const multiContext: ToolExecutionContext = {
  toolName: 'analyze_image',
  parameters: {},
  userQuery: '分析这张图片并找到类似的',
  hasImage: true
};

const formattedMultiple = ResponseFormatter.formatMultipleResults(multipleResults, multiContext);
console.log(formattedMultiple);

// 完成工作流
conversationContextManager.completeWorkflow(conversationId, 'completed');
console.log('\n✅ Demo completed successfully!');

console.log('\n🎉 All response formatting and conversation management features are working correctly!');