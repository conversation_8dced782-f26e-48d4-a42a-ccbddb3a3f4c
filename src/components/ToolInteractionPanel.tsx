/**
 * Tool Interaction Panel Component
 * Provides controls for image tool interactions and result management
 */

import { useState } from 'react';
import { 
  Settings, 
  Save, 
  Share2, 
  Download, 
  RefreshCw, 
  Filter, 
  Sliders,
  Search,
  ChevronDown,
  ChevronUp,
  Grid3X3,
  SortAsc,
  SortDesc
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface ToolInteractionPanelProps {
  className?: string;
  onRefineSearch?: (parameters: any) => void;
  onSaveResults?: (results: any[]) => void;
  onShareResults?: (results: any[]) => void;
  onExportResults?: (results: any[], format: 'json' | 'csv' | 'pdf') => void;
  currentResults?: any[];
  searchParameters?: any;
  isLoading?: boolean;
}

export function ToolInteractionPanel({
  className,
  onRefineSearch,
  onSaveResults,
  onShareResults,
  onExportResults,
  currentResults = [],
  searchParameters = {},
  isLoading = false
}: ToolInteractionPanelProps) {
  const [isExpanded, setIsExpanded] = useState(false);
  const [activeTab, setActiveTab] = useState<'refine' | 'results' | 'export'>('refine');

  return (
    <div className={cn(
      'bg-white/90 dark:bg-slate-800/90 backdrop-blur-sm border border-slate-200/50 dark:border-slate-600/50 rounded-xl overflow-hidden transition-all duration-300',
      isExpanded ? 'shadow-lg' : 'shadow-sm',
      className
    )}>
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-slate-200/50 dark:border-slate-600/50">
        <div className="flex items-center space-x-3">
          <div className="p-2 bg-blue-100 dark:bg-blue-900/30 rounded-lg">
            <Settings className="h-5 w-5 text-blue-600 dark:text-blue-400" />
          </div>
          <div>
            <h3 className="font-semibold text-slate-900 dark:text-slate-100">
              工具控制面板
            </h3>
            <p className="text-sm text-slate-600 dark:text-slate-400">
              管理搜索结果和参数调整
            </p>
          </div>
        </div>
        <button
          onClick={() => setIsExpanded(!isExpanded)}
          className="p-2 text-slate-600 dark:text-slate-400 hover:text-slate-900 dark:hover:text-slate-100 hover:bg-slate-100 dark:hover:bg-slate-700 rounded-lg transition-colors"
        >
          {isExpanded ? <ChevronUp className="h-5 w-5" /> : <ChevronDown className="h-5 w-5" />}
        </button>
      </div>

      {/* Quick actions (always visible) */}
      <div className="p-4 bg-slate-50/50 dark:bg-slate-800/50">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <span className="text-sm font-medium text-slate-700 dark:text-slate-300">
              快速操作
            </span>
            {currentResults.length > 0 && (
              <span className="text-xs px-2 py-1 bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 rounded-full">
                {currentResults.length} 个结果
              </span>
            )}
          </div>
          {isLoading && (
            <div className="flex items-center space-x-2 text-blue-600 dark:text-blue-400">
              <RefreshCw className="h-4 w-4 animate-spin" />
              <span className="text-sm">处理中...</span>
            </div>
          )}
        </div>
        
        <div className="grid grid-cols-2 md:grid-cols-4 gap-2 mt-3">
          <button
            onClick={() => onRefineSearch && onRefineSearch({})}
            disabled={isLoading}
            className="flex items-center justify-center space-x-2 px-3 py-2 text-sm font-medium text-blue-700 dark:text-blue-300 bg-blue-100 dark:bg-blue-900/30 hover:bg-blue-200 dark:hover:bg-blue-900/50 rounded-lg transition-colors disabled:opacity-50"
          >
            <Filter className="h-4 w-4" />
            <span>调整搜索</span>
          </button>
          
          <button
            onClick={() => onSaveResults && onSaveResults(currentResults)}
            disabled={isLoading || currentResults.length === 0}
            className="flex items-center justify-center space-x-2 px-3 py-2 text-sm font-medium text-green-700 dark:text-green-300 bg-green-100 dark:bg-green-900/30 hover:bg-green-200 dark:hover:bg-green-900/50 rounded-lg transition-colors disabled:opacity-50"
          >
            <Save className="h-4 w-4" />
            <span>保存结果</span>
          </button>
          
          <button
            onClick={() => onShareResults && onShareResults(currentResults)}
            disabled={isLoading || currentResults.length === 0}
            className="flex items-center justify-center space-x-2 px-3 py-2 text-sm font-medium text-purple-700 dark:text-purple-300 bg-purple-100 dark:bg-purple-900/30 hover:bg-purple-200 dark:hover:bg-purple-900/50 rounded-lg transition-colors disabled:opacity-50"
          >
            <Share2 className="h-4 w-4" />
            <span>分享</span>
          </button>
          
          <button
            onClick={() => onExportResults && onExportResults(currentResults, 'json')}
            disabled={isLoading || currentResults.length === 0}
            className="flex items-center justify-center space-x-2 px-3 py-2 text-sm font-medium text-orange-700 dark:text-orange-300 bg-orange-100 dark:bg-orange-900/30 hover:bg-orange-200 dark:hover:bg-orange-900/50 rounded-lg transition-colors disabled:opacity-50"
          >
            <Download className="h-4 w-4" />
            <span>导出</span>
          </button>
        </div>
      </div>

      {/* Expanded content */}
      {isExpanded && (
        <div className="border-t border-slate-200/50 dark:border-slate-600/50">
          {/* Tabs */}
          <div className="flex border-b border-slate-200/50 dark:border-slate-600/50">
            {[
              { id: 'refine', label: '搜索调整', icon: Sliders },
              { id: 'results', label: '结果管理', icon: Grid3X3 },
              { id: 'export', label: '导出选项', icon: Download }
            ].map(({ id, label, icon: Icon }) => (
              <button
                key={id}
                onClick={() => setActiveTab(id as any)}
                className={cn(
                  'flex items-center space-x-2 px-4 py-3 text-sm font-medium transition-colors',
                  activeTab === id
                    ? 'text-blue-600 dark:text-blue-400 border-b-2 border-blue-600 dark:border-blue-400 bg-blue-50/50 dark:bg-blue-900/20'
                    : 'text-slate-600 dark:text-slate-400 hover:text-slate-900 dark:hover:text-slate-100'
                )}
              >
                <Icon className="h-4 w-4" />
                <span>{label}</span>
              </button>
            ))}
          </div>

          {/* Tab content */}
          <div className="p-4">
            {activeTab === 'refine' && (
              <SearchRefinementPanel 
                searchParameters={searchParameters}
                onRefineSearch={onRefineSearch}
                isLoading={isLoading}
              />
            )}
            
            {activeTab === 'results' && (
              <ResultsManagementPanel 
                results={currentResults}
                onSaveResults={onSaveResults}
                onShareResults={onShareResults}
              />
            )}
            
            {activeTab === 'export' && (
              <ExportOptionsPanel 
                results={currentResults}
                onExportResults={onExportResults}
                isLoading={isLoading}
              />
            )}
          </div>
        </div>
      )}
    </div>
  );
}

interface SearchRefinementPanelProps {
  searchParameters: any;
  onRefineSearch?: (parameters: any) => void;
  isLoading: boolean;
}

function SearchRefinementPanel({ searchParameters, onRefineSearch, isLoading }: SearchRefinementPanelProps) {
  const [threshold, setThreshold] = useState(searchParameters.threshold || 0.5);
  const [limit, setLimit] = useState(searchParameters.limit || 20);
  const [tags, setTags] = useState(searchParameters.tags?.join(', ') || '');
  const [description, setDescription] = useState(searchParameters.description || '');

  const handleRefine = () => {
    const newParameters = {
      threshold,
      limit,
      ...(tags && { tags: tags.split(',').map((t: string) => t.trim()).filter((t: string) => t) }),
      ...(description && { description })
    };
    onRefineSearch && onRefineSearch(newParameters);
  };

  return (
    <div className="space-y-6">
      <div>
        <h4 className="text-sm font-semibold text-slate-900 dark:text-slate-100 mb-4">
          搜索参数调整
        </h4>
        
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">
              相似度阈值: {(threshold * 100).toFixed(0)}%
            </label>
            <input
              type="range"
              min="0"
              max="1"
              step="0.05"
              value={threshold}
              onChange={(e) => setThreshold(parseFloat(e.target.value))}
              className="w-full h-2 bg-slate-200 dark:bg-slate-700 rounded-lg appearance-none cursor-pointer"
            />
            <div className="flex justify-between text-xs text-slate-500 dark:text-slate-400 mt-1">
              <span>宽松</span>
              <span>严格</span>
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">
              结果数量: {limit}
            </label>
            <input
              type="range"
              min="5"
              max="100"
              step="5"
              value={limit}
              onChange={(e) => setLimit(parseInt(e.target.value))}
              className="w-full h-2 bg-slate-200 dark:bg-slate-700 rounded-lg appearance-none cursor-pointer"
            />
            <div className="flex justify-between text-xs text-slate-500 dark:text-slate-400 mt-1">
              <span>5</span>
              <span>100</span>
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">
              搜索描述
            </label>
            <input
              type="text"
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              placeholder="输入搜索描述..."
              className="w-full px-3 py-2 text-sm bg-white dark:bg-slate-800 border border-slate-200 dark:border-slate-600 rounded-lg focus:border-blue-500 focus:ring-2 focus:ring-blue-500/20 outline-none transition-colors"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">
              标签 (用逗号分隔)
            </label>
            <input
              type="text"
              value={tags}
              onChange={(e) => setTags(e.target.value)}
              placeholder="例如: 猫, 动物, 可爱"
              className="w-full px-3 py-2 text-sm bg-white dark:bg-slate-800 border border-slate-200 dark:border-slate-600 rounded-lg focus:border-blue-500 focus:ring-2 focus:ring-blue-500/20 outline-none transition-colors"
            />
          </div>
        </div>
      </div>

      <button
        onClick={handleRefine}
        disabled={isLoading}
        className="w-full flex items-center justify-center space-x-2 px-4 py-3 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg transition-colors disabled:opacity-50"
      >
        <Search className="h-4 w-4" />
        <span>应用新参数搜索</span>
      </button>
    </div>
  );
}

interface ResultsManagementPanelProps {
  results: any[];
  onSaveResults?: (results: any[]) => void;
  onShareResults?: (results: any[]) => void;
}

function ResultsManagementPanel({ results, onSaveResults, onShareResults }: ResultsManagementPanelProps) {
  const [selectedResults, setSelectedResults] = useState<number[]>([]);
  const [sortBy, setSortBy] = useState<'similarity' | 'title' | 'date'>('similarity');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');

  const toggleSelectResult = (index: number) => {
    setSelectedResults(prev => 
      prev.includes(index) 
        ? prev.filter((i: any) => i !== index)
        : [...prev, index]
    );
  };

  const selectAll = () => {
    setSelectedResults(results.map((_, index) => index));
  };

  const clearSelection = () => {
    setSelectedResults([]);
  };

  const getSelectedResults = () => {
    return selectedResults.map((index: any) => results[index]);
  };

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h4 className="text-sm font-semibold text-slate-900 dark:text-slate-100">
          结果管理 ({results.length} 个结果)
        </h4>
        <div className="flex items-center space-x-2">
          <button
            onClick={selectAll}
            className="text-xs text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300"
          >
            全选
          </button>
          <span className="text-slate-300 dark:text-slate-600">|</span>
          <button
            onClick={clearSelection}
            className="text-xs text-slate-600 dark:text-slate-400 hover:text-slate-700 dark:hover:text-slate-300"
          >
            清除
          </button>
        </div>
      </div>

      {selectedResults.length > 0 && (
        <div className="p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
          <div className="flex items-center justify-between">
            <span className="text-sm text-blue-700 dark:text-blue-300">
              已选择 {selectedResults.length} 个结果
            </span>
            <div className="flex space-x-2">
              <button
                onClick={() => onSaveResults && onSaveResults(getSelectedResults())}
                className="text-xs px-2 py-1 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
              >
                保存选中
              </button>
              <button
                onClick={() => onShareResults && onShareResults(getSelectedResults())}
                className="text-xs px-2 py-1 bg-green-600 text-white rounded hover:bg-green-700 transition-colors"
              >
                分享选中
              </button>
            </div>
          </div>
        </div>
      )}

      <div className="flex items-center space-x-4">
        <select
          value={sortBy}
          onChange={(e) => setSortBy(e.target.value as any)}
          className="text-sm bg-white dark:bg-slate-800 border border-slate-200 dark:border-slate-600 rounded px-2 py-1"
        >
          <option value="similarity">按相似度</option>
          <option value="title">按标题</option>
          <option value="date">按日期</option>
        </select>
        <button
          onClick={() => setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')}
          className="p-1 text-slate-600 dark:text-slate-400 hover:text-slate-900 dark:hover:text-slate-100"
        >
          {sortOrder === 'asc' ? <SortAsc className="h-4 w-4" /> : <SortDesc className="h-4 w-4" />}
        </button>
      </div>

      <div className="max-h-48 overflow-y-auto space-y-2">
        {results.map((result, index) => (
          <div
            key={index}
            className={cn(
              'flex items-center space-x-3 p-2 rounded-lg cursor-pointer transition-colors',
              selectedResults.includes(index)
                ? 'bg-blue-100 dark:bg-blue-900/30'
                : 'hover:bg-slate-100 dark:hover:bg-slate-800'
            )}
            onClick={() => toggleSelectResult(index)}
          >
            <input
              type="checkbox"
              checked={selectedResults.includes(index)}
              onChange={() => toggleSelectResult(index)}
              className="rounded"
            />
            <div className="flex-1 min-w-0">
              <p className="text-sm font-medium text-slate-900 dark:text-slate-100 truncate">
                {result.title || `结果 ${index + 1}`}
              </p>
              {result.similarity && (
                <p className="text-xs text-slate-600 dark:text-slate-400">
                  相似度: {(result.similarity * 100).toFixed(1)}%
                </p>
              )}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}

interface ExportOptionsPanelProps {
  results: any[];
  onExportResults?: (results: any[], format: 'json' | 'csv' | 'pdf') => void;
  isLoading: boolean;
}

function ExportOptionsPanel({ results, onExportResults, isLoading }: ExportOptionsPanelProps) {
  const [exportFormat, setExportFormat] = useState<'json' | 'csv' | 'pdf'>('json');
  const [includeImages, setIncludeImages] = useState(false);
  const [includeMetadata, setIncludeMetadata] = useState(true);

  const handleExport = () => {
    onExportResults && onExportResults(results, exportFormat);
  };

  return (
    <div className="space-y-4">
      <h4 className="text-sm font-semibold text-slate-900 dark:text-slate-100">
        导出选项
      </h4>

      <div className="space-y-3">
        <div>
          <label className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">
            导出格式
          </label>
          <div className="grid grid-cols-3 gap-2">
            {[
              { value: 'json', label: 'JSON', desc: '结构化数据' },
              { value: 'csv', label: 'CSV', desc: '表格数据' },
              { value: 'pdf', label: 'PDF', desc: '报告格式' }
            ].map(({ value, label, desc }) => (
              <button
                key={value}
                onClick={() => setExportFormat(value as any)}
                className={cn(
                  'p-3 text-center rounded-lg border transition-colors',
                  exportFormat === value
                    ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300'
                    : 'border-slate-200 dark:border-slate-600 hover:bg-slate-50 dark:hover:bg-slate-800'
                )}
              >
                <div className="font-medium text-sm">{label}</div>
                <div className="text-xs text-slate-500 dark:text-slate-400">{desc}</div>
              </button>
            ))}
          </div>
        </div>

        <div className="space-y-2">
          <label className="flex items-center space-x-2">
            <input
              type="checkbox"
              checked={includeImages}
              onChange={(e) => setIncludeImages(e.target.checked)}
              className="rounded"
            />
            <span className="text-sm text-slate-700 dark:text-slate-300">
              包含图片数据
            </span>
          </label>
          <label className="flex items-center space-x-2">
            <input
              type="checkbox"
              checked={includeMetadata}
              onChange={(e) => setIncludeMetadata(e.target.checked)}
              className="rounded"
            />
            <span className="text-sm text-slate-700 dark:text-slate-300">
              包含元数据
            </span>
          </label>
        </div>
      </div>

      <button
        onClick={handleExport}
        disabled={isLoading || results.length === 0}
        className="w-full flex items-center justify-center space-x-2 px-4 py-3 bg-green-600 hover:bg-green-700 text-white font-medium rounded-lg transition-colors disabled:opacity-50"
      >
        <Download className="h-4 w-4" />
        <span>导出 {results.length} 个结果</span>
      </button>
    </div>
  );
}