import React, { useState } from 'react'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { cn } from '@/lib/utils'
import { 
  SearchMethod,
  SEARCH_METHODS,
  SIMILARITY_THRESHOLDS,
  SimilarityThresholdConfig,
  getPrecisionLevel,
  getPrecisionLabel,
  validateThreshold,
  getThresholdGuidance,
  getRecommendedThreshold
} from '../types/search'

interface SearchControlPanelProps {
  selectedMethod: SearchMethod
  onMethodChange: (method: SearchMethod) => void
  threshold: number
  onThresholdChange: (threshold: number) => void
  disabled?: boolean
  className?: string
}

export function SearchControlPanel({
  selectedMethod,
  onMethodChange,
  threshold,
  onThresholdChange,
  disabled = false,
  className
}: SearchControlPanelProps): JSX.Element {
  const [isDragging, setIsDragging] = useState(false)
  const [isMethodExpanded, setIsMethodExpanded] = useState(false)
  
  // 组件是否应该显示阈值控制（只在向量搜索或自动模式下显示）
  const showThresholdControl = selectedMethod === 'vector' || selectedMethod === 'auto'
  
  // 获取当前阈值的验证结果和指导信息
  const validation = validateThreshold(threshold)
  const guidance = getThresholdGuidance(threshold)
  const currentPrecision = getPrecisionLevel(threshold)
  const precisionLabel = getPrecisionLabel(threshold)

  // 处理方法变更
  const handleMethodChange = (method: SearchMethod): void => {
    if (disabled) return
    onMethodChange(method)
  }

  // 处理阈值变更
  const handleThresholdChange = (newThreshold: number): void => {
    if (disabled) return
    onThresholdChange(newThreshold)
  }

  // 应用推荐阈值
  const applyRecommendedThreshold = (useCase: 'precise' | 'balanced' | 'exploratory'): void => {
    const recommended = getRecommendedThreshold(useCase)
    handleThresholdChange(recommended)
  }

  // 处理滑块输入
  const handleSliderChange = (event: React.ChangeEvent<HTMLInputElement>): void => {
    const newThreshold = parseFloat(event.target.value)
    handleThresholdChange(newThreshold)
  }

  // 处理预设阈值点击
  const handlePresetClick = (preset: SimilarityThresholdConfig): void => {
    handleThresholdChange(preset.value)
  }

  return (
    <Card className={cn("w-full", className)}>
      <CardContent className="p-6">
        <div className="space-y-6">
          {/* 搜索方法选择 - 简化版 */}
          <div className="space-y-3">
            <h4 className="text-sm font-medium text-slate-700 dark:text-slate-300">
              搜索方法
            </h4>
            
            {/* 当前选中的方法显示 - 可点击展开 */}
            <button
              onClick={() => setIsMethodExpanded(!isMethodExpanded)}
              disabled={disabled}
              className={cn(
                "w-full p-3 bg-slate-50 dark:bg-slate-800/50 rounded-lg border border-slate-200 dark:border-slate-700 transition-all",
                "hover:bg-slate-100 dark:hover:bg-slate-700/50",
                "focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50",
                disabled && "opacity-50 cursor-not-allowed"
              )}
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <span className="text-lg" role="img" aria-label={SEARCH_METHODS.find(m => m.id === selectedMethod)?.name}>
                    {SEARCH_METHODS.find(m => m.id === selectedMethod)?.icon}
                  </span>
                  <div className="text-left">
                    <div className="font-medium text-slate-900 dark:text-slate-100">
                      {SEARCH_METHODS.find(m => m.id === selectedMethod)?.name}
                    </div>
                    <p className="text-sm text-slate-600 dark:text-slate-400">
                      {SEARCH_METHODS.find(m => m.id === selectedMethod)?.description}
                    </p>
                  </div>
                </div>
                <div className={cn(
                  "text-slate-400 dark:text-slate-500 transition-transform duration-200",
                  isMethodExpanded && "rotate-180"
                )}>
                  ▼
                </div>
              </div>
            </button>
            
            {/* 展开的方法选择 */}
            {isMethodExpanded && (
              <div className="space-y-1 animate-fade-in-up">
                {SEARCH_METHODS.filter(method => method.id !== selectedMethod).map((method) => (
                  <button
                    key={method.id}
                    onClick={() => {
                      handleMethodChange(method.id)
                      setIsMethodExpanded(false)
                    }}
                    disabled={disabled}
                    className={cn(
                      "w-full flex items-center space-x-3 p-3 rounded-lg transition-all",
                      "hover:bg-slate-100 dark:hover:bg-slate-700/50",
                      "focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50",
                      "border border-slate-200 dark:border-slate-700",
                      disabled && "opacity-50 cursor-not-allowed"
                    )}
                  >
                    <span className="text-lg" role="img" aria-label={method.name}>
                      {method.icon}
                    </span>
                    <div className="flex-1 text-left">
                      <div className="font-medium text-slate-900 dark:text-slate-100">
                        {method.name}
                      </div>
                      <p className="text-sm text-slate-600 dark:text-slate-400">
                        {method.description}
                      </p>
                    </div>
                  </button>
                ))}
              </div>
            )}
          </div>

          {/* 相似度阈值控制 */}
          {showThresholdControl && (
            <div className="space-y-4 pt-4 border-t border-slate-200 dark:border-slate-700">
              <div className="flex items-center justify-between">
                <h4 className="text-sm font-medium text-slate-700 dark:text-slate-300">
                  相似度阈值
                </h4>
                <Badge 
                  variant={currentPrecision === 'high' ? 'default' : 'secondary'}
                  className={cn(
                    "text-xs",
                    currentPrecision === 'high' && "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200",
                    currentPrecision === 'medium' && "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200",
                    currentPrecision === 'low' && "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200"
                  )}
                >
                  {precisionLabel}
                </Badge>
              </div>

              {/* 滑块控件 */}
              <div className="space-y-4">
                <div className="relative">
                  {/* 自定义滑块容器 */}
                  <div className="relative h-6 flex items-center">
                    {/* 滑块轨道 */}
                    <div className="w-full h-2 bg-slate-200 dark:bg-slate-700 rounded-full relative">
                      {/* 已填充部分 */}
                      <div 
                        className="h-full bg-gradient-to-r from-blue-500 to-blue-600 rounded-full transition-all duration-200"
                        style={{ width: `${((threshold - 0.1) / 0.9) * 100}%` }}
                      />
                      {/* 滑块拇指 */}
                      <div 
                        className={cn(
                          "absolute top-1/2 -translate-y-1/2 w-5 h-5 bg-white dark:bg-slate-200 rounded-full shadow-lg border-2 border-blue-500 transition-all duration-200",
                          "hover:scale-110 hover:shadow-xl",
                          isDragging && "scale-110 shadow-xl",
                          disabled && "opacity-50 cursor-not-allowed"
                        )}
                        style={{ left: `calc(${((threshold - 0.1) / 0.9) * 100}% - 10px)` }}
                      />
                    </div>
                    {/* 隐藏的原生滑块用于交互 */}
                    <input
                      type="range"
                      min="0.1"
                      max="1.0"
                      step="0.05"
                      value={threshold}
                      onChange={handleSliderChange}
                      onMouseDown={() => setIsDragging(true)}
                      onMouseUp={() => setIsDragging(false)}
                      onTouchStart={() => setIsDragging(true)}
                      onTouchEnd={() => setIsDragging(false)}
                      disabled={disabled}
                      className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                    />
                  </div>
                  
                  {/* 阈值数值显示 */}
                  <div className="flex justify-between items-center text-xs text-slate-500 dark:text-slate-400 mt-2">
                    <span>0.1</span>
                    <div className="px-2 py-1 bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 rounded-md font-medium">
                      {threshold.toFixed(2)}
                    </div>
                    <span>1.0</span>
                  </div>
                </div>

                {/* 快速设置按钮 - 精简版 */}
                <div className="flex gap-2">
                  <button
                    onClick={() => applyRecommendedThreshold('precise')}
                    disabled={disabled}
                    className={cn(
                      "flex-1 px-3 py-2 text-xs rounded-md border transition-all",
                      "hover:bg-green-50 dark:hover:bg-green-900/20",
                      "focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-opacity-50",
                      "disabled:opacity-50 disabled:cursor-not-allowed",
                      "border-green-300 text-green-700 dark:border-green-600 dark:text-green-300"
                    )}
                    title="精确搜索 (0.8) - 只显示高度相关的结果"
                  >
                    🎯 精确
                  </button>
                  <button
                    onClick={() => applyRecommendedThreshold('balanced')}
                    disabled={disabled}
                    className={cn(
                      "flex-1 px-3 py-2 text-xs rounded-md border transition-all",
                      "hover:bg-blue-50 dark:hover:bg-blue-900/20",
                      "focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50",
                      "disabled:opacity-50 disabled:cursor-not-allowed",
                      "border-blue-300 text-blue-700 dark:border-blue-600 dark:text-blue-300"
                    )}
                    title="平衡搜索 (0.6) - 推荐设置，平衡相关性和结果数量"
                  >
                    ⚖️ 平衡
                  </button>
                  <button
                    onClick={() => applyRecommendedThreshold('exploratory')}
                    disabled={disabled}
                    className={cn(
                      "flex-1 px-3 py-2 text-xs rounded-md border transition-all",
                      "hover:bg-purple-50 dark:hover:bg-purple-900/20",
                      "focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-opacity-50",
                      "disabled:opacity-50 disabled:cursor-not-allowed",
                      "border-purple-300 text-purple-700 dark:border-purple-600 dark:text-purple-300"
                    )}
                    title="探索搜索 (0.4) - 显示更多可能相关的结果"
                  >
                    🔍 探索
                  </button>
                </div>

                {/* 阈值指导信息 */}
                {(validation.warning || guidance.warning || guidance.description) && (
                  <div className="p-3 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg">
                    <div className="space-y-1">
                      <p className="text-sm text-blue-700 dark:text-blue-300">
                        {guidance.description}
                      </p>
                      {(validation.warning || guidance.warning) && (
                        <p className="text-xs text-blue-600 dark:text-blue-400 font-medium">
                          ⚠️ {validation.warning || guidance.warning}
                        </p>
                      )}
                      {validation.suggestion && (
                        <p className="text-xs text-blue-600 dark:text-blue-400">
                          💡 {validation.suggestion}
                        </p>
                      )}
                    </div>
                  </div>
                )}
              </div>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )
}