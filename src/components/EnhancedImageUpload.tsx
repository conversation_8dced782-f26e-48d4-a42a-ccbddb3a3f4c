/**
 * Enhanced Image Upload Component
 * Provides image upload with quick action buttons for common operations
 */

import React, { useState, useRef } from 'react';
import { 
  Upload, 
  X, 
  Image as ImageIcon, 
  Eye, 
  Search, 
  Tags, 
  Sparkles,
  Camera,
  FileImage,
  Zap,
  Target,
  Grid3X3
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface ImageData {
  url: string;
  base64: string;
  file: File;
}

interface EnhancedImageUploadProps {
  onImageSelect: (imageData: ImageData) => void;
  onQuickAction?: (action: 'analyze' | 'search_similar' | 'search_tags', imageData: ImageData) => void;
  disabled?: boolean;
  className?: string;
  showQuickActions?: boolean;
  maxFileSize?: number;
  acceptedFormats?: string[];
}

export function EnhancedImageUpload({ 
  onImageSelect, 
  onQuickAction,
  disabled = false, 
  className,
  showQuickActions = true,
  maxFileSize = 10,
  acceptedFormats = ['image/jpeg', 'image/png', 'image/webp', 'image/gif']
}: EnhancedImageUploadProps): JSX.Element {
  const [previewImage, setPreviewImage] = useState<string | null>(null);
  const [currentImageData, setCurrentImageData] = useState<ImageData | null>(null);
  const [isDragOver, setIsDragOver] = useState<boolean>(false);
  const [isProcessing, setIsProcessing] = useState<boolean>(false);
  const [uploadProgress, setUploadProgress] = useState<number>(0);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const validateFile = (file: File): { valid: boolean; error?: string } => {
    if (!acceptedFormats.includes(file.type)) {
      return { 
        valid: false, 
        error: `不支持的文件格式。请使用: ${acceptedFormats.map((f: any) => f.split('/')[1].toUpperCase()).join(', ')}` 
      };
    }

    if (file.size > maxFileSize * 1024 * 1024) {
      return { 
        valid: false, 
        error: `文件大小超过限制 (${maxFileSize}MB)` 
      };
    }

    return { valid: true };
  };

  const handleFileSelect = async (file: File): Promise<void> => {
    const validation = validateFile(file);
    if (!validation.valid) {
      alert(validation.error);
      return;
    }

    setIsProcessing(true);
    setUploadProgress(0);

    const progressInterval = setInterval(() => {
      setUploadProgress(prev => {
        if (prev >= 90) {
          clearInterval(progressInterval);
          return 90;
        }
        return prev + 10;
      });
    }, 100);

    try {
      const reader = new FileReader();
      reader.onload = (e: ProgressEvent<FileReader>): void => {
        const result = e.target?.result as string;
        const imageData: ImageData = {
          url: result,
          base64: result,
          file
        };

        setPreviewImage(result);
        setCurrentImageData(imageData);
        setUploadProgress(100);
        
        setTimeout(() => {
          setIsProcessing(false);
          setUploadProgress(0);
          onImageSelect(imageData);
        }, 300);
      };
      
      reader.onerror = (): void => {
        clearInterval(progressInterval);
        setIsProcessing(false);
        setUploadProgress(0);
        alert('文件读取失败，请重试');
      };

      reader.readAsDataURL(file);
    } catch (error) {
      clearInterval(progressInterval);
      setIsProcessing(false);
      setUploadProgress(0);
      alert('文件处理失败，请重试');
    }
  };

  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>): void => {
    const file = e.target.files?.[0];
    if (file) {
      handleFileSelect(file);
    }
  };

  const handleDragOver = (e: React.DragEvent<HTMLDivElement>): void => {
    e.preventDefault();
    if (!disabled) {
      setIsDragOver(true);
    }
  };

  const handleDragLeave = (e: React.DragEvent<HTMLDivElement>): void => {
    e.preventDefault();
    setIsDragOver(false);
  };

  const handleDrop = (e: React.DragEvent<HTMLDivElement>): void => {
    e.preventDefault();
    setIsDragOver(false);
    
    if (disabled) return;
    
    const file = e.dataTransfer.files?.[0];
    if (file) {
      handleFileSelect(file);
    }
  };

  const clearPreview = (): void => {
    setPreviewImage(null);
    setCurrentImageData(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const openFileDialog = (): void => {
    if (!disabled && fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  const handleQuickAction = (action: 'analyze' | 'search_similar' | 'search_tags'): void => {
    if (currentImageData && onQuickAction) {
      onQuickAction(action, currentImageData);
    }
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <div className={cn('relative', className)}>
      <input
        ref={fileInputRef}
        type="file"
        accept={acceptedFormats.join(',')}
        onChange={handleFileInputChange}
        className="hidden"
        disabled={disabled}
      />

      {!previewImage ? (
        <div
          className={cn(
            'border-2 border-dashed rounded-xl p-8 text-center cursor-pointer transition-all duration-300',
            isDragOver
              ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20 scale-105'
              : 'border-slate-300 dark:border-slate-600 hover:border-blue-400 hover:bg-slate-50 dark:hover:bg-slate-800/50',
            disabled && 'opacity-50 cursor-not-allowed',
            isProcessing && 'pointer-events-none'
          )}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onDrop={handleDrop}
          onClick={openFileDialog}
        >
          {isProcessing ? (
            <div className="space-y-4">
              <div className="animate-spin mx-auto">
                <Sparkles className="h-8 w-8 text-blue-500" />
              </div>
              <div className="space-y-2">
                <p className="text-sm font-medium text-slate-700 dark:text-slate-300">
                  处理图片中...
                </p>
                <div className="w-full bg-slate-200 dark:bg-slate-700 rounded-full h-2">
                  <div 
                    className="bg-blue-500 h-2 rounded-full transition-all duration-300"
                    style={{ width: `${uploadProgress}%` }}
                  />
                </div>
                <p className="text-xs text-slate-500 dark:text-slate-400">
                  {uploadProgress}%
                </p>
              </div>
            </div>
          ) : (
            <div className="space-y-4">
              <div className="flex justify-center">
                <div className="p-4 bg-blue-100 dark:bg-blue-900/30 rounded-full">
                  <Upload className="h-8 w-8 text-blue-600 dark:text-blue-400" />
                </div>
              </div>
              <div className="space-y-2">
                <p className="text-lg font-medium text-slate-900 dark:text-slate-100">
                  上传图片
                </p>
                <p className="text-sm text-slate-600 dark:text-slate-400">
                  点击选择文件或拖拽图片到此处
                </p>
                <p className="text-xs text-slate-500 dark:text-slate-400">
                  支持 {acceptedFormats.map((f: any) => f.split('/')[1].toUpperCase()).join(', ')} 格式，最大 {maxFileSize}MB
                </p>
              </div>
            </div>
          )}
        </div>
      ) : (
        <div className="space-y-4">
          {/* Image preview */}
          <div className="relative group">
            <img
              src={previewImage}
              alt="预览图片"
              className="w-full max-h-64 rounded-xl object-cover shadow-lg"
            />
            <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-colors duration-200 rounded-xl" />
            <button
              onClick={clearPreview}
              disabled={disabled}
              className="absolute top-3 right-3 p-2 bg-black/50 hover:bg-black/70 text-white rounded-full transition-colors opacity-0 group-hover:opacity-100"
            >
              <X className="h-4 w-4" />
            </button>
            
            {/* Image info overlay */}
            {currentImageData && (
              <div className="absolute bottom-3 left-3 right-3 bg-black/50 backdrop-blur-sm text-white p-3 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                <div className="flex items-center justify-between text-sm">
                  <span className="font-medium truncate">
                    {currentImageData.file.name}
                  </span>
                  <span className="text-white/80 ml-2">
                    {formatFileSize(currentImageData.file.size)}
                  </span>
                </div>
              </div>
            )}
          </div>

          {/* Quick action buttons */}
          {showQuickActions && currentImageData && onQuickAction && (
            <div className="space-y-3">
              <div className="flex items-center space-x-2">
                <Zap className="h-4 w-4 text-blue-600 dark:text-blue-400" />
                <span className="text-sm font-medium text-slate-700 dark:text-slate-300">
                  快速操作
                </span>
              </div>
              <div className="grid grid-cols-1 sm:grid-cols-3 gap-2">
                <button
                  onClick={() => handleQuickAction('analyze')}
                  disabled={disabled}
                  className="flex items-center justify-center space-x-2 px-4 py-3 bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300 hover:bg-green-200 dark:hover:bg-green-900/50 rounded-lg transition-colors disabled:opacity-50"
                >
                  <Eye className="h-4 w-4" />
                  <span className="text-sm font-medium">分析图片</span>
                </button>
                <button
                  onClick={() => handleQuickAction('search_similar')}
                  disabled={disabled}
                  className="flex items-center justify-center space-x-2 px-4 py-3 bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 hover:bg-blue-200 dark:hover:bg-blue-900/50 rounded-lg transition-colors disabled:opacity-50"
                >
                  <Search className="h-4 w-4" />
                  <span className="text-sm font-medium">找相似</span>
                </button>
                <button
                  onClick={() => handleQuickAction('search_tags')}
                  disabled={disabled}
                  className="flex items-center justify-center space-x-2 px-4 py-3 bg-purple-100 dark:bg-purple-900/30 text-purple-700 dark:text-purple-300 hover:bg-purple-200 dark:hover:bg-purple-900/50 rounded-lg transition-colors disabled:opacity-50"
                >
                  <Tags className="h-4 w-4" />
                  <span className="text-sm font-medium">标签搜索</span>
                </button>
              </div>
            </div>
          )}

          {/* Alternative upload button */}
          <button
            onClick={openFileDialog}
            disabled={disabled}
            className="w-full flex items-center justify-center space-x-2 px-4 py-2 text-sm font-medium text-slate-600 dark:text-slate-400 bg-slate-100 dark:bg-slate-800 hover:bg-slate-200 dark:hover:bg-slate-700 rounded-lg transition-colors disabled:opacity-50"
          >
            <FileImage className="h-4 w-4" />
            <span>选择其他图片</span>
          </button>
        </div>
      )}
    </div>
  );
}

interface ImageUploadStatsProps {
  totalUploads: number;
  successfulUploads: number;
  averageFileSize: number;
  className?: string;
}

export function ImageUploadStats({ 
  totalUploads, 
  successfulUploads, 
  averageFileSize,
  className 
}: ImageUploadStatsProps): JSX.Element {
  const successRate = totalUploads > 0 ? (successfulUploads / totalUploads) * 100 : 0;

  return (
    <div className={cn('grid grid-cols-3 gap-4', className)}>
      <div className="text-center p-3 bg-slate-50 dark:bg-slate-800/50 rounded-lg">
        <div className="text-lg font-semibold text-slate-900 dark:text-slate-100">
          {totalUploads}
        </div>
        <div className="text-xs text-slate-600 dark:text-slate-400">
          总上传数
        </div>
      </div>
      <div className="text-center p-3 bg-slate-50 dark:bg-slate-800/50 rounded-lg">
        <div className="text-lg font-semibold text-green-600 dark:text-green-400">
          {successRate.toFixed(1)}%
        </div>
        <div className="text-xs text-slate-600 dark:text-slate-400">
          成功率
        </div>
      </div>
      <div className="text-center p-3 bg-slate-50 dark:bg-slate-800/50 rounded-lg">
        <div className="text-lg font-semibold text-blue-600 dark:text-blue-400">
          {(averageFileSize / 1024 / 1024).toFixed(1)}MB
        </div>
        <div className="text-xs text-slate-600 dark:text-slate-400">
          平均大小
        </div>
      </div>
    </div>
  );
}