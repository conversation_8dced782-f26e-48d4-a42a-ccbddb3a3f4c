/**
 * Enhanced Tool Result Display Component
 * Provides improved visual presentation for image tool results
 */

import React, { useState } from 'react';
import { 
  Image as ImageIcon, 
  Search, 
  Tags, 
  Eye, 
  Star, 
  ChevronDown, 
  ChevronUp,
  AlertCircle,
  CheckCircle,
  ExternalLink,
  Download,
  Share2,
  Maximize2,
  Filter,
  Grid3X3,
  List,
  <PERSON>rk<PERSON>,
  Clock,
  Target,
  Palette,
  Camera
} from 'lucide-react';
import { ToolCallResponse } from '../types/tools';

interface EnhancedToolResultDisplayProps {
  toolResult: ToolCallResponse;
  className?: string;
  onImageClick?: (imageUrl: string, imageData: any) => void;
  onRefineSearch?: (parameters: any) => void;
  onSaveResult?: (result: any) => void;
}

export function EnhancedToolResultDisplay({ 
  toolResult, 
  className = '',
  onImageClick,
  onRefineSearch,
  onSaveResult
}: EnhancedToolResultDisplayProps) {
  const [isExpanded, setIsExpanded] = useState(false);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [showFilters, setShowFilters] = useState(false);
  
  const { result } = toolResult;
  const toolName = result.metadata?.toolName || 'unknown';

  if (!result.success) {
    return (
      <EnhancedErrorDisplay 
        toolResult={toolResult} 
        className={className}
        onRefineSearch={onRefineSearch}
      />
    );
  }

  switch (toolName) {
    case 'analyze_image':
      return (
        <EnhancedImageAnalysisDisplay 
          toolResult={toolResult} 
          className={className}
          onRefineSearch={onRefineSearch}
          onSaveResult={onSaveResult}
        />
      );
    case 'find_similar_images_by_image':
    case 'find_similar_images_by_description':
    case 'find_images_by_tags':
      return (
        <EnhancedImageSearchDisplay 
          toolResult={toolResult} 
          className={className}
          viewMode={viewMode}
          onViewModeChange={setViewMode}
          onImageClick={onImageClick}
          onRefineSearch={onRefineSearch}
          onSaveResult={onSaveResult}
        />
      );
    default:
      return (
        <GenericEnhancedToolResult 
          toolResult={toolResult} 
          className={className}
        />
      );
  }
}

interface EnhancedErrorDisplayProps {
  toolResult: ToolCallResponse;
  className?: string;
  onRefineSearch?: (parameters: any) => void;
}

function EnhancedErrorDisplay({ toolResult, className = '', onRefineSearch }: EnhancedErrorDisplayProps) {
  const { result } = toolResult;
  const toolName = result.metadata?.toolName || 'unknown';

  const getErrorSuggestions = (toolName: string, error: string) => {
    const suggestions: string[] = [];
    
    switch (toolName) {
      case 'analyze_image':
        suggestions.push('检查图片格式是否为 JPEG、PNG 或 WebP');
        suggestions.push('确保图片文件大小合理');
        suggestions.push('尝试使用不同的图片');
        break;
      case 'find_similar_images_by_description':
        suggestions.push('尝试使用更具体或不同的关键词');
        suggestions.push('使用更简单的描述');
        suggestions.push('尝试按标签搜索');
        break;
      case 'find_images_by_tags':
        suggestions.push('使用不同或更常见的标签');
        suggestions.push('尝试"任意匹配"而不是"全部匹配"');
        suggestions.push('使用描述搜索获得更灵活的结果');
        break;
    }
    
    return suggestions;
  };

  const suggestions = getErrorSuggestions(toolName, result.error || '');

  return (
    <div className={`bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800/30 rounded-xl p-6 ${className}`}>
      <div className="flex items-start space-x-4">
        <div className="p-3 bg-red-100 dark:bg-red-900/30 rounded-full">
          <AlertCircle className="h-6 w-6 text-red-500" />
        </div>
        <div className="flex-1 space-y-4">
          <div>
            <h4 className="text-lg font-semibold text-red-700 dark:text-red-300 mb-2">
              工具执行失败
            </h4>
            <p className="text-red-600 dark:text-red-400">
              {result.error || '未知错误'}
            </p>
          </div>
          
          {suggestions.length > 0 && (
            <div>
              <h5 className="text-sm font-medium text-red-700 dark:text-red-300 mb-2">
                💡 建议尝试：
              </h5>
              <ul className="space-y-1">
                {suggestions.map((suggestion, index) => (
                  <li key={index} className="text-sm text-red-600 dark:text-red-400 flex items-start">
                    <span className="mr-2">•</span>
                    <span>{suggestion}</span>
                  </li>
                ))}
              </ul>
            </div>
          )}

          {onRefineSearch && (
            <div className="flex space-x-3">
              <button
                onClick={() => onRefineSearch({})}
                className="inline-flex items-center px-4 py-2 text-sm font-medium text-red-700 dark:text-red-300 bg-red-100 dark:bg-red-900/30 hover:bg-red-200 dark:hover:bg-red-900/50 rounded-lg transition-colors"
              >
                <Filter className="h-4 w-4 mr-2" />
                调整参数重试
              </button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

interface EnhancedImageAnalysisDisplayProps {
  toolResult: ToolCallResponse;
  className?: string;
  onRefineSearch?: (parameters: any) => void;
  onSaveResult?: (result: any) => void;
}

function EnhancedImageAnalysisDisplay({ 
  toolResult, 
  className = '',
  onRefineSearch,
  onSaveResult
}: EnhancedImageAnalysisDisplayProps) {
  const [activeTab, setActiveTab] = useState<'overview' | 'details' | 'technical'>('overview');
  const { result } = toolResult;
  const data = result.data;
  
  if (!data) {
    return <GenericEnhancedToolResult toolResult={toolResult} className={className} />;
  }

  const confidenceLevel = data.confidence >= 0.8 ? 'high' : data.confidence >= 0.6 ? 'good' : 'moderate';
  const confidenceColor = confidenceLevel === 'high' ? 'text-green-600' : 
                         confidenceLevel === 'good' ? 'text-blue-600' : 'text-yellow-600';

  return (
    <div className={`bg-white/90 dark:bg-slate-800/90 backdrop-blur-sm border border-slate-200/50 dark:border-slate-600/50 rounded-xl overflow-hidden ${className}`}>
      {/* Header */}
      <div className="p-6 bg-gradient-to-r from-green-50 to-blue-50 dark:from-green-900/20 dark:to-blue-900/20 border-b border-slate-200/50 dark:border-slate-600/50">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="p-3 bg-green-100 dark:bg-green-900/30 rounded-full">
              <Eye className="h-6 w-6 text-green-600 dark:text-green-400" />
            </div>
            <div>
              <h4 className="text-lg font-semibold text-slate-900 dark:text-slate-100">
                图像分析结果
              </h4>
              <div className="flex items-center space-x-4 mt-1">
                <span className={`text-sm font-medium ${confidenceColor}`}>
                  置信度: {(data.confidence * 100).toFixed(1)}%
                </span>
                <span className="text-sm text-slate-500 dark:text-slate-400">
                  {result.metadata?.executionTime}ms
                </span>
              </div>
            </div>
          </div>
          <div className="flex space-x-2">
            {onSaveResult && (
              <button
                onClick={() => onSaveResult(data)}
                className="p-2 text-slate-600 dark:text-slate-400 hover:text-slate-900 dark:hover:text-slate-100 hover:bg-white/50 dark:hover:bg-slate-700/50 rounded-lg transition-colors"
              >
                <Download className="h-5 w-5" />
              </button>
            )}
            <button className="p-2 text-slate-600 dark:text-slate-400 hover:text-slate-900 dark:hover:text-slate-100 hover:bg-white/50 dark:hover:bg-slate-700/50 rounded-lg transition-colors">
              <Share2 className="h-5 w-5" />
            </button>
          </div>
        </div>
      </div>

      {/* Tabs */}
      <div className="flex border-b border-slate-200/50 dark:border-slate-600/50">
        {[
          { id: 'overview', label: '概览', icon: Sparkles },
          { id: 'details', label: '详细信息', icon: List },
          { id: 'technical', label: '技术信息', icon: Camera }
        ].map(({ id, label, icon: Icon }) => (
          <button
            key={id}
            onClick={() => setActiveTab(id as any)}
            className={`flex items-center space-x-2 px-6 py-3 text-sm font-medium transition-colors ${
              activeTab === id
                ? 'text-blue-600 dark:text-blue-400 border-b-2 border-blue-600 dark:border-blue-400 bg-blue-50/50 dark:bg-blue-900/20'
                : 'text-slate-600 dark:text-slate-400 hover:text-slate-900 dark:hover:text-slate-100'
            }`}
          >
            <Icon className="h-4 w-4" />
            <span>{label}</span>
          </button>
        ))}
      </div>

      {/* Content */}
      <div className="p-6">
        {activeTab === 'overview' && (
          <div className="space-y-6">
            {data.description && (
              <div>
                <h5 className="text-sm font-semibold text-slate-700 dark:text-slate-300 mb-3 flex items-center">
                  <Sparkles className="h-4 w-4 mr-2" />
                  图像描述
                </h5>
                <p className="text-slate-600 dark:text-slate-400 leading-relaxed bg-slate-50 dark:bg-slate-800/50 p-4 rounded-lg">
                  {data.description}
                </p>
              </div>
            )}

            {data.tags && data.tags.length > 0 && (
              <div>
                <h5 className="text-sm font-semibold text-slate-700 dark:text-slate-300 mb-3 flex items-center">
                  <Tags className="h-4 w-4 mr-2" />
                  识别标签
                </h5>
                <div className="flex flex-wrap gap-2">
                  {data.tags.map((tag: string, index: number) => (
                    <span 
                      key={index} 
                      className="inline-flex items-center px-3 py-1 text-sm bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 rounded-full hover:bg-blue-200 dark:hover:bg-blue-900/50 transition-colors cursor-pointer"
                      onClick={() => onRefineSearch && onRefineSearch({ tags: [tag] })}
                    >
                      {tag}
                    </span>
                  ))}
                </div>
              </div>
            )}

            {data.structuredData?.colors && data.structuredData.colors.length > 0 && (
              <div>
                <h5 className="text-sm font-semibold text-slate-700 dark:text-slate-300 mb-3 flex items-center">
                  <Palette className="h-4 w-4 mr-2" />
                  主要颜色
                </h5>
                <div className="flex flex-wrap gap-2">
                  {data.structuredData.colors.map((color: string, index: number) => (
                    <span key={index} className="px-3 py-1 text-sm bg-slate-100 dark:bg-slate-800 text-slate-700 dark:text-slate-300 rounded-full">
                      {color}
                    </span>
                  ))}
                </div>
              </div>
            )}
          </div>
        )}

        {activeTab === 'details' && (
          <div className="space-y-6">
            {data.structuredData?.objects && data.structuredData.objects.length > 0 && (
              <div>
                <h5 className="text-sm font-semibold text-slate-700 dark:text-slate-300 mb-4">
                  识别对象
                </h5>
                <div className="space-y-3">
                  {data.structuredData.objects.map((obj: any, index: number) => (
                    <div key={index} className="flex items-center justify-between p-4 bg-slate-50 dark:bg-slate-800/50 rounded-lg">
                      <div className="flex-1">
                        <div className="font-medium text-slate-900 dark:text-slate-100">
                          {obj.name}
                        </div>
                        {obj.attributes && obj.attributes.length > 0 && (
                          <div className="text-sm text-slate-600 dark:text-slate-400 mt-1">
                            {obj.attributes.join(', ')}
                          </div>
                        )}
                      </div>
                      <div className="flex items-center space-x-3">
                        <div className="w-24 bg-slate-200 dark:bg-slate-600 rounded-full h-2">
                          <div 
                            className="bg-green-500 h-2 rounded-full transition-all duration-500"
                            style={{ width: `${(obj.confidence * 100)}%` }}
                          />
                        </div>
                        <span className="text-sm font-medium text-slate-700 dark:text-slate-300 min-w-[3rem]">
                          {(obj.confidence * 100).toFixed(1)}%
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {(data.structuredData?.scene || data.structuredData?.mood) && (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {data.structuredData.scene && (
                  <div className="p-4 bg-slate-50 dark:bg-slate-800/50 rounded-lg">
                    <h6 className="text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">场景</h6>
                    <p className="text-slate-600 dark:text-slate-400">{data.structuredData.scene}</p>
                  </div>
                )}
                {data.structuredData.mood && (
                  <div className="p-4 bg-slate-50 dark:bg-slate-800/50 rounded-lg">
                    <h6 className="text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">情绪</h6>
                    <p className="text-slate-600 dark:text-slate-400">{data.structuredData.mood}</p>
                  </div>
                )}
              </div>
            )}
          </div>
        )}

        {activeTab === 'technical' && (
          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="p-4 bg-slate-50 dark:bg-slate-800/50 rounded-lg">
                <div className="flex items-center space-x-2 mb-2">
                  <Target className="h-4 w-4 text-slate-500" />
                  <span className="text-sm font-medium text-slate-700 dark:text-slate-300">置信度</span>
                </div>
                <p className="text-lg font-semibold text-slate-900 dark:text-slate-100">
                  {(data.confidence * 100).toFixed(1)}%
                </p>
              </div>
              <div className="p-4 bg-slate-50 dark:bg-slate-800/50 rounded-lg">
                <div className="flex items-center space-x-2 mb-2">
                  <Clock className="h-4 w-4 text-slate-500" />
                  <span className="text-sm font-medium text-slate-700 dark:text-slate-300">分析时间</span>
                </div>
                <p className="text-lg font-semibold text-slate-900 dark:text-slate-100">
                  {data.analysisTime || result.metadata?.executionTime}ms
                </p>
              </div>
            </div>
            {data.model && (
              <div className="p-4 bg-slate-50 dark:bg-slate-800/50 rounded-lg">
                <span className="text-sm font-medium text-slate-700 dark:text-slate-300">使用模型: </span>
                <span className="text-slate-600 dark:text-slate-400">{data.model}</span>
              </div>
            )}
          </div>
        )}
      </div>

      {/* Action buttons */}
      {onRefineSearch && (
        <div className="px-6 py-4 bg-slate-50/50 dark:bg-slate-800/50 border-t border-slate-200/50 dark:border-slate-600/50">
          <div className="flex space-x-3">
            <button
              onClick={() => onRefineSearch({ 
                description: data.description,
                tags: data.tags?.slice(0, 3) 
              })}
              className="inline-flex items-center px-4 py-2 text-sm font-medium text-blue-700 dark:text-blue-300 bg-blue-100 dark:bg-blue-900/30 hover:bg-blue-200 dark:hover:bg-blue-900/50 rounded-lg transition-colors"
            >
              <Search className="h-4 w-4 mr-2" />
              查找相似图片
            </button>
            {data.tags && data.tags.length > 0 && (
              <button
                onClick={() => onRefineSearch({ tags: data.tags.slice(0, 3) })}
                className="inline-flex items-center px-4 py-2 text-sm font-medium text-green-700 dark:text-green-300 bg-green-100 dark:bg-green-900/30 hover:bg-green-200 dark:hover:bg-green-900/50 rounded-lg transition-colors"
              >
                <Tags className="h-4 w-4 mr-2" />
                按标签搜索
              </button>
            )}
          </div>
        </div>
      )}
    </div>
  );
}

interface EnhancedImageSearchDisplayProps {
  toolResult: ToolCallResponse;
  className?: string;
  viewMode: 'grid' | 'list';
  onViewModeChange: (mode: 'grid' | 'list') => void;
  onImageClick?: (imageUrl: string, imageData: any) => void;
  onRefineSearch?: (parameters: any) => void;
  onSaveResult?: (result: any) => void;
}

function EnhancedImageSearchDisplay({ 
  toolResult, 
  className = '',
  viewMode,
  onViewModeChange,
  onImageClick,
  onRefineSearch,
  onSaveResult
}: EnhancedImageSearchDisplayProps) {
  const [showAll, setShowAll] = useState(false);
  const [sortBy, setSortBy] = useState<'similarity' | 'date' | 'title'>('similarity');
  const { result } = toolResult;
  const data = result.data;
  
  if (!data || !data.results) {
    return <GenericEnhancedToolResult toolResult={toolResult} className={className} />;
  }

  const images = data.results;
  const displayImages = showAll ? images : images.slice(0, 12);
  const hasMore = images.length > 12;

  // Sort images
  const sortedImages = [...displayImages].sort((a, b) => {
    switch (sortBy) {
      case 'similarity':
        return (b.similarity || 0) - (a.similarity || 0);
      case 'title':
        return (a.title || '').localeCompare(b.title || '');
      default:
        return 0;
    }
  });

  const getSearchMethodIcon = (toolName: string) => {
    switch (toolName) {
      case 'find_similar_images_by_image':
        return ImageIcon;
      case 'find_similar_images_by_description':
        return Search;
      case 'find_images_by_tags':
        return Tags;
      default:
        return Search;
    }
  };

  const getSearchMethodName = (toolName: string) => {
    switch (toolName) {
      case 'find_similar_images_by_image':
        return '以图搜图';
      case 'find_similar_images_by_description':
        return '描述搜索';
      case 'find_images_by_tags':
        return '标签搜索';
      default:
        return '图片搜索';
    }
  };

  const SearchIcon = getSearchMethodIcon(result.metadata?.toolName || '');

  return (
    <div className={`bg-white/90 dark:bg-slate-800/90 backdrop-blur-sm border border-slate-200/50 dark:border-slate-600/50 rounded-xl overflow-hidden ${className}`}>
      {/* Header */}
      <div className="p-6 bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 border-b border-slate-200/50 dark:border-slate-600/50">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="p-3 bg-blue-100 dark:bg-blue-900/30 rounded-full">
              <SearchIcon className="h-6 w-6 text-blue-600 dark:text-blue-400" />
            </div>
            <div>
              <h4 className="text-lg font-semibold text-slate-900 dark:text-slate-100">
                {getSearchMethodName(result.metadata?.toolName || '')}结果
              </h4>
              <div className="flex items-center space-x-4 mt-1">
                <span className="text-sm font-medium text-blue-600 dark:text-blue-400">
                  找到 {data.total || images.length} 张图片
                </span>
                <span className="text-sm text-slate-500 dark:text-slate-400">
                  {result.metadata?.executionTime}ms
                </span>
              </div>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <div className="flex bg-slate-100 dark:bg-slate-700 rounded-lg p-1">
              <button
                onClick={() => onViewModeChange('grid')}
                className={`p-2 rounded transition-colors ${
                  viewMode === 'grid' 
                    ? 'bg-white dark:bg-slate-600 text-slate-900 dark:text-slate-100 shadow-sm' 
                    : 'text-slate-600 dark:text-slate-400'
                }`}
              >
                <Grid3X3 className="h-4 w-4" />
              </button>
              <button
                onClick={() => onViewModeChange('list')}
                className={`p-2 rounded transition-colors ${
                  viewMode === 'list' 
                    ? 'bg-white dark:bg-slate-600 text-slate-900 dark:text-slate-100 shadow-sm' 
                    : 'text-slate-600 dark:text-slate-400'
                }`}
              >
                <List className="h-4 w-4" />
              </button>
            </div>
            {onSaveResult && (
              <button
                onClick={() => onSaveResult(data)}
                className="p-2 text-slate-600 dark:text-slate-400 hover:text-slate-900 dark:hover:text-slate-100 hover:bg-white/50 dark:hover:bg-slate-700/50 rounded-lg transition-colors"
              >
                <Download className="h-5 w-5" />
              </button>
            )}
          </div>
        </div>
      </div>

      {/* Controls */}
      <div className="px-6 py-4 bg-slate-50/50 dark:bg-slate-800/50 border-b border-slate-200/50 dark:border-slate-600/50">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value as any)}
              className="text-sm bg-white dark:bg-slate-700 border border-slate-200 dark:border-slate-600 rounded-lg px-3 py-1"
            >
              <option value="similarity">按相似度排序</option>
              <option value="title">按标题排序</option>
              <option value="date">按日期排序</option>
            </select>
          </div>
          {onRefineSearch && (
            <button
              onClick={() => onRefineSearch({})}
              className="inline-flex items-center px-3 py-1 text-sm font-medium text-blue-700 dark:text-blue-300 bg-blue-100 dark:bg-blue-900/30 hover:bg-blue-200 dark:hover:bg-blue-900/50 rounded-lg transition-colors"
            >
              <Filter className="h-4 w-4 mr-1" />
              调整搜索
            </button>
          )}
        </div>
      </div>

      {/* Results */}
      <div className="p-6">
        {viewMode === 'grid' ? (
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
            {sortedImages.map((image: any, index: number) => (
              <div key={index} className="group relative">
                <div className="aspect-square rounded-lg overflow-hidden bg-slate-100 dark:bg-slate-800 cursor-pointer"
                     onClick={() => onImageClick && onImageClick(image.url, image)}>
                  <img
                    src={image.url || '/api/placeholder/200/200'}
                    alt={image.title || '搜索结果'}
                    className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-200"
                  />
                  <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-colors duration-200 flex items-center justify-center">
                    <Maximize2 className="h-6 w-6 text-white opacity-0 group-hover:opacity-100 transition-opacity duration-200" />
                  </div>
                </div>
                <div className="mt-3 space-y-2">
                  <h5 className="text-sm font-medium text-slate-900 dark:text-slate-100 line-clamp-1">
                    {image.title || '未命名图片'}
                  </h5>
                  {image.similarity && (
                    <div className="flex items-center space-x-2">
                      <Star className="h-3 w-3 text-yellow-500" />
                      <span className="text-xs text-slate-600 dark:text-slate-400">
                        {(image.similarity * 100).toFixed(1)}% 相似
                      </span>
                    </div>
                  )}
                  {image.tags && image.tags.length > 0 && (
                    <div className="flex flex-wrap gap-1">
                      {image.tags.slice(0, 2).map((tag: string, tagIndex: number) => (
                        <span key={tagIndex} className="text-xs px-2 py-1 bg-slate-100 dark:bg-slate-800 text-slate-600 dark:text-slate-400 rounded">
                          {tag}
                        </span>
                      ))}
                      {image.tags.length > 2 && (
                        <span className="text-xs text-slate-500 dark:text-slate-400">
                          +{image.tags.length - 2}
                        </span>
                      )}
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="space-y-4">
            {sortedImages.map((image: any, index: number) => (
              <div key={index} className="flex items-center space-x-4 p-4 bg-slate-50 dark:bg-slate-800/50 rounded-lg hover:bg-slate-100 dark:hover:bg-slate-800/70 transition-colors cursor-pointer"
                   onClick={() => onImageClick && onImageClick(image.url, image)}>
                <img
                  src={image.url || '/api/placeholder/80/80'}
                  alt={image.title || '搜索结果'}
                  className="w-16 h-16 rounded-lg object-cover"
                />
                <div className="flex-1 space-y-1">
                  <h5 className="font-medium text-slate-900 dark:text-slate-100">
                    {image.title || '未命名图片'}
                  </h5>
                  {image.description && (
                    <p className="text-sm text-slate-600 dark:text-slate-400 line-clamp-2">
                      {image.description}
                    </p>
                  )}
                  {image.tags && image.tags.length > 0 && (
                    <div className="flex flex-wrap gap-1">
                      {image.tags.slice(0, 4).map((tag: string, tagIndex: number) => (
                        <span key={tagIndex} className="text-xs px-2 py-1 bg-slate-200 dark:bg-slate-700 text-slate-600 dark:text-slate-400 rounded">
                          {tag}
                        </span>
                      ))}
                    </div>
                  )}
                </div>
                {image.similarity && (
                  <div className="text-right">
                    <div className="flex items-center space-x-1 mb-1">
                      <Star className="h-4 w-4 text-yellow-500" />
                      <span className="text-sm font-medium text-slate-700 dark:text-slate-300">
                        {(image.similarity * 100).toFixed(1)}%
                      </span>
                    </div>
                    <div className="w-16 bg-slate-200 dark:bg-slate-600 rounded-full h-1">
                      <div 
                        className="bg-blue-500 h-1 rounded-full transition-all duration-300"
                        style={{ width: `${(image.similarity * 100)}%` }}
                      />
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>
        )}

        {hasMore && (
          <div className="mt-6 text-center">
            <button
              onClick={() => setShowAll(!showAll)}
              className="inline-flex items-center space-x-2 px-6 py-3 text-sm font-medium text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/30 hover:bg-blue-100 dark:hover:bg-blue-900/50 rounded-lg transition-colors"
            >
              {showAll ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
              <span>{showAll ? '收起' : `显示全部 ${images.length} 张图片`}</span>
            </button>
          </div>
        )}
      </div>
    </div>
  );
}

interface GenericEnhancedToolResultProps {
  toolResult: ToolCallResponse;
  className?: string;
}

function GenericEnhancedToolResult({ toolResult, className = '' }: GenericEnhancedToolResultProps) {
  const { result } = toolResult;
  const toolName = result.metadata?.toolName || 'unknown';

  return (
    <div className={`bg-white/90 dark:bg-slate-800/90 backdrop-blur-sm border border-slate-200/50 dark:border-slate-600/50 rounded-xl p-6 ${className}`}>
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center space-x-3">
          <div className="p-3 bg-green-100 dark:bg-green-900/30 rounded-full">
            <CheckCircle className="h-6 w-6 text-green-600 dark:text-green-400" />
          </div>
          <div>
            <h4 className="text-lg font-semibold text-slate-900 dark:text-slate-100">
              工具执行成功
            </h4>
            <p className="text-sm text-slate-600 dark:text-slate-400">
              {toolName} • {result.metadata?.executionTime}ms
            </p>
          </div>
        </div>
      </div>

      {result.data && (
        <div className="p-4 bg-slate-50 dark:bg-slate-800/50 rounded-lg">
          <pre className="text-sm text-slate-700 dark:text-slate-300 whitespace-pre-wrap overflow-x-auto">
            {JSON.stringify(result.data, null, 2)}
          </pre>
        </div>
      )}
    </div>
  );
}