import React from 'react'
import { BaseProgress, ScanProgress, ImportProgress, AIAnalysisProgress } from '../types/progress'
import { Button } from './ui/button'
import { Badge } from './ui/badge'
import { Card, CardContent, CardHeader, CardTitle } from './ui/card'
import { 
  Pause, 
  Play, 
  X, 
  FileImage, 
  Brain, 
  Upload,
  CheckCircle2,
  XCircle,
  Clock,
  Activity,
  HardDrive,
  Zap
} from 'lucide-react'

interface ProgressCardProps {
  progress: BaseProgress
  showDetails?: boolean
  allowControls?: boolean
  onPause?: () => void
  onResume?: () => void
  onCancel?: () => void
  compact?: boolean
}

export const ProgressCard: React.FC<ProgressCardProps> = ({
  progress,
  showDetails = true,
  allowControls = true,
  onPause,
  onResume,
  onCancel,
  compact = false
}) => {
  // 获取状态颜色
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'running': return 'border-blue-500 bg-blue-50'
      case 'completed': return 'border-green-500 bg-green-50'
      case 'failed': return 'border-red-500 bg-red-50'
      case 'paused': return 'border-yellow-500 bg-yellow-50'
      case 'cancelled': return 'border-gray-500 bg-gray-50'
      default: return 'border-gray-300 bg-gray-50'
    }
  }

  // 获取状态图标
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'running': return <Activity className="h-4 w-4 text-blue-600 animate-pulse" />
      case 'completed': return <CheckCircle2 className="h-4 w-4 text-green-600" />
      case 'failed': return <XCircle className="h-4 w-4 text-red-600" />
      case 'paused': return <Pause className="h-4 w-4 text-yellow-600" />
      case 'cancelled': return <X className="h-4 w-4 text-gray-600" />
      default: return <Clock className="h-4 w-4 text-gray-600" />
    }
  }

  // 获取类型图标
  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'scan': return <HardDrive className="h-5 w-5 text-blue-600" />
      case 'import': return <Upload className="h-5 w-5 text-green-600" />
      case 'analysis': return <Brain className="h-5 w-5 text-purple-600" />
      default: return <FileImage className="h-5 w-5 text-gray-600" />
    }
  }

  // 计算进度百分比
  const getProgressPercentage = (): number => {
    if (progress.type === 'scan') {
      const scanProgress = progress as ScanProgress
      return scanProgress.totalFiles > 0 
        ? Math.round((scanProgress.processedFiles / scanProgress.totalFiles) * 100)
        : 0
    } else if (progress.type === 'import') {
      const importProgress = progress as ImportProgress
      return importProgress.totalFiles > 0
        ? Math.round((importProgress.processedFiles / importProgress.totalFiles) * 100)
        : 0
    } else if (progress.type === 'analysis') {
      const analysisProgress = progress as AIAnalysisProgress
      return analysisProgress.totalImages > 0
        ? Math.round((analysisProgress.analyzedImages / analysisProgress.totalImages) * 100)
        : 0
    }
    return 0
  }

  // 格式化时间
  const formatTime = (date: Date): string => {
    return new Intl.DateTimeFormat('zh-CN', {
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    }).format(date)
  }

  // 格式化持续时间
  const formatDuration = (startTime: Date, endTime?: Date): string => {
    const end = endTime || new Date()
    const duration = Math.floor((end.getTime() - startTime.getTime()) / 1000)
    
    if (duration < 60) {
      return `${duration}秒`
    } else if (duration < 3600) {
      return `${Math.floor(duration / 60)}分${duration % 60}秒`
    } else {
      const hours = Math.floor(duration / 3600)
      const minutes = Math.floor((duration % 3600) / 60)
      return `${hours}时${minutes}分`
    }
  }

  // 获取标题
  const getTitle = (): string => {
    if (progress.type === 'scan') {
      const scanProgress = progress as ScanProgress
      return `扫描: ${scanProgress.libraryName}`
    } else if (progress.type === 'import') {
      const importProgress = progress as ImportProgress
      return `导入: ${importProgress.sourceFolder.split(/[\/\\]/).pop()}`
    } else if (progress.type === 'analysis') {
      return 'AI分析'
    }
    return '未知任务'
  }

  const percentage = getProgressPercentage()

  if (compact) {
    return (
      <div className={`border rounded-lg p-3 ${getStatusColor(progress.status)}`}>
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            {getTypeIcon(progress.type)}
            {getStatusIcon(progress.status)}
            <span className="text-sm font-medium truncate">{getTitle()}</span>
          </div>
          <div className="flex items-center space-x-2">
            <span className="text-sm text-gray-600">{percentage}%</span>
            {allowControls && progress.status === 'running' && (
              <Button 
                size="sm" 
                variant="ghost" 
                onClick={onPause}
                className="h-6 w-6 p-0"
              >
                <Pause className="h-3 w-3" />
              </Button>
            )}
          </div>
        </div>
        <div className="mt-2">
          <div className="w-full bg-gray-200 rounded-full h-1.5">
            <div 
              className="h-1.5 rounded-full transition-all duration-300 bg-blue-500"
              style={{ width: `${percentage}%` }}
            />
          </div>
        </div>
      </div>
    )
  }

  return (
    <Card className={`${getStatusColor(progress.status)} border-l-4`}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            {getTypeIcon(progress.type)}
            <div>
              <CardTitle className="text-lg">{getTitle()}</CardTitle>
              <div className="flex items-center space-x-2 mt-1">
                {getStatusIcon(progress.status)}
                <Badge variant="secondary" className="text-xs">
                  {progress.status === 'running' ? '运行中' :
                   progress.status === 'completed' ? '已完成' :
                   progress.status === 'failed' ? '失败' :
                   progress.status === 'paused' ? '已暂停' :
                   progress.status === 'cancelled' ? '已取消' : '待处理'}
                </Badge>
              </div>
            </div>
          </div>
          
          {allowControls && (
            <div className="flex space-x-2">
              {progress.status === 'running' && (
                <Button size="sm" variant="outline" onClick={onPause}>
                  <Pause className="h-4 w-4 mr-1" />
                  暂停
                </Button>
              )}
              {progress.status === 'paused' && (
                <Button size="sm" variant="outline" onClick={onResume}>
                  <Play className="h-4 w-4 mr-1" />
                  继续
                </Button>
              )}
              {(progress.status === 'running' || progress.status === 'paused') && (
                <Button size="sm" variant="destructive" onClick={onCancel}>
                  <X className="h-4 w-4 mr-1" />
                  取消
                </Button>
              )}
            </div>
          )}
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* 总体进度条 */}
        <div>
          <div className="flex justify-between text-sm text-gray-600 mb-2">
            <span>总进度</span>
            <span>{percentage}%</span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-3">
            <div 
              className="h-3 rounded-full transition-all duration-300 bg-gradient-to-r from-blue-500 to-blue-600"
              style={{ width: `${percentage}%` }}
            />
          </div>
        </div>

        {/* 详细信息 */}
        {showDetails && progress.type === 'scan' && (
          <ScanProgressDetails progress={progress as ScanProgress} />
        )}
        
        {showDetails && progress.type === 'import' && (
          <ImportProgressDetails progress={progress as ImportProgress} />
        )}
        
        {showDetails && progress.type === 'analysis' && (
          <AnalysisProgressDetails progress={progress as AIAnalysisProgress} />
        )}

        {/* 时间信息 */}
        <div className="grid grid-cols-2 gap-4 text-sm text-gray-600 bg-gray-50 p-3 rounded">
          <div>
            <span className="font-medium">开始时间:</span> {formatTime(progress.startTime)}
          </div>
          <div>
            <span className="font-medium">持续时间:</span> {formatDuration(progress.startTime, progress.endTime)}
          </div>
        </div>

        {/* 错误信息 */}
        {progress.error && (
          <div className="bg-red-50 border border-red-200 rounded p-3">
            <div className="flex items-center space-x-2 text-red-800">
              <XCircle className="h-4 w-4" />
              <span className="font-medium">错误信息</span>
            </div>
            <p className="text-red-700 text-sm mt-1">{progress.error}</p>
          </div>
        )}
      </CardContent>
    </Card>
  )
}

// 扫描进度详情组件
const ScanProgressDetails: React.FC<{ progress: ScanProgress }> = ({ progress }) => (
  <div className="space-y-4">
    {/* 阶段进度 */}
    <div>
      <h4 className="text-sm font-medium text-gray-700 mb-3">扫描阶段</h4>
      <div className="grid grid-cols-3 gap-4">
        {Object.entries(progress.phases).map(([key, phase]) => (
          <div key={key} className="text-center">
            <div className={`w-10 h-10 rounded-full mx-auto mb-2 flex items-center justify-center text-white text-sm font-medium
              ${phase.status === 'completed' ? 'bg-green-500' : 
                phase.status === 'running' ? 'bg-blue-500' : 
                phase.status === 'failed' ? 'bg-red-500' : 'bg-gray-400'}`}>
              {phase.status === 'completed' ? '✓' : 
               phase.status === 'running' ? <Zap className="h-4 w-4" /> : 
               phase.status === 'failed' ? '✗' : key[0].toUpperCase()}
            </div>
            <div className="text-sm font-medium">{phase.name}</div>
            <div className="text-xs text-gray-600">{phase.progress}%</div>
            {phase.message && (
              <div className="text-xs text-gray-500 mt-1">{phase.message}</div>
            )}
          </div>
        ))}
      </div>
    </div>

    {/* 统计信息 */}
    <div>
      <h4 className="text-sm font-medium text-gray-700 mb-3">文件统计</h4>
      <div className="grid grid-cols-2 md:grid-cols-5 gap-3">
        <div className="text-center p-2 bg-blue-50 rounded">
          <div className="text-lg font-bold text-blue-600">{progress.totalFiles}</div>
          <div className="text-xs text-gray-600">总文件</div>
        </div>
        <div className="text-center p-2 bg-green-50 rounded">
          <div className="text-lg font-bold text-green-600">{progress.processedFiles}</div>
          <div className="text-xs text-gray-600">已处理</div>
        </div>
        <div className="text-center p-2 bg-red-50 rounded">
          <div className="text-lg font-bold text-red-600">{progress.failedFiles}</div>
          <div className="text-xs text-gray-600">失败</div>
        </div>
        <div className="text-center p-2 bg-yellow-50 rounded">
          <div className="text-lg font-bold text-yellow-600">{progress.skippedFiles}</div>
          <div className="text-xs text-gray-600">跳过</div>
        </div>
        <div className="text-center p-2 bg-purple-50 rounded">
          <div className="text-lg font-bold text-purple-600">{progress.duplicateFiles || 0}</div>
          <div className="text-xs text-gray-600">重复</div>
        </div>
      </div>
    </div>

    {/* 当前处理文件 */}
    {progress.currentFile && (
      <div className="bg-blue-50 border border-blue-200 rounded p-3">
        <div className="flex items-center space-x-2 text-blue-800">
          <FileImage className="h-4 w-4" />
          <span className="font-medium">正在处理:</span>
        </div>
        <p className="text-blue-700 text-sm mt-1 break-all">{progress.currentFile}</p>
      </div>
    )}

    {/* 预计剩余时间 */}
    {progress.estimatedTimeRemaining && (
      <div className="text-sm text-gray-600 flex items-center space-x-2">
        <Clock className="h-4 w-4" />
        <span>预计剩余时间: {Math.round(progress.estimatedTimeRemaining / 60000)} 分钟</span>
      </div>
    )}
  </div>
)

// 导入进度详情组件
const ImportProgressDetails: React.FC<{ progress: ImportProgress }> = ({ progress }) => (
  <div className="space-y-4">
    <div className="grid grid-cols-3 gap-4">
      <div className="text-center p-3 bg-blue-50 rounded">
        <div className="text-xl font-bold text-blue-600">{progress.totalFiles}</div>
        <div className="text-sm text-gray-600">总文件</div>
      </div>
      <div className="text-center p-3 bg-green-50 rounded">
        <div className="text-xl font-bold text-green-600">{progress.successfulFiles}</div>
        <div className="text-sm text-gray-600">成功</div>
      </div>
      <div className="text-center p-3 bg-red-50 rounded">
        <div className="text-xl font-bold text-red-600">{progress.failedFiles}</div>
        <div className="text-sm text-gray-600">失败</div>
      </div>
    </div>

    {progress.currentFile && (
      <div className="bg-green-50 border border-green-200 rounded p-3">
        <div className="flex items-center space-x-2 text-green-800">
          <Upload className="h-4 w-4" />
          <span className="font-medium">正在导入:</span>
        </div>
        <p className="text-green-700 text-sm mt-1 break-all">{progress.currentFile}</p>
      </div>
    )}
  </div>
)

// AI分析进度详情组件
const AnalysisProgressDetails: React.FC<{ progress: AIAnalysisProgress }> = ({ progress }) => (
  <div className="space-y-4">
    <div className="grid grid-cols-3 gap-4">
      <div className="text-center p-3 bg-purple-50 rounded">
        <div className="text-xl font-bold text-purple-600">{progress.totalImages}</div>
        <div className="text-sm text-gray-600">总图片</div>
      </div>
      <div className="text-center p-3 bg-green-50 rounded">
        <div className="text-xl font-bold text-green-600">{progress.analyzedImages}</div>
        <div className="text-sm text-gray-600">已分析</div>
      </div>
      <div className="text-center p-3 bg-red-50 rounded">
        <div className="text-xl font-bold text-red-600">{progress.failedImages}</div>
        <div className="text-sm text-gray-600">失败</div>
      </div>
    </div>

    <div className="bg-purple-50 border border-purple-200 rounded p-3">
      <h4 className="text-sm font-medium text-purple-800 mb-2">队列状态</h4>
      <div className="grid grid-cols-3 gap-2">
        <div className="text-center">
          <div className="text-lg font-bold text-orange-600">{progress.queueStatus.pending}</div>
          <div className="text-xs text-gray-600">待处理</div>
        </div>
        <div className="text-center">
          <div className="text-lg font-bold text-blue-600">{progress.queueStatus.processing}</div>
          <div className="text-xs text-gray-600">处理中</div>
        </div>
        <div className="text-center">
          <div className="text-lg font-bold text-green-600">{progress.queueStatus.completed}</div>
          <div className="text-xs text-gray-600">已完成</div>
        </div>
      </div>
    </div>

    {progress.currentImage && (
      <div className="bg-purple-50 border border-purple-200 rounded p-3">
        <div className="flex items-center space-x-2 text-purple-800">
          <Brain className="h-4 w-4" />
          <span className="font-medium">正在分析:</span>
        </div>
        <p className="text-purple-700 text-sm mt-1 break-all">{progress.currentImage}</p>
      </div>
    )}
  </div>
) 