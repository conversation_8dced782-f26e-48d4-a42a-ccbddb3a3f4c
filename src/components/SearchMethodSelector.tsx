import React, { useEffect } from 'react'
import { Card, CardContent } from '@/components/ui/card'
import { cn } from '@/lib/utils'
import { 
  SearchMethod, 
  SEARCH_METHODS, 
  SearchPreferences, 
  DEFAULT_SEARCH_PREFERENCES 
} from '../types/search'

interface SearchMethodSelectorProps {
  selectedMethod: SearchMethod
  onMethodChange: (method: SearchMethod) => void
  disabled?: boolean
  className?: string
  persistPreferences?: boolean
}

// 本地存储键名
const SEARCH_PREFERENCES_KEY = 'enhanced-search-preferences'

// 保存搜索偏好到本地存储
const saveSearchPreferences = (preferences: SearchPreferences): void => {
  try {
    localStorage.setItem(SEARCH_PREFERENCES_KEY, JSON.stringify(preferences))
  } catch (error) {
    console.warn('Failed to save search preferences:', error)
  }
}

// 从本地存储加载搜索偏好
const loadSearchPreferences = (): SearchPreferences => {
  try {
    const stored = localStorage.getItem(SEARCH_PREFERENCES_KEY)
    if (stored) {
      const parsed = JSON.parse(stored) as SearchPreferences
      // 验证加载的数据是否有效
      if (parsed.method && ['auto', 'hybrid', 'vector'].includes(parsed.method)) {
        return {
          ...DEFAULT_SEARCH_PREFERENCES,
          ...parsed,
          lastUpdated: new Date().toISOString()
        }
      }
    }
  } catch (error) {
    console.warn('Failed to load search preferences:', error)
  }
  return DEFAULT_SEARCH_PREFERENCES
}

export function SearchMethodSelector({
  selectedMethod,
  onMethodChange,
  disabled = false,
  className,
  persistPreferences = true
}: SearchMethodSelectorProps): JSX.Element {
  // 在组件挂载时加载保存的偏好设置
  useEffect(() => {
    if (persistPreferences) {
      const savedPreferences = loadSearchPreferences()
      if (savedPreferences.method !== selectedMethod) {
        onMethodChange(savedPreferences.method)
      }
    }
  }, [persistPreferences, selectedMethod, onMethodChange])

  // 处理方法变更并保存偏好设置
  const handleMethodChange = (method: SearchMethod): void => {
    if (disabled) return
    
    onMethodChange(method)
    
    if (persistPreferences) {
      const preferences: SearchPreferences = {
        method,
        threshold: 0.6, // 默认阈值，将在SimilarityThresholdControl中更新
        lastUpdated: new Date().toISOString()
      }
      saveSearchPreferences(preferences)
    }
  }
  return (
    <Card className={cn("w-full", className)}>
      <CardContent className="p-4">
        <div className="space-y-3">
          <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300">
            搜索方法
          </h4>
          
          <div className="space-y-2">
            {SEARCH_METHODS.map((method) => (
              <label
                key={method.id}
                className={cn(
                  "flex items-start space-x-3 p-3 rounded-lg border cursor-pointer transition-all",
                  "hover:bg-gray-50 dark:hover:bg-gray-800",
                  selectedMethod === method.id
                    ? "border-blue-500 bg-blue-50 dark:bg-blue-950 dark:border-blue-400"
                    : "border-gray-200 dark:border-gray-700",
                  disabled && "opacity-50 cursor-not-allowed"
                )}
              >
                <input
                  type="radio"
                  name="searchMethod"
                  value={method.id}
                  checked={selectedMethod === method.id}
                  onChange={() => handleMethodChange(method.id)}
                  disabled={disabled}
                  className="mt-1 h-4 w-4 text-blue-600 border-gray-300 focus:ring-blue-500"
                />
                
                <div className="flex-1 min-w-0">
                  <div className="flex items-center space-x-2">
                    <span className="text-lg" role="img" aria-label={method.name}>
                      {method.icon}
                    </span>
                    <span className="font-medium text-gray-900 dark:text-gray-100">
                      {method.name}
                    </span>
                  </div>
                  <p className="mt-1 text-sm text-gray-600 dark:text-gray-400">
                    {method.description}
                  </p>
                </div>
              </label>
            ))}
          </div>
        </div>
      </CardContent>
    </Card>
  )
}