// import React, { useState } from 'react'
// import { trpcClient } from '../lib/trpcClient'
// import { Button } from './ui/button'
// import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './ui/card'
//
// export const TRPCExample: React.FC = () => {
//   const [testResult, setTestResult] = useState<string>('')
//   const [libraries, setLibraries] = useState<any[]>([])
//   const [tags, setTags] = useState<any[]>([])
//   const [images, setImages] = useState<any[]>([])
//   const [loading, setLoading] = useState(false)
//
//   const handleTestAI = async () => {
//     setLoading(true)
//     try {
//       const result = await trpcClient.ai.testConnection.query()
//       if (result.success) {
//         setTestResult(`AI Connection: Success (Connected: ${result.connected})`)
//       } else {
//         setTestResult(`AI Connection: Failed - ${result.success}`)
//       }
//     } catch (error) {
//       setTestResult(`AI Connection Error: ${error}`)
//     }
//     setLoading(false)
//   }
//
//   const handleGetLibraries = async () => {
//     setLoading(true)
//     try {
//       const result = await trpcClient.library.getAllLibraries.query()
//       if (result.success) {
//         setLibraries(result.libraries || [])
//         setTestResult(`Successfully loaded ${result.libraries?.length || 0} libraries`)
//       } else {
//         setTestResult(`Libraries Error: ${result.success}`)
//       }
//     } catch (error) {
//       setTestResult(`Libraries Error: ${error}`)
//     }
//     setLoading(false)
//   }
//
//   const handleCreateLibrary = async () => {
//     setLoading(true)
//     try {
//       const result = await trpcClient.library.createLibrary.mutate({
//         name: 'Test Library',
//         rootPath: 'C:\\test\\path',
//         description: 'Created via tRPC',
//         type: 'local'
//       })
//       if (result.success) {
//         setTestResult('Library created successfully')
//         // 刷新库列表
//         handleGetLibraries()
//       } else {
//         setTestResult(`Library creation failed: ${result.success}`)
//       }
//     } catch (error) {
//       setTestResult(`Library creation error: ${error}`)
//     }
//     setLoading(false)
//   }
//
//   const handleGetTags = async () => {
//     setLoading(true)
//     try {
//       const result = await trpcClient.tag.getAllTags.query()
//       if (result.success) {
//         setTags(result.tags || [])
//         setTestResult(`Successfully loaded ${result.tags?.length || 0} tags`)
//       } else {
//         setTestResult(`Tags Error: ${result.success}`)
//       }
//     } catch (error) {
//       setTestResult(`Tags Error: ${error}`)
//     }
//     setLoading(false)
//   }
//
//   const handleCreateTag = async () => {
//     setLoading(true)
//     try {
//       const result = await trpcClient.tag.createTag.mutate({
//         tagText: 'test-tag-' + Date.now(),
//         category: 'test',
//         frequency: 1
//       })
//       if (result.success) {
//         setTestResult('Tag created successfully')
//         // 刷新标签列表
//         handleGetTags()
//       } else {
//         setTestResult(`Tag creation failed: ${result.success}`)
//       }
//     } catch (error) {
//       setTestResult(`Tag creation error: ${error}`)
//     }
//     setLoading(false)
//   }
//
//   const handleGetImages = async () => {
//     setLoading(true)
//     try {
//       const result = await trpcClient.image.queryImages.query({
//         limit: 10,
//         sortBy: 'capturedAt',
//         sortOrder: 'desc'
//       })
//       if (result.error) {
//         setTestResult(`Images Error: ${result.error}`)
//       } else {
//         setImages(result.images || [])
//         setTestResult(`Successfully loaded ${result.images?.length || 0} images (total: ${result.total})`)
//       }
//     } catch (error) {
//       setTestResult(`Images Error: ${error}`)
//     }
//     setLoading(false)
//   }
//
//   return (
//     <Card className="w-full max-w-2xl mx-auto">
//       <CardHeader>
//         <CardTitle>tRPC Integration Test</CardTitle>
//         <CardDescription>
//           Test the electron-trpc integration with your existing services
//         </CardDescription>
//       </CardHeader>
//       <CardContent className="space-y-4">
//         <div className="flex gap-2 flex-wrap">
//           <Button onClick={handleTestAI} disabled={loading}>
//             Test AI Connection
//           </Button>
//           <Button onClick={handleGetLibraries} disabled={loading}>
//             Get Libraries
//           </Button>
//           <Button onClick={handleCreateLibrary} disabled={loading}>
//             Create Test Library
//           </Button>
//           <Button onClick={handleGetImages} disabled={loading}>
//             Get Images
//           </Button>
//         </div>
//
//         {testResult && (
//           <div className="p-4 bg-gray-50 rounded-md">
//             <pre className="text-sm">{testResult}</pre>
//           </div>
//         )}
//
//         {libraries.length > 0 && (
//           <div className="space-y-2">
//             <h3 className="font-semibold">Libraries:</h3>
//             {libraries.map((lib) => (
//               <div key={lib.id} className="p-2 border rounded">
//                 <div className="font-medium">{lib.name}</div>
//                 <div className="text-sm text-gray-600">{lib.path}</div>
//                 <div className="text-xs text-gray-500">
//                   Images: {lib.imageCount} | Status: {lib.status}
//                 </div>
//               </div>
//             ))}
//           </div>
//         )}
//
//         {images.length > 0 && (
//           <div className="space-y-2">
//             <h3 className="font-semibold">Images:</h3>
//             <div className="max-h-40 overflow-y-auto">
//               {images.slice(0, 5).map((img) => (
//                 <div key={img.id} className="p-2 border rounded text-xs">
//                   <div className="font-medium truncate">{img.description || 'No description'}</div>
//                   <div className="text-gray-600 truncate">{img.imagePath}</div>
//                   <div className="text-gray-500">
//                     ID: {img.id} | Tags: {img.tags?.length || 0}
//                   </div>
//                 </div>
//               ))}
//               {images.length > 5 && (
//                 <div className="text-xs text-gray-500 text-center">
//                   ... and {images.length - 5} more images
//                 </div>
//               )}
//             </div>
//           </div>
//         )}
//       </CardContent>
//     </Card>
//   )
// }