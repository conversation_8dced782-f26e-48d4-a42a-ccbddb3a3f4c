/**
 * Image Viewer Component
 * Provides full-screen image viewing with metadata and actions
 */

import React, { useState, useEffect } from 'react';
import { 
  X, 
  Download, 
  Share2, 
  Search, 
  Tags, 
  Star, 
  ZoomIn, 
  ZoomOut, 
  RotateCw,
  Info,
  Eye,
  ChevronLeft,
  ChevronRight
} from 'lucide-react';

interface ImageViewerProps {
  isOpen: boolean;
  imageUrl: string;
  imageData?: any;
  onClose: () => void;
  onSearchSimilar?: (imageData: any) => void;
  onAnalyzeImage?: (imageUrl: string) => void;
  onTagSearch?: (tags: string[]) => void;
}

export function ImageViewer({
  isOpen,
  imageUrl,
  imageData,
  onClose,
  onSearchSimilar,
  onAnalyzeImage,
  onTagSearch
}: ImageViewerProps) {
  const [zoom, setZoom] = useState(1);
  const [rotation, setRotation] = useState(0);
  const [showInfo, setShowInfo] = useState(false);

  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
    }

    return () => {
      document.body.style.overflow = 'unset';
    };
  }, [isOpen]);

  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (!isOpen) return;
      
      switch (e.key) {
        case 'Escape':
          onClose();
          break;
        case '+':
        case '=':
          setZoom(prev => Math.min(prev + 0.25, 3));
          break;
        case '-':
          setZoom(prev => Math.max(prev - 0.25, 0.25));
          break;
        case 'r':
        case 'R':
          setRotation(prev => (prev + 90) % 360);
          break;
        case 'i':
        case 'I':
          setShowInfo(prev => !prev);
          break;
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [isOpen, onClose]);

  if (!isOpen) return null;

  const handleDownload = () => {
    const link = document.createElement('a');
    link.href = imageUrl;
    link.download = imageData?.title || 'image.jpg';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const handleShare = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: imageData?.title || '图片分享',
          text: imageData?.description || '查看这张图片',
          url: imageUrl
        });
      } catch (error) {
        console.log('分享失败:', error);
      }
    } else {
      // Fallback: copy to clipboard
      navigator.clipboard.writeText(imageUrl);
      // You might want to show a toast notification here
    }
  };

  return (
    <div className="fixed inset-0 z-50 bg-black/90 backdrop-blur-sm">
      {/* Header */}
      <div className="absolute top-0 left-0 right-0 z-10 bg-gradient-to-b from-black/50 to-transparent">
        <div className="flex items-center justify-between p-6">
          <div className="flex items-center space-x-4">
            <h3 className="text-white font-medium">
              {imageData?.title || '图片查看'}
            </h3>
            {imageData?.similarity && (
              <div className="flex items-center space-x-1 text-yellow-400">
                <Star className="h-4 w-4" />
                <span className="text-sm">
                  {(imageData.similarity * 100).toFixed(1)}% 相似
                </span>
              </div>
            )}
          </div>
          <button
            onClick={onClose}
            className="p-2 text-white hover:bg-white/20 rounded-full transition-colors"
          >
            <X className="h-6 w-6" />
          </button>
        </div>
      </div>

      {/* Main image */}
      <div className="flex items-center justify-center h-full p-20">
        <img
          src={imageUrl}
          alt={imageData?.title || '查看图片'}
          className="max-w-full max-h-full object-contain transition-transform duration-200"
          style={{
            transform: `scale(${zoom}) rotate(${rotation}deg)`
          }}
        />
      </div>

      {/* Controls */}
      <div className="absolute bottom-0 left-0 right-0 z-10 bg-gradient-to-t from-black/50 to-transparent">
        <div className="flex items-center justify-between p-6">
          {/* Zoom and rotation controls */}
          <div className="flex items-center space-x-2">
            <button
              onClick={() => setZoom(prev => Math.max(prev - 0.25, 0.25))}
              className="p-2 text-white hover:bg-white/20 rounded-full transition-colors"
              disabled={zoom <= 0.25}
            >
              <ZoomOut className="h-5 w-5" />
            </button>
            <span className="text-white text-sm min-w-[3rem] text-center">
              {Math.round(zoom * 100)}%
            </span>
            <button
              onClick={() => setZoom(prev => Math.min(prev + 0.25, 3))}
              className="p-2 text-white hover:bg-white/20 rounded-full transition-colors"
              disabled={zoom >= 3}
            >
              <ZoomIn className="h-5 w-5" />
            </button>
            <div className="w-px h-6 bg-white/30 mx-2" />
            <button
              onClick={() => setRotation(prev => (prev + 90) % 360)}
              className="p-2 text-white hover:bg-white/20 rounded-full transition-colors"
            >
              <RotateCw className="h-5 w-5" />
            </button>
          </div>

          {/* Action buttons */}
          <div className="flex items-center space-x-2">
            <button
              onClick={() => setShowInfo(!showInfo)}
              className={`p-2 rounded-full transition-colors ${
                showInfo 
                  ? 'bg-white/30 text-white' 
                  : 'text-white hover:bg-white/20'
              }`}
            >
              <Info className="h-5 w-5" />
            </button>
            
            {onAnalyzeImage && (
              <button
                onClick={() => onAnalyzeImage(imageUrl)}
                className="p-2 text-white hover:bg-white/20 rounded-full transition-colors"
                title="分析图片"
              >
                <Eye className="h-5 w-5" />
              </button>
            )}
            
            {onSearchSimilar && (
              <button
                onClick={() => onSearchSimilar(imageData)}
                className="p-2 text-white hover:bg-white/20 rounded-full transition-colors"
                title="搜索相似图片"
              >
                <Search className="h-5 w-5" />
              </button>
            )}
            
            <button
              onClick={handleDownload}
              className="p-2 text-white hover:bg-white/20 rounded-full transition-colors"
              title="下载图片"
            >
              <Download className="h-5 w-5" />
            </button>
            
            <button
              onClick={handleShare}
              className="p-2 text-white hover:bg-white/20 rounded-full transition-colors"
              title="分享图片"
            >
              <Share2 className="h-5 w-5" />
            </button>
          </div>
        </div>
      </div>

      {/* Info panel */}
      {showInfo && imageData && (
        <div className="absolute top-20 right-6 w-80 bg-white/95 dark:bg-slate-800/95 backdrop-blur-sm rounded-xl p-6 shadow-2xl">
          <h4 className="font-semibold text-slate-900 dark:text-slate-100 mb-4">
            图片信息
          </h4>
          
          <div className="space-y-4">
            {imageData.title && (
              <div>
                <label className="text-sm font-medium text-slate-700 dark:text-slate-300">
                  标题
                </label>
                <p className="text-slate-600 dark:text-slate-400 mt-1">
                  {imageData.title}
                </p>
              </div>
            )}
            
            {imageData.description && (
              <div>
                <label className="text-sm font-medium text-slate-700 dark:text-slate-300">
                  描述
                </label>
                <p className="text-slate-600 dark:text-slate-400 mt-1">
                  {imageData.description}
                </p>
              </div>
            )}
            
            {imageData.tags && imageData.tags.length > 0 && (
              <div>
                <label className="text-sm font-medium text-slate-700 dark:text-slate-300 mb-2 block">
                  标签
                </label>
                <div className="flex flex-wrap gap-1">
                  {imageData.tags.map((tag: string, index: number) => (
                    <button
                      key={index}
                      onClick={() => onTagSearch && onTagSearch([tag])}
                      className="inline-flex items-center px-2 py-1 text-xs bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 rounded-full hover:bg-blue-200 dark:hover:bg-blue-900/50 transition-colors"
                    >
                      <Tags className="h-3 w-3 mr-1" />
                      {tag}
                    </button>
                  ))}
                </div>
              </div>
            )}
            
            {imageData.similarity && (
              <div>
                <label className="text-sm font-medium text-slate-700 dark:text-slate-300">
                  相似度
                </label>
                <div className="flex items-center space-x-2 mt-1">
                  <div className="flex-1 bg-slate-200 dark:bg-slate-600 rounded-full h-2">
                    <div 
                      className="bg-yellow-500 h-2 rounded-full transition-all duration-300"
                      style={{ width: `${(imageData.similarity * 100)}%` }}
                    />
                  </div>
                  <span className="text-sm text-slate-600 dark:text-slate-400">
                    {(imageData.similarity * 100).toFixed(1)}%
                  </span>
                </div>
              </div>
            )}
            
            {imageData.metadata && (
              <div>
                <label className="text-sm font-medium text-slate-700 dark:text-slate-300">
                  技术信息
                </label>
                <div className="text-xs text-slate-500 dark:text-slate-400 mt-1 space-y-1">
                  {imageData.metadata.resolution && (
                    <div>分辨率: {imageData.metadata.resolution}</div>
                  )}
                  {imageData.metadata.fileSize && (
                    <div>文件大小: {imageData.metadata.fileSize}</div>
                  )}
                  {imageData.metadata.format && (
                    <div>格式: {imageData.metadata.format}</div>
                  )}
                </div>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Keyboard shortcuts help */}
      <div className="absolute bottom-20 left-6 text-white/70 text-xs space-y-1">
        <div>ESC: 关闭 | +/-: 缩放 | R: 旋转 | I: 信息</div>
      </div>
    </div>
  );
}