import { useState, useEffect, ReactNode } from 'react'
import { <PERSON>ert<PERSON><PERSON>cle, Loader2, RefreshCw } from 'lucide-react'
import { frontendImageManager, ImageDisplayOptions } from '../lib/FrontendImageManager'
import { ThumbnailSize } from '../lib/ImageThumbnailService'

interface OptimizedLocalImageProps {
  imagePath: string
  alt: string
  className?: string
  fallbackSrc?: string
  lazy?: boolean
  showRetry?: boolean
  thumbnailSize?: ThumbnailSize | number
  priority?: 'high' | 'normal' | 'low'
  forceReload?: boolean
  placeholder?: ReactNode
  errorPlaceholder?: ReactNode
  onLoad?: (blob: Blob) => void
  onError?: (error: string) => void
}

export function OptimizedLocalImage({
  imagePath,
  alt,
  className,
  fallbackSrc,
  lazy = true,
  showRetry = false,
  thumbnailSize,
  priority = 'normal',
  forceReload = false,
  placeholder,
  errorPlaceholder,
  onLoad,
  onError
}: OptimizedLocalImageProps): JSX.Element {
  const [imageSrc, setImageSrc] = useState<string | null>(null)
  const [loading, setLoading] = useState(!lazy)
  const [error, setError] = useState<string | null>(null)
  const [isInView, setIsInView] = useState(!lazy)
  const [retryCount, setRetryCount] = useState(0)
  const [imageBlob, setImageBlob] = useState<Blob | null>(null)

  // 懒加载处理
  useEffect(() => {
    if (!lazy) return

    const observer = new IntersectionObserver(
      (entries) => {
        if (entries[0].isIntersecting) {
          setIsInView(true)
          observer.disconnect()
        }
      },
      { threshold: 0.1, rootMargin: '200px' } // 提前200px加载
    )

    const elementId = `optimized-image-${imagePath.replace(/[^a-zA-Z0-9]/g, '-')}`
    const element = document.getElementById(elementId)
    if (element) {
      observer.observe(element)
    }

    return () => {
      observer.disconnect()
    }
  }, [lazy, imagePath])

  // 加载图片
  useEffect(() => {
    // 如果是懒加载且还未进入视图，不加载图片
    if (lazy && !isInView) return

    const loadImage = async () => {
      if (!imagePath) {
        setError('图片路径为空')
        setLoading(false)
        onError?.('图片路径为空')
        return
      }

      try {
        setLoading(true)
        setError(null)

        // 配置图片显示选项
        const options: ImageDisplayOptions = {
          thumbnailSize,
          priority,
          forceReload,
          placeholder: fallbackSrc
        }

        // 获取图片Blob
        const blob = await frontendImageManager.getImageBlob(imagePath, options)
        setImageBlob(blob)
        
        // 转换为DataURL
        const dataUrl = await frontendImageManager.blobToDataUrl(blob)
        setImageSrc(dataUrl)
        
        // 触发加载成功回调
        onLoad?.(blob)
      } catch (err) {
        console.error('加载本地图片失败:', err)
        const errorMessage = err instanceof Error ? err.message : String(err)
        setError(errorMessage)
        onError?.(errorMessage)
        
        // 如果有fallback图片，使用它
        if (fallbackSrc) {
          setImageSrc(fallbackSrc)
        }
      } finally {
        setLoading(false)
      }
    }

    loadImage()
  }, [imagePath, fallbackSrc, thumbnailSize, priority, forceReload, lazy, isInView, retryCount, onLoad, onError])

  // 重试加载
  const handleRetry = () => {
    setRetryCount(prev => prev + 1)
  }

  // 懒加载占位符
  if (lazy && !isInView) {
    return (
      <div 
        id={`optimized-image-${imagePath.replace(/[^a-zA-Z0-9]/g, '-')}`}
        className={`bg-slate-100 dark:bg-slate-800 flex items-center justify-center ${className}`}
      >
        <div className="w-6 h-6" />
      </div>
    )
  }

  // 加载中
  if (loading) {
    return (
      <div 
        id={lazy ? `optimized-image-${imagePath.replace(/[^a-zA-Z0-9]/g, '-')}` : undefined}
        className={`bg-slate-100 dark:bg-slate-800 flex items-center justify-center ${className}`}
      >
        {placeholder || <Loader2 className="h-6 w-6 animate-spin text-slate-400" />}
      </div>
    )
  }

  // 错误状态
  if (error && !imageSrc) {
    return (
      <div 
        id={lazy ? `optimized-image-${imagePath.replace(/[^a-zA-Z0-9]/g, '-')}` : undefined}
        className={`bg-slate-100 dark:bg-slate-800 flex items-center justify-center ${className}`}
      >
        {errorPlaceholder || (
          <div className="text-center p-4">
            <AlertCircle className="h-6 w-6 text-red-500 mx-auto mb-2" />
            <p className="text-xs text-red-500 mb-2">加载失败</p>
            {showRetry && (
              <button 
                onClick={handleRetry}
                className="flex items-center justify-center space-x-1 text-xs bg-slate-200 dark:bg-slate-700 hover:bg-slate-300 dark:hover:bg-slate-600 text-slate-700 dark:text-slate-300 px-2 py-1 rounded-md transition-colors"
              >
                <RefreshCw className="h-3 w-3 mr-1" />
                重试
              </button>
            )}
          </div>
        )}
      </div>
    )
  }

  // 正常显示图片
  return (
    <img 
      id={lazy ? `optimized-image-${imagePath.replace(/[^a-zA-Z0-9]/g, '-')}` : undefined}
      src={imageSrc || fallbackSrc} 
      alt={alt}
      className={className}
      loading={lazy ? "lazy" : "eager"}
      onError={() => {
        if (fallbackSrc && imageSrc !== fallbackSrc) {
          setImageSrc(fallbackSrc)
        } else {
          setError('图片加载失败')
          onError?.('图片加载失败')
        }
      }}
    />
  )
}