import { FC } from 'react';
import { <PERSON><PERSON><PERSON>, Loader2, <PERSON><PERSON><PERSON>cle, AlertCircle, Clock } from 'lucide-react';
import { ToolCallSession, ToolCallState } from '../lib/toolCallStateManager';

interface ToolCallStatusProps {
  session: ToolCallSession;
  className?: string;
}

export function ToolCallStatus({ session, className = '' }: ToolCallStatusProps) {
  const getStatusIcon = (status: ToolCallState['status']) => {
    switch (status) {
      case 'pending':
        return <Clock className="h-4 w-4 text-slate-500" />;
      case 'executing':
        return <Loader2 className="h-4 w-4 text-blue-500 animate-spin" />;
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'failed':
        return <AlertCircle className="h-4 w-4 text-red-500" />;
      default:
        return <Clock className="h-4 w-4 text-slate-500" />;
    }
  };

  const getStatusColor = (status: ToolCallState['status']) => {
    switch (status) {
      case 'pending':
        return 'text-slate-600 dark:text-slate-400';
      case 'executing':
        return 'text-blue-600 dark:text-blue-400';
      case 'completed':
        return 'text-green-600 dark:text-green-400';
      case 'failed':
        return 'text-red-600 dark:text-red-400';
      default:
        return 'text-slate-600 dark:text-slate-400';
    }
  };

  const getStatusText = (status: ToolCallState['status']) => {
    switch (status) {
      case 'pending':
        return '等待中';
      case 'executing':
        return '执行中';
      case 'completed':
        return '已完成';
      case 'failed':
        return '失败';
      default:
        return '未知';
    }
  };

  const getToolDisplayName = (toolName: string) => {
    const toolNameMap: Record<string, string> = {
      'find_similar_images_by_image': '以图搜图',
      'find_similar_images_by_description': '文本搜索图片',
      'find_images_by_tags': '标签搜索',
      'get_image_analysis': '图片分析'
    };
    return toolNameMap[toolName] || toolName;
  };

  const progressPercentage = session.totalTools > 0 
    ? (session.completedTools / session.totalTools) * 100 
    : 0;

  return (
    <div className={`bg-white/80 dark:bg-slate-700/80 backdrop-blur-sm border border-slate-200/50 dark:border-slate-600/50 rounded-xl p-4 ${className}`}>
      <div className="flex items-center space-x-3 mb-3">
        <div className="p-2 bg-blue-100 dark:bg-blue-900/30 rounded-lg">
          <Settings className="h-4 w-4 text-blue-600 dark:text-blue-400" />
        </div>
        <div className="flex-1">
          <h4 className="text-sm font-medium text-slate-900 dark:text-slate-100">
            工具执行中
          </h4>
          <p className="text-xs text-slate-600 dark:text-slate-400">
            {session.completedTools}/{session.totalTools} 完成
            {session.failedTools > 0 && (
              <span className="text-red-600 dark:text-red-400 ml-1">
                ({session.failedTools} 失败)
              </span>
            )}
          </p>
        </div>
        <div className={`text-sm font-medium ${getStatusColor(session.status)}`}>
          {getStatusText(session.status)}
        </div>
      </div>

      {/* Progress bar */}
      <div className="w-full bg-slate-200 dark:bg-slate-600 rounded-full h-2 mb-3">
        <div 
          className="bg-blue-500 h-2 rounded-full transition-all duration-300"
          style={{ width: `${progressPercentage}%` }}
        />
      </div>

      {/* Tool calls list */}
      <div className="space-y-2">
        {session.toolCalls.map((toolCall) => (
          <div 
            key={toolCall.id}
            className="flex items-center justify-between p-2 bg-slate-50/50 dark:bg-slate-800/50 rounded-lg"
          >
            <div className="flex items-center space-x-3">
              {getStatusIcon(toolCall.status)}
              <div className="flex-1">
                <div className="text-sm font-medium text-slate-900 dark:text-slate-100">
                  {getToolDisplayName(toolCall.toolCall.name)}
                </div>
                {toolCall.progress !== undefined && toolCall.status === 'executing' && (
                  <div className="w-24 bg-slate-200 dark:bg-slate-600 rounded-full h-1 mt-1">
                    <div 
                      className="bg-blue-500 h-1 rounded-full transition-all duration-300"
                      style={{ width: `${toolCall.progress}%` }}
                    />
                  </div>
                )}
              </div>
            </div>
            <div className="text-xs text-slate-500 dark:text-slate-400">
              {toolCall.endTime && toolCall.startTime && (
                <span>
                  {toolCall.endTime.getTime() - toolCall.startTime.getTime()}ms
                </span>
              )}
            </div>
          </div>
        ))}
      </div>

      {/* Error details */}
      {session.failedTools > 0 && (
        <div className="mt-3 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800/30 rounded-lg">
          <div className="flex items-center space-x-2 mb-2">
            <AlertCircle className="h-4 w-4 text-red-500" />
            <span className="text-sm font-medium text-red-700 dark:text-red-300">
              部分工具执行失败
            </span>
          </div>
          {session.toolCalls
            .filter((tc: any) => tc.status === 'failed')
            .map((toolCall) => (
              <div key={toolCall.id} className="text-xs text-red-600 dark:text-red-400">
                {getToolDisplayName(toolCall.toolCall.name)}: {toolCall.error}
              </div>
            ))}
        </div>
      )}
    </div>
  );
}

interface ToolCallStatusListProps {
  sessions: ToolCallSession[];
  className?: string;
}

export function ToolCallStatusList({ sessions, className = '' }: ToolCallStatusListProps) {
  const activeSessions = sessions.filter((session: any) => 
    session.status === 'pending' || session.status === 'executing'
  );

  if (activeSessions.length === 0) {
    return null;
  }

  return (
    <div className={`space-y-3 ${className}`}>
      {activeSessions.map((session) => (
        <ToolCallStatus key={session.id} session={session} />
      ))}
    </div>
  );
}