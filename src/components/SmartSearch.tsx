import { useState, useEffect, useMemo } from 'react'
import { Input } from '@/components/ui/input.tsx'
import { Button } from '@/components/ui/button.tsx'
import { Badge } from '@/components/ui/badge.tsx'
import { Card, CardContent } from '@/components/ui/card.tsx'
import { 
  Search, 
  Filter, 
  Clock, 
  Sparkles, 
  TrendingUp,
  X,
  Calendar,
  Tag,
  Palette
} from 'lucide-react'

// 类型定义
interface SearchFilters {
  dateRange: string
  tags: string[]
  location: string
  colors: string[]
}

interface SmartSearchProps {
  onSearch?: (query: string, filters: SearchFilters) => void
  searchHistory?: string[]
  popularTags?: string[]
}

export function SmartSearch({ onSearch, searchHistory = [], popularTags = [] }: SmartSearchProps): JSX.Element {
  const [query, setQuery] = useState<string>('')
  const [showFilters, setShowFilters] = useState<boolean>(false)
  const [filters, setFilters] = useState<SearchFilters>({
    dateRange: '',
    tags: [],
    location: '',
    colors: []
  })
  const [suggestions, setSuggestions] = useState<string[]>([])
  const [showSuggestions, setShowSuggestions] = useState<boolean>(false)

  // 搜索建议
  const searchSuggestions: string[] = useMemo(() => [
    '去年拍的蓝天白云',
    '有咖啡的照片',
    '城市夜景',
    '人物肖像',
    '自然风景',
    '美食照片',
    '建筑摄影',
    '动物照片',
    '生活记录',
    '黑白照片',
    '日落时分',
    '雨天的照片'
  ], [])

  // 热门标签
  const defaultPopularTags: string[] = [
    '蓝天白云', '城市夜景', '美食照片', '人物肖像', 
    '自然风景', '建筑摄影', '动物照片', '生活记录'
  ]

  const tags = popularTags.length > 0 ? popularTags : defaultPopularTags

  useEffect(() => {
    if (query.length > 0) {
      const filtered = searchSuggestions.filter((suggestion: any) =>
        suggestion.toLowerCase().includes(query.toLowerCase())
      )
      setSuggestions(filtered.slice(0, 5))
      setShowSuggestions(true)
    } else {
      setShowSuggestions(false)
    }
  }, [query, searchSuggestions])

  const handleSearch = (): void => {
    if (query.trim()) {
      onSearch?.(query, filters)
      setShowSuggestions(false)
    }
  }

  const handleSuggestionClick = (suggestion: string): void => {
    setQuery(suggestion)
    setShowSuggestions(false)
    onSearch?.(suggestion, filters)
  }

  const handleTagClick = (tag: string): void => {
    setQuery(tag)
    onSearch?.(tag, filters)
  }

  const handleKeyPress = (e: React.KeyboardEvent<HTMLInputElement>): void => {
    if (e.key === 'Enter') {
      handleSearch()
    }
  }

  const addFilter = (type: keyof SearchFilters, value: string): void => {
    setFilters(prev => ({
      ...prev,
      [type]: type === 'tags' || type === 'colors' 
        ? [...(prev[type] as string[]), value]
        : value
    }))
  }

  const removeFilter = (type: keyof SearchFilters, value: string): void => {
    setFilters(prev => ({
      ...prev,
      [type]: type === 'tags' || type === 'colors'
        ? (prev[type] as string[]).filter((item: any) => item !== value)
        : ''
    }))
  }

  return (
    <div className="space-y-4">
      {/* 主搜索框 */}
      <div className="relative">
        <div className="flex space-x-2">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400" />
            <Input
              placeholder="输入描述性搜索，如：去年拍的蓝天白云、有咖啡的照片..."
              value={query}
              onChange={(e) => setQuery(e.target.value)}
              onKeyPress={handleKeyPress}
              className="pl-10 pr-4"
            />
          </div>
          <Button onClick={handleSearch} disabled={!query.trim()}>
            <Search className="h-4 w-4 mr-2" />
            搜索
          </Button>
          <Button 
            variant="outline" 
            onClick={() => setShowFilters(!showFilters)}
            className={showFilters ? 'bg-blue-50 dark:bg-blue-950' : ''}
          >
            <Filter className="h-4 w-4 mr-2" />
            筛选
          </Button>
        </div>

        {/* 搜索建议 */}
        {showSuggestions && suggestions.length > 0 && (
          <Card className="absolute top-full left-0 right-0 mt-1 z-10 shadow-lg">
            <CardContent className="p-2">
              {suggestions.map((suggestion, index) => (
                <div
                  key={index}
                  className="flex items-center space-x-2 p-2 hover:bg-slate-100 dark:hover:bg-slate-800 rounded cursor-pointer"
                  onClick={() => handleSuggestionClick(suggestion)}
                >
                  <Sparkles className="h-4 w-4 text-blue-500" />
                  <span className="text-sm">{suggestion}</span>
                </div>
              ))}
            </CardContent>
          </Card>
        )}
      </div>

      {/* 高级筛选 */}
      {showFilters && (
        <Card>
          <CardContent className="p-4 space-y-4">
            <h4 className="font-semibold flex items-center">
              <Filter className="h-4 w-4 mr-2" />
              高级筛选
            </h4>

            {/* 时间筛选 */}
            <div className="space-y-2">
              <label className="text-sm font-medium flex items-center">
                <Calendar className="h-4 w-4 mr-2" />
                时间范围
              </label>
              <div className="flex flex-wrap gap-2">
                {['今天', '本周', '本月', '本年', '去年'].map((period) => (
                  <Button
                    key={period}
                    variant={filters.dateRange === period ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => addFilter('dateRange', period)}
                  >
                    {period}
                  </Button>
                ))}
              </div>
            </div>

            {/* 标签筛选 */}
            <div className="space-y-2">
              <label className="text-sm font-medium flex items-center">
                <Tag className="h-4 w-4 mr-2" />
                标签
              </label>
              <div className="flex flex-wrap gap-2">
                {tags.map((tag) => (
                  <Button
                    key={tag}
                    variant={filters.tags.includes(tag) ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => 
                      filters.tags.includes(tag) 
                        ? removeFilter('tags', tag)
                        : addFilter('tags', tag)
                    }
                  >
                    {tag}
                  </Button>
                ))}
              </div>
            </div>

            {/* 颜色筛选 */}
            <div className="space-y-2">
              <label className="text-sm font-medium flex items-center">
                <Palette className="h-4 w-4 mr-2" />
                主色调
              </label>
              <div className="flex flex-wrap gap-2">
                {[
                  { name: '蓝色', color: 'bg-blue-500' },
                  { name: '绿色', color: 'bg-green-500' },
                  { name: '红色', color: 'bg-red-500' },
                  { name: '黄色', color: 'bg-yellow-500' },
                  { name: '紫色', color: 'bg-purple-500' },
                  { name: '橙色', color: 'bg-orange-500' },
                  { name: '粉色', color: 'bg-pink-500' },
                  { name: '黑白', color: 'bg-gray-500' }
                ].map((colorOption) => (
                  <Button
                    key={colorOption.name}
                    variant={filters.colors.includes(colorOption.name) ? 'default' : 'outline'}
                    size="sm"
                    className="flex items-center space-x-2"
                    onClick={() => 
                      filters.colors.includes(colorOption.name)
                        ? removeFilter('colors', colorOption.name)
                        : addFilter('colors', colorOption.name)
                    }
                  >
                    <div className={`w-3 h-3 rounded-full ${colorOption.color}`} />
                    <span>{colorOption.name}</span>
                  </Button>
                ))}
              </div>
            </div>

            {/* 已选筛选条件 */}
            {(filters.dateRange || filters.tags.length > 0 || filters.colors.length > 0) && (
              <div className="space-y-2">
                <label className="text-sm font-medium">已选筛选条件</label>
                <div className="flex flex-wrap gap-2">
                  {filters.dateRange && (
                    <Badge variant="secondary" className="flex items-center space-x-1">
                      <Calendar className="h-3 w-3" />
                      <span>{filters.dateRange}</span>
                      <X 
                        className="h-3 w-3 cursor-pointer" 
                        onClick={() => removeFilter('dateRange', '')}
                      />
                    </Badge>
                  )}
                  {filters.tags.map((tag) => (
                    <Badge key={tag} variant="secondary" className="flex items-center space-x-1">
                      <Tag className="h-3 w-3" />
                      <span>{tag}</span>
                      <X 
                        className="h-3 w-3 cursor-pointer" 
                        onClick={() => removeFilter('tags', tag)}
                      />
                    </Badge>
                  ))}
                  {filters.colors.map((color) => (
                    <Badge key={color} variant="secondary" className="flex items-center space-x-1">
                      <Palette className="h-3 w-3" />
                      <span>{color}</span>
                      <X 
                        className="h-3 w-3 cursor-pointer" 
                        onClick={() => removeFilter('colors', color)}
                      />
                    </Badge>
                  ))}
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* 快速标签 */}
      <div className="space-y-2">
        <div className="flex items-center justify-between">
          <h4 className="text-sm font-medium flex items-center">
            <TrendingUp className="h-4 w-4 mr-2" />
            热门搜索
          </h4>
        </div>
        <div className="flex flex-wrap gap-2">
          {tags.slice(0, 8).map((tag) => (
            <Button
              key={tag}
              variant="outline"
              size="sm"
              onClick={() => handleTagClick(tag)}
              className="text-xs hover:bg-blue-50 dark:hover:bg-blue-950"
            >
              {tag}
            </Button>
          ))}
        </div>
      </div>

      {/* 搜索历史 */}
      {searchHistory.length > 0 && (
        <div className="space-y-2">
          <h4 className="text-sm font-medium flex items-center">
            <Clock className="h-4 w-4 mr-2" />
            最近搜索
          </h4>
          <div className="flex flex-wrap gap-2">
            {searchHistory.slice(0, 5).map((item, index) => (
              <Button
                key={index}
                variant="ghost"
                size="sm"
                onClick={() => handleTagClick(item)}
                className="text-xs text-slate-600 dark:text-slate-400 hover:text-slate-900 dark:hover:text-slate-100"
              >
                <Clock className="h-3 w-3 mr-1" />
                {item}
              </Button>
            ))}
          </div>
        </div>
      )}
    </div>
  )
} 