// import { useState, useRef, useEffect, useCallback } from 'react';
// import { Button } from '@/components/ui/button.tsx';
// import {
//   Image as ImageIcon,
//   Upload,
//   Camera,
//   FolderOpen,
//   X,
//   Check,
//   Grid3X3,
//   Search,
//   Loader2,
//   RefreshCw
// } from 'lucide-react';
// import { cn } from '@/lib/utils.ts';
// import { ImageData } from '@/data/mockData.ts';
// import { LocalImage } from './LocalImage';
// import { SimpleImage } from './SimpleImage';
//
// interface ImageSelectorProps {
//   onImageSelect: (imageData: { url: string; base64: string; file: File }) => void;
//   onLibraryImageSelect?: (image: ImageData) => void;
//   disabled?: boolean;
//   className?: string;
//   availableImages?: ImageData[];
// }
//
// type SelectionMode = 'upload' | 'library' | 'camera';
//
// export function ImageSelector({
//   onImageSelect,
//   onLibraryImageSelect,
//   disabled = false,
//   className,
//   availableImages = []
// }: ImageSelectorProps): JSX.Element {
//   const [mode, setMode] = useState<SelectionMode>('upload');
//   const [previewImage, setPreviewImage] = useState<string | null>(null);
//   const [selectedLibraryImage, setSelectedLibraryImage] = useState<ImageData | null>(null);
//   const [isDragOver, setIsDragOver] = useState<boolean>(false);
//   const fileInputRef = useRef<HTMLInputElement>(null);
//   const cameraInputRef = useRef<HTMLInputElement>(null);
//
//   // 图片库相关状态
//   const [librarySearchQuery, setLibrarySearchQuery] = useState('');
//   const [showLibrarySearch, setShowLibrarySearch] = useState(false);
//
//   const handleFileSelect = (file: File): void => {
//     if (!file.type.startsWith('image/')) {
//       alert('请选择图片文件');
//       return;
//     }
//
//     const reader = new FileReader();
//     reader.onload = (e) => {
//       const result = e.target?.result as string;
//       setPreviewImage(result);
//
//       onImageSelect({
//         url: result,
//         base64: result,
//         file
//       });
//     };
//     reader.readAsDataURL(file);
//   };
//
//   const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>): void => {
//     const file = e.target.files?.[0];
//     if (file) {
//       handleFileSelect(file);
//     }
//   };
//
//   const handleDragOver = (e: React.DragEvent): void => {
//     e.preventDefault();
//     setIsDragOver(true);
//   };
//
//   const handleDragLeave = (e: React.DragEvent): void => {
//     e.preventDefault();
//     setIsDragOver(false);
//   };
//
//   const handleDrop = (e: React.DragEvent): void => {
//     e.preventDefault();
//     setIsDragOver(false);
//
//     const file = e.dataTransfer.files?.[0];
//     if (file) {
//       handleFileSelect(file);
//     }
//   };
//
//   const handleLibraryImageSelect = (image: ImageData): void => {
//     setSelectedLibraryImage(image);
//     if (onLibraryImageSelect) {
//       onLibraryImageSelect(image);
//     }
//   };
//
//   const handleCameraCapture = (): void => {
//     cameraInputRef.current?.click();
//   };
//
//   const clearSelection = (): void => {
//     setPreviewImage(null);
//     setSelectedLibraryImage(null);
//     if (fileInputRef.current) {
//       fileInputRef.current.value = '';
//     }
//     if (cameraInputRef.current) {
//       cameraInputRef.current.value = '';
//     }
//   };
//
//   const openFileDialog = (): void => {
//     fileInputRef.current?.click();
//   };
//
//   // 当切换到图片库模式时加载数据
//   useEffect(() => {
//     if (mode === 'library') {
//       // 首先尝试使用传入的 availableImages
//       if (availableImages.length > 0) {
//         setLibraryQueryResult({
//           images: availableImages,
//           total: availableImages.length,
//           loading: false
//         });
//       } else {
//         // 如果没有传入图片，则从图片库加载
//         loadLibraryImages();
//       }
//     }
//   }, [mode, availableImages, loadLibraryImages]);
//
//   // 处理图片库搜索
//   const handleLibrarySearch = (value: string) => {
//     setLibrarySearchQuery(value);
//     if (availableImages.length === 0) {
//       // 只有在没有传入图片时才从数据库搜索
//       loadLibraryImages(value);
//     }
//   };
//
//   // 筛选可用图片（当使用传入的 availableImages 时）
//   const filteredAvailableImages = availableImages.length > 0 && librarySearchQuery
//     ? availableImages.filter(image =>
//         image.title.toLowerCase().includes(librarySearchQuery.toLowerCase()) ||
//         image.tags.some(tag => tag.toLowerCase().includes(librarySearchQuery.toLowerCase()))
//       )
//     : availableImages;
//
//   // 获取实际显示的图片列表
//   const displayImages = availableImages.length > 0
//     ? (librarySearchQuery ? filteredAvailableImages : availableImages)
//     : libraryQueryResult.images;
//
//   return (
//     <div className={cn('space-y-4', className)}>
//       {/* 隐藏的文件输入 */}
//       <input
//         ref={fileInputRef}
//         type="file"
//         accept="image/*"
//         onChange={handleFileInputChange}
//         className="hidden"
//         disabled={disabled}
//       />
//
//       {/* 隐藏的相机输入 */}
//       <input
//         ref={cameraInputRef}
//         type="file"
//         accept="image/*"
//         capture="environment"
//         onChange={handleFileInputChange}
//         className="hidden"
//         disabled={disabled}
//       />
//
//       {/* 模式选择按钮 */}
//       <div className="flex space-x-2 p-1 bg-slate-100/60 dark:bg-slate-800/60 rounded-lg">
//         <Button
//           variant={mode === 'upload' ? 'default' : 'ghost'}
//           size="sm"
//           onClick={() => setMode('upload')}
//           disabled={disabled}
//           className="flex-1"
//         >
//           <Upload className="h-4 w-4 mr-2" />
//           上传
//         </Button>
//         <Button
//           variant={mode === 'library' ? 'default' : 'ghost'}
//           size="sm"
//           onClick={() => setMode('library')}
//           disabled={disabled}
//           className="flex-1"
//         >
//           <FolderOpen className="h-4 w-4 mr-2" />
//           图片库
//         </Button>
//         <Button
//           variant={mode === 'camera' ? 'default' : 'ghost'}
//           size="sm"
//           onClick={() => setMode('camera')}
//           disabled={disabled}
//           className="flex-1"
//         >
//           <Camera className="h-4 w-4 mr-2" />
//           拍照
//         </Button>
//       </div>
//
//       {/* 上传模式 */}
//       {mode === 'upload' && (
//         <div>
//           {!previewImage ? (
//             <div
//               className={cn(
//                 'border-2 border-dashed rounded-lg p-6 text-center cursor-pointer transition-all duration-200',
//                 isDragOver
//                   ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
//                   : 'border-slate-300 dark:border-slate-600 hover:border-blue-400 hover:bg-slate-50 dark:hover:bg-slate-800',
//                 disabled && 'opacity-50 cursor-not-allowed'
//               )}
//               onDragOver={handleDragOver}
//               onDragLeave={handleDragLeave}
//               onDrop={handleDrop}
//               onClick={!disabled ? openFileDialog : undefined}
//             >
//               <Upload className="h-12 w-12 mx-auto mb-4 text-slate-400" />
//               <p className="text-lg font-medium text-slate-700 dark:text-slate-300 mb-2">
//                 拖拽图片到此处
//               </p>
//               <p className="text-sm text-slate-500 dark:text-slate-400 mb-4">
//                 或点击选择文件
//               </p>
//               <Button variant="outline" size="sm" disabled={disabled}>
//                 <ImageIcon className="h-4 w-4 mr-2" />
//                 选择图片
//               </Button>
//               <p className="text-xs text-slate-400 mt-2">
//                 支持 JPG、PNG、GIF 格式，最大 10MB
//               </p>
//             </div>
//           ) : (
//             <div className="relative">
//               <img
//                 src={previewImage}
//                 alt="预览图片"
//                 className="w-full max-h-64 rounded-lg object-cover"
//               />
//               <Button
//                 variant="ghost"
//                 size="icon"
//                 onClick={clearSelection}
//                 className="absolute top-2 right-2 h-8 w-8 bg-black/50 hover:bg-black/70 text-white rounded-full"
//                 disabled={disabled}
//               >
//                 <X className="h-4 w-4" />
//               </Button>
//               <div className="absolute bottom-2 left-2 px-3 py-1 bg-black/50 text-white text-sm rounded-full">
//                 已选择
//               </div>
//             </div>
//           )}
//         </div>
//       )}
//
//       {/* 图片库模式 */}
//       {mode === 'library' && (
//         <div className="space-y-4">
//           {/* 搜索栏 */}
//           <div className="space-y-3">
//             <div className="flex items-center justify-between">
//               <div className="flex items-center space-x-2">
//                 <Grid3X3 className="h-4 w-4 text-slate-500" />
//                 <span className="text-sm text-slate-600 dark:text-slate-400">
//                   图片库选择
//                   {libraryQueryResult.loading ? (
//                     <Loader2 className="h-3 w-3 animate-spin ml-2 inline" />
//                   ) : (
//                     ` (${displayImages.length} 张图片)`
//                   )}
//                 </span>
//               </div>
//               <div className="flex items-center space-x-2">
//                 <Button
//                   variant="ghost"
//                   size="sm"
//                   onClick={() => setShowLibrarySearch(!showLibrarySearch)}
//                   className="text-slate-500 hover:text-slate-700"
//                 >
//                   <Search className="h-4 w-4" />
//                 </Button>
//                 {availableImages.length === 0 && (
//                   <Button
//                     variant="ghost"
//                     size="sm"
//                     onClick={() => loadLibraryImages(librarySearchQuery)}
//                     disabled={libraryQueryResult.loading}
//                     className="text-slate-500 hover:text-slate-700"
//                   >
//                     <RefreshCw className={`h-4 w-4 ${libraryQueryResult.loading ? 'animate-spin' : ''}`} />
//                   </Button>
//                 )}
//               </div>
//             </div>
//
//             {/* 搜索输入框 */}
//             {showLibrarySearch && (
//               <div className="relative">
//                 <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400" />
//                 <input
//                   type="text"
//                   placeholder={availableImages.length > 0 ? "搜索图片标题或标签..." : "语义搜索图片内容..."}
//                   value={librarySearchQuery}
//                   onChange={(e) => handleLibrarySearch(e.target.value)}
//                   className="w-full pl-10 pr-4 py-2 text-sm bg-white/60 dark:bg-slate-800/60 backdrop-blur-sm border border-slate-200/50 dark:border-slate-700/50 rounded-lg focus:border-blue-500/50 focus:ring-blue-500/20 focus:ring-2 outline-none transition-all"
//                 />
//                 {librarySearchQuery && (
//                   <Button
//                     variant="ghost"
//                     size="sm"
//                     onClick={() => handleLibrarySearch('')}
//                     className="absolute right-1 top-1/2 transform -translate-y-1/2 h-6 w-6 p-0 text-slate-400 hover:text-slate-600"
//                   >
//                     <X className="h-3 w-3" />
//                   </Button>
//                 )}
//               </div>
//             )}
//           </div>
//
//           {/* 图片网格 */}
//           {libraryQueryResult.loading ? (
//             <div className="flex items-center justify-center py-8">
//               <Loader2 className="h-5 w-5 animate-spin mr-2 text-blue-500" />
//               <span className="text-sm text-slate-600 dark:text-slate-400">加载图片中...</span>
//             </div>
//           ) : displayImages.length > 0 ? (
//             <div className="space-y-4">
//               <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3 max-h-80 overflow-y-auto scrollbar-thin scrollbar-thumb-slate-300 dark:scrollbar-thumb-slate-600 scrollbar-track-transparent">
//                 {displayImages.map((image) => (
//                   <div
//                     key={image.id}
//                     className={cn(
//                       'relative cursor-pointer rounded-lg overflow-hidden transition-all duration-200 hover:scale-105 hover:shadow-lg',
//                       selectedLibraryImage?.id === image.id && 'ring-2 ring-blue-500 shadow-lg scale-105'
//                     )}
//                     onClick={() => handleLibraryImageSelect(image)}
//                   >
//                     {image.url.includes(':\\') || image.url.startsWith('/') ? (
//                       // 本地图片路径 - 使用简化组件
//                       <SimpleImage
//                         imagePath={image.url}
//                         alt={image.title}
//                         className="w-full h-24 object-cover"
//                         fallbackSrc="/api/placeholder/400/300"
//                       />
//                     ) : image.url.startsWith('/api/image/') ? (
//                       <LocalImage
//                         relativePath={image.url.replace('/api/image/', '')}
//                         alt={image.title}
//                         className="w-full h-24 object-cover"
//                         useAppProtocol={false}
//                       />
//                     ) : (
//                       // 网络图片或占位符
//                       <img
//                         src={image.url}
//                         alt={image.title}
//                         className="w-full h-24 object-cover"
//                         loading="lazy"
//                         onError={(e) => {
//                           console.error('❌ [ImageSelector] 图片加载失败:', image.url)
//                           // 如果图片加载失败，显示占位符
//                           e.currentTarget.style.display = 'none'
//                           e.currentTarget.nextElementSibling?.classList.remove('hidden')
//                         }}
//                       />
//                     )}
//
//                     {/* 备用占位符 */}
//                     <div className="hidden w-full h-24 flex items-center justify-center bg-slate-100 dark:bg-slate-800">
//                       <ImageIcon className="h-8 w-8 text-slate-400" />
//                     </div>
//                     {selectedLibraryImage?.id === image.id && (
//                       <div className="absolute inset-0 bg-blue-500/20 flex items-center justify-center">
//                         <div className="p-1.5 bg-blue-500 rounded-full shadow-lg">
//                           <Check className="h-4 w-4 text-white" />
//                         </div>
//                       </div>
//                     )}
//                     <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/70 to-transparent p-2">
//                       <p className="text-xs text-white truncate font-medium">{image.title}</p>
//                       {image.tags.length > 0 && (
//                         <p className="text-xs text-white/80 truncate mt-0.5">
//                           {image.tags.slice(0, 2).join(', ')}
//                         </p>
//                       )}
//                     </div>
//                   </div>
//                 ))}
//               </div>
//
//               {/* 选中图片预览 */}
//               {selectedLibraryImage && (
//                 <div className="p-4 bg-blue-50/60 dark:bg-blue-900/20 backdrop-blur-sm border border-blue-200/50 dark:border-blue-700/50 rounded-xl">
//                   <div className="flex items-center space-x-4">
//                     {selectedLibraryImage.url.includes(':\\') || selectedLibraryImage.url.startsWith('/') ? (
//                       // 本地图片路径 - 使用简化组件
//                       <SimpleImage
//                         imagePath={selectedLibraryImage.url}
//                         alt={selectedLibraryImage.title}
//                         className="w-16 h-16 rounded-lg object-cover shadow-sm"
//                         fallbackSrc="/api/placeholder/400/300"
//                       />
//                                          ) : selectedLibraryImage.url.startsWith('/api/image/') ? (
//                       <LocalImage
//                         relativePath={selectedLibraryImage.url.replace('/api/image/', '')}
//                         alt={selectedLibraryImage.title}
//                         className="w-16 h-16 rounded-lg object-cover shadow-sm"
//                         useAppProtocol={false}
//                       />
//                     ) : (
//                       <img
//                         src={selectedLibraryImage.url}
//                         alt={selectedLibraryImage.title}
//                         className="w-16 h-16 rounded-lg object-cover shadow-sm"
//                       />
//                     )}
//                     <div className="flex-1 min-w-0">
//                       <p className="font-medium text-slate-900 dark:text-slate-100 truncate">
//                         {selectedLibraryImage.title}
//                       </p>
//                       <p className="text-sm text-slate-600 dark:text-slate-400 truncate mt-1">
//                         {selectedLibraryImage.tags.slice(0, 4).join(', ')}
//                       </p>
//                                              <div className="flex items-center space-x-3 mt-2 text-xs text-slate-500">
//                          <span>已选择</span>
//                          <span className="px-2 py-0.5 bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300 rounded">
//                            {selectedLibraryImage.fileSize}
//                          </span>
//                        </div>
//                     </div>
//                     <Button
//                       variant="ghost"
//                       size="icon"
//                       onClick={clearSelection}
//                       className="h-8 w-8 text-slate-400 hover:text-slate-600 hover:bg-slate-100 dark:hover:bg-slate-700"
//                       disabled={disabled}
//                     >
//                       <X className="h-4 w-4" />
//                     </Button>
//                   </div>
//                 </div>
//               )}
//             </div>
//           ) : (
//             <div className="text-center py-12">
//               <FolderOpen className="h-12 w-12 mx-auto mb-4 text-slate-400" />
//               <p className="text-slate-600 dark:text-slate-400 mb-2">
//                 {librarySearchQuery ? '未找到匹配的图片' : '图片库为空'}
//               </p>
//               <p className="text-sm text-slate-500">
//                 {librarySearchQuery
//                   ? '请尝试其他搜索关键词'
//                   : '请先上传一些图片到图片库'
//                 }
//               </p>
//               {librarySearchQuery && (
//                 <Button
//                   variant="outline"
//                   size="sm"
//                   onClick={() => handleLibrarySearch('')}
//                   className="mt-3"
//                 >
//                   清除搜索
//                 </Button>
//               )}
//             </div>
//           )}
//         </div>
//       )}
//
//       {/* 拍照模式 */}
//       {mode === 'camera' && (
//         <div>
//           <div className="border-2 border-dashed border-slate-300 dark:border-slate-600 rounded-lg p-8 text-center">
//             <Camera className="h-16 w-16 mx-auto mb-4 text-slate-400" />
//             <p className="text-lg font-medium text-slate-700 dark:text-slate-300 mb-2">
//               使用相机拍照
//             </p>
//             <p className="text-sm text-slate-500 dark:text-slate-400 mb-6">
//               点击下方按钮打开相机拍摄照片
//             </p>
//             <Button
//               onClick={handleCameraCapture}
//               disabled={disabled}
//               className="bg-blue-500 hover:bg-blue-600"
//             >
//               <Camera className="h-4 w-4 mr-2" />
//               打开相机
//             </Button>
//             <p className="text-xs text-slate-400 mt-4">
//               需要浏览器支持相机访问权限
//             </p>
//           </div>
//         </div>
//       )}
//     </div>
//   );
// }