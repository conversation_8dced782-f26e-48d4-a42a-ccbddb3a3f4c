import { useState } from 'react';
import { 
  Bar<PERSON>hart3, 
  <PERSON>, 
  Check<PERSON><PERSON>cle, 
  AlertCircle, 
  TrendingUp, 
  Activity,
  ChevronDown,
  ChevronUp
} from 'lucide-react';
import { ToolCallHistory } from '../lib/toolCallStateManager';

interface ToolResultSummaryProps {
  statistics: {
    totalCalls: number;
    successfulCalls: number;
    failedCalls: number;
    averageExecutionTime: number;
    mostUsedTools: { toolName: string; count: number }[];
  };
  recentHistory: ToolCallHistory[];
  className?: string;
}

export function ToolResultSummary({ 
  statistics, 
  recentHistory, 
  className = '' 
}: ToolResultSummaryProps) {
  const [showHistory, setShowHistory] = useState(false);
  
  const successRate = statistics.totalCalls > 0 
    ? (statistics.successfulCalls / statistics.totalCalls) * 100 
    : 0;

  const getToolDisplayName = (toolName: string) => {
    const toolNameMap: Record<string, string> = {
      'find_similar_images_by_image': '以图搜图',
      'find_similar_images_by_description': '文本搜索图片',
      'find_images_by_tags': '标签搜索',
      'get_image_analysis': '图片分析'
    };
    return toolNameMap[toolName] || toolName;
  };

  const formatExecutionTime = (ms: number) => {
    if (ms < 1000) return `${ms}ms`;
    return `${(ms / 1000).toFixed(1)}s`;
  };

  return (
    <div className={`bg-white/80 dark:bg-slate-700/80 backdrop-blur-sm border border-slate-200/50 dark:border-slate-600/50 rounded-lg p-4 ${className}`}>
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center space-x-2">
          <BarChart3 className="h-4 w-4 text-blue-600 dark:text-blue-400" />
          <h4 className="text-sm font-medium text-slate-900 dark:text-slate-100">
            工具使用统计
          </h4>
        </div>
        <button
          onClick={() => setShowHistory(!showHistory)}
          className="flex items-center space-x-1 text-xs text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300"
        >
          {showHistory ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
          <span>历史记录</span>
        </button>
      </div>

      {/* Statistics Overview */}
      <div className="grid grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
        <div className="text-center">
          <div className="text-lg font-semibold text-slate-900 dark:text-slate-100">
            {statistics.totalCalls}
          </div>
          <div className="text-xs text-slate-600 dark:text-slate-400">
            总调用次数
          </div>
        </div>
        
        <div className="text-center">
          <div className="flex items-center justify-center space-x-1">
            <CheckCircle className="h-4 w-4 text-green-500" />
            <span className="text-lg font-semibold text-green-600 dark:text-green-400">
              {successRate.toFixed(1)}%
            </span>
          </div>
          <div className="text-xs text-slate-600 dark:text-slate-400">
            成功率
          </div>
        </div>
        
        <div className="text-center">
          <div className="flex items-center justify-center space-x-1">
            <Clock className="h-4 w-4 text-blue-500" />
            <span className="text-lg font-semibold text-blue-600 dark:text-blue-400">
              {formatExecutionTime(statistics.averageExecutionTime)}
            </span>
          </div>
          <div className="text-xs text-slate-600 dark:text-slate-400">
            平均耗时
          </div>
        </div>
        
        <div className="text-center">
          <div className="flex items-center justify-center space-x-1">
            <AlertCircle className="h-4 w-4 text-red-500" />
            <span className="text-lg font-semibold text-red-600 dark:text-red-400">
              {statistics.failedCalls}
            </span>
          </div>
          <div className="text-xs text-slate-600 dark:text-slate-400">
            失败次数
          </div>
        </div>
      </div>

      {/* Most Used Tools */}
      {statistics.mostUsedTools.length > 0 && (
        <div className="mb-4">
          <h5 className="text-xs font-medium text-slate-700 dark:text-slate-300 mb-2">
            常用工具
          </h5>
          <div className="space-y-2">
            {statistics.mostUsedTools.slice(0, 3).map((tool, index) => (
              <div key={index} className="flex items-center justify-between">
                <span className="text-sm text-slate-600 dark:text-slate-400">
                  {getToolDisplayName(tool.toolName)}
                </span>
                <div className="flex items-center space-x-2">
                  <div className="w-16 bg-slate-200 dark:bg-slate-600 rounded-full h-2">
                    <div 
                      className="bg-blue-500 h-2 rounded-full"
                      style={{ width: `${(tool.count / statistics.mostUsedTools[0].count) * 100}%` }}
                    />
                  </div>
                  <span className="text-xs text-slate-500 dark:text-slate-400 min-w-[2rem]">
                    {tool.count}
                  </span>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Recent History */}
      {showHistory && recentHistory.length > 0 && (
        <div>
          <h5 className="text-xs font-medium text-slate-700 dark:text-slate-300 mb-2">
            最近调用 ({recentHistory.length})
          </h5>
          <div className="space-y-2 max-h-48 overflow-y-auto">
            {recentHistory.map((item, index) => (
              <div key={index} className="flex items-center justify-between p-2 bg-slate-50 dark:bg-slate-800 rounded">
                <div className="flex items-center space-x-2">
                  {item.status === 'completed' ? (
                    <CheckCircle className="h-3 w-3 text-green-500" />
                  ) : (
                    <AlertCircle className="h-3 w-3 text-red-500" />
                  )}
                  <span className="text-xs text-slate-600 dark:text-slate-400">
                    {getToolDisplayName(item.toolName)}
                  </span>
                </div>
                <div className="flex items-center space-x-2">
                  <span className="text-xs text-slate-500 dark:text-slate-400">
                    {formatExecutionTime(item.executionTime)}
                  </span>
                  <span className="text-xs text-slate-400 dark:text-slate-500">
                    {item.timestamp.toLocaleTimeString()}
                  </span>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Empty State */}
      {statistics.totalCalls === 0 && (
        <div className="text-center py-8">
          <Activity className="h-8 w-8 text-slate-400 dark:text-slate-600 mx-auto mb-2" />
          <p className="text-sm text-slate-500 dark:text-slate-400">
            暂无工具调用记录
          </p>
        </div>
      )}
    </div>
  );
}

interface ToolResultInsightsProps {
  statistics: {
    totalCalls: number;
    successfulCalls: number;
    failedCalls: number;
    averageExecutionTime: number;
    mostUsedTools: { toolName: string; count: number }[];
  };
  className?: string;
}

export function ToolResultInsights({ statistics, className = '' }: ToolResultInsightsProps) {
  const insights = [];
  
  if (statistics.totalCalls > 0) {
    const successRate = (statistics.successfulCalls / statistics.totalCalls) * 100;
    
    if (successRate > 95) {
      insights.push({
        icon: <TrendingUp className="h-4 w-4 text-green-500" />,
        message: '工具执行成功率优秀',
        type: 'success'
      });
    } else if (successRate < 80) {
      insights.push({
        icon: <AlertCircle className="h-4 w-4 text-red-500" />,
        message: '工具执行成功率较低，建议检查参数',
        type: 'warning'
      });
    }
    
    if (statistics.averageExecutionTime > 5000) {
      insights.push({
        icon: <Clock className="h-4 w-4 text-yellow-500" />,
        message: '平均执行时间较长，可能影响用户体验',
        type: 'warning'
      });
    }
    
    if (statistics.mostUsedTools.length > 0) {
      const mostUsedTool = statistics.mostUsedTools[0];
      insights.push({
        icon: <Activity className="h-4 w-4 text-blue-500" />,
        message: `最常用的工具：${mostUsedTool.toolName} (${mostUsedTool.count} 次)`,
        type: 'info'
      });
    }
  }

  if (insights.length === 0) {
    return null;
  }

  return (
    <div className={`space-y-2 ${className}`}>
      {insights.map((insight, index) => (
        <div key={index} className={`flex items-center space-x-2 p-2 rounded ${
          insight.type === 'success' ? 'bg-green-50 dark:bg-green-900/20' :
          insight.type === 'warning' ? 'bg-yellow-50 dark:bg-yellow-900/20' :
          'bg-blue-50 dark:bg-blue-900/20'
        }`}>
          {insight.icon}
          <span className="text-xs text-slate-600 dark:text-slate-400">
            {insight.message}
          </span>
        </div>
      ))}
    </div>
  );
}