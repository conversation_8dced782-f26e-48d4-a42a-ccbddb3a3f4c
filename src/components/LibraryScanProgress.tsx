import React, { useState, useEffect } from 'react'

interface ScanProgress {
  libraryId: string
  scanId: string
  status: 'pending' | 'scanning' | 'paused' | 'completed' | 'error' | 'cancelled'
  totalFiles: number
  processedFiles: number
  failedFiles: number
  skippedFiles: number
  currentFile?: string
  startTime: string
  estimatedEndTime?: string
  elapsedTime: number
  processingSpeed: number
  errors: string[]
}

interface LibraryScanProgressProps {
  libraryId: string
  onComplete?: () => void
}

export const LibraryScanProgress: React.FC<LibraryScanProgressProps> = ({ 
  libraryId, 
  onComplete 
}) => {
  const [progress, setProgress] = useState<ScanProgress | null>(null)
  const [isScanning, setIsScanning] = useState(true)
  
  useEffect(() => {
    const pollProgress = async () => {
      try {
        const response = await window.electronAPI.imageLibrary.getScanProgress(libraryId)
        if (response.success && response.progress) {
          setProgress(response.progress)
          
          // 检查是否完成
          const { totalFiles, processedFiles, failedFiles, status } = response.progress
          if (['completed', 'error', 'cancelled'].includes(status)) {
            setIsScanning(false)
            onComplete?.()
          }
        }
      } catch (error) {
        console.error('获取扫描进度失败:', error)
      }
    }
    
    // 立即执行一次
    pollProgress()
    
    // 设置定时轮询
    const interval = setInterval(pollProgress, 1000)
    return () => clearInterval(interval)
  }, [libraryId, onComplete])
  
  const percentage = progress && progress.totalFiles > 0 
    ? Math.round(((progress.processedFiles + progress.failedFiles) / progress.totalFiles) * 100)
    : 0
  
  const successRate = progress && progress.processedFiles + progress.failedFiles > 0
    ? Math.round((progress.processedFiles / (progress.processedFiles + progress.failedFiles)) * 100)
    : 100
  
  return (
    <div className="scan-progress mt-4 p-4 bg-gray-50 rounded-lg">
      <div className="progress-header flex justify-between items-center mb-3">
        <span className="text-sm font-medium text-gray-700">扫描进度</span>
        <span className="text-sm font-bold text-gray-800">{percentage}%</span>
      </div>
      
      <div className="progress-bar w-full bg-gray-200 rounded-full h-2 mb-4">
        <div 
          className="progress-fill h-2 rounded-full transition-all duration-300 ease-out"
          style={{ 
            width: `${percentage}%`,
            backgroundColor: isScanning ? '#4CAF50' : '#2196F3'
          }}
        />
      </div>
      
      <div className="progress-stats grid grid-cols-2 md:grid-cols-4 gap-3 text-sm">
        <div className="stat-item">
          <span className="stat-label text-gray-600 block">总计:</span>
          <span className="stat-value font-semibold text-gray-800">{progress?.totalFiles || 0}</span>
        </div>
        <div className="stat-item">
          <span className="stat-label text-gray-600 block">已处理:</span>
          <span className="stat-value font-semibold text-green-600">{progress?.processedFiles || 0}</span>
        </div>
        <div className="stat-item">
          <span className="stat-label text-gray-600 block">失败:</span>
          <span className="stat-value font-semibold text-red-600">{progress?.failedFiles || 0}</span>
        </div>
        <div className="stat-item">
          <span className="stat-label text-gray-600 block">成功率:</span>
          <span className="stat-value font-semibold text-blue-600">{successRate}%</span>
        </div>
      </div>
      
      {!isScanning && (
        <div className="scan-complete flex items-center justify-center mt-4 p-2 bg-green-100 rounded-lg">
          <span className="complete-icon mr-2 text-green-600">✅</span>
          <span className="text-green-700 font-medium">扫描完成</span>
        </div>
      )}
    </div>
  )
}