/**
 * Enhanced AI Tool Result Display Component
 * 全新设计的AI工具执行结果显示组件，提供现代化的UI和更好的用户体验
 */

import React, { useState, useMemo } from 'react';
import { 
  Image as ImageIcon, 
  Search, 
  Tags, 
  Eye, 
  Star, 
  ChevronDown, 
  ChevronUp,
  AlertCircle,
  ExternalLink,
  Download,
  Share2,
  Maximize2,
  Filter,
  Grid3X3,
  List,
  Sparkles,
  Clock,
  Target,
  Palette,
  Camera,
  Zap,
  Activity,
  TrendingUp,
  Copy,
  RefreshCw,
  Settings,
  MoreHorizontal,
  Play,
  Pause,
  BarChart3,
  Layers,
  Hash,
  FileImage,
  Compass
} from 'lucide-react';
import { ToolCallResponse } from '../types/tools';
import { Badge } from './ui/badge';
import { Card, CardContent, CardHeader } from './ui/card';
import { getImageUrl, getThumbnailUrl, handleImageError, handleImageLoad, extractImagePath } from '../lib/imageUrlUtils';

interface EnhancedAIToolResultDisplayProps {
  toolResult: ToolCallResponse;
  className?: string;
  onImageClick?: (imageUrl: string, imageData: any) => void;
  onRefineSearch?: (parameters: any) => void;
  onSaveResult?: (result: any) => void;
  showMetrics?: boolean;
  showQuickActions?: boolean;
  compact?: boolean;
}

export function EnhancedAIToolResultDisplay({ 
  toolResult, 
  className = '',
  onImageClick,
  onRefineSearch,
  onSaveResult,
  showMetrics = true,
  showQuickActions = true,
  compact = false
}: EnhancedAIToolResultDisplayProps) {
  const [isExpanded, setIsExpanded] = useState(!compact);
  const [viewMode, setViewMode] = useState<'grid' | 'list' | 'masonry'>('grid');
  const [showAdvancedControls, setShowAdvancedControls] = useState(false);
  const [sortBy, setSortBy] = useState<'similarity' | 'title' | 'date' | 'relevance'>('similarity');
  const [filterBy, setFilterBy] = useState<'all' | 'high' | 'medium' | 'low'>('all');
  
  const { result } = toolResult;
  const toolName = result.metadata?.toolName || 'unknown';
  const executionTime = result.metadata?.executionTime || 0;
  const parameters = result.metadata?.parameters || {};

  // 计算统计数据
  const stats = useMemo(() => {
    if (!result.success || !result.data?.results) return null;
    
    const results = result.data.results;
    const avgSimilarity = results.reduce((sum: number, r: any) => sum + (r.similarity || 0), 0) / results.length;
    const highQuality = results.filter((r: any) => (r.similarity || 0) > 0.8).length;
    const mediumQuality = results.filter((r: any) => (r.similarity || 0) > 0.5 && (r.similarity || 0) <= 0.8).length;
    const lowQuality = results.filter((r: any) => (r.similarity || 0) <= 0.5).length;
    
    return {
      total: results.length,
      avgSimilarity: avgSimilarity * 100,
      highQuality,
      mediumQuality,
      lowQuality,
      hasTag: results.filter((r: any) => r.tags && r.tags.length > 0).length
    };
  }, [result]);

  if (!result.success) {
    return (
      <EnhancedErrorDisplay 
        toolResult={toolResult} 
        className={className}
        onRefineSearch={onRefineSearch}
        compact={compact}
      />
    );
  }

  switch (toolName) {
    case 'analyze_image':
      return (
        <EnhancedImageAnalysisDisplay 
          toolResult={toolResult} 
          className={className}
          onRefineSearch={onRefineSearch}
          onSaveResult={onSaveResult}
          showMetrics={showMetrics}
          showQuickActions={showQuickActions}
          compact={compact}
        />
      );
    case 'find_similar_images_by_image':
    case 'find_similar_images_by_description':
    case 'find_images_by_tags':
      return (
        <EnhancedImageSearchDisplay 
          toolResult={toolResult} 
          className={className}
          viewMode={viewMode}
          onViewModeChange={setViewMode}
          onImageClick={onImageClick}
          onRefineSearch={onRefineSearch}
          onSaveResult={onSaveResult}
          showMetrics={showMetrics}
          showQuickActions={showQuickActions}
          compact={compact}
          stats={stats}
          sortBy={sortBy}
          onSortChange={(sort) => setSortBy(sort as any)}
          filterBy={filterBy}
          onFilterChange={(filter) => setFilterBy(filter as any)}
        />
      );
    default:
      return (
        <GenericEnhancedToolResult 
          toolResult={toolResult} 
          className={className}
          compact={compact}
        />
      );
  }
}

// 错误显示组件
interface EnhancedErrorDisplayProps {
  toolResult: ToolCallResponse;
  className?: string;
  onRefineSearch?: (parameters: any) => void;
  compact?: boolean;
}

function EnhancedErrorDisplay({ 
  toolResult, 
  className = '', 
  onRefineSearch,
  compact = false 
}: EnhancedErrorDisplayProps) {
  const { result } = toolResult;
  const toolName = result.metadata?.toolName || 'unknown';
  const executionTime = result.metadata?.executionTime || 0;

  const getErrorIcon = (error: string) => {
    if (error.includes('网络') || error.includes('连接')) return Activity;
    if (error.includes('格式') || error.includes('文件')) return FileImage;
    if (error.includes('参数') || error.includes('输入')) return Settings;
    return AlertCircle;
  };

  const getErrorSuggestions = (toolName: string, error: string) => {
    const suggestions: { icon: any; text: string; action?: () => void }[] = [];
    
    switch (toolName) {
      case 'analyze_image':
        suggestions.push(
          { icon: FileImage, text: '检查图片格式（支持 JPEG、PNG、WebP）' },
          { icon: Layers, text: '确保图片文件大小适中（< 10MB）' },
          { icon: RefreshCw, text: '尝试重新上传图片', action: () => onRefineSearch?.({}) }
        );
        break;
      case 'find_similar_images_by_description':
        suggestions.push(
          { icon: Hash, text: '使用更具体的关键词描述' },
          { icon: Tags, text: '尝试切换到标签搜索模式' },
          { icon: Compass, text: '简化搜索描述', action: () => onRefineSearch?.({}) }
        );
        break;
      case 'find_images_by_tags':
        suggestions.push(
          { icon: Tags, text: '使用更常见的标签' },
          { icon: Settings, text: '切换到"任意匹配"模式' },
          { icon: Search, text: '尝试描述搜索', action: () => onRefineSearch?.({}) }
        );
        break;
    }
    
    return suggestions;
  };

  const ErrorIcon = getErrorIcon(result.error || '');
  const suggestions = getErrorSuggestions(toolName, result.error || '');

  return (
    <Card className={`border-red-200/50 dark:border-red-800/30 tool-result-container error-shake ${className}`}>
      <CardHeader className="bg-gradient-to-r from-red-50/50 to-orange-50/50 dark:from-red-900/10 dark:to-orange-900/10">
        <div className="flex items-center space-x-4">
          <div className="p-3 bg-red-100 dark:bg-red-900/30 rounded-full">
            <ErrorIcon className="h-6 w-6 text-red-500" />
          </div>
          <div className="flex-1">
            <h4 className="text-lg font-semibold text-red-700 dark:text-red-300">
              工具执行失败
            </h4>
            <div className="flex items-center space-x-4 mt-1">
              <Badge variant="destructive" className="text-xs">
                {toolName}
              </Badge>
              <span className="text-xs text-red-600/70 dark:text-red-400/70">
                <Clock className="h-3 w-3 inline mr-1" />
                {executionTime}ms
              </span>
            </div>
          </div>
        </div>
      </CardHeader>
      
      <CardContent className="p-6">
        <div className="space-y-4">
          <div className="p-4 bg-red-50/50 dark:bg-red-900/10 rounded-lg border border-red-200/30 dark:border-red-800/20">
            <p className="text-sm text-red-700 dark:text-red-300 font-medium">错误信息：</p>
            <p className="text-sm text-red-600 dark:text-red-400 mt-1">
              {result.error || '未知错误'}
            </p>
          </div>
          
          {!compact && suggestions.length > 0 && (
            <div className="space-y-3">
              <p className="text-sm font-medium text-slate-700 dark:text-slate-300">建议解决方案：</p>
              <div className="space-y-2">
                {suggestions.map((suggestion, index) => (
                  <div key={index} className="flex items-center space-x-3 p-3 bg-slate-50/50 dark:bg-slate-800/50 rounded-lg border border-slate-200/30 dark:border-slate-700/30">
                    <suggestion.icon className="h-4 w-4 text-slate-500 dark:text-slate-400 flex-shrink-0" />
                    <span className="text-sm text-slate-600 dark:text-slate-300 flex-1">
                      {suggestion.text}
                    </span>
                    {suggestion.action && (
                      <button
                        onClick={suggestion.action}
                        className="text-xs text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 font-medium"
                      >
                        试试看
                      </button>
                    )}
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}

// 图片分析结果显示组件
interface EnhancedImageAnalysisDisplayProps {
  toolResult: ToolCallResponse;
  className?: string;
  onRefineSearch?: (parameters: any) => void;
  onSaveResult?: (result: any) => void;
  showMetrics?: boolean;
  showQuickActions?: boolean;
  compact?: boolean;
}

function EnhancedImageAnalysisDisplay({
  toolResult,
  className = '',
  onRefineSearch,
  onSaveResult,
  showMetrics = true,
  showQuickActions = true,
  compact = false
}: EnhancedImageAnalysisDisplayProps) {
  const { result } = toolResult;
  const data = result.data || result;
  const executionTime = result.metadata?.executionTime || 0;
  
  const confidence = data.confidence || 0;
  const getConfidenceColor = (conf: number) => {
    if (conf >= 0.8) return 'text-green-600 dark:text-green-400';
    if (conf >= 0.6) return 'text-yellow-600 dark:text-yellow-400';
    return 'text-red-600 dark:text-red-400';
  };

  const getConfidenceBadge = (conf: number) => {
    if (conf >= 0.8) return { variant: 'default' as const, text: '高置信度' };
    if (conf >= 0.6) return { variant: 'secondary' as const, text: '中等置信度' };
    return { variant: 'destructive' as const, text: '低置信度' };
  };

  const confidenceBadge = getConfidenceBadge(confidence);

  return (
    <Card className={`border-blue-200/50 dark:border-blue-700/30 tool-result-container success-check ${className}`}>
      <CardHeader className="bg-gradient-to-r from-blue-50/50 to-indigo-50/50 dark:from-blue-900/10 dark:to-indigo-900/10">
        <div className="flex items-center space-x-4">
          <div className="p-3 bg-blue-100 dark:bg-blue-900/30 rounded-full">
            <Camera className="h-6 w-6 text-blue-600 dark:text-blue-400" />
          </div>
          <div className="flex-1">
            <h4 className="text-lg font-semibold text-slate-900 dark:text-slate-100">
              图片分析结果
            </h4>
            <div className="flex items-center space-x-3 mt-1">
              <Badge variant={confidenceBadge.variant} className="text-xs">
                {confidenceBadge.text}
              </Badge>
              <span className="text-xs text-slate-500 dark:text-slate-400">
                <Clock className="h-3 w-3 inline mr-1" />
                {executionTime}ms
              </span>
            </div>
          </div>
          {showQuickActions && (
            <div className="flex items-center space-x-2">
              {onSaveResult && (
                <button
                  onClick={() => onSaveResult(data)}
                  className="p-2 text-slate-600 dark:text-slate-400 hover:text-slate-900 dark:hover:text-slate-100 hover:bg-white/50 dark:hover:bg-slate-700/50 rounded-lg transition-colors"
                  title="保存结果"
                >
                  <Download className="h-4 w-4" />
                </button>
              )}
              <button
                className="p-2 text-slate-600 dark:text-slate-400 hover:text-slate-900 dark:hover:text-slate-100 hover:bg-white/50 dark:hover:bg-slate-700/50 rounded-lg transition-colors"
                title="分享结果"
              >
                <Share2 className="h-4 w-4" />
              </button>
            </div>
          )}
        </div>
      </CardHeader>

      <CardContent className="p-6">
        <div className="space-y-6">
          {/* 置信度指标 */}
          {showMetrics && (
            <div className="flex items-center space-x-6 p-4 bg-slate-50/50 dark:bg-slate-800/50 rounded-lg">
              <div className="flex items-center space-x-3">
                <Target className="h-5 w-5 text-slate-500 dark:text-slate-400" />
                <div>
                  <p className="text-sm font-medium text-slate-700 dark:text-slate-300">分析置信度</p>
                  <p className={`text-lg font-bold ${getConfidenceColor(confidence)}`}>
                    {(confidence * 100).toFixed(1)}%
                  </p>
                </div>
              </div>
              <div className="flex-1">
                <div className="w-full bg-slate-200 dark:bg-slate-700 rounded-full h-2">
                  <div 
                    className={`h-2 rounded-full transition-all duration-500 ${
                      confidence >= 0.8 ? 'bg-green-500' : 
                      confidence >= 0.6 ? 'bg-yellow-500' : 'bg-red-500'
                    }`}
                    style={{ width: `${confidence * 100}%` }}
                  />
                </div>
              </div>
            </div>
          )}

          {/* 描述信息 */}
          {data.description && (
            <div className="space-y-3">
              <div className="flex items-center space-x-2">
                <Eye className="h-4 w-4 text-slate-500 dark:text-slate-400" />
                <h5 className="text-sm font-semibold text-slate-700 dark:text-slate-300">图片描述</h5>
              </div>
              <p className="text-sm text-slate-600 dark:text-slate-300 leading-relaxed bg-slate-50/50 dark:bg-slate-800/50 p-4 rounded-lg">
                {data.description}
              </p>
            </div>
          )}

          {/* 标签信息 */}
          {data.tags && data.tags.length > 0 && (
            <div className="space-y-3">
              <div className="flex items-center space-x-2">
                <Tags className="h-4 w-4 text-slate-500 dark:text-slate-400" />
                <h5 className="text-sm font-semibold text-slate-700 dark:text-slate-300">识别标签</h5>
                <Badge variant="secondary" className="text-xs">
                  {data.tags.length} 个
                </Badge>
              </div>
              <div className="flex flex-wrap gap-2">
                {data.tags.map((tag: string, index: number) => (
                  <Badge key={index} variant="outline" className="text-xs tag-item">
                    {tag}
                  </Badge>
                ))}
              </div>
            </div>
          )}

          {/* 快速操作 */}
          {showQuickActions && !compact && (
            <div className="flex flex-wrap gap-2 pt-4 border-t border-slate-200/50 dark:border-slate-700/50">
              <button
                onClick={() => onRefineSearch?.({ description: data.description })}
                className="inline-flex items-center px-3 py-2 text-xs font-medium text-blue-700 dark:text-blue-300 bg-blue-100 dark:bg-blue-900/30 hover:bg-blue-200 dark:hover:bg-blue-900/50 rounded-lg transition-colors"
              >
                <Search className="h-3 w-3 mr-1" />
                搜索相似图片
              </button>
              <button
                onClick={() => onRefineSearch?.({ tags: data.tags })}
                className="inline-flex items-center px-3 py-2 text-xs font-medium text-green-700 dark:text-green-300 bg-green-100 dark:bg-green-900/30 hover:bg-green-200 dark:hover:bg-green-900/50 rounded-lg transition-colors"
              >
                <Tags className="h-3 w-3 mr-1" />
                按标签搜索
              </button>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}

// 图片搜索结果显示组件
interface EnhancedImageSearchDisplayProps {
  toolResult: ToolCallResponse;
  className?: string;
  viewMode: 'grid' | 'list' | 'masonry';
  onViewModeChange: (mode: 'grid' | 'list' | 'masonry') => void;
  onImageClick?: (imageUrl: string, imageData: any) => void;
  onRefineSearch?: (parameters: any) => void;
  onSaveResult?: (result: any) => void;
  showMetrics?: boolean;
  showQuickActions?: boolean;
  compact?: boolean;
  stats?: any;
  sortBy: string;
  onSortChange: (sort: string) => void;
  filterBy: string;
  onFilterChange: (filter: string) => void;
}

function EnhancedImageSearchDisplay({
  toolResult,
  className = '',
  viewMode,
  onViewModeChange,
  onImageClick,
  onRefineSearch,
  onSaveResult,
  showMetrics = true,
  showQuickActions = true,
  compact = false,
  stats,
  sortBy,
  onSortChange,
  filterBy,
  onFilterChange
}: EnhancedImageSearchDisplayProps) {
  const [showAll, setShowAll] = useState(false);
  const { result } = toolResult;
  const data = result.data || result;
  const executionTime = result.metadata?.executionTime || 0;
  const toolName = result.metadata?.toolName || '';
  
  if (!data || !data.results) {
    return <GenericEnhancedToolResult toolResult={toolResult} className={className} compact={compact} />;
  }

  const images = data.results;
  const displayLimit = compact ? 6 : 12;
  let filteredImages = images;

  // 应用过滤器
  if (filterBy !== 'all') {
    filteredImages = images.filter((img: any) => {
      const similarity = img.similarity || 0;
      switch (filterBy) {
        case 'high': return similarity > 0.8;
        case 'medium': return similarity > 0.5 && similarity <= 0.8;
        case 'low': return similarity <= 0.5;
        default: return true;
      }
    });
  }

  // 应用排序
  const sortedImages = [...filteredImages].sort((a, b) => {
    switch (sortBy) {
      case 'similarity':
        return (b.similarity || 0) - (a.similarity || 0);
      case 'title':
        return (a.title || '').localeCompare(b.title || '');
      case 'date':
        return new Date(b.uploadTime || 0).getTime() - new Date(a.uploadTime || 0).getTime();
      default:
        return 0;
    }
  });

  const displayImages = showAll ? sortedImages : sortedImages.slice(0, displayLimit);
  const hasMore = sortedImages.length > displayLimit;

  const getSearchMethodIcon = (toolName: string) => {
    switch (toolName) {
      case 'find_similar_images_by_image': return ImageIcon;
      case 'find_similar_images_by_description': return Search;
      case 'find_images_by_tags': return Tags;
      default: return Search;
    }
  };

  const getSearchMethodName = (toolName: string) => {
    switch (toolName) {
      case 'find_similar_images_by_image': return '以图搜图';
      case 'find_similar_images_by_description': return '描述搜索';
      case 'find_images_by_tags': return '标签搜索';
      default: return '图片搜索';
    }
  };

  const SearchIcon = getSearchMethodIcon(toolName);

  return (
    <Card className={`border-blue-200/50 dark:border-blue-700/30 tool-result-container ${className}`}>
      <CardHeader className="bg-gradient-to-r from-blue-50/50 to-purple-50/50 dark:from-blue-900/10 dark:to-purple-900/10">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="p-3 bg-blue-100 dark:bg-blue-900/30 rounded-full">
              <SearchIcon className="h-6 w-6 text-blue-600 dark:text-blue-400" />
            </div>
            <div>
              <h4 className="text-lg font-semibold text-slate-900 dark:text-slate-100">
                {getSearchMethodName(toolName)}结果
              </h4>
              <div className="flex items-center space-x-4 mt-1">
                <Badge variant="default" className="text-xs">
                  找到 {sortedImages.length} 张图片
                </Badge>
                <span className="text-xs text-slate-500 dark:text-slate-400">
                  <Clock className="h-3 w-3 inline mr-1" />
                  {executionTime}ms
                </span>
              </div>
            </div>
          </div>
          {showQuickActions && (
            <div className="flex items-center space-x-2">
              {/* 视图模式切换 */}
              <div className="flex bg-slate-100 dark:bg-slate-700 rounded-lg p-1">
                <button
                  onClick={() => onViewModeChange('grid')}
                  className={`p-2 rounded transition-colors ${
                    viewMode === 'grid' 
                      ? 'bg-white dark:bg-slate-600 text-slate-900 dark:text-slate-100 shadow-sm' 
                      : 'text-slate-600 dark:text-slate-400'
                  }`}
                  title="网格视图"
                >
                  <Grid3X3 className="h-4 w-4" />
                </button>
                <button
                  onClick={() => onViewModeChange('list')}
                  className={`p-2 rounded transition-colors ${
                    viewMode === 'list' 
                      ? 'bg-white dark:bg-slate-600 text-slate-900 dark:text-slate-100 shadow-sm' 
                      : 'text-slate-600 dark:text-slate-400'
                  }`}
                  title="列表视图"
                >
                  <List className="h-4 w-4" />
                </button>
                <button
                  onClick={() => onViewModeChange('masonry')}
                  className={`p-2 rounded transition-colors ${
                    viewMode === 'masonry' 
                      ? 'bg-white dark:bg-slate-600 text-slate-900 dark:text-slate-100 shadow-sm' 
                      : 'text-slate-600 dark:text-slate-400'
                  }`}
                  title="瀑布流视图"
                >
                  <Layers className="h-4 w-4" />
                </button>
              </div>
              {onSaveResult && (
                <button
                  onClick={() => onSaveResult(data)}
                  className="p-2 text-slate-600 dark:text-slate-400 hover:text-slate-900 dark:hover:text-slate-100 hover:bg-white/50 dark:hover:bg-slate-700/50 rounded-lg transition-colors"
                  title="保存结果"
                >
                  <Download className="h-4 w-4" />
                </button>
              )}
            </div>
          )}
        </div>
      </CardHeader>

      {/* 统计信息 */}
      {showMetrics && stats && !compact && (
        <div className="px-6 py-4 bg-slate-50/30 dark:bg-slate-800/30 border-b border-slate-200/50 dark:border-slate-700/50">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center">
              <p className="text-lg font-bold text-blue-600 dark:text-blue-400 stat-number">{stats.total}</p>
              <p className="text-xs text-slate-500 dark:text-slate-400">总结果</p>
            </div>
            <div className="text-center">
              <p className="text-lg font-bold text-green-600 dark:text-green-400 stat-number">{stats.highQuality}</p>
              <p className="text-xs text-slate-500 dark:text-slate-400">高相似度</p>
            </div>
            <div className="text-center">
              <p className="text-lg font-bold text-yellow-600 dark:text-yellow-400 stat-number">{stats.avgSimilarity.toFixed(1)}%</p>
              <p className="text-xs text-slate-500 dark:text-slate-400">平均相似度</p>
            </div>
            <div className="text-center">
              <p className="text-lg font-bold text-purple-600 dark:text-purple-400 stat-number">{stats.hasTag}</p>
              <p className="text-xs text-slate-500 dark:text-slate-400">有标签</p>
            </div>
          </div>
        </div>
      )}

      {/* 控制栏 */}
      {!compact && (
        <div className="px-6 py-4 bg-slate-50/30 dark:bg-slate-800/30 border-b border-slate-200/50 dark:border-slate-700/50">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <select
                value={sortBy}
                onChange={(e) => onSortChange(e.target.value)}
                className="text-sm bg-white dark:bg-slate-700 border border-slate-200 dark:border-slate-600 rounded-lg px-3 py-2"
              >
                <option value="similarity">按相似度排序</option>
                <option value="title">按标题排序</option>
                <option value="date">按日期排序</option>
                <option value="relevance">按相关性排序</option>
              </select>
              <select
                value={filterBy}
                onChange={(e) => onFilterChange(e.target.value)}
                className="text-sm bg-white dark:bg-slate-700 border border-slate-200 dark:border-slate-600 rounded-lg px-3 py-2"
              >
                <option value="all">全部结果</option>
                <option value="high">高相似度 (&gt;80%)</option>
                <option value="medium">中等相似度 (50-80%)</option>
                <option value="low">低相似度 (&lt;50%)</option>
              </select>
            </div>
            {onRefineSearch && (
              <button
                onClick={() => onRefineSearch({})}
                className="inline-flex items-center px-3 py-2 text-sm font-medium text-blue-700 dark:text-blue-300 bg-blue-100 dark:bg-blue-900/30 hover:bg-blue-200 dark:hover:bg-blue-900/50 rounded-lg transition-colors"
              >
                <Filter className="h-4 w-4 mr-1" />
                调整搜索
              </button>
            )}
          </div>
        </div>
      )}

      <CardContent className="p-6">
        {/* 图片网格 */}
        {viewMode === 'grid' && (
          <div className={`grid gap-4 ${compact ? 'grid-cols-2 md:grid-cols-3' : 'grid-cols-2 md:grid-cols-3 lg:grid-cols-4'}`}>
            {displayImages.map((image: any, index: number) => (
              <ImageCard
                key={index}
                image={image}
                onClick={() => onImageClick && onImageClick(image.url, image)}
                compact={compact}
              />
            ))}
          </div>
        )}

        {/* 图片列表 */}
        {viewMode === 'list' && (
          <div className="space-y-4">
            {displayImages.map((image: any, index: number) => (
              <ImageListItem
                key={index}
                image={image}
                onClick={() => onImageClick && onImageClick(image.url, image)}
              />
            ))}
          </div>
        )}

        {/* 瀑布流视图 */}
        {viewMode === 'masonry' && (
          <div className="columns-2 md:columns-3 lg:columns-4 gap-4">
            {displayImages.map((image: any, index: number) => (
              <div key={index} className="break-inside-avoid mb-4">
                <ImageCard
                  image={image}
                  onClick={() => onImageClick && onImageClick(image.url, image)}
                  compact={compact}
                  masonry
                />
              </div>
            ))}
          </div>
        )}

        {/* 显示更多按钮 */}
        {hasMore && (
          <div className="text-center mt-6">
            <button
              onClick={() => setShowAll(!showAll)}
              className="inline-flex items-center px-4 py-2 text-sm font-medium text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/20 hover:bg-blue-100 dark:hover:bg-blue-900/30 rounded-lg transition-colors"
            >
              {showAll ? (
                <>
                  <ChevronUp className="h-4 w-4 mr-1" />
                  收起
                </>
              ) : (
                <>
                  <ChevronDown className="h-4 w-4 mr-1" />
                  显示更多 ({sortedImages.length - displayLimit} 张)
                </>
              )}
            </button>
          </div>
        )}
      </CardContent>
    </Card>
  );
}

// 图片卡片组件
interface ImageCardProps {
  image: any;
  onClick: () => void;
  compact?: boolean;
  masonry?: boolean;
}

function ImageCard({ image, onClick, compact = false, masonry = false }: ImageCardProps) {
  const [imageLoaded, setImageLoaded] = useState(false);
  const [imageError, setImageError] = useState(false);

  const imagePath = extractImagePath(image);
  const imageUrl = getImageUrl(imagePath);

  return (
    <div className="group relative cursor-pointer image-card" onClick={onClick}>
      <div className={`rounded-lg overflow-hidden bg-slate-100 dark:bg-slate-800 ${!masonry ? 'aspect-square' : ''}`}>
        {!imageError ? (
          <img
            src={imageUrl}
            alt={image.title || '搜索结果'}
            className={`w-full h-full object-cover group-hover:scale-105 transition-transform duration-200 ${
              imageLoaded ? 'opacity-100' : 'opacity-0'
            }`}
            onLoad={() => {
              setImageLoaded(true);
              handleImageLoad(imageUrl);
            }}
            onError={() => {
              setImageError(true);
              setImageLoaded(true);
              handleImageError(imageUrl, imagePath);
            }}
          />
        ) : (
          <div className="w-full h-full flex items-center justify-center">
            <FileImage className="h-8 w-8 text-slate-400" />
          </div>
        )}
        <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-colors duration-200 flex items-center justify-center">
          <Maximize2 className="h-6 w-6 text-white opacity-0 group-hover:opacity-100 transition-opacity duration-200" />
        </div>
      </div>
      
      {!compact && (
        <div className="mt-3 space-y-2">
          <h5 className="text-sm font-medium text-slate-900 dark:text-slate-100 line-clamp-1">
            {image.title || '未命名图片'}
          </h5>
          {image.similarity && (
            <div className="flex items-center space-x-2">
              <Star className="h-3 w-3 text-yellow-500" />
              <span className="text-xs text-slate-600 dark:text-slate-400">
                {Math.round(image.similarity * 100)}% 相似
              </span>
            </div>
          )}
          {image.tags && image.tags.length > 0 && (
            <div className="flex flex-wrap gap-1">
              {image.tags.slice(0, 2).map((tag: string, tagIndex: number) => (
                <Badge key={tagIndex} variant="outline" className="text-xs px-1 py-0">
                  {tag}
                </Badge>
              ))}
              {image.tags.length > 2 && (
                <span className="text-xs text-slate-500 dark:text-slate-400">
                  +{image.tags.length - 2}
                </span>
              )}
            </div>
          )}
        </div>
      )}
    </div>
  );
}

// 图片列表项组件
interface ImageListItemProps {
  image: any;
  onClick: () => void;
}

function ImageListItem({ image, onClick }: ImageListItemProps) {
  const imagePath = extractImagePath(image);
  const imageUrl = getThumbnailUrl(imagePath);

  return (
    <div 
      className="flex items-center space-x-4 p-4 bg-slate-50/50 dark:bg-slate-800/50 rounded-lg hover:bg-slate-100/50 dark:hover:bg-slate-800/70 transition-colors cursor-pointer"
      onClick={onClick}
    >
      <img
        src={imageUrl}
        alt={image.title || '搜索结果'}
        className="w-16 h-16 object-cover rounded-lg"
        onError={(e) => {
          handleImageError(imageUrl, imagePath, '/api/placeholder/80/80');
          e.currentTarget.src = '/api/placeholder/80/80';
        }}
        onLoad={() => {
          handleImageLoad(imageUrl);
        }}
      />
      <div className="flex-1 min-w-0">
        <h5 className="text-sm font-medium text-slate-900 dark:text-slate-100 truncate">
          {image.title || '未命名图片'}
        </h5>
        <div className="flex items-center space-x-4 mt-1">
          {image.similarity && (
            <div className="flex items-center space-x-1">
              <Star className="h-3 w-3 text-yellow-500" />
              <span className="text-xs text-slate-600 dark:text-slate-400">
                {Math.round(image.similarity * 100)}%
              </span>
            </div>
          )}
          {image.uploadTime && (
            <span className="text-xs text-slate-500 dark:text-slate-400">
              {new Date(image.uploadTime).toLocaleDateString()}
            </span>
          )}
        </div>
        {image.tags && image.tags.length > 0 && (
          <div className="flex flex-wrap gap-1 mt-2">
            {image.tags.slice(0, 3).map((tag: string, index: number) => (
              <Badge key={index} variant="outline" className="text-xs px-1 py-0">
                {tag}
              </Badge>
            ))}
            {image.tags.length > 3 && (
              <span className="text-xs text-slate-500 dark:text-slate-400">
                +{image.tags.length - 3}
              </span>
            )}
          </div>
        )}
      </div>
      <Maximize2 className="h-5 w-5 text-slate-400" />
    </div>
  );
}

// 通用工具结果显示组件
interface GenericEnhancedToolResultProps {
  toolResult: ToolCallResponse;
  className?: string;
  compact?: boolean;
}

function GenericEnhancedToolResult({ 
  toolResult, 
  className = '',
  compact = false 
}: GenericEnhancedToolResultProps) {
  const { result } = toolResult;
  const toolName = result.metadata?.toolName || 'unknown';
  const executionTime = result.metadata?.executionTime || 0;

  return (
    <Card className={`border-slate-200/50 dark:border-slate-600/50 ${className}`}>
      <CardHeader className="bg-gradient-to-r from-slate-50/50 to-gray-50/50 dark:from-slate-800/50 dark:to-gray-800/50">
        <div className="flex items-center space-x-3">
          <div className="p-3 bg-slate-100 dark:bg-slate-700 rounded-full">
            <Zap className="h-6 w-6 text-slate-600 dark:text-slate-400" />
          </div>
          <div>
            <h4 className="text-lg font-semibold text-slate-900 dark:text-slate-100">
              工具执行结果
            </h4>
            <div className="flex items-center space-x-3 mt-1">
              <Badge variant="secondary" className="text-xs">
                {toolName}
              </Badge>
              <span className="text-xs text-slate-500 dark:text-slate-400">
                <Clock className="h-3 w-3 inline mr-1" />
                {executionTime}ms
              </span>
            </div>
          </div>
        </div>
      </CardHeader>
      
      <CardContent className="p-6">
        <pre className="text-sm text-slate-600 dark:text-slate-300 bg-slate-50/50 dark:bg-slate-800/50 p-4 rounded-lg overflow-auto">
          {JSON.stringify(result, null, 2)}
        </pre>
      </CardContent>
    </Card>
  );
} 