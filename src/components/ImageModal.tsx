import React from 'react'
import { But<PERSON> } from '@/components/ui/button.tsx'
import { Badge } from '@/components/ui/badge.tsx'
import { LocalImage } from './LocalImage.tsx'
import { 
  Image as ImageIcon, 
  Share2, 
  Download, 
  Calendar,
  MapPin,
  Camera,
  X
} from 'lucide-react'
import { ImageData } from '../data/mockData'

interface ImageModalProps {
  image: ImageData | null
  isOpen: boolean
  onClose: () => void
  onImageClick?: (imageUrl: string, imageData: any) => void
}

export function ImageModal({ image, isOpen, onClose, onImageClick }: ImageModalProps) {
  if (!isOpen || !image) return null

  const handleImageClick = () => {
    if (onImageClick && image) {
      onImageClick(image.url, image)
    }
  }

  return (
    <div className="fixed inset-0 bg-black/40 backdrop-blur-sm flex items-center justify-center z-50 p-6">
      <div className="bg-white/95 dark:bg-slate-900/95 backdrop-blur-md rounded-2xl shadow-2xl border border-slate-200/50 dark:border-slate-700/50 max-w-6xl w-full max-h-[90vh] overflow-hidden flex">
        {/* 左侧：图片展示 */}
        <div className="flex-1 flex items-center justify-center bg-slate-50/50 dark:bg-slate-800/50 p-8 min-w-0">
          <div className="w-full max-w-3xl">
            <div 
              className="bg-slate-200 dark:bg-slate-700 rounded-xl flex items-center justify-center overflow-hidden shadow-lg cursor-pointer"
              onClick={handleImageClick}
            >
              {image.url.startsWith('/api/image/') ? (
                // 本地图片
                <LocalImage
                  relativePath={image.url.replace('/api/image/', '')}
                  alt={image.title}
                  className="w-full h-auto max-h-[70vh] object-contain"
                />
              ) : (
                // 网络图片或占位符
                <img 
                  src={image.url} 
                  alt={image.title}
                  className="w-full h-auto max-h-[70vh] object-contain"
                  onError={(e) => {
                    e.currentTarget.style.display = 'none'
                    e.currentTarget.nextElementSibling?.classList.remove('hidden')
                  }}
                />
              )}
              
              {/* 备用占位符 */}
              <div className="hidden w-full h-64 flex items-center justify-center">
                <ImageIcon className="h-16 w-16 text-slate-400" />
              </div>
            </div>
          </div>
        </div>

        {/* 右侧：详细信息 */}
        <div className="w-96 bg-white/60 dark:bg-slate-900/60 backdrop-blur-sm border-l border-slate-200/50 dark:border-slate-700/50 flex flex-col shrink-0">
          {/* 标题栏 */}
          <div className="flex items-center justify-between p-6 border-b border-slate-200/50 dark:border-slate-700/50">
            <div className="flex items-center space-x-3">
              <div className="w-6 h-6 rounded-full bg-[#007aff]/10 flex items-center justify-center">
                <ImageIcon className="h-3 w-3 text-[#007aff]" />
              </div>
              <h3 className="text-lg font-semibold text-slate-900 dark:text-white">图片详情</h3>
            </div>
            <Button 
              variant="ghost" 
              size="icon"
              onClick={onClose}
              className="h-8 w-8 rounded-full hover:bg-slate-100 dark:hover:bg-slate-800"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>

          {/* 内容区域 */}
          <div className="flex-1 p-6 overflow-y-auto space-y-6">
            {/* 基本信息 */}
            <div className="space-y-3">
              <h4 className="text-base font-medium text-slate-900 dark:text-white">{image.title}</h4>
              <p className="text-sm text-slate-600 dark:text-slate-400 leading-relaxed">
                {image.description}
              </p>
            </div>

            {/* 标签 */}
            <div className="space-y-3">
              <h5 className="text-sm font-medium text-slate-900 dark:text-white">标签</h5>
              <div className="flex flex-wrap gap-2">
                {image.tags?.map((tag, index) => (
                  <Badge 
                    key={index} 
                    variant="secondary"
                    className="text-xs bg-[#007aff]/10 text-[#007aff] border-[#007aff]/20 hover:bg-[#007aff]/20 transition-colors"
                  >
                    {tag}
                  </Badge>
                ))}
              </div>
            </div>

            {/* 基本属性 */}
            <div className="space-y-3">
              <h5 className="text-sm font-medium text-slate-900 dark:text-white">基本信息</h5>
              <div className="space-y-2">
                <div className="flex items-center justify-between text-sm">
                  <div className="flex items-center space-x-2">
                    <Calendar className="h-4 w-4 text-slate-400" />
                    <span className="text-slate-600 dark:text-slate-400">上传时间</span>
                  </div>
                  <span className="text-slate-900 dark:text-white">{image.uploadTime}</span>
                </div>
                
                <div className="flex items-center justify-between text-sm">
                  <div className="flex items-center space-x-2">
                    <ImageIcon className="h-4 w-4 text-slate-400" />
                    <span className="text-slate-600 dark:text-slate-400">文件大小</span>
                  </div>
                  <span className="text-slate-900 dark:text-white">{image.fileSize}</span>
                </div>
                
                <div className="flex items-center justify-between text-sm">
                  <div className="flex items-center space-x-2">
                    <ImageIcon className="h-4 w-4 text-slate-400" />
                    <span className="text-slate-600 dark:text-slate-400">分辨率</span>
                  </div>
                  <span className="text-slate-900 dark:text-white">{image.resolution}</span>
                </div>
                
                {image.camera && (
                  <div className="flex items-center justify-between text-sm">
                    <div className="flex items-center space-x-2">
                      <Camera className="h-4 w-4 text-slate-400" />
                      <span className="text-slate-600 dark:text-slate-400">设备</span>
                    </div>
                    <span className="text-slate-900 dark:text-white">{image.camera}</span>
                  </div>
                )}
                
                {image.location && (
                  <div className="flex items-center justify-between text-sm">
                    <div className="flex items-center space-x-2">
                      <MapPin className="h-4 w-4 text-slate-400" />
                      <span className="text-slate-600 dark:text-slate-400">位置</span>
                    </div>
                    <span className="text-slate-900 dark:text-white">{image.location}</span>
                  </div>
                )}
              </div>
            </div>

            {/* EXIF 信息 */}
            {image.exif && (
              <div className="space-y-3">
                <h5 className="text-sm font-medium text-slate-900 dark:text-white">拍摄参数</h5>
                <div className="grid grid-cols-2 gap-3">
                  <div className="bg-slate-50/50 dark:bg-slate-800/50 rounded-lg p-3">
                    <div className="text-xs text-slate-600 dark:text-slate-400">ISO</div>
                    <div className="text-sm font-medium text-slate-900 dark:text-white">{image.exif.iso}</div>
                  </div>
                  <div className="bg-slate-50/50 dark:bg-slate-800/50 rounded-lg p-3">
                    <div className="text-xs text-slate-600 dark:text-slate-400">光圈</div>
                    <div className="text-sm font-medium text-slate-900 dark:text-white">{image.exif.aperture}</div>
                  </div>
                  <div className="bg-slate-50/50 dark:bg-slate-800/50 rounded-lg p-3">
                    <div className="text-xs text-slate-600 dark:text-slate-400">快门</div>
                    <div className="text-sm font-medium text-slate-900 dark:text-white">{image.exif.shutterSpeed}</div>
                  </div>
                  <div className="bg-slate-50/50 dark:bg-slate-800/50 rounded-lg p-3">
                    <div className="text-xs text-slate-600 dark:text-slate-400">焦距</div>
                    <div className="text-sm font-medium text-slate-900 dark:text-white">{image.exif.focalLength}</div>
                  </div>
                </div>
              </div>
            )}

            {/* 色彩信息 */}
            {image.colors && image.colors.length > 0 && (
              <div className="space-y-3">
                <h5 className="text-sm font-medium text-slate-900 dark:text-white">主色调</h5>
                <div className="flex flex-wrap gap-2">
                  {image.colors.map((color, index) => (
                    <div key={index} className="flex items-center space-x-2">
                      <div className="w-4 h-4 rounded-full bg-slate-300 dark:bg-slate-600 border border-slate-200 dark:border-slate-700"></div>
                      <span className="text-sm text-slate-600 dark:text-slate-400">{color}</span>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* AI 分析状态 */}
            <div className="space-y-3">
              <h5 className="text-sm font-medium text-slate-900 dark:text-white">AI 分析</h5>
              <div className="flex items-center space-x-2">
                {image.aiAnalysis ? (
                  <>
                    <div className="w-2 h-2 rounded-full bg-green-500"></div>
                    <span className="text-sm text-green-600 dark:text-green-400">已完成分析</span>
                  </>
                ) : (
                  <>
                    <div className="w-2 h-2 rounded-full bg-yellow-500"></div>
                    <span className="text-sm text-yellow-600 dark:text-yellow-400">待分析</span>
                  </>
                )}
              </div>
            </div>
          </div>

          {/* 操作按钮 */}
          <div className="p-6 border-t border-slate-200/50 dark:border-slate-700/50">
            <div className="flex space-x-3">
              <Button size="sm" className="flex-1 bg-[#007aff] hover:bg-[#0056cc] text-white">
                <Share2 className="h-4 w-4 mr-2" />
                分享
              </Button>
              <Button size="sm" variant="outline" className="flex-1 bg-white/60 dark:bg-slate-800/60 backdrop-blur-sm">
                <Download className="h-4 w-4 mr-2" />
                下载
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}