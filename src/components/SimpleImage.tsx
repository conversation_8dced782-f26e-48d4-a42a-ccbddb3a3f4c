import { AlertCircle } from 'lucide-react'
import { getImageUrl, generateImagePlaceholder } from '../lib/imageUrlUtils'

interface SimpleImageProps {
  imagePath: string
  alt: string
  className?: string
  fallbackSrc?: string
}

export function SimpleImage({ imagePath, alt, className, fallbackSrc }: SimpleImageProps): JSX.Element {
  // 使用统一的图片URL处理工具，与GalleryPage保持一致
  const fileUrl = getImageUrl(imagePath, fallbackSrc)

  const handleError = (e: React.SyntheticEvent<HTMLImageElement>) => {
    console.error('❌ [SimpleImage] 图片加载失败:', fileUrl)
    console.error('❌ [SimpleImage] 原始路径:', imagePath)

    const target = e.currentTarget
    if (!target.dataset.errorHandled) {
      target.dataset.errorHandled = 'true'
      if (fallbackSrc) {
        console.log('🔄 [SimpleImage] 使用备用图片:', fallbackSrc)
        target.src = fallbackSrc
      } else {
        // 如果没有提供fallbackSrc，使用默认占位符
        const placeholder = generateImagePlaceholder(400, 400)
        console.log('🔄 [SimpleImage] 使用默认占位符')
        target.src = placeholder
        target.alt = '图片加载失败'
      }
    }
  }

  const handleLoad = () => {
    console.log('✅ [SimpleImage] 图片加载成功:', fileUrl)
  }

  return (
    <img 
      src={fileUrl}
      alt={alt}
      className={className}
      onError={handleError}
      onLoad={handleLoad}
    />
  )
}