import React, { useState } from 'react'
import { SearchOptions } from '../lib/galleryService'
import {SearchMetadata} from "../types/search.ts";

interface SearchControlsProps {
  onSearchOptionsChange: (options: SearchOptions) => void
  searchMetadata?: SearchMetadata
  className?: string
  pathModeEnabled?: boolean  // 新增：路径模式支持
  onPathModeChange?: (enabled: boolean) => void  // 新增：路径模式切换回调
}

export const SearchControls: React.FC<SearchControlsProps> = ({
  onSearchOptionsChange,
  searchMetadata,
  className = '',
  pathModeEnabled = true,  // 默认启用路径模式
  onPathModeChange
}) => {
  const [similarityThreshold, setSimilarityThreshold] = useState(0.3)
  const [showAdvanced, setShowAdvanced] = useState(false)

  const handleThresholdChange = (value: number) => {
    setSimilarityThreshold(value)
    onSearchOptionsChange({
      similarityThreshold: value,
      enableSemanticFallback: true
    })
  }

  const getThresholdLabel = (value: number) => {
    if (value <= 0.2) return '宽松匹配'
    if (value <= 0.4) return '标准匹配'
    if (value <= 0.6) return '严格匹配'
    return '精确匹配'
  }

  const getMethodDisplayName = (method: string) => {
    switch (method) {
      case 'enhanced_hybrid':
        return '增强混合搜索'
      case 'semantic_vector':
        return '语义向量搜索'
      case 'tag_based':
        return '标签精确匹配'
      default:
        return '未知搜索方法'
    }
  }

  return (
    <div className={`bg-white rounded-lg border border-gray-200 p-4 ${className}`}>
      {/*/!* 搜索方法信息显示 *!/*/}
      {/*{searchMetadata && (*/}
      {/*  <div className="mb-4 p-3 bg-blue-50 rounded-lg border border-blue-200">*/}
      {/*    <div className="flex items-center justify-between mb-2">*/}
      {/*      <span className="text-sm font-medium text-blue-900">*/}
      {/*        搜索方法: {getMethodDisplayName(searchMetadata.method)}*/}
      {/*      </span>*/}
      {/*      {searchMetadata.processingTime && (*/}
      {/*        <span className="text-xs text-blue-600">*/}
      {/*          {searchMetadata.processingTime}ms*/}
      {/*        </span>*/}
      {/*      )}*/}
      {/*    </div>*/}
      {/*    */}
      {/*    <div className="grid grid-cols-2 gap-4 text-xs text-blue-700">*/}
      {/*      {searchMetadata.similarityThreshold && (*/}
      {/*        <div>*/}
      {/*          <span className="font-medium">相似度阈值:</span>*/}
      {/*          <span className="ml-1">{Math.round(searchMetadata.similarityThreshold * 100)}%</span>*/}
      {/*        </div>*/}
      {/*      )}*/}
      {/*      */}
      {/*      {searchMetadata.totalCandidates && (*/}
      {/*        <div>*/}
      {/*          <span className="font-medium">候选结果:</span>*/}
      {/*          <span className="ml-1">{searchMetadata.totalCandidates}张</span>*/}
      {/*        </div>*/}
      {/*      )}*/}
      {/*      */}
      {/*      {searchMetadata.filteredCount !== undefined && searchMetadata.filteredCount > 0 && (*/}
      {/*        <div>*/}
      {/*          <span className="font-medium">已过滤:</span>*/}
      {/*          <span className="ml-1">{searchMetadata.filteredCount}张</span>*/}
      {/*        </div>*/}
      {/*      )}*/}
      {/*    </div>*/}
      {/*    */}
      {/*    {searchMetadata.method === 'semantic_vector' && (*/}
      {/*      <div className="mt-2 text-xs text-blue-600">*/}
      {/*        使用深度学习向量相似度匹配，基于图像内容语义理解*/}
      {/*      </div>*/}
      {/*    )}*/}
      {/*  </div>*/}
      {/*)}*/}

      {/*/!* 高级搜索控制 *!/*/}
      {/*<div className="space-y-4">*/}
      {/*  <div className="flex items-center justify-between">*/}
      {/*    <h3 className="text-sm font-medium text-gray-900">搜索设置</h3>*/}
      {/*    <button*/}
      {/*      onClick={() => setShowAdvanced(!showAdvanced)}*/}
      {/*      className="text-xs text-blue-600 hover:text-blue-800 transition-colors"*/}
      {/*    >*/}
      {/*      {showAdvanced ? '收起' : '展开'}*/}
      {/*    </button>*/}
      {/*  </div>*/}

      {/*  {showAdvanced && (*/}
      {/*    <div className="space-y-4 pt-2 border-t border-gray-100">*/}
      {/*      /!* 路径模式控制 *!/*/}
      {/*      <div>*/}
      {/*        <div className="flex items-center justify-between mb-2">*/}
      {/*          <label className="text-sm font-medium text-gray-700">*/}
      {/*            图片处理模式*/}
      {/*          </label>*/}
      {/*          <span className="text-xs text-gray-500">*/}
      {/*            {pathModeEnabled ? '路径优化模式' : '传统Base64模式'}*/}
      {/*          </span>*/}
      {/*        </div>*/}
      {/*        */}
      {/*        <div className="flex items-center space-x-3">*/}
      {/*          <label className="flex items-center space-x-2 cursor-pointer">*/}
      {/*            <input*/}
      {/*              type="checkbox"*/}
      {/*              checked={pathModeEnabled}*/}
      {/*              onChange={(e) => onPathModeChange?.(e.target.checked)}*/}
      {/*              className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"*/}
      {/*            />*/}
      {/*            <span className="text-sm text-gray-700">启用路径优化模式</span>*/}
      {/*          </label>*/}
      {/*        </div>*/}
      {/*        */}
      {/*        <div className="flex items-center mt-2">*/}
      {/*          <div className={`w-2 h-2 rounded-full ${pathModeEnabled ? 'bg-green-500' : 'bg-gray-400'} mr-2`}></div>*/}
      {/*          <p className="text-xs text-gray-500">*/}
      {/*            {pathModeEnabled */}
      {/*              ? '路径优化模式已启用：使用文件路径直接访问图片，提供更快的加载速度和更低的内存占用' */}
      {/*              : '传统模式：使用base64编码传输图片，可能导致内存占用较高'}*/}
      {/*          </p>*/}
      {/*        </div>*/}
      {/*        */}
      {/*        {pathModeEnabled && (*/}
      {/*          <div className="mt-2 bg-blue-50 rounded-md p-2">*/}
      {/*            <p className="text-xs text-blue-600">*/}
      {/*              <span className="font-medium">性能提升：</span> 路径模式可减少高达90%的内存占用，并显著提高图片加载速度*/}
      {/*            </p>*/}
      {/*          </div>*/}
      {/*        )}*/}
      {/*      </div>*/}

      {/*      /!* 相似度阈值控制 *!/*/}
      {/*      <div>*/}
      {/*        <div className="flex items-center justify-between mb-2">*/}
      {/*          <label className="text-sm font-medium text-gray-700">*/}
      {/*            相似度阈值*/}
      {/*          </label>*/}
      {/*          <span className="text-xs text-gray-500">*/}
      {/*            {getThresholdLabel(similarityThreshold)} ({Math.round(similarityThreshold * 100)}%)*/}
      {/*          </span>*/}
      {/*        </div>*/}
      {/*        */}
      {/*        <div className="space-y-2">*/}
      {/*          <input*/}
      {/*            type="range"*/}
      {/*            min="0.1"*/}
      {/*            max="0.8"*/}
      {/*            step="0.05"*/}
      {/*            value={similarityThreshold}*/}
      {/*            onChange={(e) => handleThresholdChange(parseFloat(e.target.value))}*/}
      {/*            className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer slider"*/}
      {/*          />*/}
      {/*          */}
      {/*          <div className="flex justify-between text-xs text-gray-400">*/}
      {/*            <span>10%</span>*/}
      {/*            <span>40%</span>*/}
      {/*            <span>80%</span>*/}
      {/*          </div>*/}
      {/*        </div>*/}
      {/*        */}
      {/*        <p className="text-xs text-gray-500 mt-2">*/}
      {/*          阈值越高，搜索结果越精确但数量可能减少；阈值越低，结果更丰富但可能包含不太相关的图片*/}
      {/*        </p>*/}
      {/*      </div>*/}

      {/*      /!* 搜索方法说明 *!/*/}
      {/*      <div className="bg-gray-50 rounded-lg p-3">*/}
      {/*        <h4 className="text-xs font-medium text-gray-700 mb-2">搜索方法说明</h4>*/}
      {/*        <div className="space-y-2 text-xs text-gray-600">*/}
      {/*          <div>*/}
      {/*            <span className="font-medium text-blue-600">增强混合搜索:</span>*/}
      {/*            <span className="ml-1">结合关键词扩展和语义理解的智能搜索</span>*/}
      {/*          </div>*/}
      {/*          <div>*/}
      {/*            <span className="font-medium text-green-600">语义向量搜索:</span>*/}
      {/*            <span className="ml-1">基于深度学习的图像内容语义理解匹配</span>*/}
      {/*          </div>*/}
      {/*          <div>*/}
      {/*            <span className="font-medium text-orange-600">标签精确匹配:</span>*/}
      {/*            <span className="ml-1">基于预定义标签的精确文本匹配</span>*/}
      {/*          </div>*/}
      {/*        </div>*/}
      {/*      </div>*/}
      {/*    </div>*/}
      {/*  )}*/}
      {/*</div>*/}

      {/*<style dangerouslySetInnerHTML={{*/}
      {/*  __html: `*/}
      {/*  .slider::-webkit-slider-thumb {*/}
      {/*    appearance: none;*/}
      {/*    height: 16px;*/}
      {/*    width: 16px;*/}
      {/*    border-radius: 50%;*/}
      {/*    background: #3b82f6;*/}
      {/*    cursor: pointer;*/}
      {/*    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);*/}
      {/*  }*/}
      {/*  */}
      {/*  .slider::-moz-range-thumb {*/}
      {/*    height: 16px;*/}
      {/*    width: 16px;*/}
      {/*    border-radius: 50%;*/}
      {/*    background: #3b82f6;*/}
      {/*    cursor: pointer;*/}
      {/*    border: none;*/}
      {/*    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);*/}
      {/*  }*/}
      {/*  `*/}
      {/*}} />*/}
    </div>
  )
}