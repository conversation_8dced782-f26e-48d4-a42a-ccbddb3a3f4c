import React, { useState, useEffect } from 'react';
import { ImageIcon, AlertCircle } from 'lucide-react';

interface LocalImageProps {
  relativePath: string;
  alt: string;
  className?: string;
  style?: React.CSSProperties;
  onClick?: () => void;
  useAppProtocol?: boolean; // 新增：是否使用app://协议
}

export function LocalImage({ 
  relativePath, 
  alt, 
  className = '', 
  style = {},
  onClick,
  useAppProtocol = false
}: LocalImageProps): JSX.Element {
  const [imageSrc, setImageSrc] = useState<string>('');
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [hasError, setHasError] = useState<boolean>(false);
  const [retryCount, setRetryCount] = useState<number>(0);

  useEffect(() => {
    let fullPath: string;
    
    // 如果路径看起来像Windows绝对路径，使用app://协议
    if (relativePath.includes(':\\') || relativePath.startsWith('/') || useAppProtocol) {
      // 将Windows路径转换为app:// URL格式
      fullPath = `app://${relativePath.replace(/\\/g, '/').replace(/^([A-Za-z]):/, '$1')}`;
      console.log('🔗 [LocalImage] 使用app://协议:', fullPath);
    } else {
      // 使用API路径
      fullPath = `/api/image/${relativePath}`;
      console.log('🔗 [LocalImage] 使用API路径:', fullPath);
    }
    
    setImageSrc(fullPath);
    setIsLoading(true);
    setHasError(false);
  }, [relativePath, useAppProtocol, retryCount]);

  const handleLoad = (): void => {
    console.log('✅ [LocalImage] 图片加载成功:', imageSrc);
    setIsLoading(false);
    setHasError(false);
  };

  const handleError = (): void => {
    console.error('❌ [LocalImage] 图片加载失败:', imageSrc);
    console.error('❌ [LocalImage] 原始路径:', relativePath);
    
    setIsLoading(false);
    
    // 如果是第一次失败且当前使用的是API路径，尝试app://协议
    if (retryCount === 0 && imageSrc.startsWith('/api/image/')) {
      console.log('🔄 [LocalImage] 尝试使用app://协议重试');
      setRetryCount(1);
      return;
    }
    
    // 如果是第一次失败且当前使用的是app://协议，尝试API路径
    if (retryCount === 0 && imageSrc.startsWith('app://')) {
      console.log('🔄 [LocalImage] 尝试使用API路径重试');
      setRetryCount(1);
      return;
    }
    
    // 所有重试都失败了
    setHasError(true);
  };

  const handleRetry = (): void => {
    setRetryCount(prev => prev + 1);
    setHasError(false);
  };

  if (hasError) {
    return (
      <div 
        className={`
          flex flex-col items-center justify-center bg-slate-100 dark:bg-slate-800 
          border-2 border-dashed border-slate-300 dark:border-slate-600 
          rounded-xl p-4 min-h-[120px] ${className}
        `}
        style={style}
      >
        <AlertCircle className="h-8 w-8 text-slate-400 dark:text-slate-500 mb-2" />
        <p className="text-sm text-slate-500 dark:text-slate-400 text-center">
          图片加载失败
        </p>
        <p className="text-xs text-slate-400 dark:text-slate-500 text-center mt-1">
          {alt}
        </p>
        <button
          onClick={handleRetry}
          className="mt-2 px-3 py-1 text-xs bg-slate-200 dark:bg-slate-700 text-slate-600 dark:text-slate-300 rounded hover:bg-slate-300 dark:hover:bg-slate-600 transition-colors"
        >
          重试
        </button>
      </div>
    );
  }

  return (
    <div className="relative">
      {isLoading && (
        <div 
          className={`
            absolute inset-0 flex flex-col items-center justify-center 
            bg-slate-100 dark:bg-slate-800 rounded-xl z-10 ${className}
          `}
          style={style}
        >
          <ImageIcon className="h-8 w-8 text-slate-400 dark:text-slate-500 mb-2 animate-pulse" />
          <p className="text-sm text-slate-500 dark:text-slate-400">
            加载中...
          </p>
        </div>
      )}
      
      <img
        src={imageSrc}
        alt={alt}
        className={`
          transition-all duration-300 ease-in-out
          ${isLoading ? 'opacity-0' : 'opacity-100'}
          ${onClick ? 'cursor-pointer hover:scale-[1.02] hover:shadow-lg' : ''}
          ${className}
        `}
        style={style}
        onLoad={handleLoad}
        onError={handleError}
        onClick={onClick}
        loading="lazy"
      />
    </div>
  );
} 