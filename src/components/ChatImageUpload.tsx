import { useState, useRef } from 'react';
import { Button } from '@/components/ui/button.tsx';
import { Image as ImageIcon, X, Upload } from 'lucide-react';
import { cn } from '@/lib/utils.ts';

interface ChatImageUploadProps {
  onImageSelect: (imageData: { url: string; base64: string; file: File }) => void;
  disabled?: boolean;
  className?: string;
}

export function ChatImageUpload({ onImageSelect, disabled = false, className }: ChatImageUploadProps): JSX.Element {
  const [previewImage, setPreviewImage] = useState<string | null>(null);
  const [isDragOver, setIsDragOver] = useState<boolean>(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileSelect = (file: File): void => {
    if (!file.type.startsWith('image/')) {
      alert('请选择图片文件');
      return;
    }

    const reader = new FileReader();
    reader.onload = (e) => {
      if (!e.target) return;
      const result = e.target.result as string;
      setPreviewImage(result);
      
      onImageSelect({
        url: result,
        base64: result,
        file
      });
    };
    reader.readAsDataURL(file);
  };

  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>): void => {
    const file = e.target.files?.[0];
    if (file) {
      handleFileSelect(file);
    }
  };

  const handleDragOver = (e: React.DragEvent<HTMLDivElement>): void => {
    e.preventDefault();
    setIsDragOver(true);
  };

  const handleDragLeave = (e: React.DragEvent<HTMLDivElement>): void => {
    e.preventDefault();
    setIsDragOver(false);
  };

  const handleDrop = (e: React.DragEvent<HTMLDivElement>): void => {
    e.preventDefault();
    setIsDragOver(false);
    
    const file = e.dataTransfer.files?.[0];
    if (file) {
      handleFileSelect(file);
    }
  };

  const clearPreview = (): void => {
    setPreviewImage(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const openFileDialog = (): void => {
    if (!disabled && fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  return (
    <div className={cn('relative', className)}>
      <input
        ref={fileInputRef}
        type="file"
        accept="image/*"
        onChange={handleFileInputChange}
        className="hidden"
        disabled={disabled}
      />

      {!previewImage ? (
        <div
          className={cn(
            'border-2 border-dashed rounded-lg p-4 text-center cursor-pointer transition-all duration-200',
            isDragOver
              ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
              : 'border-slate-300 dark:border-slate-600 hover:border-blue-400 hover:bg-slate-50 dark:hover:bg-slate-800',
            disabled && 'opacity-50 cursor-not-allowed'
          )}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onDrop={handleDrop}
          onClick={disabled ? undefined : openFileDialog}
        >
          <Upload className="h-8 w-8 mx-auto mb-2 text-slate-400" />
          <p className="text-sm text-slate-600 dark:text-slate-400 mb-1">
            点击上传图片或拖拽到此处
          </p>
          <p className="text-xs text-slate-400">
            支持 JPG、PNG、GIF 格式
          </p>
        </div>
      ) : (
        <div className="relative">
          <img
            src={previewImage}
            alt="预览图片"
            className="max-w-full max-h-48 rounded-lg object-cover mx-auto"
          />
          <Button
            variant="ghost"
            size="icon"
            onClick={clearPreview}
            className="absolute top-2 right-2 h-6 w-6 bg-black/50 hover:bg-black/70 text-white rounded-full"
            disabled={disabled}
          >
            <X className="h-4 w-4" />
          </Button>
        </div>
      )}

      {!previewImage && (
        <Button
          variant="outline"
          size="sm"
          onClick={openFileDialog}
          disabled={disabled}
          className="mt-2 w-full"
        >
          <ImageIcon className="h-4 w-4 mr-2" />
          选择图片
        </Button>
      )}
    </div>
  );
}