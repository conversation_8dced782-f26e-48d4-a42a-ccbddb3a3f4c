// import React, { useState, useRef } from 'react'
// import { Button } from '@/components/ui/button'
// import { Input } from '@/components/ui/input'
// import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
// import { Upload, Search, X, Image as ImageIcon } from 'lucide-react'
// import { toast } from 'sonner'
// import { ImageRecord } from '../types/database'
//
// interface VectorSimilaritySearchProps {
//   onSearchResults?: (results: ImageRecord[]) => void
//   libraryId?: string
// }
//
// interface SearchResult {
//   id: string
//   filePath: string
//   filename: string
//   similarity: number
//   distance?: number
//   metadata?: any
// }
//
// export const VectorSimilaritySearch: React.FC<VectorSimilaritySearchProps> = ({
//   onSearchResults,
//   libraryId
// }) => {
//   const [selectedImage, setSelectedImage] = useState<File | null>(null)
//   const [imagePreview, setImagePreview] = useState<string>('')
//   const [searchResults, setSearchResults] = useState<SearchResult[]>([])
//   const [isSearching, setIsSearching] = useState(false)
//   const [threshold, setThreshold] = useState(0.7)
//   const [limit, setLimit] = useState(20)
//   const fileInputRef = useRef<HTMLInputElement>(null)
//
//   // 处理图片选择
//   const handleImageSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
//     const file = event.target.files?.[0]
//     if (!file) return
//
//     // 验证文件类型
//     if (!file.type.startsWith('image/')) {
//       toast.error('请选择有效的图片文件')
//       return
//     }
//
//     setSelectedImage(file)
//
//     // 创建预览
//     const reader = new FileReader()
//     reader.onload = (e) => {
//       setImagePreview(e.target?.result as string)
//     }
//     reader.readAsDataURL(file)
//   }
//
//   // 清除选择的图片
//   const clearSelectedImage = () => {
//     setSelectedImage(null)
//     setImagePreview('')
//     setSearchResults([])
//     if (fileInputRef.current) {
//       fileInputRef.current.value = ''
//     }
//   }
//
//   // 执行相似度搜索
//   const performSimilaritySearch = async () => {
//     if (!selectedImage) {
//       toast.error('请先选择一张图片')
//       return
//     }
//
//     setIsSearching(true)
//
//     try {
//       // 将图片转换为Base64
//       const base64Data = await new Promise<string>((resolve, reject) => {
//         const reader = new FileReader()
//         reader.onload = () => {
//           const result = reader.result as string
//           // 移除data:image/...;base64,前缀
//           const base64 = result.split(',')[1]
//           resolve(base64)
//         }
//         reader.onerror = reject
//         reader.readAsDataURL(selectedImage)
//       })
//
//       // 调用相似度搜索API
//       if (window.electronAPI?.imageLibrary?.searchSimilarImages) {
//         const result = await window.electronAPI.imageLibrary.searchSimilarImages({
//           imageBase64: base64Data,
//           libraryId,
//           threshold,
//           limit
//         })
//
//         if (result.success && result.results) {
//           const formattedResults: SearchResult[] = result.results.map((item, index) => ({
//             id: `search_${index}`,
//             filePath: item.imagePath,
//             filename: item.imagePath.split('/').pop() || '',
//             similarity: item.similarity,
//             distance: 0,
//             metadata: item.metadata
//           }))
//           setSearchResults(formattedResults)
//           toast.success(`找到 ${result.results.length} 张相似图片`)
//
//           // 如果有回调函数，转换结果格式并调用
//           if (onSearchResults) {
//             const imageRecords: ImageRecord[] = formattedResults.map((item, index) => ({
//               id: `search_${index}`,
//               imagePath: item.filePath,
//               description: '',
//               tags: [],
//               embedding: [],
//               metadata: {
//                 filename: item.filename,
//                 fileSize: 0,
//                 fileChecksum: '',
//                 createdAt: new Date().toISOString(),
//                 modifiedAt: new Date().toISOString(),
//                 importedAt: new Date().toISOString(),
//                 originalPath: item.filePath,
//                 status: 'active' as const,
//                 ...item.metadata
//               },
//               similarity: item.similarity
//             }))
//             onSearchResults(imageRecords)
//           }
//         } else {
//           toast.error(result.error || '搜索失败')
//         }
//       } else {
//         toast.error('相似度搜索功能不可用')
//       }
//     } catch (error) {
//       console.error('相似度搜索失败:', error)
//       toast.error('搜索过程中发生错误')
//     } finally {
//       setIsSearching(false)
//     }
//   }
//
//   return (
//     <Card className="w-full">
//       <CardHeader>
//         <CardTitle className="flex items-center gap-2">
//           <Search className="h-5 w-5" />
//           向量相似度搜索
//         </CardTitle>
//       </CardHeader>
//       <CardContent className="space-y-4">
//         {/* 图片上传区域 */}
//         <div className="space-y-2">
//           <label className="text-sm font-medium">选择参考图片</label>
//           <div className="flex items-center gap-2">
//             <input
//               ref={fileInputRef}
//               type="file"
//               accept="image/*"
//               onChange={handleImageSelect}
//               className="hidden"
//             />
//             <Button
//               variant="outline"
//               onClick={() => fileInputRef.current?.click()}
//               className="flex items-center gap-2"
//             >
//               <Upload className="h-4 w-4" />
//               选择图片
//             </Button>
//             {selectedImage && (
//               <Button
//                 variant="ghost"
//                 size="sm"
//                 onClick={clearSelectedImage}
//                 className="flex items-center gap-1"
//               >
//                 <X className="h-3 w-3" />
//                 清除
//               </Button>
//             )}
//           </div>
//         </div>
//
//         {/* 图片预览 */}
//         {imagePreview && (
//           <div className="space-y-2">
//             <label className="text-sm font-medium">预览</label>
//             <div className="relative w-32 h-32 border rounded-lg overflow-hidden">
//               <img
//                 src={imagePreview}
//                 alt="预览图片"
//                 className="w-full h-full object-cover"
//               />
//             </div>
//           </div>
//         )}
//
//         {/* 搜索参数 */}
//         <div className="grid grid-cols-2 gap-4">
//           <div className="space-y-2">
//             <label className="text-sm font-medium">相似度阈值</label>
//             <Input
//               type="number"
//               min="0"
//               max="1"
//               step="0.1"
//               value={threshold}
//               onChange={(e) => setThreshold(parseFloat(e.target.value))}
//               placeholder="0.7"
//             />
//           </div>
//           <div className="space-y-2">
//             <label className="text-sm font-medium">结果数量</label>
//             <Input
//               type="number"
//               min="1"
//               max="100"
//               value={limit}
//               onChange={(e) => setLimit(parseInt(e.target.value))}
//               placeholder="20"
//             />
//           </div>
//         </div>
//
//         {/* 搜索按钮 */}
//         <Button
//           onClick={performSimilaritySearch}
//           disabled={!selectedImage || isSearching}
//           className="w-full"
//         >
//           {isSearching ? (
//             <>
//               <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
//               搜索中...
//             </>
//           ) : (
//             <>
//               <Search className="h-4 w-4 mr-2" />
//               开始搜索
//             </>
//           )}
//         </Button>
//
//         {/* 搜索结果 */}
//         {searchResults.length > 0 && (
//           <div className="space-y-2">
//             <label className="text-sm font-medium">搜索结果 ({searchResults.length})</label>
//             <div className="max-h-64 overflow-y-auto space-y-2">
//               {searchResults.map((result, index) => (
//                 <div
//                   key={index}
//                   className="flex items-center justify-between p-2 border rounded-lg"
//                 >
//                   <div className="flex items-center gap-2">
//                     <ImageIcon className="h-4 w-4 text-gray-500" />
//                     <span className="text-sm truncate">
//                       {result.filename}
//                     </span>
//                   </div>
//                   <div className="text-sm text-gray-500">
//                     相似度: {(result.similarity * 100).toFixed(1)}%
//                   </div>
//                 </div>
//               ))}
//             </div>
//           </div>
//         )}
//       </CardContent>
//     </Card>
//   )
// }
//
// export default VectorSimilaritySearch