import { useState } from 'react';
import {
  Image as ImageIcon,
  Search,
  Tags,
  Eye,
  ChevronDown,
  ChevronUp,
  AlertCircle,
  CheckCircle, Star
} from 'lucide-react';
import { ToolCallResponse } from '../types/tools';

interface ToolResultDisplayProps {
  toolResult: ToolCallResponse;
  className?: string;
}

export function ToolResultDisplay({ toolResult, className = '' }: ToolResultDisplayProps) {
  
  const { result } = toolResult;
  const toolName = result.metadata?.toolName || 'unknown';

  if (!result.success) {
    return (
      <div className={`bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800/30 rounded-lg p-4 ${className}`}>
        <div className="flex items-center space-x-2">
          <AlertCircle className="h-4 w-4 text-red-500 flex-shrink-0" />
          <div>
            <h4 className="text-sm font-medium text-red-700 dark:text-red-300">
              工具执行失败
            </h4>
            <p className="text-sm text-red-600 dark:text-red-400">
              {result.error || '未知错误'}
            </p>
          </div>
        </div>
      </div>
    );
  }

  switch (toolName) {
    case 'find_similar_images_by_image':
    case 'find_similar_images_by_description':
      return <ImageSearchResults toolResult={toolResult} className={className} />;
    case 'find_images_by_tags':
      return <TagSearchResults toolResult={toolResult} className={className} />;
    case 'get_image_analysis':
      return <ImageAnalysisResults toolResult={toolResult} className={className} />;
    default:
      return <GenericToolResult toolResult={toolResult} className={className} />;
  }
}

interface ImageSearchResultsProps {
  toolResult: ToolCallResponse;
  className?: string;
}

function ImageSearchResults({ toolResult, className = '' }: ImageSearchResultsProps) {
  const [showAll, setShowAll] = useState(false);
  const { result } = toolResult;
  const data = result.data;
  
  if (!data || !data.images) {
    return <GenericToolResult toolResult={toolResult} className={className} />;
  }

  const images = data.images;
  const displayImages = showAll ? images : images.slice(0, 6);
  const hasMore = images.length > 6;

  return (
    <div className={`bg-white/80 dark:bg-slate-700/80 backdrop-blur-sm border border-slate-200/50 dark:border-slate-600/50 rounded-lg p-4 ${className}`}>
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center space-x-2">
          <Search className="h-4 w-4 text-blue-600 dark:text-blue-400" />
          <h4 className="text-sm font-medium text-slate-900 dark:text-slate-100">
            搜索结果
          </h4>
          <span className="text-xs px-2 py-1 bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 rounded-full">
            {data.total || images.length} 张图片
          </span>
        </div>
        <div className="text-xs text-slate-500 dark:text-slate-400">
          {result.metadata?.executionTime}ms
        </div>
      </div>

      <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
        {displayImages.map((image: any, index: number) => (
          <div key={index} className="group relative">
            <div className="aspect-square rounded-lg overflow-hidden bg-slate-100 dark:bg-slate-800">
              <img
                src={image.url || '/api/placeholder/200/200'}
                alt={image.title || '搜索结果'}
                className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-200"
              />
            </div>
            <div className="mt-2 space-y-1">
              <h5 className="text-xs font-medium text-slate-900 dark:text-slate-100 line-clamp-1">
                {image.title || '未命名图片'}
              </h5>
              {image.similarity && (
                <div className="flex items-center space-x-1">
                  <Star className="h-3 w-3 text-yellow-500" />
                  <span className="text-xs text-slate-600 dark:text-slate-400">
                    {(image.similarity * 100).toFixed(1)}%
                  </span>
                </div>
              )}
              {image.description && (
                <p className="text-xs text-slate-500 dark:text-slate-400 line-clamp-2">
                  {image.description}
                </p>
              )}
              {image.tags && image.tags.length > 0 && (
                <div className="flex flex-wrap gap-1">
                  {image.tags.slice(0, 2).map((tag: string, tagIndex: number) => (
                    <span key={tagIndex} className="text-xs px-1 py-0.5 bg-slate-100 dark:bg-slate-800 text-slate-600 dark:text-slate-400 rounded">
                      {tag}
                    </span>
                  ))}
                  {image.tags.length > 2 && (
                    <span className="text-xs text-slate-500 dark:text-slate-400">
                      +{image.tags.length - 2}
                    </span>
                  )}
                </div>
              )}
            </div>
          </div>
        ))}
      </div>

      {hasMore && (
        <div className="mt-4 text-center">
          <button
            onClick={() => setShowAll(!showAll)}
            className="inline-flex items-center space-x-1 text-sm text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300"
          >
            {showAll ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
            <span>{showAll ? '收起' : `显示全部 ${images.length} 张`}</span>
          </button>
        </div>
      )}
    </div>
  );
}

interface TagSearchResultsProps {
  toolResult: ToolCallResponse;
  className?: string;
}

function TagSearchResults({ toolResult, className = '' }: TagSearchResultsProps) {
  return <ImageSearchResults toolResult={toolResult} className={className} />;
}

interface ImageAnalysisResultsProps {
  toolResult: ToolCallResponse;
  className?: string;
}

function ImageAnalysisResults({ toolResult, className = '' }: ImageAnalysisResultsProps) {
  const [isExpanded, setIsExpanded] = useState(false);
  const { result } = toolResult;
  const data = result.data;
  
  if (!data) {
    return <GenericToolResult toolResult={toolResult} className={className} />;
  }

  return (
    <div className={`bg-white/80 dark:bg-slate-700/80 backdrop-blur-sm border border-slate-200/50 dark:border-slate-600/50 rounded-lg p-4 ${className}`}>
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center space-x-2">
          <Eye className="h-4 w-4 text-green-600 dark:text-green-400" />
          <h4 className="text-sm font-medium text-slate-900 dark:text-slate-100">
            图片分析结果
          </h4>
        </div>
        <div className="text-xs text-slate-500 dark:text-slate-400">
          {result.metadata?.executionTime}ms
        </div>
      </div>

      <div className="space-y-4">
        {data.description && (
          <div>
            <h5 className="text-xs font-medium text-slate-700 dark:text-slate-300 mb-2">
              描述
            </h5>
            <p className="text-sm text-slate-600 dark:text-slate-400">
              {data.description}
            </p>
          </div>
        )}

        {data.tags && data.tags.length > 0 && (
          <div>
            <h5 className="text-xs font-medium text-slate-700 dark:text-slate-300 mb-2">
              标签
            </h5>
            <div className="flex flex-wrap gap-1">
              {data.tags.map((tag: string, index: number) => (
                <span key={index} className="inline-flex items-center px-2 py-1 text-xs bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 rounded-full">
                  <Tags className="h-3 w-3 mr-1" />
                  {tag}
                </span>
              ))}
            </div>
          </div>
        )}

        {data.objects && data.objects.length > 0 && (
          <div>
            <div className="flex items-center justify-between">
              <h5 className="text-xs font-medium text-slate-700 dark:text-slate-300 mb-2">
                识别对象
              </h5>
              <button
                onClick={() => setIsExpanded(!isExpanded)}
                className="text-xs text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300"
              >
                {isExpanded ? '收起' : '展开'}
              </button>
            </div>
            <div className="space-y-2">
              {(isExpanded ? data.objects : data.objects.slice(0, 3)).map((obj: any, index: number) => (
                <div key={index} className="flex items-center justify-between p-2 bg-slate-50 dark:bg-slate-800 rounded">
                  <span className="text-sm text-slate-700 dark:text-slate-300">
                    {obj.name}
                  </span>
                  <div className="flex items-center space-x-2">
                    <div className="w-16 bg-slate-200 dark:bg-slate-600 rounded-full h-1">
                      <div 
                        className="bg-green-500 h-1 rounded-full transition-all duration-300"
                        style={{ width: `${(obj.confidence * 100)}%` }}
                      />
                    </div>
                    <span className="text-xs text-slate-500 dark:text-slate-400">
                      {(obj.confidence * 100).toFixed(1)}%
                    </span>
                  </div>
                </div>
              ))}
              {!isExpanded && data.objects.length > 3 && (
                <div className="text-xs text-slate-500 dark:text-slate-400 text-center">
                  还有 {data.objects.length - 3} 个对象
                </div>
              )}
            </div>
          </div>
        )}

        {data.metadata && (
          <div>
            <h5 className="text-xs font-medium text-slate-700 dark:text-slate-300 mb-2">
              技术信息
            </h5>
            <div className="grid grid-cols-2 gap-2 text-xs">
              {data.metadata.resolution && (
                <div className="flex items-center space-x-1">
                  <ImageIcon className="h-3 w-3 text-slate-500" />
                  <span className="text-slate-600 dark:text-slate-400">
                    {data.metadata.resolution}
                  </span>
                </div>
              )}
              {data.metadata.fileSize && (
                <div className="flex items-center space-x-1">
                  <span className="text-slate-600 dark:text-slate-400">
                    {data.metadata.fileSize}
                  </span>
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}

interface GenericToolResultProps {
  toolResult: ToolCallResponse;
  className?: string;
}

function GenericToolResult({ toolResult, className = '' }: GenericToolResultProps) {
  const { result } = toolResult;
  const toolName = result.metadata?.toolName || 'unknown';

  return (
    <div className={`bg-white/80 dark:bg-slate-700/80 backdrop-blur-sm border border-slate-200/50 dark:border-slate-600/50 rounded-lg p-4 ${className}`}>
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center space-x-2">
          <CheckCircle className="h-4 w-4 text-green-600 dark:text-green-400" />
          <h4 className="text-sm font-medium text-slate-900 dark:text-slate-100">
            工具执行成功
          </h4>
        </div>
        <div className="text-xs text-slate-500 dark:text-slate-400">
          {result.metadata?.executionTime}ms
        </div>
      </div>

      <div className="space-y-2">
        <div className="text-sm text-slate-600 dark:text-slate-400">
          工具: {toolName}
        </div>
        {result.data && (
          <div className="p-3 bg-slate-50 dark:bg-slate-800 rounded text-sm font-mono text-slate-700 dark:text-slate-300">
            <pre className="whitespace-pre-wrap">
              {JSON.stringify(result.data, null, 2)}
            </pre>
          </div>
        )}
      </div>
    </div>
  );
}

interface ToolResultsListProps {
  toolResults: ToolCallResponse[];
  className?: string;
}

export function ToolResultsList({ toolResults, className = '' }: ToolResultsListProps) {
  if (toolResults.length === 0) {
    return null;
  }

  return (
    <div className={`space-y-3 ${className}`}>
      {toolResults.map((toolResult, index) => (
        <ToolResultDisplay key={index} toolResult={toolResult} />
      ))}
    </div>
  );
}