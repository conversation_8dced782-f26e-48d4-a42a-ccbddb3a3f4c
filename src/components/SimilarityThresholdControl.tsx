import React, { useEffect, useState } from 'react'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { cn } from '@/lib/utils'
import { 
  SearchMethod,
  SIMILARITY_THRESHOLDS,
  SimilarityThresholdConfig,
  getPrecisionLevel,
  getPrecisionLabel,
  SearchPreferences,
  DEFAULT_SEARCH_PREFERENCES,
  validateThreshold,
  getThresholdGuidance,
  getRecommendedThreshold,
  ThresholdValidation
} from '../types/search'

interface SimilarityThresholdControlProps {
  threshold: number
  onThresholdChange: (threshold: number) => void
  searchMethod: SearchMethod
  disabled?: boolean
  className?: string
  persistPreferences?: boolean
}

// 本地存储键名
const SEARCH_PREFERENCES_KEY = 'enhanced-search-preferences'

// 保存阈值偏好到本地存储
const saveThresholdPreference = (threshold: number): void => {
  try {
    const stored = localStorage.getItem(SEARCH_PREFERENCES_KEY)
    const currentPreferences = stored ? JSON.parse(stored) : DEFAULT_SEARCH_PREFERENCES
    const updatedPreferences: SearchPreferences = {
      ...currentPreferences,
      threshold,
      lastUpdated: new Date().toISOString()
    }
    localStorage.setItem(SEARCH_PREFERENCES_KEY, JSON.stringify(updatedPreferences))
  } catch (error) {
    console.warn('Failed to save threshold preference:', error)
  }
}

// 从本地存储加载阈值偏好
const loadThresholdPreference = (): number => {
  try {
    const stored = localStorage.getItem(SEARCH_PREFERENCES_KEY)
    if (stored) {
      const preferences = JSON.parse(stored) as SearchPreferences
      if (typeof preferences.threshold === 'number' && 
          preferences.threshold >= 0.1 && 
          preferences.threshold <= 1.0) {
        return preferences.threshold
      }
    }
  } catch (error) {
    console.warn('Failed to load threshold preference:', error)
  }
  return DEFAULT_SEARCH_PREFERENCES.threshold
}

export function SimilarityThresholdControl({
  threshold,
  onThresholdChange,
  searchMethod,
  disabled = false,
  className,
  persistPreferences = true
}: SimilarityThresholdControlProps): JSX.Element {
  const [localThreshold, setLocalThreshold] = useState(threshold)
  const [isDragging, setIsDragging] = useState(false)
  const [showValidationWarning, setShowValidationWarning] = useState(false)

  // 组件是否应该显示（只在向量搜索或自动模式下显示）
  const isVisible = searchMethod === 'vector' || searchMethod === 'auto'
  
  // 获取当前阈值的验证结果和指导信息
  const validation = validateThreshold(localThreshold)
  const guidance = getThresholdGuidance(localThreshold)
  const hasWarning = validation.warning || guidance.warning

  // 在组件挂载时加载保存的阈值偏好
  useEffect(() => {
    if (persistPreferences && isVisible) {
      const savedThreshold = loadThresholdPreference()
      if (savedThreshold !== threshold) {
        setLocalThreshold(savedThreshold)
        onThresholdChange(savedThreshold)
      }
    }
  }, [isVisible, persistPreferences, threshold, onThresholdChange])

  // 同步外部threshold变化
  useEffect(() => {
    setLocalThreshold(threshold)
  }, [threshold])

  // 处理阈值变更（带验证和自动修正）
  const handleThresholdChange = (newThreshold: number): void => {
    if (disabled) return
    
    // 验证阈值
    const validation = validateThreshold(newThreshold)
    
    // 如果无效且有自动修正值，使用修正值
    let finalThreshold = newThreshold
    if (!validation.isValid && validation.autoCorrect !== undefined) {
      finalThreshold = validation.autoCorrect
      setShowValidationWarning(true)
      // 3秒后隐藏警告
      setTimeout(() => setShowValidationWarning(false), 3000)
    } else {
      // 确保阈值在有效范围内
      finalThreshold = Math.max(0.1, Math.min(1.0, newThreshold))
    }
    
    setLocalThreshold(finalThreshold)
    onThresholdChange(finalThreshold)
    
    if (persistPreferences) {
      saveThresholdPreference(finalThreshold)
    }
  }

  // 应用推荐阈值
  const applyRecommendedThreshold = (useCase: 'precise' | 'balanced' | 'exploratory'): void => {
    const recommended = getRecommendedThreshold(useCase)
    handleThresholdChange(recommended)
  }

  // 处理滑块输入
  const handleSliderChange = (event: React.ChangeEvent<HTMLInputElement>): void => {
    const newThreshold = parseFloat(event.target.value)
    handleThresholdChange(newThreshold)
  }

  // 处理预设阈值点击
  const handlePresetClick = (preset: SimilarityThresholdConfig): void => {
    handleThresholdChange(preset.value)
  }

  // 获取当前精度级别
  const currentPrecision = getPrecisionLevel(localThreshold)
  const precisionLabel = getPrecisionLabel(localThreshold)

  // 如果不应该显示，返回null
  if (!isVisible) {
    return <></>
  }

  return (
    <Card className={cn("w-full", className)}>
      <CardContent className="p-4">
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300">
              相似度阈值
            </h4>
            <Badge 
              variant={currentPrecision === 'high' ? 'default' : 'secondary'}
              className={cn(
                "text-xs",
                currentPrecision === 'high' && "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200",
                currentPrecision === 'medium' && "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200",
                currentPrecision === 'low' && "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200"
              )}
            >
              {precisionLabel}
            </Badge>
          </div>

          {/* 滑块控件 */}
          <div className="space-y-3">
            <div className="relative">
              <input
                type="range"
                min="0.1"
                max="1.0"
                step="0.05"
                value={localThreshold}
                onChange={handleSliderChange}
                disabled={disabled}
                className={cn(
                  "w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer",
                  "focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50",
                  "disabled:opacity-50 disabled:cursor-not-allowed",
                  // 自定义滑块样式
                  "[&::-webkit-slider-thumb]:appearance-none [&::-webkit-slider-thumb]:h-5 [&::-webkit-slider-thumb]:w-5",
                  "[&::-webkit-slider-thumb]:rounded-full [&::-webkit-slider-thumb]:bg-blue-600",
                  "[&::-webkit-slider-thumb]:cursor-pointer [&::-webkit-slider-thumb]:shadow-md",
                  "[&::-webkit-slider-thumb]:transition-all [&::-webkit-slider-thumb]:hover:bg-blue-700",
                  "[&::-moz-range-thumb]:h-5 [&::-moz-range-thumb]:w-5 [&::-moz-range-thumb]:rounded-full",
                  "[&::-moz-range-thumb]:bg-blue-600 [&::-moz-range-thumb]:cursor-pointer",
                  "[&::-moz-range-thumb]:border-none [&::-moz-range-thumb]:shadow-md",
                  isDragging && "[&::-webkit-slider-thumb]:scale-110 [&::-moz-range-thumb]:scale-110"
                )}
                onMouseDown={() => setIsDragging(true)}
                onMouseUp={() => setIsDragging(false)}
                onTouchStart={() => setIsDragging(true)}
                onTouchEnd={() => setIsDragging(false)}
              />
              
              {/* 阈值刻度标记 */}
              <div className="flex justify-between mt-1 text-xs text-gray-500">
                <span>0.1</span>
                <span>0.3</span>
                <span>0.5</span>
                <span>0.7</span>
                <span>0.9</span>
                <span>1.0</span>
              </div>
            </div>

            {/* 当前值显示 */}
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600 dark:text-gray-400">
                当前值
              </span>
              <span className="font-mono text-sm font-medium text-gray-900 dark:text-gray-100">
                {localThreshold.toFixed(2)}
              </span>
            </div>
          </div>

          {/* 验证警告和自动修正提示 */}
          {(showValidationWarning || !validation.isValid) && (
            <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-3">
              <div className="flex items-start space-x-2">
                <div className="flex-shrink-0">
                  <svg className="h-4 w-4 text-yellow-600 dark:text-yellow-400 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                  </svg>
                </div>
                <div className="flex-1">
                  <h6 className="text-sm font-medium text-yellow-800 dark:text-yellow-200">
                    {!validation.isValid ? '阈值无效' : '阈值已自动调整'}
                  </h6>
                  <p className="text-xs text-yellow-700 dark:text-yellow-300 mt-1">
                    {validation.warning || validation.suggestion}
                  </p>
                </div>
              </div>
            </div>
          )}

          {/* 阈值指导和警告 */}
          {hasWarning && validation.isValid && (
            <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-3">
              <div className="flex items-start space-x-2">
                <div className="flex-shrink-0">
                  <svg className="h-4 w-4 text-blue-600 dark:text-blue-400 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                  </svg>
                </div>
                <div className="flex-1">
                  <h6 className="text-sm font-medium text-blue-800 dark:text-blue-200">
                    {guidance.label}
                  </h6>
                  <p className="text-xs text-blue-700 dark:text-blue-300 mt-1">
                    {guidance.description}
                  </p>
                  {(validation.warning || guidance.warning) && (
                    <p className="text-xs text-blue-600 dark:text-blue-400 mt-1 font-medium">
                      ⚠️ {validation.warning || guidance.warning}
                    </p>
                  )}
                  {validation.suggestion && (
                    <p className="text-xs text-blue-600 dark:text-blue-400 mt-1">
                      💡 {validation.suggestion}
                    </p>
                  )}
                </div>
              </div>
            </div>
          )}

          {/* 推荐阈值按钮 */}
          <div className="space-y-2">
            <h5 className="text-xs font-medium text-gray-600 dark:text-gray-400">
              推荐设置
            </h5>
            <div className="flex flex-wrap gap-2">
              <button
                onClick={() => applyRecommendedThreshold('precise')}
                disabled={disabled}
                className={cn(
                  "px-3 py-1.5 text-xs rounded-md border transition-all",
                  "hover:bg-green-50 dark:hover:bg-green-900/20",
                  "focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-opacity-50",
                  "disabled:opacity-50 disabled:cursor-not-allowed",
                  "border-green-300 text-green-700 dark:border-green-600 dark:text-green-300"
                )}
                title="适合精确查找，只显示高度相关的结果"
              >
                🎯 精确搜索 (0.8)
              </button>
              <button
                onClick={() => applyRecommendedThreshold('balanced')}
                disabled={disabled}
                className={cn(
                  "px-3 py-1.5 text-xs rounded-md border transition-all",
                  "hover:bg-blue-50 dark:hover:bg-blue-900/20",
                  "focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50",
                  "disabled:opacity-50 disabled:cursor-not-allowed",
                  "border-blue-300 text-blue-700 dark:border-blue-600 dark:text-blue-300"
                )}
                title="推荐设置，平衡相关性和结果数量"
              >
                ⚖️ 平衡搜索 (0.6)
              </button>
              <button
                onClick={() => applyRecommendedThreshold('exploratory')}
                disabled={disabled}
                className={cn(
                  "px-3 py-1.5 text-xs rounded-md border transition-all",
                  "hover:bg-purple-50 dark:hover:bg-purple-900/20",
                  "focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-opacity-50",
                  "disabled:opacity-50 disabled:cursor-not-allowed",
                  "border-purple-300 text-purple-700 dark:border-purple-600 dark:text-purple-300"
                )}
                title="适合探索发现，显示更多可能相关的结果"
              >
                🔍 探索搜索 (0.4)
              </button>
            </div>
          </div>

          {/* 预设阈值快捷按钮 */}
          <div className="space-y-2">
            <h5 className="text-xs font-medium text-gray-600 dark:text-gray-400">
              快速设置
            </h5>
            <div className="flex flex-wrap gap-2">
              {SIMILARITY_THRESHOLDS.map((preset) => (
                <button
                  key={preset.value}
                  onClick={() => handlePresetClick(preset)}
                  disabled={disabled}
                  className={cn(
                    "px-3 py-1.5 text-xs rounded-md border transition-all",
                    "hover:bg-gray-50 dark:hover:bg-gray-800",
                    "focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50",
                    "disabled:opacity-50 disabled:cursor-not-allowed",
                    Math.abs(localThreshold - preset.value) < 0.01
                      ? "border-blue-500 bg-blue-50 text-blue-700 dark:bg-blue-950 dark:text-blue-300"
                      : "border-gray-300 text-gray-700 dark:border-gray-600 dark:text-gray-300"
                  )}
                  title={preset.description}
                >
                  {preset.label} ({preset.value})
                </button>
              ))}
            </div>
          </div>

          {/* 帮助文本 */}
          <div className="text-xs text-gray-500 dark:text-gray-400 space-y-1">
            <p>• 高阈值 (0.7+): 只显示高度相关的结果</p>
            <p>• 中阈值 (0.4-0.7): 平衡相关性和结果数量</p>
            <p>• 低阈值 (0.1-0.4): 显示更多可能相关的结果</p>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}