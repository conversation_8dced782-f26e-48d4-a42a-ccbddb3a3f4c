import { useState } from 'react'
import { EnhancedTag } from './ui/enhanced-tag'
import { CreativeTag } from './ui/creative-tag'
import { Palette, Spark<PERSON>, Eye } from 'lucide-react'

export function TagStyleShowcase() {
  const [selectedStyle, setSelectedStyle] = useState<string>('enhanced-default')

  const sampleTags = [
    { name: '自然风景', count: 24, category: 'scene' },
    { name: '美食摄影', count: 18, category: 'object' },
    { name: '蓝色调', count: 32, category: 'color' },
    { name: '跑步运动', count: 15, category: 'action' },
    { name: '复古风格', count: 21, category: 'style' },
    { name: '城市街道', count: 27, category: 'location' }
  ]

  const styleGroups = [
    {
      title: '增强样式',
      styles: [
        { key: 'enhanced-default', name: '默认', component: 'enhanced', variant: 'default' },
        { key: 'enhanced-blur', name: '背景文案', component: 'enhanced', variant: 'blur-text' },
        { key: 'enhanced-gradient', name: '渐变', component: 'enhanced', variant: 'gradient' },
        { key: 'enhanced-pattern', name: '图案', component: 'enhanced', variant: 'pattern' }
      ]
    },
    {
      title: '创意样式',
      styles: [
        { key: 'creative-neon', name: '霓虹灯', component: 'creative', variant: 'neon' },
        { key: 'creative-glass', name: '玻璃拟态', component: 'creative', variant: 'glass' },
        { key: 'creative-paper', name: '纸质卡片', component: 'creative', variant: 'paper' },
        { key: 'creative-holo', name: '全息效果', component: 'creative', variant: 'holographic' },
        { key: 'creative-minimal', name: '极简风格', component: 'creative', variant: 'minimal' }
      ]
    }
  ]

  const renderTag = (tag: any, style: any, index: number) => {
    if (style.component === 'enhanced') {
      return (
        <EnhancedTag
          key={`${style.key}-${index}`}
          name={tag.name}
          count={tag.count}
          category={tag.category}
          variant={style.variant as any}
          selected={index === 0}
        />
      )
    } else {
      return (
        <CreativeTag
          key={`${style.key}-${index}`}
          name={tag.name}
          count={tag.count}
          category={tag.category}
          variant={style.variant as any}
          selected={index === 0}
        />
      )
    }
  }

  return (
    <div className="space-y-8">
      {/* 标题 */}
      <div className="text-center space-y-2">
        <div className="flex items-center justify-center space-x-2">
          <Sparkles className="h-6 w-6 text-[#007aff]" />
          <h2 className="text-2xl font-bold text-slate-900 dark:text-slate-100">
            标签样式展示
          </h2>
        </div>
        <p className="text-slate-600 dark:text-slate-400">
          选择您喜欢的标签样式，让标签更加生动有趣
        </p>
      </div>

      {/* 样式选择器 */}
      <div className="bg-white/60 dark:bg-slate-800/60 backdrop-blur-sm border border-slate-200/50 dark:border-slate-700/50 rounded-2xl p-6">
        <div className="flex items-center space-x-2 mb-4">
          <Palette className="h-5 w-5 text-[#007aff]" />
          <h3 className="text-lg font-semibold text-slate-900 dark:text-slate-100">
            样式选择
          </h3>
        </div>
        
        <div className="space-y-6">
          {styleGroups.map((group) => (
            <div key={group.title}>
              <h4 className="text-sm font-medium text-slate-700 dark:text-slate-300 mb-3">
                {group.title}
              </h4>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                {group.styles.map((style) => (
                  <button
                    key={style.key}
                    onClick={() => setSelectedStyle(style.key)}
                    className={`p-3 rounded-lg text-sm font-medium transition-all duration-200 ${
                      selectedStyle === style.key
                        ? 'bg-[#007aff] text-white shadow-md'
                        : 'bg-white/40 dark:bg-slate-700/40 text-slate-700 dark:text-slate-300 hover:bg-white/60 dark:hover:bg-slate-700/60'
                    }`}
                  >
                    {style.name}
                  </button>
                ))}
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* 样式预览 */}
      <div className="bg-white/60 dark:bg-slate-800/60 backdrop-blur-sm border border-slate-200/50 dark:border-slate-700/50 rounded-2xl p-6">
        <div className="flex items-center space-x-2 mb-6">
          <Eye className="h-5 w-5 text-[#007aff]" />
          <h3 className="text-lg font-semibold text-slate-900 dark:text-slate-100">
            样式预览
          </h3>
          <span className="px-2 py-1 text-xs bg-[#007aff]/10 text-[#007aff] rounded-md">
            {styleGroups.flatMap(g => g.styles).find((s: any) => s.key === selectedStyle)?.name}
          </span>
        </div>

        {/* 特殊背景处理 */}
        <div className={`p-6 rounded-xl ${
          selectedStyle.includes('neon') 
            ? 'bg-slate-900' 
            : selectedStyle.includes('glass')
            ? 'bg-gradient-to-br from-blue-400/20 to-purple-400/20'
            : 'bg-slate-50 dark:bg-slate-800/50'
        }`}>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {sampleTags.map((tag, index) => {
              const currentStyle = styleGroups
                .flatMap(g => g.styles)
                .find((s: any) => s.key === selectedStyle)
              
              if (!currentStyle) return null
              
              return renderTag(tag, currentStyle, index)
            })}
          </div>
        </div>
      </div>

      {/* 使用说明 */}
      <div className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-slate-800 dark:to-slate-700 border border-blue-200/50 dark:border-slate-600/50 rounded-2xl p-6">
        <h3 className="text-lg font-semibold text-slate-900 dark:text-slate-100 mb-3">
          样式说明
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-slate-600 dark:text-slate-400">
          <div>
            <h4 className="font-medium text-slate-800 dark:text-slate-200 mb-2">增强样式</h4>
            <ul className="space-y-1">
              <li>• <strong>默认</strong>: 简洁的卡片样式，适合日常使用</li>
              <li>• <strong>背景文案</strong>: 背景显示分类文字，增强视觉层次</li>
              <li>• <strong>渐变</strong>: 渐变色背景，现代感十足</li>
              <li>• <strong>图案</strong>: 点状纹理背景，增加质感</li>
            </ul>
          </div>
          <div>
            <h4 className="font-medium text-slate-800 dark:text-slate-200 mb-2">创意样式</h4>
            <ul className="space-y-1">
              <li>• <strong>霓虹灯</strong>: 发光效果，适合深色主题</li>
              <li>• <strong>玻璃拟态</strong>: 毛玻璃效果，现代时尚</li>
              <li>• <strong>纸质卡片</strong>: 纸质质感，温馨自然</li>
              <li>• <strong>全息效果</strong>: 彩虹渐变，科技感强</li>
              <li>• <strong>极简风格</strong>: 最简设计，专注内容</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  )
}