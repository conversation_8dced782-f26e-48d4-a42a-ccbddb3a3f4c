import React from 'react';
import { Brain, MessageCircle, Sparkles } from 'lucide-react';
import { ToolResultsList } from './ToolResultDisplay';
import { EnhancedToolResultDisplay } from './EnhancedToolResultDisplay';
import { EnhancedAIToolResultDisplay } from './EnhancedAIToolResultDisplay';
import { LocalImage } from './LocalImage';
import { ToolCallResponse } from '../types/tools';
import { SimpleImage } from './SimpleImage';
import { ReActStepDisplay, ReActCurrentStep, ReActStep } from './ReActStepDisplay';

interface ImageData {
  url: string;
  base64?: string;
  description?: string;
}

interface Message {
  id: number;
  type: 'user' | 'assistant';
  content: string;
  timestamp: Date;
  image?: ImageData;
  isStreaming?: boolean; // 新增：标识是否为流式消息
  isError?: boolean; // 新增：标识是否为错误消息
  reactSteps?: ReActStep[]; // 新增：ReAct步骤
  currentReActStep?: ReActStep; // 新增：当前正在执行的ReAct步骤
  toolResults?: Array<{
    toolCallId: string;
    result: {
      success: boolean;
      data?: any;
      error?: string;
      metadata?: {
        executionTime?: number;
        toolName?: string;
        parameters?: any;
      };
    };
  }>;
}

interface ChatMessageProps {
  message: Message;
  className?: string;
  onImageClick?: (imageUrl: string, imageData: ImageData) => void;
  onRefineSearch?: (parameters: Record<string, unknown>) => void;
  onSaveResult?: (result: Record<string, unknown>) => void;
  useEnhancedDisplay?: boolean;
}

export function ChatMessageDisplay({ 
  message, 
  className = '',
  onImageClick,
  onRefineSearch,
  onSaveResult,
  useEnhancedDisplay = true
}: ChatMessageProps): JSX.Element {
  const isUser = message.type === 'user';
  const hasToolResults = message.toolResults && message.toolResults.length > 0;

  const handleImageClick = (imageUrl: string, imageData: ImageData): void => {
    onImageClick?.(imageUrl, imageData);
  };

  return (
    <div className={`flex items-start space-x-3 ${isUser ? 'justify-end' : ''} ${className}`}>
      {!isUser && (
        <div className={`p-2 rounded-full shadow-md flex-shrink-0 ${
          message.isError ? 'bg-red-500' : 'bg-[#007aff]'
        }`}>
          <Brain className="h-4 w-4 text-white" />
        </div>
      )}
      
      <div className={`flex-1 ${isUser ? 'flex justify-end' : ''}`}>
        <div className={`space-y-3 ${
          isUser 
            ? 'max-w-[85%] sm:max-w-[75%] lg:max-w-[65%] xl:max-w-[600px]' 
            : 'max-w-[90%] sm:max-w-[80%] lg:max-w-[70%] xl:max-w-[700px]'
        }`}>
          <div className={`
            rounded-2xl p-4 transition-all duration-200 hover:shadow-lg
            ${isUser
              ? 'bg-[#007aff] text-white shadow-md'
              : message.isError
                ? 'bg-red-50/80 dark:bg-red-900/20 border-red-200/50 dark:border-red-800/50'
                : 'bg-white/80 dark:bg-slate-700/80 backdrop-blur-sm border border-slate-200/50 dark:border-slate-600/50'
            }
          `}>
            {/* 消息头部 */}
            {!isUser && (
              <div className="flex items-center space-x-2 mb-3">
                <span className={`text-sm font-medium ${
                  message.isError ? 'text-red-600 dark:text-red-400' : 'text-[#007aff]'
                }`}>
                  {message.isError ? 'Error' : 'AI Assistant'}
                </span>
                {/* 流式指示器 */}
                {message.isStreaming && (
                  <div className="flex items-center space-x-1">
                    <div className="flex space-x-1">
                      <div className="w-1 h-1 bg-[#007aff] rounded-full animate-pulse"></div>
                      <div className="w-1 h-1 bg-[#007aff] rounded-full animate-pulse" style={{ animationDelay: '0.2s' }}></div>
                      <div className="w-1 h-1 bg-[#007aff] rounded-full animate-pulse" style={{ animationDelay: '0.4s' }}></div>
                    </div>
                    <span className="text-xs text-[#007aff]">正在输入...</span>
                  </div>
                )}
              </div>
            )}

            {message.image && (
              <div className="mb-3">
                <SimpleImage
                    imagePath={message.image.url}
                    alt={message.image.description || '用户上传的图片'}
                    className='w-full rounded-xl object-cover cursor-pointer hover:opacity-90 transition-opacity'
                  />
              </div>
            )}
            
            {message.content && (
              <div className={`text-sm leading-relaxed ${
                message.isError 
                  ? 'text-red-700 dark:text-red-300'
                  : isUser
                    ? 'text-white'
                    : 'text-slate-700 dark:text-slate-300'
              }`}>
                {message.content}
                {/* 流式光标 */}
                {message.isStreaming && !isUser && (
                  <span className="inline-block w-0.5 h-4 bg-[#007aff] ml-1 animate-pulse"></span>
                )}
              </div>
            )}
            
            <div className="flex items-center justify-between text-xs opacity-70 mt-2">
              <span>{message.timestamp.toLocaleTimeString()}</span>
              {!isUser && !message.isError && (
                <div className="flex items-center space-x-1">
                  {message.isStreaming ? (
                    <>
                      <div className="w-2 h-2 bg-yellow-500 rounded-full animate-pulse"></div>
                      <span>正在输入</span>
                    </>
                  ) : (
                    <>
                      <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                      <span>已发送</span>
                    </>
                  )}
                </div>
              )}
            </div>
          </div>

          {/* ReAct步骤显示 */}
          {!isUser && (message.reactSteps || message.currentReActStep) && (
            <div className="mt-4">
              {message.currentReActStep && (
                <div className="mb-3">
                  <ReActCurrentStep step={message.currentReActStep} />
                </div>
              )}
              {message.reactSteps && message.reactSteps.length > 0 && (
                <ReActStepDisplay 
                  steps={message.reactSteps} 
                  compact={message.reactSteps.length > 3}
                />
              )}
            </div>
          )}

          {/* 工具结果显示 - 仅在没有ReAct步骤时显示，或者作为补充信息 */}
          {hasToolResults && !isUser && !(message.reactSteps && message.reactSteps.length > 0) && (
            <div className="space-y-4">
              <div className="flex items-center space-x-2 px-3 py-2 bg-blue-50/80 dark:bg-blue-900/20 rounded-lg border border-blue-200/50 dark:border-blue-700/50">
                <Sparkles className="h-4 w-4 text-blue-600 dark:text-blue-400" />
                <span className="text-sm font-medium text-blue-700 dark:text-blue-300">
                  AI工具执行结果
                </span>
                <div className="flex-1"></div>
                <span className="text-xs text-blue-600/70 dark:text-blue-400/70 bg-blue-100/50 dark:bg-blue-800/30 px-2 py-1 rounded-full">
                  {message?.toolResults?.length || 0} 个结果
                </span>
              </div>
              {useEnhancedDisplay ? (
                <div className="space-y-4">
                  {message?.toolResults?.map((toolResult, index) => (
                    <EnhancedAIToolResultDisplay
                      key={index}
                      toolResult={toolResult as any}
                      onImageClick={onImageClick}
                      onRefineSearch={onRefineSearch}
                      onSaveResult={onSaveResult}
                      showMetrics={true}
                      showQuickActions={true}
                      compact={false}
                    />
                  ))}
                </div>
              ) : (
                <div className="pl-2 border-l-2 border-blue-200/50 dark:border-blue-700/50">
                  <ToolResultsList toolResults={message.toolResults as any || []} />
                </div>
              )}
            </div>
          )}
        </div>
      </div>

      {isUser && (
        <div className="p-2 bg-slate-200 dark:bg-slate-600 rounded-full shadow-md flex-shrink-0">
          <MessageCircle className="h-4 w-4 text-slate-600 dark:text-slate-300" />
        </div>
      )}
    </div>
  );
}

interface ChatMessagesListProps {
  messages: Message[];
  className?: string;
  onImageClick?: (imageUrl: string, imageData: ImageData) => void;
  onRefineSearch?: (parameters: Record<string, unknown>) => void;
  onSaveResult?: (result: Record<string, unknown>) => void;
  useEnhancedDisplay?: boolean;
}

export function ChatMessagesList({ 
  messages, 
  className = '',
  onImageClick,
  onRefineSearch,
  onSaveResult,
  useEnhancedDisplay = true
}: ChatMessagesListProps): JSX.Element | null {
  if (messages.length === 0) {
    return null;
  }

  return (
    <div className={`space-y-4 ${className}`}>
      {messages.map((message) => (
        <ChatMessageDisplay 
          key={message.id} 
          message={message}
          onImageClick={onImageClick}
          onRefineSearch={onRefineSearch}
          onSaveResult={onSaveResult}
          useEnhancedDisplay={useEnhancedDisplay}
        />
      ))}
    </div>
  );
}