import { useState } from 'react'
import { MapPin, Clock, Calendar, Search, Loader2, Globe } from 'lucide-react'
import { type ImageData } from '@/data/mockData'

interface LocationTimeSearchProps {
  onImageSelect: (image: ImageData) => void
}

interface LocationSearchParams {
  latitude?: number
  longitude?: number
  radius?: number
  city?: string
  country?: string
}

interface TimeSearchParams {
  startDate?: string
  endDate?: string
  year?: number
  month?: number
  day?: number
}

export function LocationTimeSearch({ onImageSelect }: LocationTimeSearchProps): JSX.Element {
  const [searchType, setSearchType] = useState<'location' | 'time' | 'combined'>('location')
  const [isSearching, setIsSearching] = useState(false)
  const [searchResults, setSearchResults] = useState<ImageData[]>([])
  
  // 位置搜索参数
  const [locationParams, setLocationParams] = useState<LocationSearchParams>({})
  
  // 时间搜索参数
  const [timeParams, setTimeParams] = useState<TimeSearchParams>({})

  // 执行位置搜索
  const handleLocationSearch = async () => {
    if (!locationParams.latitude && !locationParams.city) {
      alert('请输入经纬度坐标或城市名称')
      return
    }

    setIsSearching(true)
    try {
      let results: ImageData[] = []
      
      if (locationParams.latitude && locationParams.longitude) {
        // 按坐标搜索
        const response = await window.electronAPI.database.queryImagesByLocation({
          latitude: locationParams.latitude,
          longitude: locationParams.longitude,
          radius: locationParams.radius || 10000, // 默认10km半径
          limit: 50
        })
        if (response.results) {
          results = response.results.map((item: any): ImageData => ({
            id: item.id,
            url: item.imagePath || item.filePath,
            title: item.filename || item.title || '未命名图片',
            description: item.description || '',
            tags: item.tags || [],
            uploadTime: item.capturedAt || item.createdAt || new Date().toISOString(),
            location: [item.locationCity, item.locationCountry].filter(Boolean).join(', ') || '未知位置',
            camera: item.metadata?.camera || item.camera || '未知设备',
            colors: item.metadata?.colors || [],
            aiAnalysis: Boolean(item.metadata?.aiAnalysis),
            similarity: item.metadata?.similarity || 0,
            fileSize: item.metadata?.fileSize || item.fileSize || '未知大小',
            resolution: item.metadata?.resolution || '未知分辨率',
            exif: {
              iso: item.metadata?.exif?.iso || item.exif?.iso || 0,
              aperture: item.metadata?.exif?.aperture || item.exif?.aperture || 'f/0',
              shutterSpeed: item.metadata?.exif?.shutterSpeed || item.exif?.shutterSpeed || '1/0s',
              focalLength: item.metadata?.exif?.focalLength || item.exif?.focalLength || '0mm'
            }
          }))
        }
      } else if (locationParams.city || locationParams.country) {
        // 按城市/国家搜索
        const locationName = [locationParams.city, locationParams.country].filter(Boolean).join(', ')
        const response = await window.electronAPI.database.queryImagesByLocationName({
          locationName,
          limit: 50
        })
        if (response.results) {
          results = response.results.map((item: any): ImageData => ({
            id: item.id,
            url: item.imagePath || item.filePath,
            title: item.filename || item.title || '未命名图片',
            description: item.description || '',
            tags: item.tags || [],
            uploadTime: item.capturedAt || item.createdAt || new Date().toISOString(),
            location: [item.locationCity, item.locationCountry].filter(Boolean).join(', ') || '未知位置',
            camera: item.metadata?.camera || item.camera || '未知设备',
            colors: item.metadata?.colors || [],
            aiAnalysis: Boolean(item.metadata?.aiAnalysis),
            similarity: item.metadata?.similarity || 0,
            fileSize: item.metadata?.fileSize || item.fileSize || '未知大小',
            resolution: item.metadata?.resolution || '未知分辨率',
            exif: {
              iso: item.metadata?.exif?.iso || item.exif?.iso || 0,
              aperture: item.metadata?.exif?.aperture || item.exif?.aperture || 'f/0',
              shutterSpeed: item.metadata?.exif?.shutterSpeed || item.exif?.shutterSpeed || '1/0s',
              focalLength: item.metadata?.exif?.focalLength || item.exif?.focalLength || '0mm'
            }
          }))
        }
      }
      
      setSearchResults(results)
    } catch (error) {
      console.error('位置搜索失败:', error)
      alert('位置搜索失败，请检查输入参数')
    } finally {
      setIsSearching(false)
    }
  }

  // 执行时间搜索
  const handleTimeSearch = async () => {
    if (!timeParams.startDate && !timeParams.year) {
      alert('请选择时间范围或年份')
      return
    }

    setIsSearching(true)
    try {
      let results: ImageData[] = []
      
      if (timeParams.startDate && timeParams.endDate) {
        // 按时间范围搜索
        const response = await window.electronAPI.database.queryImagesByTimeRange({
          startTime: timeParams.startDate,
          endTime: timeParams.endDate,
          limit: 50
        })
        if (response.results) {
          results = response.results.map((item: any): ImageData => ({
            id: item.id,
            url: item.imagePath || item.filePath,
            title: item.filename || item.title || '未命名图片',
            description: item.description || '',
            tags: item.tags || [],
            uploadTime: item.capturedAt || item.createdAt || new Date().toISOString(),
            location: [item.locationCity, item.locationCountry].filter(Boolean).join(', ') || '未知位置',
            camera: item.metadata?.camera || item.camera || '未知设备',
            colors: item.metadata?.colors || [],
            aiAnalysis: Boolean(item.metadata?.aiAnalysis),
            similarity: item.metadata?.similarity || 0,
            fileSize: item.metadata?.fileSize || item.fileSize || '未知大小',
            resolution: item.metadata?.resolution || '未知分辨率',
            exif: {
              iso: item.metadata?.exif?.iso || item.exif?.iso || 0,
              aperture: item.metadata?.exif?.aperture || item.exif?.aperture || 'f/0',
              shutterSpeed: item.metadata?.exif?.shutterSpeed || item.exif?.shutterSpeed || '1/0s',
              focalLength: item.metadata?.exif?.focalLength || item.exif?.focalLength || '0mm'
            }
          }))
        }
      } else if (timeParams.year && timeParams.month && timeParams.day) {
        // 按具体日期搜索
        const dateStr = `${timeParams.year}-${String(timeParams.month).padStart(2, '0')}-${String(timeParams.day).padStart(2, '0')}`
        const response = await window.electronAPI.database.queryImagesByDate({
          date: dateStr,
          limit: 50
        })
        if (response.results) {
          results = response.results.map((item: any): ImageData => ({
            id: item.id,
            url: item.imagePath || item.filePath,
            title: item.filename || item.title || '未命名图片',
            description: item.description || '',
            tags: item.tags || [],
            uploadTime: item.capturedAt || item.createdAt || new Date().toISOString(),
            location: [item.locationCity, item.locationCountry].filter(Boolean).join(', ') || '未知位置',
            camera: item.metadata?.camera || item.camera || '未知设备',
            colors: item.metadata?.colors || [],
            aiAnalysis: Boolean(item.metadata?.aiAnalysis),
            similarity: item.metadata?.similarity || 0,
            fileSize: item.metadata?.fileSize || item.fileSize || '未知大小',
            resolution: item.metadata?.resolution || '未知分辨率',
            exif: {
              iso: item.metadata?.exif?.iso || item.exif?.iso || 0,
              aperture: item.metadata?.exif?.aperture || item.exif?.aperture || 'f/0',
              shutterSpeed: item.metadata?.exif?.shutterSpeed || item.exif?.shutterSpeed || '1/0s',
              focalLength: item.metadata?.exif?.focalLength || item.exif?.focalLength || '0mm'
            }
          }))
        }
      }
      
      setSearchResults(results)
    } catch (error) {
      console.error('时间搜索失败:', error)
      alert('时间搜索失败，请检查输入参数')
    } finally {
      setIsSearching(false)
    }
  }

  // 执行组合搜索
  const handleCombinedSearch = async () => {
    // 这里可以实现位置和时间的组合搜索逻辑
    alert('组合搜索功能正在开发中')
  }

  const handleSearch = () => {
    switch (searchType) {
      case 'location':
        handleLocationSearch()
        break
      case 'time':
        handleTimeSearch()
        break
      case 'combined':
        handleCombinedSearch()
        break
    }
  }

  return (
    <div className="space-y-6">
      {/* 搜索类型选择 */}
      <div className="flex space-x-4">
        <button
          onClick={() => setSearchType('location')}
          className={`flex items-center px-4 py-2 rounded-lg transition-colors ${
            searchType === 'location'
              ? 'bg-blue-500 text-white'
              : 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600'
          }`}
        >
          <MapPin className="h-4 w-4 mr-2" />
          位置搜索
        </button>
        <button
          onClick={() => setSearchType('time')}
          className={`flex items-center px-4 py-2 rounded-lg transition-colors ${
            searchType === 'time'
              ? 'bg-blue-500 text-white'
              : 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600'
          }`}
        >
          <Clock className="h-4 w-4 mr-2" />
          时间搜索
        </button>
        <button
          onClick={() => setSearchType('combined')}
          className={`flex items-center px-4 py-2 rounded-lg transition-colors ${
            searchType === 'combined'
              ? 'bg-blue-500 text-white'
              : 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600'
          }`}
        >
          <Globe className="h-4 w-4 mr-2" />
          组合搜索
        </button>
      </div>

      {/* 位置搜索表单 */}
      {searchType === 'location' && (
        <div className="bg-white/60 dark:bg-slate-800/60 backdrop-blur-sm border border-slate-200/50 dark:border-slate-700/50 rounded-2xl p-6">
          <h3 className="text-lg font-semibold mb-4 flex items-center">
            <MapPin className="h-5 w-5 mr-2 text-blue-500" />
            位置搜索
          </h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium mb-2">经度</label>
              <input
                type="number"
                step="any"
                placeholder="例: 116.4074"
                value={locationParams.latitude || ''}
                onChange={(e) => setLocationParams(prev => ({
                  ...prev,
                  latitude: e.target.value ? parseFloat(e.target.value) : undefined
                }))}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium mb-2">纬度</label>
              <input
                type="number"
                step="any"
                placeholder="例: 39.9042"
                value={locationParams.longitude || ''}
                onChange={(e) => setLocationParams(prev => ({
                  ...prev,
                  longitude: e.target.value ? parseFloat(e.target.value) : undefined
                }))}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium mb-2">搜索半径 (米)</label>
              <input
                type="number"
                placeholder="例: 10000"
                value={locationParams.radius || ''}
                onChange={(e) => setLocationParams(prev => ({
                  ...prev,
                  radius: e.target.value ? parseInt(e.target.value) : undefined
                }))}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100"
              />
            </div>
            
            <div className="md:col-span-2 border-t pt-4">
              <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">或者按城市/国家搜索：</p>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium mb-2">城市</label>
                  <input
                    type="text"
                    placeholder="例: 北京"
                    value={locationParams.city || ''}
                    onChange={(e) => setLocationParams(prev => ({
                      ...prev,
                      city: e.target.value
                    }))}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium mb-2">国家</label>
                  <input
                    type="text"
                    placeholder="例: 中国"
                    value={locationParams.country || ''}
                    onChange={(e) => setLocationParams(prev => ({
                      ...prev,
                      country: e.target.value
                    }))}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* 时间搜索表单 */}
      {searchType === 'time' && (
        <div className="bg-white/60 dark:bg-slate-800/60 backdrop-blur-sm border border-slate-200/50 dark:border-slate-700/50 rounded-2xl p-6">
          <h3 className="text-lg font-semibold mb-4 flex items-center">
            <Clock className="h-5 w-5 mr-2 text-green-500" />
            时间搜索
          </h3>
          
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium mb-2">开始日期</label>
                <input
                  type="date"
                  value={timeParams.startDate || ''}
                  onChange={(e) => setTimeParams(prev => ({
                    ...prev,
                    startDate: e.target.value
                  }))}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium mb-2">结束日期</label>
                <input
                  type="date"
                  value={timeParams.endDate || ''}
                  onChange={(e) => setTimeParams(prev => ({
                    ...prev,
                    endDate: e.target.value
                  }))}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100"
                />
              </div>
            </div>
            
            <div className="border-t pt-4">
              <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">或者按具体日期搜索：</p>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label className="block text-sm font-medium mb-2">年份</label>
                  <input
                    type="number"
                    placeholder="例: 2024"
                    value={timeParams.year || ''}
                    onChange={(e) => setTimeParams(prev => ({
                      ...prev,
                      year: e.target.value ? parseInt(e.target.value) : undefined
                    }))}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium mb-2">月份</label>
                  <input
                    type="number"
                    min="1"
                    max="12"
                    placeholder="例: 12"
                    value={timeParams.month || ''}
                    onChange={(e) => setTimeParams(prev => ({
                      ...prev,
                      month: e.target.value ? parseInt(e.target.value) : undefined
                    }))}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium mb-2">日期</label>
                  <input
                    type="number"
                    min="1"
                    max="31"
                    placeholder="例: 25"
                    value={timeParams.day || ''}
                    onChange={(e) => setTimeParams(prev => ({
                      ...prev,
                      day: e.target.value ? parseInt(e.target.value) : undefined
                    }))}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* 组合搜索表单 */}
      {searchType === 'combined' && (
        <div className="bg-white/60 dark:bg-slate-800/60 backdrop-blur-sm border border-slate-200/50 dark:border-slate-700/50 rounded-2xl p-6">
          <h3 className="text-lg font-semibold mb-4 flex items-center">
            <Globe className="h-5 w-5 mr-2 text-purple-500" />
            组合搜索
          </h3>
          <p className="text-gray-600 dark:text-gray-400">
            组合搜索功能正在开发中，敬请期待...
          </p>
        </div>
      )}

      {/* 搜索按钮 */}
      <div className="flex justify-center">
        <button
          onClick={handleSearch}
          disabled={isSearching}
          className="flex items-center px-6 py-3 bg-blue-500 hover:bg-blue-600 text-white rounded-lg font-medium transition-colors disabled:opacity-50"
        >
          {isSearching ? (
            <>
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              搜索中...
            </>
          ) : (
            <>
              <Search className="h-4 w-4 mr-2" />
              开始搜索
            </>
          )}
        </button>
      </div>

      {/* 搜索结果 */}
      {searchResults.length > 0 && (
        <div className="bg-white/60 dark:bg-slate-800/60 backdrop-blur-sm border border-slate-200/50 dark:border-slate-700/50 rounded-2xl p-6">
          <h3 className="text-lg font-semibold mb-4">搜索结果 ({searchResults.length})</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {searchResults.map((image) => (
              <div
                key={image.id}
                onClick={() => onImageSelect(image)}
                className="cursor-pointer group relative overflow-hidden rounded-lg border border-gray-200 dark:border-gray-700 hover:shadow-lg transition-all"
              >
                <img
                  src={image.url}
                  alt={image.description}
                  className="w-full h-48 object-cover group-hover:scale-105 transition-transform"
                />
                <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-colors" />
                <div className="p-3">
                  <h4 className="font-medium text-sm truncate">{image.title}</h4>
                  {image.location && (
                    <p className="text-xs text-gray-600 dark:text-gray-400 flex items-center mt-1">
                      <MapPin className="h-3 w-3 mr-1" />
                      {image.location}
                    </p>
                  )}
                  {image.uploadTime && (
                    <p className="text-xs text-gray-600 dark:text-gray-400 flex items-center mt-1">
                      <Calendar className="h-3 w-3 mr-1" />
                      {new Date(image.uploadTime).toLocaleDateString()}
                    </p>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  )
}