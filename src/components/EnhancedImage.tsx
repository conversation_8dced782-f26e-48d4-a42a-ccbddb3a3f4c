import { useState, useRef, useEffect } from 'react'
import { ImageIcon, AlertCircle } from 'lucide-react'

interface EnhancedImageProps {
  src: string
  alt: string
  className?: string
  style?: React.CSSProperties
  onClick?: (url: string, alt: string) => void
  lazy?: boolean
  placeholder?: React.ReactNode
  errorPlaceholder?: React.ReactNode
}

export function EnhancedImage({
  src,
  alt,
  className = '',
  style,
  onClick,
  lazy = true,
  placeholder,
  errorPlaceholder
}: EnhancedImageProps) {
  const [isLoaded, setIsLoaded] = useState(false)
  const [hasError, setHasError] = useState(false)
  const [isInView, setIsInView] = useState(!lazy)
  const imgRef = useRef<HTMLImageElement>(null)
  const containerRef = useRef<HTMLDivElement>(null)

  // 懒加载逻辑
  useEffect(() => {
    if (!lazy || isInView) return

    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsInView(true)
          observer.disconnect()
        }
      },
      { threshold: 0.1 }
    )

    if (containerRef.current) {
      observer.observe(containerRef.current)
    }

    return () => observer.disconnect()
  }, [lazy, isInView])

  const handleLoad = () => {
    setIsLoaded(true)
    setHasError(false)
  }

  const handleError = () => {
    setHasError(true)
    setIsLoaded(false)
  }

  const handleClick = () => {
    if (onClick && !hasError) {
      onClick(src, alt)
    }
  }

  const defaultPlaceholder = (
    <div className="flex items-center justify-center bg-slate-100 dark:bg-slate-700 animate-pulse">
      <ImageIcon className="h-8 w-8 text-slate-400" />
    </div>
  )

  const defaultErrorPlaceholder = (
    <div className="flex flex-col items-center justify-center bg-slate-100 dark:bg-slate-700 text-slate-500 dark:text-slate-400">
      <AlertCircle className="h-8 w-8 mb-2" />
      <span className="text-xs text-center px-2">图片加载失败</span>
    </div>
  )

  return (
    <div 
      ref={containerRef}
      className={`relative overflow-hidden ${className}`}
      style={style}
    >
      {/* 占位符 */}
      {!isLoaded && !hasError && (
        <div className="absolute inset-0 flex items-center justify-center">
          {placeholder || defaultPlaceholder}
        </div>
      )}

      {/* 错误占位符 */}
      {hasError && (
        <div className="absolute inset-0 flex items-center justify-center">
          {errorPlaceholder || defaultErrorPlaceholder}
        </div>
      )}

      {/* 实际图片 */}
      {isInView && (
        <img
          ref={imgRef}
          src={src}
          alt={alt}
          className={`
            w-full h-full object-cover transition-opacity duration-300
            ${isLoaded ? 'opacity-100' : 'opacity-0'}
            ${onClick && !hasError ? 'cursor-pointer hover:opacity-90' : ''}
          `}
          onLoad={handleLoad}
          onError={handleError}
          onClick={handleClick}
          loading={lazy ? 'lazy' : 'eager'}
        />
      )}
    </div>
  )
}