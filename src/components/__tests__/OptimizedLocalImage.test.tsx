// /**
//  * Tests for OptimizedLocalImage component
//  */
//
// import { describe, it, expect, vi, beforeEach } from 'vitest'
// import { render, screen, waitFor } from '@testing-library/react'
// import { OptimizedLocalImage } from '../OptimizedLocalImage'
//
// // Mock the useImage hook
// vi.mock('../../hooks/useImageManager', () => ({
//   useImage: vi.fn()
// }))
//
// import { useImage } from '../../hooks/useImageManager'
//
// describe('OptimizedLocalImage', () => {
//   const mockUseImage = useImage as any
//
//   beforeEach(() => {
//     vi.clearAllMocks()
//   })
//
//   it('should show loading state', () => {
//     mockUseImage.mockReturnValue({
//       dataUrl: null,
//       blob: null,
//       loading: true,
//       error: null,
//       reload: vi.fn()
//     })
//
//     const { container } = render(
//       <OptimizedLocalImage
//         imagePath="test.jpg"
//         alt="Test image"
//         className="w-32 h-32"
//       />
//     )
//
//     // Check for loading indicator (spinner icon)
//     expect(container.querySelector('.lucide-loader-circle')).toBeInTheDocument()
//     // Check for placeholder icon
//     expect(container.querySelector('.lucide-image')).toBeInTheDocument()
//   })
//
//   it('should show error state', () => {
//     mockUseImage.mockReturnValue({
//       dataUrl: null,
//       blob: null,
//       loading: false,
//       error: 'Failed to load',
//       reload: vi.fn()
//     })
//
//     render(
//       <OptimizedLocalImage
//         imagePath="test.jpg"
//         alt="Test image"
//         showRetry={true}
//       />
//     )
//
//     expect(screen.getByText('图片加载失败')).toBeInTheDocument()
//     expect(screen.getByText('重试')).toBeInTheDocument()
//   })
//
//   it('should show image when loaded successfully', () => {
//     mockUseImage.mockReturnValue({
//       dataUrl: 'data:image/png;base64,test',
//       blob: new Blob(['test'], { type: 'image/png' }),
//       loading: false,
//       error: null,
//       reload: vi.fn()
//     })
//
//     render(
//       <OptimizedLocalImage
//         imagePath="test.jpg"
//         alt="Test image"
//       />
//     )
//
//     const img = screen.getByRole('img')
//     expect(img).toHaveAttribute('src', 'data:image/png;base64,test')
//     expect(img).toHaveAttribute('alt', 'Test image')
//   })
//
//   it('should handle click events', async () => {
//     const mockOnClick = vi.fn()
//
//     mockUseImage.mockReturnValue({
//       dataUrl: 'data:image/png;base64,test',
//       blob: new Blob(['test'], { type: 'image/png' }),
//       loading: false,
//       error: null,
//       reload: vi.fn()
//     })
//
//     render(
//       <OptimizedLocalImage
//         imagePath="test.jpg"
//         alt="Test image"
//         onClick={mockOnClick}
//       />
//     )
//
//     const img = screen.getByRole('img')
//     img.click()
//
//     expect(mockOnClick).toHaveBeenCalledWith('test.jpg', 'data:image/png;base64,test')
//   })
//
//   it('should call onLoad callback when blob is available', () => {
//     const mockOnLoad = vi.fn()
//     const testBlob = new Blob(['test'], { type: 'image/png' })
//
//     mockUseImage.mockReturnValue({
//       dataUrl: 'data:image/png;base64,test',
//       blob: testBlob,
//       loading: false,
//       error: null,
//       reload: vi.fn()
//     })
//
//     render(
//       <OptimizedLocalImage
//         imagePath="test.jpg"
//         alt="Test image"
//         onLoad={mockOnLoad}
//       />
//     )
//
//     expect(mockOnLoad).toHaveBeenCalledWith(testBlob)
//   })
//
//   it('should call onError callback when error occurs', () => {
//     const mockOnError = vi.fn()
//
//     mockUseImage.mockReturnValue({
//       dataUrl: null,
//       blob: null,
//       loading: false,
//       error: 'Load failed',
//       reload: vi.fn()
//     })
//
//     render(
//       <OptimizedLocalImage
//         imagePath="test.jpg"
//         alt="Test image"
//         onError={mockOnError}
//       />
//     )
//
//     expect(mockOnError).toHaveBeenCalledWith('Load failed')
//   })
// })