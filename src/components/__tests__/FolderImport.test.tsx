// import { describe, it, expect, vi, beforeEach } from 'vitest'
// import { render, screen, fireEvent, waitFor } from '@testing-library/react'
// import FolderImport from '../FolderImport'
// import * as imageImportService from '../../lib/imageImport'
//
// // Mock window.electronAPI
// const mockElectronAPI = {
//   fileSystem: {
//     selectFolderAndGetImages: vi.fn(),
//     getFileMd5: vi.fn()
//   },
//   database: {
//     checkImageExists: vi.fn()
//   }
// }
//
// // Mock imageImportService
// vi.mock('../../lib/imageImport', () => ({
//   imageImportService: {
//     importSingleImageFromPath: vi.fn()
//   }
// }))
//
// describe('FolderImport', () => {
//   beforeEach(() => {
//     vi.clearAllMocks()
//     // Setup window.electronAPI
//     ;(window as any).electronAPI = mockElectronAPI
//   })
//
//   it('应该正确渲染初始界面', () => {
//     render(<FolderImport />)
//
//     expect(screen.getByText('文件夹批量导入')).toBeInTheDocument()
//     expect(screen.getByRole('button', { name: /选择文件夹/i })).toBeInTheDocument()
//     expect(screen.queryByText('检查数据库')).not.toBeInTheDocument()
//     expect(screen.queryByText('导入新文件')).not.toBeInTheDocument()
//   })
//
//   it('应该能够选择文件夹', async () => {
//     const mockFolderPath = '/test/folder/path'
//     mockElectronAPI.fileSystem.selectFolderAndGetImages.mockResolvedValue({
//       success: true,
//       folderPath: mockFolderPath,
//       imageFiles: []
//     })
//
//     render(<FolderImport />)
//
//     const selectButton = screen.getByRole('button', { name: /选择文件夹/i })
//     fireEvent.click(selectButton)
//
//     await waitFor(() => {
//       expect(mockElectronAPI.fileSystem.selectFolderAndGetImages).toHaveBeenCalled()
//       expect(screen.getByText(/已选择/)).toBeInTheDocument()
//     })
//   })
//
//   it('应该能够扫描文件夹中的图片文件', async () => {
//     const mockFolderPath = '/test/folder/path'
//     const mockImages = [
//       { path: '/test/folder/path/image1.jpg', relativePath: 'image1.jpg', name: 'image1.jpg', size: 1024 },
//       { path: '/test/folder/path/image2.png', relativePath: 'image2.png', name: 'image2.png', size: 2048 },
//       { path: '/test/folder/path/subfolder/image3.webp', relativePath: 'subfolder/image3.webp', name: 'image3.webp', size: 3072 }
//     ]
//
//     mockElectronAPI.fileSystem.selectFolderAndGetImages.mockResolvedValue({
//       success: true,
//       folderPath: mockFolderPath,
//       imageFiles: mockImages
//     })
//
//     render(<FolderImport />)
//
//     // 选择文件夹
//     const selectButton = screen.getByRole('button', { name: /选择文件夹/i })
//     fireEvent.click(selectButton)
//
//     await waitFor(() => {
//       expect(screen.getByText(/发现的图片文件.*3.*个/)).toBeInTheDocument()
//       expect(screen.getByText('检查数据库')).toBeInTheDocument()
//     })
//   })
//
//   it('应该能够检查数据库中的重复图片', async () => {
//     const mockImages = [
//       { path: '/test/image1.jpg', relativePath: 'image1.jpg', name: 'image1.jpg', size: 1024 },
//       { path: '/test/image2.jpg', relativePath: 'image2.jpg', name: 'image2.jpg', size: 2048 }
//     ]
//
//     mockElectronAPI.fileSystem.selectFolderAndGetImages.mockResolvedValue({
//       success: true,
//       folderPath: '/test',
//       imageFiles: mockImages
//     })
//
//     // Mock MD5 计算
//     mockElectronAPI.fileSystem.getFileMd5.mockImplementation((path) => {
//       if (path === '/test/image1.jpg') return Promise.resolve({ success: true, md5: 'md5-1' })
//       if (path === '/test/image2.jpg') return Promise.resolve({ success: true, md5: 'md5-2' })
//       return Promise.resolve({ success: false })
//     })
//
//     // Mock 数据库检查 - image1 已存在，image2 不存在
//     mockElectronAPI.database.checkImageExists.mockImplementation((path, md5) => {
//       if (md5 === 'md5-1') return Promise.resolve({ success: true, exists: true })
//       return Promise.resolve({ success: true, exists: false })
//     })
//
//     render(<FolderImport />)
//
//     // 选择文件夹
//     fireEvent.click(screen.getByRole('button', { name: /选择文件夹/i }))
//     await waitFor(() => screen.getByText('检查数据库'))
//
//     // 检查重复
//     fireEvent.click(screen.getByText('检查数据库'))
//
//     await waitFor(() => {
//       expect(screen.getAllByText('新文件')).toHaveLength(2) // 统计区域和徽章区域
//       expect(screen.getAllByText('已存在')).toHaveLength(2)
//       expect(screen.getByText(/导入新文件.*1/)).toBeInTheDocument()
//     })
//   })
//
//   it('应该能够导入图片文件', async () => {
//     const mockImages = [
//       { path: '/test/new-image.jpg', relativePath: 'new-image.jpg', name: 'new-image.jpg', size: 1024 }
//     ]
//
//     mockElectronAPI.fileSystem.selectFolderAndGetImages.mockResolvedValue({
//       success: true,
//       folderPath: '/test',
//       imageFiles: mockImages
//     })
//
//     mockElectronAPI.fileSystem.getFileMd5.mockResolvedValue({ success: true, md5: 'new-md5' })
//     mockElectronAPI.database.checkImageExists.mockResolvedValue({ exists: false })
//
//     // Mock 导入成功
//     const mockImport = vi.spyOn(imageImportService.imageImportService, 'importSingleImageFromPath')
//     mockImport.mockResolvedValue({
//       success: true,
//       filename: 'new-image.jpg'
//     })
//
//     render(<FolderImport />)
//
//     // 完整流程：选择 -> 检查 -> 导入
//     fireEvent.click(screen.getByRole('button', { name: /选择文件夹/i }))
//     await waitFor(() => screen.getByText('检查数据库'))
//
//     fireEvent.click(screen.getByText('检查数据库'))
//     await waitFor(() => screen.getByText(/导入新文件.*1/))
//
//     // 开始导入
//     fireEvent.click(screen.getByText(/导入新文件.*1/))
//
//     await waitFor(() => {
//       expect(mockImport).toHaveBeenCalledWith('/test/new-image.jpg')
//       expect(screen.getByText(/成功/)).toBeInTheDocument()
//     })
//   })
//
//   it('应该正确处理导入错误', async () => {
//     const mockImages = [
//       { path: '/test/error-image.jpg', relativePath: 'error-image.jpg', name: 'error-image.jpg', size: 1024 }
//     ]
//
//     mockElectronAPI.fileSystem.selectFolderAndGetImages.mockResolvedValue({
//       success: true,
//       folderPath: '/test',
//       imageFiles: mockImages
//     })
//
//     mockElectronAPI.fileSystem.getFileMd5.mockResolvedValue({ success: true, md5: 'error-md5' })
//     mockElectronAPI.database.checkImageExists.mockResolvedValue({ success: true, exists: false })
//
//     // Mock 导入失败
//     const mockImport = vi.spyOn(imageImportService.imageImportService, 'importSingleImageFromPath')
//     mockImport.mockRejectedValue(new Error('导入失败'))
//
//     render(<FolderImport />)
//
//     // 完整流程
//     fireEvent.click(screen.getByRole('button', { name: /选择文件夹/i }))
//     await waitFor(() => screen.getByText('检查数据库'))
//
//     fireEvent.click(screen.getByText('检查数据库'))
//     await waitFor(() => screen.getByText(/导入新文件.*1/))
//
//     fireEvent.click(screen.getByText(/导入新文件.*1/))
//
//     await waitFor(() => {
//       expect(screen.getByText('导入结果')).toBeInTheDocument()
//       expect(screen.getAllByText(/失败/)).toHaveLength(3) // 统计区域和列表区域（失败的文件标题+错误信息）
//     })
//   })
//
//   it('应该支持强制更新已存在的文件', async () => {
//     const mockImages = [
//       { path: '/test/existing.jpg', relativePath: 'existing.jpg', name: 'existing.jpg', size: 1024 }
//     ]
//
//     mockElectronAPI.fileSystem.selectFolderAndGetImages.mockResolvedValue({
//       success: true,
//       folderPath: '/test',
//       imageFiles: mockImages
//     })
//
//     mockElectronAPI.fileSystem.getFileMd5.mockResolvedValue({ success: true, md5: 'existing-md5' })
//     mockElectronAPI.database.checkImageExists.mockResolvedValue({ success: true, exists: true })
//
//     const mockImport = vi.spyOn(imageImportService.imageImportService, 'importSingleImageFromPath')
//     mockImport.mockResolvedValue({ success: true, filename: 'existing.jpg' })
//
//     render(<FolderImport />)
//
//     // 选择并检查
//     fireEvent.click(screen.getByRole('button', { name: /选择文件夹/i }))
//     await waitFor(() => screen.getByText('检查数据库'))
//     fireEvent.click(screen.getByText('检查数据库'))
//
//     await waitFor(() => {
//       expect(screen.getAllByText('已存在')).toHaveLength(2)
//     })
//
//     // 应该看到强制更新按钮
//     expect(screen.getByText('强制更新全部')).toBeInTheDocument()
//   })
//
//   it('应该显示导入进度', async () => {
//     const mockImages = Array.from({ length: 5 }, (_, i) => ({
//       path: `/test/image${i}.jpg`,
//       relativePath: `image${i}.jpg`,
//       name: `image${i}.jpg`,
//       size: 1024
//     }))
//
//     mockElectronAPI.fileSystem.selectFolderAndGetImages.mockResolvedValue({
//       success: true,
//       folderPath: '/test',
//       imageFiles: mockImages
//     })
//
//     mockElectronAPI.fileSystem.getFileMd5.mockResolvedValue({ success: true, md5: 'test-md5' })
//     mockElectronAPI.database.checkImageExists.mockResolvedValue({ success: true, exists: false })
//
//     const mockImport = vi.spyOn(imageImportService.imageImportService, 'importSingleImageFromPath')
//     mockImport.mockImplementation(() =>
//       new Promise(resolve => setTimeout(() => resolve({ success: true, filename: 'image.jpg' }), 100))
//     )
//
//     render(<FolderImport />)
//
//     // 完整流程
//     fireEvent.click(screen.getByRole('button', { name: /选择文件夹/i }))
//     await waitFor(() => screen.getByText('检查数据库'))
//     fireEvent.click(screen.getByText('检查数据库'))
//     await waitFor(() => screen.getByText(/导入新文件.*5/))
//
//     fireEvent.click(screen.getByText(/导入新文件.*5/))
//
//     // 检查进度显示
//     await waitFor(() => {
//       expect(screen.getByText(/导入进度/)).toBeInTheDocument()
//     })
//
//     // 等待完成
//     await waitFor(() => {
//       expect(screen.getByText(/成功/)).toBeInTheDocument()
//     }, { timeout: 2000 })
//   })
// })