// import { describe, it, expect, vi, beforeEach } from 'vitest'
// import { render, screen, fireEvent, waitFor } from '@testing-library/react'
// import userEvent from '@testing-library/user-event'
// import { SmartSearch } from '../SmartSearch'
//
// // Mock UI components
// vi.mock('@/components/ui/input.tsx', () => ({
//   Input: ({ className, ...props }: any) => (
//     <input className={className} {...props} data-testid="search-input" />
//   )
// }))
//
// vi.mock('@/components/ui/button.tsx', () => ({
//   Button: ({ children, variant, size, className, disabled, onClick, ...props }: any) => (
//     <button
//       className={className}
//       disabled={disabled}
//       onClick={onClick}
//       data-variant={variant}
//       data-size={size}
//       {...props}
//     >
//       {children}
//     </button>
//   )
// }))
//
// vi.mock('@/components/ui/badge.tsx', () => ({
//   Badge: ({ children, variant, className }: any) => (
//     <span className={className} data-variant={variant}>
//       {children}
//     </span>
//   )
// }))
//
// vi.mock('@/components/ui/card.tsx', () => ({
//   Card: ({ children, className }: any) => (
//     <div className={className} data-testid="card">{children}</div>
//   ),
//   CardContent: ({ children, className }: any) => (
//     <div className={className} data-testid="card-content">{children}</div>
//   )
// }))
//
// // Mock Lucide React icons
// vi.mock('lucide-react', () => ({
//   Search: () => <div data-testid="search-icon" />,
//   Filter: () => <div data-testid="filter-icon" />,
//   Clock: () => <div data-testid="clock-icon" />,
//   Sparkles: () => <div data-testid="sparkles-icon" />,
//   TrendingUp: () => <div data-testid="trending-up-icon" />,
//   X: (props: any) => <div data-testid="x-icon" {...props} />,
//   Calendar: () => <div data-testid="calendar-icon" />,
//   Tag: () => <div data-testid="tag-icon" />,
//   Palette: () => <div data-testid="palette-icon" />
// }))
//
// describe('SmartSearch', () => {
//   const mockOnSearch = vi.fn()
//   const mockSearchHistory = ['城市夜景', '美食照片', '人物肖像']
//   const mockPopularTags = ['蓝天白云', '城市夜景', '美食照片', '人物肖像']
//
//   beforeEach(() => {
//     vi.clearAllMocks()
//   })
//
//   describe('基础渲染', () => {
//     it('应该正确渲染搜索组件', () => {
//       render(<SmartSearch onSearch={mockOnSearch} />)
//
//       expect(screen.getByTestId('search-input')).toBeInTheDocument()
//       expect(screen.getByText('搜索')).toBeInTheDocument()
//       expect(screen.getByText('筛选')).toBeInTheDocument()
//     })
//
//     it('应该显示占位符文本', () => {
//       render(<SmartSearch onSearch={mockOnSearch} />)
//
//       const input = screen.getByTestId('search-input')
//       expect(input).toHaveAttribute('placeholder', '输入描述性搜索，如：去年拍的蓝天白云、有咖啡的照片...')
//     })
//
//     it('应该显示热门搜索标签', () => {
//       render(<SmartSearch onSearch={mockOnSearch} />)
//
//       expect(screen.getByText('热门搜索')).toBeInTheDocument()
//       // 检查默认热门标签
//       expect(screen.getByText('蓝天白云')).toBeInTheDocument()
//       expect(screen.getByText('城市夜景')).toBeInTheDocument()
//     })
//
//     it('应该使用自定义热门标签', () => {
//       const customTags = ['自定义标签1', '自定义标签2']
//       render(<SmartSearch onSearch={mockOnSearch} popularTags={customTags} />)
//
//       expect(screen.getByText('自定义标签1')).toBeInTheDocument()
//       expect(screen.getByText('自定义标签2')).toBeInTheDocument()
//     })
//   })
//
//   describe('搜索功能', () => {
//     it('应该在输入时更新搜索查询', async () => {
//       const user = userEvent.setup()
//       render(<SmartSearch onSearch={mockOnSearch} />)
//
//       const input = screen.getByTestId('search-input')
//       await user.type(input, '测试搜索')
//
//       expect(input).toHaveValue('测试搜索')
//     })
//
//     it('应该在点击搜索按钮时调用onSearch', async () => {
//       const user = userEvent.setup()
//       render(<SmartSearch onSearch={mockOnSearch} />)
//
//       const input = screen.getByTestId('search-input')
//       const searchButton = screen.getByText('搜索')
//
//       await user.type(input, '测试查询')
//       await user.click(searchButton)
//
//       expect(mockOnSearch).toHaveBeenCalledWith('测试查询', {
//         dateRange: '',
//         tags: [],
//         location: '',
//         colors: []
//       })
//     })
//
//     it('应该在按Enter键时调用onSearch', async () => {
//       const user = userEvent.setup()
//       render(<SmartSearch onSearch={mockOnSearch} />)
//
//       const input = screen.getByTestId('search-input')
//       await user.type(input, '测试查询')
//       await user.keyboard('{Enter}')
//
//       expect(mockOnSearch).toHaveBeenCalledWith('测试查询', {
//         dateRange: '',
//         tags: [],
//         location: '',
//         colors: []
//       })
//     })
//
//     it('应该在查询为空时禁用搜索按钮', () => {
//       render(<SmartSearch onSearch={mockOnSearch} />)
//
//       const searchButton = screen.getByText('搜索')
//       expect(searchButton).toBeDisabled()
//     })
//
//     it('应该在有查询内容时启用搜索按钮', async () => {
//       const user = userEvent.setup()
//       render(<SmartSearch onSearch={mockOnSearch} />)
//
//       const input = screen.getByTestId('search-input')
//       await user.type(input, '测试')
//
//       const searchButton = screen.getByText('搜索')
//       expect(searchButton).not.toBeDisabled()
//     })
//
//     it('应该忽略空格查询', async () => {
//       const user = userEvent.setup()
//       render(<SmartSearch onSearch={mockOnSearch} />)
//
//       const input = screen.getByTestId('search-input')
//       const searchButton = screen.getByText('搜索')
//
//       await user.type(input, '   ')
//       await user.click(searchButton)
//
//       expect(mockOnSearch).not.toHaveBeenCalled()
//     })
//   })
//
//   describe('搜索建议', () => {
//     it('应该在输入时显示搜索建议', async () => {
//       const user = userEvent.setup()
//       render(<SmartSearch onSearch={mockOnSearch} />)
//
//       const input = screen.getByTestId('search-input')
//       await user.type(input, '城市')
//
//       await waitFor(() => {
//         expect(screen.getByText('城市夜景')).toBeInTheDocument()
//       })
//     })
//
//     it('应该限制建议数量为5个', async () => {
//       const user = userEvent.setup()
//       render(<SmartSearch onSearch={mockOnSearch} />)
//
//       const input = screen.getByTestId('search-input')
//       await user.type(input, 'a') // 这会匹配很多建议
//
//       await waitFor(() => {
//         const suggestions = screen.getAllByTestId('sparkles-icon')
//         expect(suggestions.length).toBeLessThanOrEqual(5)
//       })
//     })
//
//     it('应该在点击建议时执行搜索', async () => {
//       const user = userEvent.setup()
//       render(<SmartSearch onSearch={mockOnSearch} />)
//
//       const input = screen.getByTestId('search-input')
//       await user.type(input, '城市')
//
//       await waitFor(() => {
//         expect(screen.getByText('城市夜景')).toBeInTheDocument()
//       })
//
//       await user.click(screen.getByText('城市夜景'))
//
//       expect(mockOnSearch).toHaveBeenCalledWith('城市夜景', {
//         dateRange: '',
//         tags: [],
//         location: '',
//         colors: []
//       })
//     })
//
//     it('应该在空查询时隐藏建议', async () => {
//       const user = userEvent.setup()
//       render(<SmartSearch onSearch={mockOnSearch} />)
//
//       const input = screen.getByTestId('search-input')
//       await user.type(input, '城市')
//
//       await waitFor(() => {
//         expect(screen.getByText('城市夜景')).toBeInTheDocument()
//       })
//
//       await user.clear(input)
//
//       await waitFor(() => {
//         expect(screen.queryByText('城市夜景')).not.toBeInTheDocument()
//       })
//     })
//   })
//
//   describe('筛选功能', () => {
//     it('应该切换筛选面板显示状态', async () => {
//       const user = userEvent.setup()
//       render(<SmartSearch onSearch={mockOnSearch} />)
//
//       const filterButton = screen.getByText('筛选')
//
//       // 初始状态不显示筛选面板
//       expect(screen.queryByText('高级筛选')).not.toBeInTheDocument()
//
//       // 点击显示筛选面板
//       await user.click(filterButton)
//       expect(screen.getByText('高级筛选')).toBeInTheDocument()
//
//       // 再次点击隐藏筛选面板
//       await user.click(filterButton)
//       expect(screen.queryByText('高级筛选')).not.toBeInTheDocument()
//     })
//
//     it('应该能够选择时间范围筛选', async () => {
//       const user = userEvent.setup()
//       render(<SmartSearch onSearch={mockOnSearch} />)
//
//       // 打开筛选面板
//       await user.click(screen.getByText('筛选'))
//
//       // 选择时间范围
//       await user.click(screen.getByText('本周'))
//
//       // 执行搜索
//       const input = screen.getByTestId('search-input')
//       await user.type(input, '测试')
//       await user.click(screen.getByText('搜索'))
//
//       expect(mockOnSearch).toHaveBeenCalledWith('测试', {
//         dateRange: '本周',
//         tags: [],
//         location: '',
//         colors: []
//       })
//     })
//
//     it('应该能够选择和取消标签筛选', async () => {
//       const user = userEvent.setup()
//       render(<SmartSearch onSearch={mockOnSearch} />)
//
//       // 打开筛选面板
//       await user.click(screen.getByText('筛选'))
//
//       // 选择标签
//       const tagButtons = screen.getAllByText('蓝天白云')
//       await user.click(tagButtons[0]) // 筛选面板中的标签
//
//       // 执行搜索
//       const input = screen.getByTestId('search-input')
//       await user.type(input, '测试')
//       await user.click(screen.getByText('搜索'))
//
//       expect(mockOnSearch).toHaveBeenCalledWith('测试', {
//         dateRange: '',
//         tags: ['蓝天白云'],
//         location: '',
//         colors: []
//       })
//
//       // 取消选择标签
//       await user.click(tagButtons[0])
//       await user.clear(input)
//       await user.type(input, '测试2')
//       await user.click(screen.getByText('搜索'))
//
//       expect(mockOnSearch).toHaveBeenCalledWith('测试2', {
//         dateRange: '',
//         tags: [],
//         location: '',
//         colors: []
//       })
//     })
//
//     it('应该能够选择颜色筛选', async () => {
//       const user = userEvent.setup()
//       render(<SmartSearch onSearch={mockOnSearch} />)
//
//       // 打开筛选面板
//       await user.click(screen.getByText('筛选'))
//
//       // 选择颜色
//       await user.click(screen.getByText('蓝色'))
//
//       // 执行搜索
//       const input = screen.getByTestId('search-input')
//       await user.type(input, '测试')
//       await user.click(screen.getByText('搜索'))
//
//       expect(mockOnSearch).toHaveBeenCalledWith('测试', {
//         dateRange: '',
//         tags: [],
//         location: '',
//         colors: ['蓝色']
//       })
//     })
//
//     it('应该显示已选筛选条件', async () => {
//       const user = userEvent.setup()
//       render(<SmartSearch onSearch={mockOnSearch} />)
//
//       // 打开筛选面板
//       await user.click(screen.getByText('筛选'))
//
//       // 选择多个筛选条件
//       await user.click(screen.getByText('本月'))
//       const tagButtons = screen.getAllByText('美食照片')
//       await user.click(tagButtons[0])
//       await user.click(screen.getByText('红色'))
//
//       // 检查已选筛选条件是否显示
//       expect(screen.getByText('已选筛选条件')).toBeInTheDocument()
//
//       // 检查筛选标签是否显示
//       const badges = screen.getAllByText('本月')
//       expect(badges.length).toBeGreaterThan(0)
//     })
//
//     it('应该能够移除已选筛选条件', async () => {
//       const user = userEvent.setup()
//       render(<SmartSearch onSearch={mockOnSearch} />)
//
//       // 打开筛选面板并选择条件
//       await user.click(screen.getByText('筛选'))
//       await user.click(screen.getByText('本月'))
//
//       // 确认筛选条件已选择
//       expect(screen.getByText('已选筛选条件')).toBeInTheDocument()
//
//       // 点击X图标移除筛选条件
//       const removeButton = screen.getByTestId('x-icon')
//       await user.click(removeButton)
//
//       // 执行搜索验证筛选条件已移除
//       const input = screen.getByTestId('search-input')
//       await user.type(input, '测试')
//       await user.click(screen.getByText('搜索'))
//
//       expect(mockOnSearch).toHaveBeenCalledWith('测试', {
//         dateRange: '',
//         tags: [],
//         location: '',
//         colors: []
//       })
//     })
//   })
//
//   describe('热门标签交互', () => {
//     it('应该在点击热门标签时执行搜索', async () => {
//       const user = userEvent.setup()
//       render(<SmartSearch onSearch={mockOnSearch} />)
//
//       const tagButtons = screen.getAllByText('蓝天白云')
//       await user.click(tagButtons[tagButtons.length - 1]) // 点击热门搜索中的标签
//
//       expect(mockOnSearch).toHaveBeenCalledWith('蓝天白云', {
//         dateRange: '',
//         tags: [],
//         location: '',
//         colors: []
//       })
//     })
//
//     it('应该限制显示的热门标签数量为8个', () => {
//       const manyTags = Array.from({ length: 20 }, (_, i) => `标签${i + 1}`)
//       render(<SmartSearch onSearch={mockOnSearch} popularTags={manyTags} />)
//
//       // 热门搜索区域应该只显示前8个标签
//       expect(screen.getByText('标签1')).toBeInTheDocument()
//       expect(screen.getByText('标签8')).toBeInTheDocument()
//       expect(screen.queryByText('标签9')).not.toBeInTheDocument()
//     })
//   })
//
//   describe('搜索历史', () => {
//     it('应该显示搜索历史', () => {
//       render(
//         <SmartSearch
//           onSearch={mockOnSearch}
//           searchHistory={mockSearchHistory}
//         />
//       )
//
//       expect(screen.getByText('最近搜索')).toBeInTheDocument()
//       expect(screen.getByText('城市夜景')).toBeInTheDocument()
//       expect(screen.getByText('美食照片')).toBeInTheDocument()
//     })
//
//     it('应该在点击历史记录时执行搜索', async () => {
//       const user = userEvent.setup()
//       render(
//         <SmartSearch
//           onSearch={mockOnSearch}
//           searchHistory={mockSearchHistory}
//         />
//       )
//
//       // 查找并点击历史记录中的项目
//       const historyItems = screen.getAllByText('城市夜景')
//       await user.click(historyItems[historyItems.length - 1]) // 点击搜索历史中的项目
//
//       expect(mockOnSearch).toHaveBeenCalledWith('城市夜景', {
//         dateRange: '',
//         tags: [],
//         location: '',
//         colors: []
//       })
//     })
//
//     it('应该限制显示的搜索历史数量为5个', () => {
//       const longHistory = Array.from({ length: 10 }, (_, i) => `历史搜索${i + 1}`)
//       render(
//         <SmartSearch
//           onSearch={mockOnSearch}
//           searchHistory={longHistory}
//         />
//       )
//
//       expect(screen.getByText('历史搜索1')).toBeInTheDocument()
//       expect(screen.getByText('历史搜索5')).toBeInTheDocument()
//       expect(screen.queryByText('历史搜索6')).not.toBeInTheDocument()
//     })
//
//     it('应该在没有搜索历史时不显示历史区域', () => {
//       render(<SmartSearch onSearch={mockOnSearch} searchHistory={[]} />)
//
//       expect(screen.queryByText('最近搜索')).not.toBeInTheDocument()
//     })
//   })
//
//   describe('边界情况处理', () => {
//     it('应该处理未提供onSearch回调的情况', async () => {
//       const user = userEvent.setup()
//       render(<SmartSearch />)
//
//       const input = screen.getByTestId('search-input')
//       await user.type(input, '测试')
//       await user.click(screen.getByText('搜索'))
//
//       // 不应该抛出错误
//       expect(true).toBe(true)
//     })
//
//     it('应该正确处理空数组的props', () => {
//       render(
//         <SmartSearch
//           onSearch={mockOnSearch}
//           searchHistory={[]}
//           popularTags={[]}
//         />
//       )
//
//       // 应该显示默认热门标签
//       expect(screen.getByText('蓝天白云')).toBeInTheDocument()
//       // 不应该显示搜索历史
//       expect(screen.queryByText('最近搜索')).not.toBeInTheDocument()
//     })
//   })
// })