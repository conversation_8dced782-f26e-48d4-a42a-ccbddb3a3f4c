// import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
// import { render, screen, fireEvent, waitFor } from '@testing-library/react'
// import userEvent from '@testing-library/user-event'
// import { SearchMethodSelector } from '../SearchMethodSelector'
// import type { SearchMethod } from '../../types/search'
//
// // Mock UI components
// vi.mock('@/components/ui/card', () => ({
//   Card: ({ children, className }: any) => (
//     <div className={className} data-testid="card">{children}</div>
//   ),
//   CardContent: ({ children, className }: any) => (
//     <div className={className} data-testid="card-content">{children}</div>
//   )
// }))
//
// vi.mock('@/lib/utils', () => ({
//   cn: (...classes: any[]) => classes.filter(Boolean).join(' ')
// }))
//
// // Mock localStorage
// const localStorageMock = {
//   getItem: vi.fn(),
//   setItem: vi.fn(),
//   removeItem: vi.fn(),
//   clear: vi.fn()
// }
//
// Object.defineProperty(window, 'localStorage', {
//   value: localStorageMock
// })
//
// describe('SearchMethodSelector', () => {
//   const mockOnMethodChange = vi.fn()
//   const defaultProps = {
//     selectedMethod: 'auto' as SearchMethod,
//     onMethodChange: mockOnMethodChange
//   }
//
//   beforeEach(() => {
//     vi.clearAllMocks()
//     localStorageMock.getItem.mockReturnValue(null)
//   })
//
//   afterEach(() => {
//     vi.clearAllMocks()
//   })
//
//   describe('基础渲染', () => {
//     it('应该正确渲染所有搜索方法选项', () => {
//       render(<SearchMethodSelector {...defaultProps} />)
//
//       expect(screen.getByText('搜索方法')).toBeInTheDocument()
//       expect(screen.getByText('自动选择')).toBeInTheDocument()
//       expect(screen.getByText('智能混合搜索')).toBeInTheDocument()
//       expect(screen.getByText('语义向量搜索')).toBeInTheDocument()
//     })
//
//     it('应该显示每个方法的描述', () => {
//       render(<SearchMethodSelector {...defaultProps} />)
//
//       expect(screen.getByText('智能选择最佳搜索方法，优先使用混合搜索，必要时回退到向量搜索')).toBeInTheDocument()
//       expect(screen.getByText('结合关键词匹配和语义理解，提供最准确的搜索结果')).toBeInTheDocument()
//       expect(screen.getByText('基于AI语义理解的向量相似度搜索，可调节相似度阈值')).toBeInTheDocument()
//     })
//
//     it('应该显示每个方法的图标', () => {
//       render(<SearchMethodSelector {...defaultProps} />)
//
//       expect(screen.getByText('🤖')).toBeInTheDocument()
//       expect(screen.getByText('⚡')).toBeInTheDocument()
//       expect(screen.getByText('🎯')).toBeInTheDocument()
//     })
//
//     it('应该正确设置选中状态', () => {
//       render(<SearchMethodSelector {...defaultProps} selectedMethod="hybrid" />)
//
//       const hybridRadio = screen.getByDisplayValue('hybrid')
//       const autoRadio = screen.getByDisplayValue('auto')
//       const vectorRadio = screen.getByDisplayValue('vector')
//
//       expect(hybridRadio).toBeChecked()
//       expect(autoRadio).not.toBeChecked()
//       expect(vectorRadio).not.toBeChecked()
//     })
//   })
//
//   describe('方法选择交互', () => {
//     it('应该在点击不同方法时调用onMethodChange', async () => {
//       const user = userEvent.setup()
//       render(<SearchMethodSelector {...defaultProps} />)
//
//       const hybridLabel = screen.getByText('智能混合搜索').closest('label')!
//       await user.click(hybridLabel)
//
//       expect(mockOnMethodChange).toHaveBeenCalledWith('hybrid')
//     })
//
//     it('应该通过单选框直接选择方法', async () => {
//       const user = userEvent.setup()
//       render(<SearchMethodSelector {...defaultProps} />)
//
//       const vectorRadio = screen.getByDisplayValue('vector')
//       await user.click(vectorRadio)
//
//       expect(mockOnMethodChange).toHaveBeenCalledWith('vector')
//     })
//
//     it('应该在禁用状态下不响应点击', async () => {
//       const user = userEvent.setup()
//       render(<SearchMethodSelector {...defaultProps} disabled={true} />)
//
//       const hybridLabel = screen.getByText('智能混合搜索').closest('label')!
//       await user.click(hybridLabel)
//
//       expect(mockOnMethodChange).not.toHaveBeenCalled()
//     })
//
//     it('应该在禁用状态下禁用所有单选框', () => {
//       render(<SearchMethodSelector {...defaultProps} disabled={true} />)
//
//       const radios = screen.getAllByRole('radio')
//       radios.forEach(radio => {
//         expect(radio).toBeDisabled()
//       })
//     })
//   })
//
//   describe('本地存储持久化', () => {
//     it('应该在组件挂载时加载保存的偏好设置', () => {
//       const savedPreferences = {
//         method: 'vector',
//         threshold: 0.8,
//         lastUpdated: '2024-01-01T00:00:00.000Z'
//       }
//       localStorageMock.getItem.mockReturnValue(JSON.stringify(savedPreferences))
//
//       render(<SearchMethodSelector {...defaultProps} />)
//
//       expect(localStorageMock.getItem).toHaveBeenCalledWith('enhanced-search-preferences')
//       expect(mockOnMethodChange).toHaveBeenCalledWith('vector')
//     })
//
//     it('应该在方法变更时保存偏好设置', async () => {
//       const user = userEvent.setup()
//       render(<SearchMethodSelector {...defaultProps} />)
//
//       const hybridLabel = screen.getByText('智能混合搜索').closest('label')!
//       await user.click(hybridLabel)
//
//       expect(localStorageMock.setItem).toHaveBeenCalledWith(
//         'enhanced-search-preferences',
//         expect.stringContaining('"method":"hybrid"')
//       )
//     })
//
//     it('应该在persistPreferences为false时不加载或保存偏好设置', async () => {
//       const user = userEvent.setup()
//       render(<SearchMethodSelector {...defaultProps} persistPreferences={false} />)
//
//       expect(localStorageMock.getItem).not.toHaveBeenCalled()
//
//       const hybridLabel = screen.getByText('智能混合搜索').closest('label')!
//       await user.click(hybridLabel)
//
//       expect(localStorageMock.setItem).not.toHaveBeenCalled()
//     })
//
//     it('应该在本地存储损坏时使用默认偏好设置', () => {
//       localStorageMock.getItem.mockReturnValue('invalid json')
//       const consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => {})
//
//       render(<SearchMethodSelector {...defaultProps} />)
//
//       expect(consoleSpy).toHaveBeenCalledWith(
//         'Failed to load search preferences:',
//         expect.any(Error)
//       )
//       // 不应该调用onMethodChange，因为默认值与当前选中值相同
//       expect(mockOnMethodChange).not.toHaveBeenCalled()
//
//       consoleSpy.mockRestore()
//     })
//
//     it('应该验证加载的偏好设置的有效性', () => {
//       const invalidPreferences = {
//         method: 'invalid_method',
//         threshold: 0.8,
//         lastUpdated: '2024-01-01T00:00:00.000Z'
//       }
//       localStorageMock.getItem.mockReturnValue(JSON.stringify(invalidPreferences))
//
//       render(<SearchMethodSelector {...defaultProps} />)
//
//       // 应该使用默认偏好设置，不调用onMethodChange
//       expect(mockOnMethodChange).not.toHaveBeenCalled()
//     })
//
//     it('应该在保存偏好设置失败时记录警告', async () => {
//       const user = userEvent.setup()
//       localStorageMock.setItem.mockImplementation(() => {
//         throw new Error('Storage quota exceeded')
//       })
//       const consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => {})
//
//       render(<SearchMethodSelector {...defaultProps} />)
//
//       const hybridLabel = screen.getByText('智能混合搜索').closest('label')!
//       await user.click(hybridLabel)
//
//       expect(consoleSpy).toHaveBeenCalledWith(
//         'Failed to save search preferences:',
//         expect.any(Error)
//       )
//
//       consoleSpy.mockRestore()
//     })
//   })
//
//   describe('样式和类名', () => {
//     it('应该应用自定义className', () => {
//       render(<SearchMethodSelector {...defaultProps} className="custom-class" />)
//
//       const card = screen.getByTestId('card')
//       expect(card).toHaveClass('custom-class')
//     })
//
//     it('应该在禁用状态下应用正确的样式', () => {
//       render(<SearchMethodSelector {...defaultProps} disabled={true} />)
//
//       const labels = screen.getAllByRole('radio').map(radio => radio.closest('label'))
//       labels.forEach(label => {
//         expect(label).toHaveClass('opacity-50', 'cursor-not-allowed')
//       })
//     })
//
//     it('应该为选中的方法应用正确的样式', () => {
//       render(<SearchMethodSelector {...defaultProps} selectedMethod="hybrid" />)
//
//       const hybridRadio = screen.getByDisplayValue('hybrid')
//       const hybridLabel = hybridRadio.closest('label')
//
//       expect(hybridLabel).toHaveClass('border-blue-500', 'bg-blue-50')
//     })
//
//     it('应该为未选中的方法应用正确的样式', () => {
//       render(<SearchMethodSelector {...defaultProps} selectedMethod="hybrid" />)
//
//       const autoRadio = screen.getByDisplayValue('auto')
//       const autoLabel = autoRadio.closest('label')
//
//       expect(autoLabel).toHaveClass('border-gray-200')
//       expect(autoLabel).not.toHaveClass('border-blue-500', 'bg-blue-50')
//     })
//   })
//
//   describe('无障碍访问', () => {
//     it('应该为图标提供正确的aria-label', () => {
//       render(<SearchMethodSelector {...defaultProps} />)
//
//       const autoIcon = screen.getByLabelText('自动选择')
//       const hybridIcon = screen.getByLabelText('智能混合搜索')
//       const vectorIcon = screen.getByLabelText('语义向量搜索')
//
//       expect(autoIcon).toBeInTheDocument()
//       expect(hybridIcon).toBeInTheDocument()
//       expect(vectorIcon).toBeInTheDocument()
//     })
//
//     it('应该为单选框组提供正确的name属性', () => {
//       render(<SearchMethodSelector {...defaultProps} />)
//
//       const radios = screen.getAllByRole('radio')
//       radios.forEach(radio => {
//         expect(radio).toHaveAttribute('name', 'searchMethod')
//       })
//     })
//
//     it('应该支持键盘导航', async () => {
//       const user = userEvent.setup()
//       render(<SearchMethodSelector {...defaultProps} />)
//
//       const firstRadio = screen.getByDisplayValue('auto')
//       firstRadio.focus()
//
//       // Tab键导航到下一个选项
//       await user.keyboard('{ArrowDown}')
//       expect(screen.getByDisplayValue('hybrid')).toHaveFocus()
//
//       // 空格键选择
//       await user.keyboard(' ')
//       expect(mockOnMethodChange).toHaveBeenCalledWith('hybrid')
//     })
//   })
//
//   describe('边界情况处理', () => {
//     it('应该处理undefined的selectedMethod', () => {
//       render(<SearchMethodSelector {...defaultProps} selectedMethod={undefined as any} />)
//
//       // 不应该有任何radio被选中
//       const radios = screen.getAllByRole('radio')
//       radios.forEach(radio => {
//         expect(radio).not.toBeChecked()
//       })
//     })
//
//     it('应该处理无效的selectedMethod', () => {
//       render(<SearchMethodSelector {...defaultProps} selectedMethod={'invalid' as any} />)
//
//       // 不应该有任何radio被选中
//       const radios = screen.getAllByRole('radio')
//       radios.forEach(radio => {
//         expect(radio).not.toBeChecked()
//       })
//     })
//
//     it('应该处理未提供onMethodChange回调的情况', () => {
//       render(<SearchMethodSelector selectedMethod="auto" onMethodChange={undefined as any} />)
//
//       // 不应该抛出错误
//       expect(screen.getByText('搜索方法')).toBeInTheDocument()
//     })
//   })
//
//   describe('偏好设置数据结构', () => {
//     it('应该保存完整的偏好设置对象', async () => {
//       const user = userEvent.setup()
//       render(<SearchMethodSelector {...defaultProps} />)
//
//       const hybridLabel = screen.getByText('智能混合搜索').closest('label')!
//       await user.click(hybridLabel)
//
//       expect(localStorageMock.setItem).toHaveBeenCalledWith(
//         'enhanced-search-preferences',
//         expect.stringMatching(/"method":"hybrid"/)
//       )
//
//       const savedData = JSON.parse(localStorageMock.setItem.mock.calls[0][1])
//       expect(savedData).toEqual({
//         method: 'hybrid',
//         threshold: 0.6,
//         lastUpdated: expect.any(String)
//       })
//     })
//
//     it('应该在加载时更新lastUpdated字段', () => {
//       const oldPreferences = {
//         method: 'vector',
//         threshold: 0.8,
//         lastUpdated: '2020-01-01T00:00:00.000Z'
//       }
//       localStorageMock.getItem.mockReturnValue(JSON.stringify(oldPreferences))
//
//       render(<SearchMethodSelector {...defaultProps} />)
//
//       // lastUpdated应该被更新为当前时间
//       expect(mockOnMethodChange).toHaveBeenCalledWith('vector')
//     })
//   })
// })