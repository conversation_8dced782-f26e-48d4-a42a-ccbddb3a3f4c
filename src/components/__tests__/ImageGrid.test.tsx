// import { describe, it, expect, vi, beforeEach } from 'vitest'
// import { render, screen, fireEvent, waitFor } from '@testing-library/react'
// import '@testing-library/jest-dom'
// import { ImageGrid } from '../ImageGrid'
// import { ImageData } from '../../data/mockData'

// // Mock SimpleImage component
// vi.mock('../SimpleImage', () => ({
//   SimpleImage: ({ imagePath, alt, className, fallbackSrc }: any) => (
//     <img
//       src={`app://${imagePath}`}
//       alt={alt}
//       className={className}
//       data-testid="simple-image"
//       onError={() => console.log('Image load error')}
//       onLoad={() => console.log('Image loaded')}
//     />
//   )
// }))

// // Mock UI components
// vi.mock('@/components/ui/card', () => ({
//   Card: ({ children, className, onClick, onMouseEnter, onMouseLeave }: any) => (
//     <div
//       className={className}
//       onClick={onClick}
//       onMouseEnter={onMouseEnter}
//       onMouseLeave={onMouseLeave}
//       data-testid="image-card"
//     >
//       {children}
//     </div>
//   ),
//   CardContent: ({ children, className }: any) => (
//     <div className={className} data-testid="card-content">
//       {children}
//     </div>
//   )
// }))

// vi.mock('@/components/ui/badge', () => ({
//   Badge: ({ children, className }: any) => (
//     <span className={className} data-testid="badge">
//       {children}
//     </span>
//   )
// }))

// vi.mock('@/components/ui/button', () => ({
//   Button: ({ children, onClick, disabled, className, size, variant }: any) => (
//     <button
//       onClick={onClick}
//       disabled={disabled}
//       className={className}
//       data-testid="button"
//     >
//       {children}
//     </button>
//   )
// }))

// describe('ImageGrid', () => {
//   const mockImages: ImageData[] = [
//     {
//       id: '1',
//       title: '测试图片1',
//       description: '这是第一张测试图片',
//       url: 'C:\\Users\\<USER>\\image1.jpg',
//       tags: ['自然', '风景'],
//       uploadTime: '2024-01-01',
//       fileSize: '2.5 MB',
//       resolution: '1920x1080',
//       similarity: 95  // Already as percentage
//     },
//     {
//       id: '2',
//       title: '测试图片2',
//       description: '这是第二张测试图片',
//       url: 'https://example.com/image2.jpg',
//       tags: ['城市', '建筑'],
//       uploadTime: '2024-01-02',
//       fileSize: '3.2 MB',
//       resolution: '2560x1440',
//       similarity: 87  // Already as percentage
//     }
//   ]

//   const mockOnImageSelect = vi.fn()
//   const mockOnImageDelete = vi.fn()

//   beforeEach(() => {
//     vi.clearAllMocks()
//   })

//   it('renders empty state when no images provided', () => {
//     render(<ImageGrid images={[]} />)
    
//     expect(screen.getByText('暂无图像')).toBeInTheDocument()
//     expect(screen.getByText('上传一些图片开始使用智能相册功能')).toBeInTheDocument()
//   })

//   it('renders images in grid mode', () => {
//     render(<ImageGrid images={mockImages} viewMode="grid" />)
    
//     expect(screen.getAllByTestId('image-card')).toHaveLength(2)
//     expect(screen.getByText('测试图片1')).toBeInTheDocument()
//     expect(screen.getByText('测试图片2')).toBeInTheDocument()
//   })

//   it('renders images in list mode', () => {
//     render(<ImageGrid images={mockImages} viewMode="list" />)
    
//     expect(screen.getAllByTestId('image-card')).toHaveLength(2)
//     // List mode should change the container styling
//     const container = screen.getAllByTestId('image-card')[0].parentElement
//     expect(container).toHaveClass('space-y-4')
//   })

//   it('handles local image paths correctly', () => {
//     render(<ImageGrid images={[mockImages[0]]} />)
    
//     const simpleImage = screen.getByTestId('simple-image')
//     // Note: The mock SimpleImage component shows the app:// URL as expected
//     expect(simpleImage).toHaveAttribute('src', 'app://C:\\Users\\<USER>\\image1.jpg')
//   })

//   it('handles network image URLs correctly', () => {
//     render(<ImageGrid images={[mockImages[1]]} />)
    
//     const networkImage = screen.getByAltText('测试图片2')
//     expect(networkImage).toHaveAttribute('src', 'https://example.com/image2.jpg')
//   })

//   it('calls onImageSelect when image is clicked', () => {
//     render(<ImageGrid images={mockImages} onImageSelect={mockOnImageSelect} />)
    
//     const firstCard = screen.getAllByTestId('image-card')[0]
//     fireEvent.click(firstCard)
    
//     expect(mockOnImageSelect).toHaveBeenCalledWith(mockImages[0])
//   })

//   it('displays image tags correctly', () => {
//     render(<ImageGrid images={mockImages} />)
    
//     expect(screen.getByText('自然')).toBeInTheDocument()
//     expect(screen.getByText('风景')).toBeInTheDocument()
//     expect(screen.getByText('城市')).toBeInTheDocument()
//     expect(screen.getByText('建筑')).toBeInTheDocument()
//   })

//   it('displays similarity scores when available', () => {
//     render(<ImageGrid images={mockImages} />)
    
//     expect(screen.getByText('95%')).toBeInTheDocument()
//     expect(screen.getByText('87%')).toBeInTheDocument()
//   })

//   it('shows metadata information', () => {
//     render(<ImageGrid images={mockImages} />)
    
//     expect(screen.getByText('2024-01-01')).toBeInTheDocument()
//     expect(screen.getByText('2024-01-02')).toBeInTheDocument()
//   })

//   it('handles hover state correctly', async () => {
//     render(<ImageGrid images={mockImages} />)
    
//     const firstCard = screen.getAllByTestId('image-card')[0]
    
//     // Hover over the card
//     fireEvent.mouseEnter(firstCard)
    
//     // Check that hover overlay becomes visible
//     const overlay = firstCard.querySelector('.absolute.inset-0')
//     expect(overlay).toHaveClass('opacity-100')
    
//     // Mouse leave should hide overlay
//     fireEvent.mouseLeave(firstCard)
//     expect(overlay).toHaveClass('opacity-0')
//   })

//   it('shows delete button when onImageDelete is provided', () => {
//     render(
//       <ImageGrid 
//         images={mockImages} 
//         onImageDelete={mockOnImageDelete}
//       />
//     )
    
//     const firstCard = screen.getAllByTestId('image-card')[0]
//     fireEvent.mouseEnter(firstCard)
    
//     // Should show delete button in overlay
//     const buttons = screen.getAllByTestId('button')
//     expect(buttons.length).toBeGreaterThan(4) // Should include delete button
//   })

//   it('truncates long tag lists', () => {
//     const imageWithManyTags: ImageData = {
//       ...mockImages[0],
//       tags: ['tag1', 'tag2', 'tag3', 'tag4', 'tag5']
//     }
    
//     render(<ImageGrid images={[imageWithManyTags]} />)
    
//     // Should show first 3 tags
//     expect(screen.getByText('tag1')).toBeInTheDocument()
//     expect(screen.getByText('tag2')).toBeInTheDocument()
//     expect(screen.getByText('tag3')).toBeInTheDocument()
    
//     // Should show "+2" for remaining tags
//     expect(screen.getByText('+2')).toBeInTheDocument()
//   })

//   it('handles image loading errors gracefully', () => {
//     const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {})
    
//     render(<ImageGrid images={[mockImages[1]]} />)
    
//     const networkImage = screen.getByAltText('测试图片2')
    
//     // Simulate image loading error
//     fireEvent.error(networkImage)
    
//     expect(consoleSpy).toHaveBeenCalledWith('❌ [ImageGrid] 图片加载失败:', 'https://example.com/image2.jpg')
    
//     consoleSpy.mockRestore()
//   })

//   it('logs successful image loads', () => {
//     const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {})
    
//     render(<ImageGrid images={[mockImages[1]]} />)
    
//     const networkImage = screen.getByAltText('测试图片2')
    
//     // Simulate successful image load
//     fireEvent.load(networkImage)
    
//     expect(consoleSpy).toHaveBeenCalledWith('🖼️ [ImageGrid] 图片加载成功:', 'https://example.com/image2.jpg')
    
//     consoleSpy.mockRestore()
//   })
// })