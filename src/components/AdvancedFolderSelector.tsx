import React, { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import {
  FolderOpen,
  Settings,
  FileImage,
  HardDrive,
  Layers,
  CheckCircle,
  AlertCircle,
  Loader2
} from 'lucide-react'
import { toast } from 'sonner'
import { handleError, handleApiError } from '@/lib/errorHandler'
import { trpcClient } from '@/lib/trpcClient'

// 扫描选项接口
interface ScanOptions {
  recursive: boolean        // 递归扫描子文件夹
  includeHidden: boolean   // 包含隐藏文件
  maxDepth: number         // 最大扫描深度
  supportedFormats: string[] // 支持的文件格式
}

// 文件夹分析结果
interface FolderAnalysis {
  estimatedFiles: number
  estimatedSize: string
  deepestLevel: number
  formatBreakdown: { [format: string]: number }
  sampleImages: string[]
  hasSubfolders: boolean
  accessPermissions: {
    readable: boolean
    writable: boolean
  }
  hasPermissionIssues: boolean
}

// 默认配置
const DEFAULT_SCAN_OPTIONS: ScanOptions = {
  recursive: true,
  includeHidden: false,
  maxDepth: 10,
  supportedFormats: ['.jpg', '.jpeg', '.png']
}

const ALL_SUPPORTED_FORMATS = [
  { ext: '.jpg', name: 'JPEG' },
  { ext: '.jpeg', name: 'JPEG' },
  { ext: '.png', name: 'PNG' },
  { ext: '.webp', name: 'WebP' },
  { ext: '.gif', name: 'GIF' },
  { ext: '.bmp', name: 'BMP' },
  { ext: '.tiff', name: 'TIFF' },
  { ext: '.svg', name: 'SVG' },
  { ext: '.ico', name: 'ICO' }
]

interface AdvancedFolderSelectorProps {
  onFolderSelected: (folderPath: string, options: ScanOptions) => void
  onCancel: () => void
  isVisible: boolean
}

export const AdvancedFolderSelector: React.FC<AdvancedFolderSelectorProps> = ({
  onFolderSelected,
  onCancel,
  isVisible
}) => {
  const [selectedFolder, setSelectedFolder] = useState<string>('')
  const [scanOptions, setScanOptions] = useState<ScanOptions>(DEFAULT_SCAN_OPTIONS)
  const [analysis, setAnalysis] = useState<FolderAnalysis | null>(null)
  const [isAnalyzing, setIsAnalyzing] = useState(false)
  const [showAdvanced, setShowAdvanced] = useState(false)

  // 选择文件夹
  const handleSelectFolder = async () => {
    try {
      console.log('📁 开始调用 tRPC selectFolder...')
      const result = await trpcClient.library.selectFolder.mutate()
      console.log('📁 tRPC selectFolder 返回结果:', result)

      if (!result.success) {
        if (result.error) {
          handleError(result.error, '选择文件夹失败')
        }
        return
      }

      // 检查用户是否取消选择
      if (result.canceled) {
        console.log('用户取消了文件夹选择')
        return // 静默返回
      }

      // 直接获取文件夹路径
      const folderPath = result.folderPath
      console.log('选择的文件夹路径:', folderPath)

      if (!folderPath) {
        console.log('未获取到有效的文件夹路径')
        return
      }

      setSelectedFolder(folderPath)

      // 自动分析文件夹
      await analyzeFolder(folderPath)
    } catch (error: any) {
      handleError(error, '选择文件夹失败')
    }
  }

  // 分析文件夹
  const analyzeFolder = async (folderPath: string) => {
    if (!folderPath) return

    try {
      setIsAnalyzing(true)
      console.log('开始分析文件夹:', folderPath, '选项:', scanOptions)

      // 调用 tRPC 分析API
      const response = await trpcClient.library.analyzeFolder.mutate({
        folderPath: folderPath,
        options: {
          maxDepth: scanOptions.maxDepth,
          includeHidden: scanOptions.includeHidden,
          recursive: scanOptions.recursive,
          supportedFormats: scanOptions.supportedFormats
        }
      })

      console.log('收到分析响应:', response)

      if (response.success && response.data) {
        // 格式化文件大小
        const formatSize = (bytes: number): string => {
          if (bytes === 0) return '0 Bytes'
          const k = 1024
          const sizes = ['Bytes', 'KB', 'MB', 'GB']
          const i = Math.floor(Math.log(bytes) / Math.log(k))
          return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
        }

        setAnalysis({
          estimatedFiles: response.data.estimatedFiles,
          estimatedSize: formatSize(response.data.totalSize),
          deepestLevel: response.data.deepestLevel,
          formatBreakdown: response.data.formatBreakdown,
          sampleImages: [],
          hasSubfolders: response.data.deepestLevel > 0,
          accessPermissions: {
            readable: true,
            writable: true
          },
          hasPermissionIssues: response.data.permissionIssues.length > 0
        } as FolderAnalysis)
      } else {
        console.error('分析失败或无数据:', response.error)
        handleError(new Error(response.error || '分析失败'), '分析文件夹失败')
      }
    } catch (error: any) {
      console.error('分析文件夹失败:', error)
      handleError(error, '分析文件夹失败')
    } finally {
      setIsAnalyzing(false)
    }
  }

  // 更新扫描选项
  const updateScanOptions = (updates: Partial<ScanOptions>) => {
    const newOptions = { ...scanOptions, ...updates }
    setScanOptions(newOptions)

    // 如果已选择文件夹，重新分析
    if (selectedFolder) {
      analyzeFolder(selectedFolder)
    }
  }

  // 切换格式支持
  const toggleFormat = (format: string) => {
    const newFormats = scanOptions.supportedFormats.includes(format)
      ? scanOptions.supportedFormats.filter(f => f !== format)
      : [...scanOptions.supportedFormats, format]

    updateScanOptions({ supportedFormats: newFormats })
  }

  // 确认选择
  const handleConfirm = () => {
    if (!selectedFolder || typeof selectedFolder !== 'string') {
      handleError(new Error(`无效的文件夹路径: ${selectedFolder}`), '请先选择有效的文件夹')
      return
    }

    console.log('确认选择文件夹:', selectedFolder, '扫描选项:', scanOptions)
    onFolderSelected(selectedFolder, scanOptions)
  }

  // 格式化文件大小
  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  if (!isVisible) return null

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto">
        <div className="p-6">
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-2xl font-bold text-gray-800">高级文件夹选择器</h2>
            <Button variant="ghost" onClick={onCancel}>
              ✕
            </Button>
          </div>

          {/* 文件夹选择 */}
          <Card className="mb-6">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FolderOpen className="h-5 w-5" />
                选择文件夹
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex gap-4 items-center">
                <Button onClick={handleSelectFolder} className="flex items-center gap-2">
                  <FolderOpen className="h-4 w-4" />
                  浏览文件夹
                </Button>
                {selectedFolder && (
                  <div className="flex-1 p-3 bg-gray-50 rounded-lg">
                    <div className="text-sm text-gray-600">已选择:</div>
                    <div className="font-medium text-gray-800 break-all">{selectedFolder}</div>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* 扫描选项 */}
          <Card className="mb-6">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Settings className="h-5 w-5" />
                扫描选项
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowAdvanced(!showAdvanced)}
                >
                  {showAdvanced ? '简化' : '高级'}
                </Button>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* 基础选项 */}
              <div className="grid grid-cols-2 gap-4">
                <label className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    checked={scanOptions.recursive}
                    onChange={(e) => updateScanOptions({ recursive: e.target.checked })}
                    className="rounded"
                  />
                  <span>递归扫描子文件夹</span>
                </label>

                <label className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    checked={scanOptions.includeHidden}
                    onChange={(e) => updateScanOptions({ includeHidden: e.target.checked })}
                    className="rounded"
                  />
                  <span>包含隐藏文件</span>
                </label>
              </div>

              {/* 最大深度 */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  最大扫描深度: {scanOptions.maxDepth}
                </label>
                <input
                  type="range"
                  min="1"
                  max="20"
                  value={scanOptions.maxDepth}
                  onChange={(e) => updateScanOptions({ maxDepth: parseInt(e.target.value) })}
                  className="w-full"
                />
                <div className="flex justify-between text-xs text-gray-500 mt-1">
                  <span>1层</span>
                  <span>20层 (Deep模式)</span>
                </div>
              </div>

              {/* 高级选项 */}
              {showAdvanced && (
                <div className="border-t pt-4">
                  <h4 className="font-medium text-gray-800 mb-3">支持的文件格式</h4>
                  <div className="grid grid-cols-3 gap-2">
                    {ALL_SUPPORTED_FORMATS.map(({ ext, name }) => (
                      <label key={ext} className="flex items-center space-x-2">
                        <input
                          type="checkbox"
                          checked={scanOptions.supportedFormats.includes(ext)}
                          onChange={() => toggleFormat(ext)}
                          className="rounded"
                        />
                        <span className="text-sm">{name} ({ext})</span>
                      </label>
                    ))}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* 文件夹分析预览 */}
          {selectedFolder && (
            <Card className="mb-6">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <FileImage className="h-5 w-5" />
                  扫描预览
                  {isAnalyzing && <Loader2 className="h-4 w-4 animate-spin" />}
                </CardTitle>
              </CardHeader>
              <CardContent>
                {isAnalyzing ? (
                  <div className="flex items-center justify-center py-8">
                    <Loader2 className="h-8 w-8 animate-spin text-blue-500" />
                    <span className="ml-2 text-gray-600">分析文件夹中...</span>
                  </div>
                ) : analysis ? (
                  <div className="space-y-4">
                    {/* 统计信息 */}
                    <div className="grid grid-cols-3 gap-4">
                      <div className="text-center p-4 bg-blue-50 rounded-lg">
                        <div className="text-2xl font-bold text-blue-600">{analysis.estimatedFiles}</div>
                        <div className="text-sm text-gray-600">预计图片数量</div>
                      </div>
                      <div className="text-center p-4 bg-green-50 rounded-lg">
                        <div className="text-2xl font-bold text-green-600">{analysis.estimatedSize}</div>
                        <div className="text-sm text-gray-600">预计总大小</div>
                      </div>
                      <div className="text-center p-4 bg-purple-50 rounded-lg">
                        <div className="text-2xl font-bold text-purple-600">{analysis.deepestLevel}</div>
                        <div className="text-sm text-gray-600">最深层级</div>
                      </div>
                    </div>

                    {/* 格式分布 */}
                    <div>
                      <h4 className="font-medium text-gray-800 mb-2">格式分布</h4>
                      <div className="flex flex-wrap gap-2">
                        {Object.entries(analysis.formatBreakdown).map(([format, count]) => (
                          <Badge key={format} variant="secondary">
                            {format.toUpperCase()}: {count}
                          </Badge>
                        ))}
                      </div>
                    </div>

                    {/* 权限警告 */}
                    {analysis.hasPermissionIssues && (
                      <div className="flex items-center gap-2 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                        <AlertCircle className="h-5 w-5 text-yellow-600" />
                        <span className="text-yellow-800">检测到部分文件夹可能存在权限问题</span>
                      </div>
                    )}
                  </div>
                ) : (
                  <div className="text-center py-8 text-gray-500">
                    选择文件夹后将显示预览信息
                  </div>
                )}
              </CardContent>
            </Card>
          )}

          {/* 操作按钮 */}
          <div className="flex justify-end gap-4">
            <Button variant="outline" onClick={onCancel}>
              取消
            </Button>
            <Button
              onClick={handleConfirm}
              disabled={!selectedFolder || isAnalyzing}
              className="flex items-center gap-2"
            >
              <CheckCircle className="h-4 w-4" />
              确认添加
            </Button>
          </div>
        </div>
      </div>
    </div>
  )
}

export default AdvancedFolderSelector