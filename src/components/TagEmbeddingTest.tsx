// import React, { useState } from 'react';
// import { databaseService } from '../lib/database';
//
// interface TagEmbeddingTestProps {
//   onClose: () => void;
// }
//
// interface TestResult {
//   step: string;
//   status: 'pending' | 'running' | 'success' | 'error';
//   message: string;
//   data?: {
//     query?: string;
//     similarTags?: Array<{
//       tag: string;
//       similarity: number;
//       frequency: number;
//       category: string;
//       score: number;
//     }>;
//     metadata?: {
//       originalQuery: string;
//       parsedKeywords: string[];
//       expandedKeywords: string[];
//       keywordExpansions: Array<{ original: string; expanded: string[]; score: number }>;
//       totalMatches: number;
//     };
//     results?: Array<{
//       id: string;
//       description?: string;
//       tags_flat?: string[];
//       score?: number;
//     }>;
//   } | Array<{
//     query: string;
//     similarTags: Array<{
//       tag: string;
//       similarity: number;
//       frequency: number;
//       category: string;
//       score: number;
//     }>;
//   }>;
// }
//
// const TagEmbeddingTest: React.FC<TagEmbeddingTestProps> = ({ onClose }) => {
//   const [testResults, setTestResults] = useState<TestResult[]>([]);
//   const [isRunning, setIsRunning] = useState(false);
//
//   const testSteps = [
//     {
//       step: '测试数据库连接',
//       test: async () => {
//         const result = await databaseService.manualTestConnection();
//         if (!result.success || !result.connected) {
//           throw new Error(result.error || '数据库连接失败');
//         }
//         return { success: true, message: '数据库连接成功' };
//       }
//     },
//     {
//       step: '初始化数据库',
//       test: async () => {
//         const result = await databaseService.manualInitCollection();
//         if (!result.success) {
//           throw new Error(result.error || '数据库初始化失败');
//         }
//         return { success: true, message: '数据库初始化成功' };
//       }
//     },
//     {
//       step: '重新初始化数据库（包含Tag向量库）',
//       test: async () => {
//         const result = await databaseService.manualInitCollection();
//         if (!result.success) throw new Error(result.error || '数据库重新初始化失败');
//         return { success: true, message: '数据库重新初始化成功（包含Tag向量库）' };
//       }
//     },
//     {
//       step: '测试相似标签查询',
//       test: async () => {
//         const testQueries = ['狗', '猫', '人', '车', '花'];
//         const results = [];
//
//         for (const query of testQueries) {
//           const similarTags = await databaseService.findSimilarTags(query, {
//             limit: 3,
//             threshold: 0.6
//           });
//           results.push({ query, similarTags });
//         }
//
//         return {
//           success: true,
//           message: `完成${testQueries.length}个查询测试`,
//           data: results
//         };
//       }
//     },
//     {
//       step: '测试增强混合搜索',
//       test: async () => {
//         const searchResults = await databaseService.enhancedHybridSearch(
//           '可爱的小狗',
//           5,
//           true,
//           0.7
//         );
//
//         return {
//           success: true,
//           message: '增强混合搜索测试完成',
//           data: searchResults
//         };
//       }
//     }
//   ];
//
//   const updateTestResult = (index: number, status: TestResult['status'], message: string, data?: TestResult['data']) => {
//     setTestResults(prev => {
//       const newResults = [...prev];
//       newResults[index] = { step: testSteps[index].step, status, message, data };
//       return newResults;
//     });
//   };
//
//   const runTests = async () => {
//     setIsRunning(true);
//     setTestResults([]);
//
//     // 初始化测试结果
//     const initialResults = testSteps.map((step: any) => ({
//       step: step.step,
//       status: 'pending' as const,
//       message: '等待执行...'
//     }));
//     setTestResults(initialResults);
//
//     for (let i = 0; i < testSteps.length; i++) {
//       updateTestResult(i, 'running', '正在执行...');
//
//       try {
//         const result = await testSteps[i].test();
//         updateTestResult(i, 'success', result.message);
//       } catch (error) {
//         const errorMessage = error instanceof Error ? error.message : String(error);
//         updateTestResult(i, 'error', errorMessage);
//         break; // 停止后续测试
//       }
//     }
//
//     setIsRunning(false);
//   };
//
//   const getStatusIcon = (status: TestResult['status']) => {
//     switch (status) {
//       case 'pending': return '⏳';
//       case 'running': return '🔄';
//       case 'success': return '✅';
//       case 'error': return '❌';
//       default: return '❓';
//     }
//   };
//
//   const getStatusColor = (status: TestResult['status']) => {
//     switch (status) {
//       case 'pending': return 'text-gray-500';
//       case 'running': return 'text-blue-500';
//       case 'success': return 'text-green-500';
//       case 'error': return 'text-red-500';
//       default: return 'text-gray-500';
//     }
//   };
//
//   return (
//     <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
//       <div className="bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto">
//         <div className="p-6">
//           <div className="flex justify-between items-center mb-6">
//             <h2 className="text-2xl font-bold text-gray-800">Tag Embedding 功能测试</h2>
//             <button
//               onClick={onClose}
//               className="text-gray-500 hover:text-gray-700 text-xl"
//             >
//               ×
//             </button>
//           </div>
//
//           <div className="mb-6">
//             <button
//               onClick={runTests}
//               disabled={isRunning}
//               className={`px-6 py-3 rounded-lg font-semibold ${
//                 isRunning
//                   ? 'bg-gray-400 text-gray-600 cursor-not-allowed'
//                   : 'bg-blue-500 text-white hover:bg-blue-600'
//               }`}
//             >
//               {isRunning ? '测试进行中...' : '开始测试'}
//             </button>
//           </div>
//
//           <div className="space-y-4">
//             {testResults.map((result, index) => (
//               <div
//                 key={index}
//                 className={`p-4 rounded-lg border-2 ${
//                   result.status === 'success' ? 'border-green-200 bg-green-50' :
//                   result.status === 'error' ? 'border-red-200 bg-red-50' :
//                   result.status === 'running' ? 'border-blue-200 bg-blue-50' :
//                   'border-gray-200 bg-gray-50'
//                 }`}
//               >
//                 <div className="flex items-center gap-3 mb-2">
//                   <span className="text-2xl">{getStatusIcon(result.status)}</span>
//                   <h3 className="font-semibold text-lg">{result.step}</h3>
//                 </div>
//                 <p className={`mb-2 ${getStatusColor(result.status)}`}>
//                   {result.message}
//                 </p>
//
//                 {result.data && (
//                   <div className="mt-3 p-3 bg-gray-100 rounded-md">
//                     <h4 className="font-semibold mb-2">详细结果：</h4>
//
//                     {/* 相似标签查询结果 */}
//                     {Array.isArray(result.data) && result.data.length > 0 && result.data[0].query && (
//                       <div className="space-y-2">
//                         {(result.data as Array<{ query: string; similarTags: Array<{ tag: string; similarity: number; frequency: number; category: string; score: number }> }>).map((queryResult, idx: number) => (
//                           <div key={idx} className="border-l-4 border-blue-400 pl-3">
//                             <p className="font-medium">查询: "{queryResult.query}"</p>
//                             {queryResult.similarTags.length > 0 ? (
//                               <ul className="text-sm text-gray-600 mt-1">
//                                 {queryResult.similarTags.map((tag, tagIdx: number) => (
//                                   <li key={tagIdx}>
//                                     {tag.tag} (相似度: {tag.similarity.toFixed(3)}, 频率: {tag.frequency})
//                                   </li>
//                                 ))}
//                               </ul>
//                             ) : (
//                               <p className="text-sm text-gray-500">没有找到相似标签</p>
//                             )}
//                           </div>
//                         ))}
//                       </div>
//                     )}
//
//                     {/* 增强混合搜索结果 */}
//                     {result.data && !Array.isArray(result.data) && result.data.metadata && (
//                       <div className="space-y-2">
//                         <p><strong>原始查询:</strong> {result.data.metadata.originalQuery}</p>
//                         <p><strong>解析的关键词:</strong> {result.data.metadata.parsedKeywords.join(', ')}</p>
//                         <p><strong>扩展的关键词:</strong> {result.data.metadata.expandedKeywords.join(', ')}</p>
//                         <p><strong>匹配总数:</strong> {result.data.metadata.totalMatches}</p>
//                         <p><strong>返回结果数:</strong> {result.data.results?.length || 0}</p>
//
//                         {result.data.results && result.data.results.length > 0 && (
//                           <div className="mt-3">
//                             <h5 className="font-semibold">前3个结果:</h5>
//                             <div className="space-y-2 mt-2">
//                               {result.data.results.slice(0, 3).map((searchResult: any, idx: number) => (
//                                 <div key={idx} className="text-sm border-l-4 border-green-400 pl-3">
//                                   <p><strong>ID:</strong> {searchResult.id}</p>
//                                   <p><strong>描述:</strong> {searchResult.description?.substring(0, 100)}...</p>
//                                   <p><strong>标签:</strong> {searchResult.tags_flat?.join(', ')}</p>
//                                   <p><strong>综合得分:</strong> {searchResult.score?.toFixed(3)}</p>
//                                 </div>
//                               ))}
//                             </div>
//                           </div>
//                         )}
//                       </div>
//                     )}
//                   </div>
//                 )}
//               </div>
//             ))}
//           </div>
//         </div>
//       </div>
//     </div>
//   );
// };
//
// export default TagEmbeddingTest;