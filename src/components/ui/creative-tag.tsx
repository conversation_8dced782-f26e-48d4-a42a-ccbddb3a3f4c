import { FC } from 'react'
import { cn } from '@/lib/utils'
import { Hash, Palette, MapPin, Camera, Activity, Grid3X3, MoreHorizontal } from 'lucide-react'

interface CreativeTagProps {
  name: string
  count: number
  category: string
  onClick?: () => void
  selected?: boolean
  variant?: 'neon' | 'glass' | 'paper' | 'holographic' | 'minimal'
}

export function CreativeTag({ 
  name, 
  count, 
  category, 
  onClick, 
  selected = false,
  variant = 'neon'
}: CreativeTagProps) {
  // 获取分类配置
  const configs: Record<string, {
    icon: any;
    neonColor: string;
    glassColor: string;
    paperColor: string;
    holoColor: string;
    minimalColor: string;
  }> = {
    scene: {
      icon: Grid3X3,
      neonColor: 'shadow-blue-500/50 border-blue-400',
      glassColor: 'bg-blue-500/20 border-blue-300/30',
      paperColor: 'bg-blue-50 border-blue-200 text-blue-700',
      holoColor: 'from-blue-400 via-cyan-400 to-blue-600',
      minimalColor: 'border-blue-300 text-blue-600'
    },
    object: {
      icon: Hash,
      neonColor: 'shadow-green-500/50 border-green-400',
      glassColor: 'bg-green-500/20 border-green-300/30',
      paperColor: 'bg-green-50 border-green-200 text-green-700',
      holoColor: 'from-green-400 via-emerald-400 to-green-600',
      minimalColor: 'border-green-300 text-green-600'
    },
    color: {
      icon: Palette,
      neonColor: 'shadow-pink-500/50 border-pink-400',
      glassColor: 'bg-pink-500/20 border-pink-300/30',
      paperColor: 'bg-pink-50 border-pink-200 text-pink-700',
      holoColor: 'from-pink-400 via-rose-400 to-pink-600',
      minimalColor: 'border-pink-300 text-pink-600'
    },
    action: {
      icon: Activity,
      neonColor: 'shadow-red-500/50 border-red-400',
      glassColor: 'bg-red-500/20 border-red-300/30',
      paperColor: 'bg-red-50 border-red-200 text-red-700',
      holoColor: 'from-red-400 via-orange-400 to-red-600',
      minimalColor: 'border-red-300 text-red-600'
    },
    style: {
      icon: Camera,
      neonColor: 'shadow-indigo-500/50 border-indigo-400',
      glassColor: 'bg-indigo-500/20 border-indigo-300/30',
      paperColor: 'bg-indigo-50 border-indigo-200 text-indigo-700',
      holoColor: 'from-indigo-400 via-purple-400 to-indigo-600',
      minimalColor: 'border-indigo-300 text-indigo-600'
    },
    location: {
      icon: MapPin,
      neonColor: 'shadow-orange-500/50 border-orange-400',
      glassColor: 'bg-orange-500/20 border-orange-300/30',
      paperColor: 'bg-orange-50 border-orange-200 text-orange-700',
      holoColor: 'from-orange-400 via-yellow-400 to-orange-600',
      minimalColor: 'border-orange-300 text-orange-600'
    },
    other: {
      icon: MoreHorizontal,
      neonColor: 'shadow-gray-500/50 border-gray-400',
      glassColor: 'bg-gray-500/20 border-gray-300/30',
      paperColor: 'bg-gray-50 border-gray-200 text-gray-700',
      holoColor: 'from-gray-400 via-slate-400 to-gray-600',
      minimalColor: 'border-gray-300 text-gray-600'
    }
  }
  // 修改 getCategoryConfig 函数
  const getCategoryConfig = (category: string) => {
    return configs[category] || configs.other;
  }

  const config = getCategoryConfig(category)
  const Icon = config.icon

  // 霓虹灯效果
  if (variant === 'neon') {
    return (
      <div
        className={cn(
          `relative p-4 rounded-xl border-2 cursor-pointer transition-all duration-300 
           bg-slate-900/90 backdrop-blur-sm hover:scale-105 group overflow-hidden`,
          config.neonColor,
          selected ? 'shadow-lg scale-105' : 'hover:shadow-2xl'
        )}
        onClick={onClick}
      >
        {/* 霓虹光效背景 */}
        <div className="absolute inset-0 opacity-20 group-hover:opacity-30 transition-opacity">
          <div className={cn("absolute inset-0 blur-xl", config.neonColor.split(' ')[0])} />
        </div>
        
        <div className="relative z-10 text-white">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Icon className="h-4 w-4" />
              <span className="font-medium">{name}</span>
            </div>
            <span className="px-2 py-1 text-xs bg-white/20 rounded-md backdrop-blur-sm">
              {count}
            </span>
          </div>
        </div>
      </div>
    )
  }

  // 玻璃拟态效果
  if (variant === 'glass') {
    return (
      <div
        className={cn(
          `relative p-4 rounded-2xl border cursor-pointer transition-all duration-300 
           backdrop-blur-md hover:backdrop-blur-lg hover:-translate-y-1 overflow-hidden`,
          config.glassColor,
          selected ? 'ring-2 ring-white/50 shadow-lg' : 'hover:shadow-xl'
        )}
        onClick={onClick}
      >
        {/* 玻璃反光效果 */}
        <div className="absolute top-0 left-0 w-full h-1/2 bg-gradient-to-b from-white/10 to-transparent rounded-t-2xl" />
        
        <div className="relative z-10">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Icon className="h-4 w-4" />
              <span className="font-medium">{name}</span>
            </div>
            <span className="px-2 py-1 text-xs bg-white/30 rounded-lg backdrop-blur-sm">
              {count}
            </span>
          </div>
        </div>
      </div>
    )
  }

  // 纸质卡片效果
  if (variant === 'paper') {
    return (
      <div
        className={cn(
          `relative p-4 rounded-lg border-2 cursor-pointer transition-all duration-300 
           hover:shadow-lg hover:-translate-y-1 bg-white dark:bg-slate-100`,
          config.paperColor,
          selected ? 'shadow-lg -translate-y-1' : ''
        )}
        onClick={onClick}
        style={{
          boxShadow: selected 
            ? '0 8px 25px rgba(0,0,0,0.15), inset 0 1px 0 rgba(255,255,255,0.6)' 
            : '0 2px 8px rgba(0,0,0,0.1), inset 0 1px 0 rgba(255,255,255,0.6)'
        }}
      >
        {/* 纸质纹理 */}
        <div 
          className="absolute inset-0 opacity-30 rounded-lg"
          style={{
            backgroundImage: `
              radial-gradient(circle at 20% 80%, currentColor 1px, transparent 1px),
              radial-gradient(circle at 80% 20%, currentColor 1px, transparent 1px),
              radial-gradient(circle at 40% 40%, currentColor 0.5px, transparent 0.5px)
            `,
            backgroundSize: '20px 20px, 20px 20px, 10px 10px'
          }}
        />
        
        <div className="relative z-10">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Icon className="h-4 w-4" />
              <span className="font-medium">{name}</span>
            </div>
            <span className="px-2 py-1 text-xs bg-white/60 rounded border">
              {count}
            </span>
          </div>
        </div>
      </div>
    )
  }

  // 全息效果
  if (variant === 'holographic') {
    return (
      <div
        className={cn(
          `relative p-4 rounded-xl cursor-pointer transition-all duration-300 
           hover:scale-105 group overflow-hidden border border-white/20`,
          selected ? 'scale-105' : ''
        )}
        onClick={onClick}
      >
        {/* 全息背景 */}
        <div className={cn(
          "absolute inset-0 bg-gradient-to-br opacity-80 group-hover:opacity-100 transition-opacity",
          config.holoColor
        )} />
        
        {/* 全息光效 */}
        <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent 
                        transform -skew-x-12 -translate-x-full group-hover:translate-x-full 
                        transition-transform duration-1000" />
        
        <div className="relative z-10 text-white">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Icon className="h-4 w-4" />
              <span className="font-medium drop-shadow-sm">{name}</span>
            </div>
            <span className="px-2 py-1 text-xs bg-black/30 rounded-lg backdrop-blur-sm">
              {count}
            </span>
          </div>
        </div>
      </div>
    )
  }

  // 极简风格
  if (variant === 'minimal') {
    return (
      <div
        className={cn(
          `p-3 rounded-lg border cursor-pointer transition-all duration-200 
           bg-white dark:bg-slate-900 hover:shadow-md`,
          config.minimalColor,
          selected ? 'border-2 shadow-md' : 'border'
        )}
        onClick={onClick}
      >
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Icon className="h-3 w-3" />
            <span className="text-sm font-medium">{name}</span>
          </div>
          <span className="text-xs text-slate-500 dark:text-slate-400">
            {count}
          </span>
        </div>
      </div>
    )
  }

  return null
}