import { FC } from 'react'
import { cn } from '@/lib/utils'
import { Hash, Palette, MapPin, Camera, Activity, Grid3X3, MoreHorizontal } from 'lucide-react'

interface EnhancedTagProps {
  name: string
  count: number
  category: string
  onClick?: () => void
  selected?: boolean
  variant?: 'default' | 'blur-text' | 'gradient' | 'pattern'
}

export function EnhancedTag({ 
  name, 
  count, 
  category, 
  onClick, 
  selected = false,
  variant = 'default'
}: EnhancedTagProps) {
  // 获取分类配置
  const getCategoryConfig = (category: string) => {
    const configs = {
      scene: {
        colors: 'bg-blue-500/10 text-blue-600 border-blue-200/50 hover:bg-blue-500/20',
        icon: Grid3X3,
        bgText: 'SCENE',
        gradient: 'from-blue-400/20 to-blue-600/20'
      },
      object: {
        colors: 'bg-green-500/10 text-green-600 border-green-200/50 hover:bg-green-500/20',
        icon: Hash,
        bgText: 'OBJ',
        gradient: 'from-green-400/20 to-green-600/20'
      },
      color: {
        colors: 'bg-pink-500/10 text-pink-600 border-pink-200/50 hover:bg-pink-500/20',
        icon: Palette,
        bgText: 'COLOR',
        gradient: 'from-pink-400/20 to-pink-600/20'
      },
      action: {
        colors: 'bg-red-500/10 text-red-600 border-red-200/50 hover:bg-red-500/20',
        icon: Activity,
        bgText: 'ACT',
        gradient: 'from-red-400/20 to-red-600/20'
      },
      style: {
        colors: 'bg-indigo-500/10 text-indigo-600 border-indigo-200/50 hover:bg-indigo-500/20',
        icon: Camera,
        bgText: 'STYLE',
        gradient: 'from-indigo-400/20 to-indigo-600/20'
      },
      location: {
        colors: 'bg-orange-500/10 text-orange-600 border-orange-200/50 hover:bg-orange-500/20',
        icon: MapPin,
        bgText: 'LOC',
        gradient: 'from-orange-400/20 to-orange-600/20'
      },
      other: {
        colors: 'bg-gray-500/10 text-gray-600 border-gray-200/50 hover:bg-gray-500/20',
        icon: MoreHorizontal,
        bgText: 'OTHER',
        gradient: 'from-gray-400/20 to-gray-600/20'
      }
    }
    return configs[category as keyof typeof configs] || configs.other
  }

  const config = getCategoryConfig(category)
  const Icon = config.icon

  // 默认样式
  if (variant === 'default') {
    return (
      <div
        className={cn(
          `p-4 rounded-xl border cursor-pointer transition-all duration-200 hover:shadow-md hover:-translate-y-1`,
          config.colors,
          selected ? 'ring-2 ring-[#007aff] shadow-lg' : ''
        )}
        onClick={onClick}
      >
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Icon className="h-4 w-4" />
            <span className="font-medium">{name}</span>
          </div>
          <span className="px-2 py-1 text-xs bg-white/50 dark:bg-slate-800/50 rounded-md">
            {count}
          </span>
        </div>
      </div>
    )
  }

  // 背景模糊文案效果
  if (variant === 'blur-text') {
    return (
      <div
        className={cn(
          `relative p-4 rounded-xl border cursor-pointer transition-all duration-200 hover:shadow-md hover:-translate-y-1 overflow-hidden`,
          config.colors,
          selected ? 'ring-2 ring-[#007aff] shadow-lg' : ''
        )}
        onClick={onClick}
      >
        {/* 背景模糊文案 */}
        <div className="absolute inset-0 flex items-center justify-center opacity-5 dark:opacity-10 pointer-events-none">
          <span className="text-4xl font-black tracking-wider transform rotate-12">
            {config.bgText}
          </span>
        </div>
        
        {/* 前景内容 */}
        <div className="relative z-10">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Icon className="h-4 w-4" />
              <span className="font-medium">{name}</span>
            </div>
            <span className="px-2 py-1 text-xs bg-white/50 dark:bg-slate-800/50 rounded-md">
              {count}
            </span>
          </div>
        </div>
      </div>
    )
  }

  // 渐变效果
  if (variant === 'gradient') {
    return (
      <div
        className={cn(
          `relative p-4 rounded-xl border cursor-pointer transition-all duration-200 hover:shadow-md hover:-translate-y-1 overflow-hidden`,
          'bg-gradient-to-br',
          config.gradient,
          'border-white/20 dark:border-slate-700/50',
          selected ? 'ring-2 ring-[#007aff] shadow-lg' : ''
        )}
        onClick={onClick}
      >
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Icon className="h-4 w-4" />
            <span className="font-medium">{name}</span>
          </div>
          <span className="px-2 py-1 text-xs bg-white/30 dark:bg-slate-800/30 rounded-md backdrop-blur-sm">
            {count}
          </span>
        </div>
      </div>
    )
  }

  // 图案纹理效果
  if (variant === 'pattern') {
    return (
      <div
        className={cn(
          `relative p-4 rounded-xl border cursor-pointer transition-all duration-200 hover:shadow-md hover:-translate-y-1 overflow-hidden`,
          config.colors,
          selected ? 'ring-2 ring-[#007aff] shadow-lg' : ''
        )}
        onClick={onClick}
        style={{
          backgroundImage: `radial-gradient(circle at 20% 80%, currentColor 1px, transparent 1px),
                           radial-gradient(circle at 80% 20%, currentColor 1px, transparent 1px)`,
          backgroundSize: '15px 15px'
        } as React.CSSProperties}
      >
        <div className="relative z-10">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Icon className="h-4 w-4" />
              <span className="font-medium">{name}</span>
            </div>
            <span className="px-2 py-1 text-xs bg-white/50 dark:bg-slate-800/50 rounded-md">
              {count}
            </span>
          </div>
        </div>
      </div>
    )
  }

  return null
}