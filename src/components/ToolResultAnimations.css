/**
 * Enhanced Tool Result Animations
 * 为AI工具执行结果提供优雅的动画效果
 */

/* 淡入动画 */
@keyframes toolResultFadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 脉冲动画用于加载状态 */
@keyframes toolResultPulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* 进度条动画 */
@keyframes progressFill {
  from {
    width: 0%;
  }
  to {
    width: var(--progress-width);
  }
}

/* 标签出现动画 */
@keyframes tagSlideIn {
  from {
    opacity: 0;
    transform: translateX(-10px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* 图片缩放悬停效果 */
@keyframes imageHover {
  from {
    transform: scale(1);
  }
  to {
    transform: scale(1.05);
  }
}

/* 统计数字计数动画 */
@keyframes countUp {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* 应用动画的CSS类 */
.tool-result-container {
  animation: toolResultFadeIn 0.6s ease-out;
}

.tool-result-loading {
  animation: toolResultPulse 1.5s ease-in-out infinite;
}

.progress-bar {
  animation: progressFill 1s ease-out;
}

.tag-item {
  animation: tagSlideIn 0.4s ease-out;
}

.tag-item:nth-child(1) { animation-delay: 0.1s; }
.tag-item:nth-child(2) { animation-delay: 0.2s; }
.tag-item:nth-child(3) { animation-delay: 0.3s; }
.tag-item:nth-child(4) { animation-delay: 0.4s; }
.tag-item:nth-child(5) { animation-delay: 0.5s; }

.image-card:hover img {
  animation: imageHover 0.3s ease-out;
}

.stat-number {
  animation: countUp 0.8s ease-out;
}

/* 响应式动画调整 */
@media (prefers-reduced-motion: reduce) {
  .tool-result-container,
  .tag-item,
  .stat-number {
    animation: none;
  }
  
  .tool-result-loading {
    animation: none;
    opacity: 0.7;
  }
  
  .image-card:hover img {
    animation: none;
    transform: scale(1.02);
    transition: transform 0.2s ease;
  }
}

/* 深色模式适配 */
@media (prefers-color-scheme: dark) {
  .progress-bar {
    box-shadow: 0 0 10px rgba(59, 130, 246, 0.3);
  }
}

/* 特殊效果 */
.shimmer-effect {
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* 工具执行成功的微动画 */
.success-check {
  animation: successBounce 0.6s ease-out;
}

@keyframes successBounce {
  0% {
    transform: scale(0);
  }
  50% {
    transform: scale(1.2);
  }
  100% {
    transform: scale(1);
  }
}

/* 错误状态摇摆动画 */
.error-shake {
  animation: errorShake 0.5s ease-in-out;
}

@keyframes errorShake {
  0%, 100% {
    transform: translateX(0);
  }
  25% {
    transform: translateX(-5px);
  }
  75% {
    transform: translateX(5px);
  }
} 