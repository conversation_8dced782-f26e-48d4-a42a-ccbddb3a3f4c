import React from 'react';
import { Brain, Wrench, Eye, CheckCircle, Clock, AlertCircle } from 'lucide-react';

export interface ReActStep {
  id: string;
  type: 'thought' | 'action' | 'observation' | 'final';
  content: string;
  timestamp: Date;
  status: 'pending' | 'active' | 'completed' | 'error';
  toolName?: string;
  toolParams?: Record<string, unknown>;
  toolResult?: any;
  executionTime?: number;
}

// 格式化观察内容
function formatObservationContent(toolResult: any): string {
  if (!toolResult) return '无结果';
  
  // 如果是图片分析结果
  if (toolResult.success && toolResult.description) {
    const parts = [];
    
    // 主要描述
    parts.push(`📸 ${toolResult.description}`);
    
    // 置信度
    if (toolResult.confidence) {
      parts.push(`🎯 置信度: ${(toolResult.confidence * 100).toFixed(1)}%`);
    }
    
    // 主要标签
    if (toolResult.tags && Array.isArray(toolResult.tags) && toolResult.tags.length > 0) {
      const mainTags = toolResult.tags.slice(0, 5); // 只显示前5个标签
      parts.push(`🏷️ 标签: ${mainTags.join(', ')}`);
    }
    
    // 检测到的对象
    if (toolResult.structuredData?.objects && toolResult.structuredData.objects.length > 0) {
      const objects = toolResult.structuredData.objects.map((obj: any) => obj.name).join(', ');
      parts.push(`🔍 检测到: ${objects}`);
    }
    
    return parts.join('\n');
  }
  
  // 如果是相似图片搜索结果
  if (Array.isArray(toolResult) && toolResult.length > 0) {
    return `🔎 找到 ${toolResult.length} 张相似图片`;
  }
  
  // 如果是简单的成功/失败结果
  if (typeof toolResult === 'object' && toolResult.success !== undefined) {
    return toolResult.success ? '✅ 执行成功' : `❌ 执行失败: ${toolResult.error || '未知错误'}`;
  }
  
  // 默认情况
  return typeof toolResult === 'string' ? toolResult : JSON.stringify(toolResult);
}

interface ReActStepDisplayProps {
  steps: ReActStep[];
  className?: string;
  compact?: boolean;
}

export function ReActStepDisplay({ 
  steps, 
  className = '', 
  compact = false 
}: ReActStepDisplayProps): JSX.Element {
  const getStepIcon = (step: ReActStep) => {
    switch (step.type) {
      case 'thought':
        return <Brain className="h-4 w-4" />;
      case 'action':
        return <Wrench className="h-4 w-4" />;
      case 'observation':
        return <Eye className="h-4 w-4" />;
      case 'final':
        return <CheckCircle className="h-4 w-4" />;
      default:
        return <Clock className="h-4 w-4" />;
    }
  };

  const getStepColor = (step: ReActStep) => {
    if (step.status === 'error') return 'text-red-500 bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-800';
    
    switch (step.type) {
      case 'thought':
        return 'text-blue-600 bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-800';
      case 'action':
        return 'text-orange-600 bg-orange-50 dark:bg-orange-900/20 border-orange-200 dark:border-orange-800';
      case 'observation':
        return 'text-green-600 bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-800';
      case 'final':
        return 'text-purple-600 bg-purple-50 dark:bg-purple-900/20 border-purple-200 dark:border-purple-800';
      default:
        return 'text-slate-600 bg-slate-50 dark:bg-slate-800/20 border-slate-200 dark:border-slate-700';
    }
  };

  const getStepTitle = (step: ReActStep) => {
    switch (step.type) {
      case 'thought':
        return '🤔 思考中';
      case 'action':
        return `🔧 执行工具${step.toolName ? `: ${step.toolName}` : ''}`;
      case 'observation':
        return '👀 观察结果';
      case 'final':
        return '✅ 最终答案';
      default:
        return '处理中';
    }
  };

  const getStatusIndicator = (step: ReActStep) => {
    switch (step.status) {
      case 'pending':
        return <Clock className="h-3 w-3 text-slate-400 animate-pulse" />;
      case 'active':
        return <div className="h-3 w-3 border-2 border-current border-t-transparent rounded-full animate-spin" />;
      case 'completed':
        return <CheckCircle className="h-3 w-3 text-green-500" />;
      case 'error':
        return <AlertCircle className="h-3 w-3 text-red-500" />;
      default:
        return null;
    }
  };

  if (steps.length === 0) {
    return <div className={className}></div>;
  }

  return (
    <div className={`space-y-3 ${className}`}>
      <div className="flex items-center space-x-2 mb-4">
        <Brain className="h-4 w-4 text-[#007aff]" />
        <span className="text-sm font-medium text-[#007aff]">ReAct 推理过程</span>
        <div className="flex-1 h-px bg-gradient-to-r from-[#007aff]/30 to-transparent"></div>
        <span className="text-xs text-slate-500 dark:text-slate-400">
          {steps.filter(s => s.status === 'completed').length}/{steps.length} 步骤完成
        </span>
      </div>

      <div className="space-y-2">
        {steps.map((step, index) => (
          <div
            key={step.id}
            className={`
              relative border rounded-xl transition-all duration-300
              ${getStepColor(step)}
              ${step.status === 'active' ? 'ring-2 ring-current ring-opacity-20' : ''}
              ${compact ? 'p-3' : 'p-4'}
            `}
          >
            {/* 连接线 */}
            {index < steps.length - 1 && (
              <div className="absolute left-6 -bottom-2 w-0.5 h-4 bg-slate-200 dark:bg-slate-600"></div>
            )}

            <div className="flex items-start space-x-3">
              {/* 图标 */}
              <div className={`
                p-2 rounded-lg bg-white/50 dark:bg-slate-800/50 flex-shrink-0
                ${step.status === 'active' ? 'animate-pulse' : ''}
              `}>
                {getStepIcon(step)}
              </div>

              {/* 内容 */}
              <div className="flex-1 min-w-0">
                <div className="flex items-center justify-between mb-2">
                  <h4 className="text-sm font-medium">
                    {getStepTitle(step)}
                  </h4>
                  <div className="flex items-center space-x-2">
                    {step.executionTime && (
                      <span className="text-xs opacity-70">
                        {step.executionTime}ms
                      </span>
                    )}
                    {getStatusIndicator(step)}
                  </div>
                </div>

                {/* 步骤内容 */}
                <div className="text-sm opacity-80 leading-relaxed">
                  {step.type === 'action' && step.toolParams && !compact && (
                    <div className="mb-2 p-2 bg-white/30 dark:bg-slate-800/30 rounded-lg">
                      <div className="text-xs font-mono">
                        参数: {JSON.stringify(step.toolParams, null, 2)}
                      </div>
                    </div>
                  )}
                  
                  <div className={compact ? 'line-clamp-2' : ''}>
                    {step.type === 'observation' && step.toolResult ? 
                      // 为观察步骤格式化显示工具结果
                      formatObservationContent(step.toolResult) :
                      step.content
                    }
                  </div>

                  {step.type === 'observation' && step.toolResult && !compact && (
                    <div className="mt-2 p-2 bg-white/30 dark:bg-slate-800/30 rounded-lg">
                      <div className="text-xs opacity-70 mb-1">详细结果:</div>
                      <div className="text-xs font-mono max-h-24 overflow-y-auto">
                        {typeof step.toolResult === 'string' 
                          ? step.toolResult 
                          : JSON.stringify(step.toolResult, null, 2)
                        }
                      </div>
                    </div>
                  )}
                </div>

                {/* 时间戳 */}
                <div className="text-xs opacity-50 mt-2">
                  {step.timestamp.toLocaleTimeString()}
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* 进度条 */}
      <div className="mt-4 bg-slate-200 dark:bg-slate-700 rounded-full h-2 overflow-hidden">
        <div 
          className="h-full bg-gradient-to-r from-[#007aff] to-blue-400 transition-all duration-500 ease-out"
          style={{ 
            width: `${(steps.filter(s => s.status === 'completed').length / steps.length) * 100}%` 
          }}
        />
      </div>
    </div>
  );
}

// 紧凑版本的单步显示
export function ReActCurrentStep({ 
  step, 
  className = '' 
}: { 
  step: ReActStep | null; 
  className?: string; 
}): JSX.Element | null {
  if (!step) return null;

  const getStepIcon = (step: ReActStep) => {
    switch (step.type) {
      case 'thought':
        return <Brain className="h-3 w-3" />;
      case 'action':
        return <Wrench className="h-3 w-3" />;
      case 'observation':
        return <Eye className="h-3 w-3" />;
      case 'final':
        return <CheckCircle className="h-3 w-3" />;
      default:
        return <Clock className="h-3 w-3" />;
    }
  };

  return (
    <div className={`
      flex items-center space-x-2 px-3 py-2 
      bg-[#007aff]/10 dark:bg-blue-900/20 
      border border-[#007aff]/20 dark:border-blue-700/50 
      rounded-lg ${className}
    `}>
      <div className="flex items-center space-x-1.5">
        {getStepIcon(step)}
        <span className="text-xs font-medium text-[#007aff] dark:text-blue-400">
          {step.type === 'thought' && '思考'}
          {step.type === 'action' && '执行'}
          {step.type === 'observation' && '观察'}
          {step.type === 'final' && '完成'}
        </span>
      </div>
      
      <div className="flex-1 min-w-0">
        <div className="text-xs text-slate-600 dark:text-slate-400 truncate">
          {step.content}
        </div>
      </div>

      {step.status === 'active' && (
        <div className="w-3 h-3 border-2 border-[#007aff]/30 border-t-[#007aff] rounded-full animate-spin flex-shrink-0" />
      )}
    </div>
  );
}