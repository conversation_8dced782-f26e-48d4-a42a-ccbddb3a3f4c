import React from 'react'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { cn } from '@/lib/utils'
import { 
  SearchMetadata, 
  SEARCH_METHODS, 
  SEARCH_PERFORMANCE_THRESHOLDS 
} from '../types/search'
import { 
  Clock, 
  Target, 
  TrendingUp, 
  AlertTriangle, 
  CheckCircle, 
  Zap,
  Search,
  BarChart3
} from 'lucide-react'

interface SearchResultsHeaderProps {
  metadata?: SearchMetadata
  totalResults: number
  query: string
  className?: string
}

export function SearchResultsHeader({
  metadata,
  totalResults,
  query,
  className
}: SearchResultsHeaderProps): JSX.Element {
  if (!metadata) {
    return <></>
  }

  // 获取搜索方法信息
  const plannedMethod = SEARCH_METHODS.find((m: any) => m.id === metadata.method)
  const actualMethod = SEARCH_METHODS.find((m: any) => m.id === metadata.actualMethod)

  // 判断性能级别
  const getPerformanceLevel = (duration: number): 'fast' | 'normal' | 'slow' => {
    if (duration < SEARCH_PERFORMANCE_THRESHOLDS.FAST) return 'fast'
    if (duration < SEARCH_PERFORMANCE_THRESHOLDS.NORMAL) return 'normal'
    return 'slow'
  }

  const performanceLevel = getPerformanceLevel(metadata.duration)

  // 格式化持续时间
  const formatDuration = (ms: number): string => {
    if (ms < 1000) return `${ms}ms`
    return `${(ms / 1000).toFixed(1)}s`
  }

  // 格式化相似度百分比
  const formatSimilarity = (similarity?: number): string => {
    if (typeof similarity !== 'number') return 'N/A'
    return `${Math.round(similarity)}%`
  }

  return (
    <Card className={cn("w-full", className)}>
      <CardContent className="p-4">
        <div className="space-y-4">
          {/* 搜索查询和结果概览 */}
          <div className="flex items-start justify-between">
            <div className="flex-1 min-w-0">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 truncate">
                搜索结果: "{query}"
              </h3>
              <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                找到 {totalResults} 个结果
              </p>
            </div>
            
            {/* 搜索方法指示器 */}
            <div className="flex items-center space-x-2 ml-4">
              {metadata.fallbackOccurred && (
                <Badge 
                  variant="secondary" 
                  className="bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200"
                >
                  <AlertTriangle className="h-3 w-3 mr-1" />
                  已回退
                </Badge>
              )}
              
              <Badge 
                variant="default"
                className={cn(
                  "flex items-center space-x-1",
                  metadata.actualMethod === 'hybrid' 
                    ? "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200"
                    : "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"
                )}
              >
                {metadata.actualMethod === 'hybrid' ? (
                  <Zap className="h-3 w-3" />
                ) : (
                  <Target className="h-3 w-3" />
                )}
                <span>{actualMethod?.name || metadata.actualMethod}</span>
              </Badge>
            </div>
          </div>

          {/* 回退通知 */}
          {metadata.fallbackOccurred && (
            <div className="flex items-start space-x-3 p-3 bg-yellow-50 dark:bg-yellow-950 rounded-lg border border-yellow-200 dark:border-yellow-800">
              <AlertTriangle className="h-5 w-5 text-yellow-600 dark:text-yellow-400 mt-0.5 flex-shrink-0" />
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium text-yellow-800 dark:text-yellow-200">
                  搜索方法已自动调整
                </p>
                <p className="text-sm text-yellow-700 dark:text-yellow-300 mt-1">
                  原计划使用 <strong>{plannedMethod?.name}</strong>，但由于技术原因已切换到 <strong>{actualMethod?.name}</strong>
                </p>
              </div>
            </div>
          )}

          {/* 性能指标 */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {/* 搜索耗时 */}
            <div className="flex items-center space-x-2">
              <Clock className={cn(
                "h-4 w-4",
                performanceLevel === 'fast' && "text-green-500",
                performanceLevel === 'normal' && "text-yellow-500",
                performanceLevel === 'slow' && "text-red-500"
              )} />
              <div>
                <p className="text-xs text-gray-500 dark:text-gray-400">搜索耗时</p>
                <p className="text-sm font-medium text-gray-900 dark:text-gray-100">
                  {formatDuration(metadata.duration)}
                </p>
              </div>
            </div>

            {/* 结果总数 */}
            <div className="flex items-center space-x-2">
              <Search className="h-4 w-4 text-blue-500" />
              <div>
                <p className="text-xs text-gray-500 dark:text-gray-400">结果总数</p>
                <p className="text-sm font-medium text-gray-900 dark:text-gray-100">
                  {metadata.totalResults}
                </p>
              </div>
            </div>

            {/* 平均相似度 */}
            {metadata.averageSimilarity !== undefined && (
              <div className="flex items-center space-x-2">
                <TrendingUp className="h-4 w-4 text-purple-500" />
                <div>
                  <p className="text-xs text-gray-500 dark:text-gray-400">平均相似度</p>
                  <p className="text-sm font-medium text-gray-900 dark:text-gray-100">
                    {formatSimilarity(metadata.averageSimilarity)}
                  </p>
                </div>
              </div>
            )}

            {/* 阈值设置 */}
            {metadata.threshold !== undefined && (
              <div className="flex items-center space-x-2">
                <BarChart3 className="h-4 w-4 text-orange-500" />
                <div>
                  <p className="text-xs text-gray-500 dark:text-gray-400">相似度阈值</p>
                  <p className="text-sm font-medium text-gray-900 dark:text-gray-100">
                    {(metadata.threshold * 100).toFixed(0)}%
                  </p>
                </div>
              </div>
            )}
          </div>

          {/* 混合搜索详细指标 */}
          {metadata.actualMethod === 'hybrid' && (metadata.keywordMatches || metadata.vectorMatches) && (
            <div className="pt-3 border-t border-gray-200 dark:border-gray-700">
              <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                混合搜索详情
              </h4>
              <div className="grid grid-cols-2 gap-4">
                {metadata.keywordMatches !== undefined && (
                  <div className="flex items-center space-x-2">
                    <CheckCircle className="h-4 w-4 text-green-500" />
                    <div>
                      <p className="text-xs text-gray-500 dark:text-gray-400">关键词匹配</p>
                      <p className="text-sm font-medium text-gray-900 dark:text-gray-100">
                        {metadata.keywordMatches} 个
                      </p>
                    </div>
                  </div>
                )}
                
                {metadata.vectorMatches !== undefined && (
                  <div className="flex items-center space-x-2">
                    <Target className="h-4 w-4 text-blue-500" />
                    <div>
                      <p className="text-xs text-gray-500 dark:text-gray-400">语义匹配</p>
                      <p className="text-sm font-medium text-gray-900 dark:text-gray-100">
                        {metadata.vectorMatches} 个
                      </p>
                    </div>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* 搜索时间戳 */}
          <div className="text-xs text-gray-400 dark:text-gray-500">
            搜索时间: {new Date(metadata.timestamp).toLocaleString('zh-CN')}
          </div>
        </div>
      </CardContent>
    </Card>
  )
}