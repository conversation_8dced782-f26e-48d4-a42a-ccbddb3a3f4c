// import React, { useState, useEffect } from 'react'
// import { toast } from 'sonner'
// import { LibraryScanProgress } from './LibraryScanProgress'
// import { AdvancedFolderSelector } from './AdvancedFolderSelector'
// import { ProgressCard } from './ProgressCard'
// import { useProgress } from '../hooks/useProgress'
// import { handleError, handleApiError } from '@/lib/errorHandler'
// import { ScanProgress } from '../types/progress'
// import {
//   Plus,
//   HardDrive,
//   Folder,
//   RefreshCw,
//   Trash2,
//   Calendar,
//   Activity,
//   TrendingUp,
//   Database,
//   CheckCircle,
//   AlertCircle,
//   XCircle,
//   Settings
// } from 'lucide-react'
//
// interface ImageLibrary {
//   id: string
//   name: string
//   rootPath: string
//   createdAt: Date
//   lastScanAt: Date
//   status: 'active' | 'offline' | 'removed'
//   scanProgress: {
//     total: number
//     processed: number
//     failed: number
//   }
// }
//
// interface LibraryManagerProps {
//   onLibraryAdded?: (library: ImageLibrary) => void
// }
//
// export const LibraryManager: React.FC<LibraryManagerProps> = ({ onLibraryAdded }) => {
//   console.log('🔥 LibraryManager 组件正在渲染')
//
//   const [libraries, setLibraries] = useState<ImageLibrary[]>([])
//   const [isLoading, setIsLoading] = useState(true)
//   const [isAdding, setIsAdding] = useState(false)
//   const [showAdvancedSelector, setShowAdvancedSelector] = useState(false)
//   const { progresses, pauseProgress, resumeProgress, cancelProgress } = useProgress()
//
//   console.log('🔥 LibraryManager 当前状态:', {
//     librariesCount: libraries.length,
//     isLoading,
//     isAdding
//   })
//
//   // 调试：监听libraries状态变化
//   useEffect(() => {
//     console.log('🔍 LibraryManager.libraries 状态变化:', libraries.length, libraries)
//   }, [libraries])
//
//   useEffect(() => {
//     console.log('🔍 LibraryManager 组件挂载，开始加载库')
//     loadLibraries()
//   }, [])
//
//   const loadLibraries = async () => {
//     console.log('🔍 LibraryManager.loadLibraries 开始加载图片库')
//     try {
//       const response = await window.electronAPI.imageLibrary.getLibraries()
//       console.log('🔍 LibraryManager.loadLibraries API响应:', response)
//
//       if (response.success) {
//         // 转换API返回的数据格式到组件期望的格式
//         // 后端IPC返回的是 data 字段，不是 libraries 字段
//         const apiLibraries = (response as any).data || response.libraries || []
//         console.log('🔍 LibraryManager.loadLibraries 原始库数据:', apiLibraries)
//
//         const convertedLibraries: ImageLibrary[] = apiLibraries.map((lib: any) => ({
//           id: lib.id,
//           name: lib.name,
//           rootPath: lib.rootPath || lib.path, // 后端返回的是 rootPath 字段
//           createdAt: new Date(lib.createdAt || Date.now()),
//           lastScanAt: new Date(lib.lastScanAt || lib.lastScanTime || Date.now()),
//           status: lib.status === 'active' ? 'active' : lib.status === 'scanning' ? 'active' : 'offline',
//           scanProgress: {
//             total: lib.scanProgress?.total || 0,
//             processed: lib.scanProgress?.processed || 0,
//             failed: lib.scanProgress?.failed || 0
//           }
//         }))
//         console.log('🔍 LibraryManager.loadLibraries 转换后的库数据:', convertedLibraries)
//         setLibraries(convertedLibraries)
//       } else {
//         console.error('❌ LibraryManager.loadLibraries API调用失败:', response.error)
//         handleError(new Error(response.error || '未知错误'), '加载图片库失败')
//       }
//     } catch (error) {
//       console.error('❌ LibraryManager.loadLibraries 异常:', error)
//       handleError(error, '加载图片库失败')
//     } finally {
//       setIsLoading(false)
//     }
//   }
//
//   const handleAddLibrary = async () => {
//     setShowAdvancedSelector(true)
//   }
//
//   // 简化版添加图片库（直接选择文件夹）
//   const handleQuickAddLibrary = async () => {
//     try {
//       const result = await window.electronAPI.selectFolder()
//       if (!result.success) {
//         if (result.error) {
//           handleError(result.error, '选择文件夹失败')
//         }
//         return
//       }
//
//       // 检查用户是否取消选择
//       if (result.canceled) {
//         console.log('用户取消了文件夹选择')
//         return // 静默返回
//       }
//
//       // 直接获取文件夹路径
//       const folderPath = result.folderPath
//
//       if (!folderPath) {
//         console.log('未获取到有效的文件夹路径')
//         return
//       }
//
//       // 使用默认扫描选项直接添加
//       const defaultOptions = {
//         recursive: true,
//         includeHidden: false,
//         maxDepth: 10,
//         supportedFormats: ['.jpg', '.jpeg', '.png', '.webp', '.gif', '.bmp', '.tiff']
//       }
//
//       await handleFolderSelected(folderPath, defaultOptions)
//     } catch (error: any) {
//       handleError(error, '添加图片库失败')
//     }
//   }
//
//   const handleFolderSelected = async (folderPath: string, scanOptions: any) => {
//     console.log('LibraryManager.handleFolderSelected 调用:', { folderPath, scanOptions, type: typeof folderPath })
//
//     // 更严格的参数验证
//     if (!folderPath) {
//       handleError(new Error('文件夹路径为空'), '请选择有效的文件夹路径')
//       return
//     }
//
//     if (typeof folderPath !== 'string') {
//       handleError(new Error(`文件夹路径类型错误，期望string，实际${typeof folderPath}: ${folderPath}`), '选择的文件夹路径格式无效')
//       return
//     }
//
//     if (folderPath.trim().length === 0) {
//       handleError(new Error('文件夹路径为空字符串'), '请选择有效的文件夹路径')
//       return
//     }
//
//     try {
//       setIsAdding(true)
//       setShowAdvancedSelector(false)
//
//       const name = folderPath.split(/[\/\\]/).pop() || 'Unknown'
//       console.log('🔥 准备添加图片库:', { name, folderPath, scanOptions })
//
//       // 使用标准的addLibrary API
//       console.log('🔥 调用 addLibrary API...')
//       const response = await window.electronAPI.imageLibrary.addLibrary({
//         name: name,
//         path: folderPath,
//         description: `通过高级选择器添加的图片库`
//       })
//       console.log('🔥 addLibrary API 原始响应:', response)
//
//               console.log('📚 LibraryManager.handleFolderSelected API响应:', response)
//
//         if (response.success && response.libraryId) {
//           console.log('✅ 图片库添加成功，libraryId:', response.libraryId)
//
//           // 创建新的图片库对象
//           const newLibrary: ImageLibrary = {
//             id: response.libraryId,
//             name: name,
//             rootPath: folderPath,
//             createdAt: new Date(),
//             lastScanAt: new Date(),
//             status: 'active',
//             scanProgress: {
//               total: 0,
//               processed: 0,
//               failed: 0
//             }
//           }
//
//           console.log('📚 创建新库对象:', newLibrary)
//           setLibraries(prev => {
//             const newList = [...prev, newLibrary]
//             console.log('📚 更新库列表，新长度:', newList.length)
//             return newList
//           })
//
//           onLibraryAdded?.(newLibrary)
//           toast.success(`成功添加图片库: ${name}`)
//
//           // 自动开始扫描
//           try {
//             console.log('🔍 开始启动扫描...')
//             const scanResponse = await window.electronAPI.imageLibrary.scanLibrary(response.libraryId)
//             console.log('🔍 扫描启动响应:', scanResponse)
//             toast.info('开始扫描图片库...')
//           } catch (scanError) {
//             console.warn('❌ 启动扫描失败:', scanError)
//           }
//         } else {
//           console.error('❌ 图片库添加失败，API响应:', response)
//           handleApiError(response, '添加图片库失败')
//         }
//     } catch (error: any) {
//       console.error('添加图片库失败:', error)
//       handleError(error, '添加图片库失败')
//     } finally {
//       setIsAdding(false)
//     }
//   }
//
//   const handleRemoveLibrary = async (libraryId: string) => {
//     if (!confirm('确定要移除这个图片库吗？')) return
//
//     try {
//       const response = await window.electronAPI.imageLibrary.removeLibrary(libraryId)
//       if (response.success) {
//         setLibraries(prev => prev.filter(lib => lib.id !== libraryId))
//         toast.success('图片库已移除')
//       } else {
//         handleApiError(response, '移除图片库失败')
//       }
//     } catch (error: any) {
//       handleError(error, '移除图片库失败')
//     }
//   }
//
//   const handleRescan = async (libraryId: string) => {
//     try {
//       const response = await window.electronAPI.imageLibrary.scanLibrary(libraryId)
//       if (response.success) {
//         toast.success('开始重新扫描')
//       } else {
//         handleApiError(response, '扫描图片库失败')
//       }
//     } catch (error: any) {
//       handleError(error, '扫描图片库失败')
//     }
//   }
//
//   // 获取统计信息
//   const getLibraryStats = () => {
//     const totalImages = libraries.reduce((sum, lib) => sum + lib.scanProgress.total, 0)
//     const processedImages = libraries.reduce((sum, lib) => sum + lib.scanProgress.processed, 0)
//     const activeLibraries = libraries.filter(lib => lib.status === 'active').length
//
//     return { totalImages, processedImages, activeLibraries, totalLibraries: libraries.length }
//   }
//
//   // 获取库相关的进度
//   const getLibraryProgress = (libraryId: string): ScanProgress | undefined => {
//     return Array.from(progresses.values()).find(
//       progress => progress.type === 'scan' && (progress as ScanProgress).libraryId === libraryId
//     ) as ScanProgress | undefined
//   }
//
//   // 处理进度控制
//   const handleProgressPause = async (progressId: string) => {
//     try {
//       const result = await pauseProgress(progressId)
//       if (!result.success) {
//         toast.error(result.error || '暂停失败')
//       } else {
//         toast.success('已暂停')
//       }
//     } catch (error) {
//       toast.error('暂停失败')
//     }
//   }
//
//   const handleProgressResume = async (progressId: string) => {
//     try {
//       const result = await resumeProgress(progressId)
//       if (!result.success) {
//         toast.error(result.error || '恢复失败')
//       } else {
//         toast.success('已恢复')
//       }
//     } catch (error) {
//       toast.error('恢复失败')
//     }
//   }
//
//   const handleProgressCancel = async (progressId: string) => {
//     try {
//       const result = await cancelProgress(progressId)
//       if (!result.success) {
//         toast.error(result.error || '取消失败')
//       } else {
//         toast.success('已取消')
//       }
//     } catch (error) {
//       toast.error('取消失败')
//     }
//   }
//
//   const stats = getLibraryStats()
//
//   if (isLoading) {
//     return (
//       <div className="p-6 space-y-6">
//         {/* 加载状态的统计卡片 */}
//         <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
//           {[1, 2].map((i) => (
//             <div key={i} className="bg-white/60 dark:bg-slate-800/60 backdrop-blur-sm border border-slate-200/50 dark:border-slate-700/50 rounded-2xl p-6">
//               <div className="animate-pulse flex space-x-4">
//                 <div className="rounded-full bg-slate-200 h-12 w-12"></div>
//                 <div className="flex-1 space-y-2 py-1">
//                   <div className="h-4 bg-slate-200 rounded w-3/4"></div>
//                   <div className="h-4 bg-slate-200 rounded w-1/2"></div>
//                 </div>
//               </div>
//             </div>
//           ))}
//         </div>
//
//         <div className="bg-white/60 dark:bg-slate-800/60 backdrop-blur-sm border border-slate-200/50 dark:border-slate-700/50 rounded-2xl p-6">
//           <div className="flex items-center justify-center py-8">
//             <RefreshCw className="h-6 w-6 animate-spin mr-3 text-[#007aff]" />
//             <span className="text-slate-600 dark:text-slate-400">加载图片库中...</span>
//           </div>
//         </div>
//       </div>
//     )
//   }
//
//   return (
//     <div className="p-6 space-y-6">
//       {/* 页面标题 */}
//       <div className="space-y-2">
//         <h1 className="text-3xl font-semibold text-slate-900 dark:text-slate-100">图片库管理</h1>
//         <p className="text-slate-600 dark:text-slate-400">
//           管理您的图片库，添加新的文件夹或扫描现有内容
//         </p>
//       </div>
//
//       {/* 统计信息 */}
//       <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
//         <div className="bg-white/60 dark:bg-slate-800/60 backdrop-blur-sm border border-slate-200/50 dark:border-slate-700/50 rounded-2xl p-6">
//           <div className="flex items-center space-x-3">
//             <div className="p-3 bg-[#007aff]/10 rounded-full">
//               <Database className="h-6 w-6 text-[#007aff]" />
//             </div>
//             <div>
//               <p className="text-2xl font-semibold text-slate-900 dark:text-slate-100">{stats.totalLibraries}</p>
//               <p className="text-sm text-slate-600 dark:text-slate-400">图片库数量</p>
//             </div>
//           </div>
//         </div>
//
//         <div className="bg-white/60 dark:bg-slate-800/60 backdrop-blur-sm border border-slate-200/50 dark:border-slate-700/50 rounded-2xl p-6">
//           <div className="flex items-center space-x-3">
//             <div className="p-3 bg-green-500/10 rounded-full">
//               <CheckCircle className="h-6 w-6 text-green-500" />
//             </div>
//             <div>
//               <p className="text-2xl font-semibold text-slate-900 dark:text-slate-100">{stats.activeLibraries}</p>
//               <p className="text-sm text-slate-600 dark:text-slate-400">活跃库</p>
//             </div>
//           </div>
//         </div>
//
//         <div className="bg-white/60 dark:bg-slate-800/60 backdrop-blur-sm border border-slate-200/50 dark:border-slate-700/50 rounded-2xl p-6">
//           <div className="flex items-center space-x-3">
//             <div className="p-3 bg-purple-500/10 rounded-full">
//               <TrendingUp className="h-6 w-6 text-purple-500" />
//             </div>
//             <div>
//               <p className="text-2xl font-semibold text-slate-900 dark:text-slate-100">{stats.totalImages}</p>
//               <p className="text-sm text-slate-600 dark:text-slate-400">总图片数</p>
//             </div>
//           </div>
//         </div>
//
//         <div className="bg-white/60 dark:bg-slate-800/60 backdrop-blur-sm border border-slate-200/50 dark:border-slate-700/50 rounded-2xl p-6">
//           <div className="flex items-center space-x-3">
//             <div className="p-3 bg-blue-500/10 rounded-full">
//               <Activity className="h-6 w-6 text-blue-500" />
//             </div>
//             <div>
//               <p className="text-2xl font-semibold text-slate-900 dark:text-slate-100">{stats.processedImages}</p>
//               <p className="text-sm text-slate-600 dark:text-slate-400">已处理</p>
//             </div>
//           </div>
//         </div>
//       </div>
//
//       {/* 添加新库 */}
//       <div className="bg-white/60 dark:bg-slate-800/60 backdrop-blur-sm border border-slate-200/50 dark:border-slate-700/50 rounded-2xl p-6">
//         <div className="flex items-center justify-between mb-6">
//           <div className="flex items-center space-x-3">
//             <div className="p-2 bg-[#007aff]/10 rounded-lg">
//               <Plus className="h-5 w-5 text-[#007aff]" />
//             </div>
//             <h2 className="text-xl font-semibold text-slate-900 dark:text-slate-100">添加图片库</h2>
//           </div>
//         </div>
//
//         <div className="text-center py-8">
//           <div className="p-4 bg-slate-100/50 dark:bg-slate-700/50 rounded-2xl inline-block mb-4">
//             <Folder className="h-12 w-12 text-slate-400 dark:text-slate-500" />
//           </div>
//           <h3 className="text-lg font-medium text-slate-900 dark:text-slate-100 mb-2">添加新的图片库</h3>
//           <p className="text-slate-600 dark:text-slate-400 mb-6 max-w-md mx-auto">
//             选择包含图片的文件夹，系统将自动扫描并处理其中的图片文件
//           </p>
//           <div className="flex gap-3 justify-center">
//             <button
//               onClick={handleQuickAddLibrary}
//               disabled={isAdding}
//               className="inline-flex items-center px-6 py-3 bg-[#007aff] text-white rounded-xl hover:bg-[#0056cc] disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 font-medium"
//             >
//               {isAdding ? (
//                 <>
//                   <RefreshCw className="h-5 w-5 mr-2 animate-spin" />
//                   添加中...
//                 </>
//               ) : (
//                 <>
//                   <Plus className="h-5 w-5 mr-2" />
//                   快速添加
//                 </>
//               )}
//             </button>
//             <button
//               onClick={handleAddLibrary}
//               disabled={isAdding}
//               className="inline-flex items-center px-6 py-3 bg-slate-600 text-white rounded-xl hover:bg-slate-700 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 font-medium"
//             >
//               <Settings className="h-5 w-5 mr-2" />
//               高级设置
//             </button>
//           </div>
//         </div>
//       </div>
//
//       {/* 图片库列表 */}
//       <div className="bg-white/60 dark:bg-slate-800/60 backdrop-blur-sm border border-slate-200/50 dark:border-slate-700/50 rounded-2xl p-6">
//         <div className="flex items-center justify-between mb-6">
//           <div className="flex items-center space-x-3">
//             <div className="p-2 bg-[#007aff]/10 rounded-lg">
//               <HardDrive className="h-5 w-5 text-[#007aff]" />
//             </div>
//             <h2 className="text-xl font-semibold text-slate-900 dark:text-slate-100">图片库列表</h2>
//           </div>
//           <div className="flex items-center space-x-3">
//             <span className="px-3 py-1 text-sm bg-slate-100 dark:bg-slate-700 text-slate-600 dark:text-slate-400 rounded-lg">
//               {libraries.length} 个库
//             </span>
//             <button
//               onClick={async () => {
//                 console.log('🔥🔥🔥 手动刷新按钮被点击!')
//                 alert('刷新按钮被点击，请查看控制台')
//                 await loadLibraries()
//                 console.log('🔥🔥🔥 刷新完成!')
//               }}
//               className="px-3 py-1 text-sm bg-blue-100 text-blue-700 rounded-lg hover:bg-blue-200 transition-colors"
//             >
//               🔄 刷新
//             </button>
//             <button
//               onClick={async () => {
//                 console.log('🧪 测试API调用')
//                 try {
//                   const response = await window.electronAPI.imageLibrary.getLibraries()
//                   console.log('🧪 API响应:', response)
//                   alert(`API响应: ${JSON.stringify(response, null, 2)}`)
//                 } catch (error) {
//                   console.error('🧪 API调用失败:', error)
//                   alert(`API调用失败: ${error}`)
//                 }
//               }}
//               className="px-3 py-1 text-sm bg-red-100 text-red-700 rounded-lg hover:bg-red-200 transition-colors"
//             >
//               🧪 测试API
//             </button>
//           </div>
//         </div>
//
//         <div className="space-y-4">
//           {(() => {
//             console.log('🔍 渲染库列表，当前状态:', {
//               librariesLength: libraries.length,
//               isLoading,
//               libraries: libraries.map(lib => ({ id: lib.id, name: lib.name }))
//             })
//             return null
//           })()}
//
//           {libraries.length === 0 ? (
//             <div className="text-center py-12">
//               <div className="p-4 bg-slate-100/50 dark:bg-slate-700/50 rounded-2xl inline-block mb-4">
//                 <Database className="h-12 w-12 text-slate-400 dark:text-slate-500" />
//               </div>
//               <h3 className="text-lg font-medium text-slate-700 dark:text-slate-300 mb-2">
//                 {isLoading ? '正在加载图片库...' : '暂无图片库'}
//               </h3>
//               <p className="text-slate-500 dark:text-slate-400">
//                 {isLoading ? '请稍候...' : '点击上方"添加图片库"开始使用'}
//               </p>
//             </div>
//           ) : (
//             libraries.map(library => {
//               console.log('🔍 渲染单个库:', library)
//               const libraryProgress = getLibraryProgress(library.id)
//               return (
//                 <div key={library.id} className="space-y-4">
//                   <LibraryCard
//                     library={library}
//                     onRemove={() => handleRemoveLibrary(library.id)}
//                     onRescan={() => handleRescan(library.id)}
//                   />
//
//                   {/* 显示活动的进度 */}
//                   {libraryProgress && (
//                     <ProgressCard
//                       progress={libraryProgress}
//                       showDetails={true}
//                       allowControls={true}
//                       onPause={() => handleProgressPause(libraryProgress.id)}
//                       onResume={() => handleProgressResume(libraryProgress.id)}
//                       onCancel={() => handleProgressCancel(libraryProgress.id)}
//                     />
//                   )}
//                 </div>
//               )
//             })
//           )}
//         </div>
//       </div>
//
//       {/* 高级文件夹选择器 */}
//       <AdvancedFolderSelector
//         isVisible={showAdvancedSelector}
//         onFolderSelected={handleFolderSelected}
//         onCancel={() => setShowAdvancedSelector(false)}
//       />
//     </div>
//   )
// }
//
// // 图片库卡片组件
// interface LibraryCardProps {
//   library: ImageLibrary
//   onRemove: () => void
//   onRescan: () => void
// }
//
// const LibraryCard: React.FC<LibraryCardProps> = ({ library, onRemove, onRescan }) => {
//   const getStatusIcon = (status: string) => {
//     switch (status) {
//       case 'active':
//         return <CheckCircle className="h-5 w-5 text-green-500" />
//       case 'offline':
//         return <AlertCircle className="h-5 w-5 text-yellow-500" />
//       case 'removed':
//         return <XCircle className="h-5 w-5 text-red-500" />
//       default:
//         return <AlertCircle className="h-5 w-5 text-gray-500" />
//     }
//   }
//
//   const getStatusText = (status: string) => {
//     switch (status) {
//       case 'active': return '活跃'
//       case 'offline': return '离线'
//       case 'removed': return '已移除'
//       default: return status
//     }
//   }
//
//   const formatDate = (date: Date) => {
//     return new Date(date).toLocaleDateString('zh-CN', {
//       year: 'numeric',
//       month: 'short',
//       day: 'numeric'
//     })
//   }
//
//   const processingRate = library.scanProgress.total > 0
//     ? Math.round((library.scanProgress.processed / library.scanProgress.total) * 100)
//     : 0
//
//   return (
//     <div className="bg-white/40 dark:bg-slate-700/40 backdrop-blur-sm border border-slate-200/30 dark:border-slate-600/30 rounded-xl p-6 hover:bg-white/60 dark:hover:bg-slate-700/60 transition-all duration-200">
//       <div className="flex items-start justify-between mb-4">
//         <div className="flex-1 min-w-0">
//           <div className="flex items-center space-x-3 mb-2">
//             <div className="p-2 bg-[#007aff]/10 rounded-lg">
//               <Folder className="h-5 w-5 text-[#007aff]" />
//             </div>
//             <h3 className="text-lg font-semibold text-slate-900 dark:text-slate-100 truncate">{library.name}</h3>
//             <div className="flex items-center space-x-1">
//               {getStatusIcon(library.status)}
//               <span className="text-sm font-medium text-slate-600 dark:text-slate-400">
//                 {getStatusText(library.status)}
//               </span>
//             </div>
//           </div>
//
//           <div className="space-y-3">
//             <div className="flex items-center text-sm text-slate-600 dark:text-slate-400">
//               <Folder className="h-4 w-4 mr-2 flex-shrink-0" />
//               <span className="truncate font-mono text-xs bg-slate-100/50 dark:bg-slate-600/50 px-2 py-1 rounded">
//                 {library.rootPath}
//               </span>
//             </div>
//
//             <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
//               <div className="flex items-center space-x-2">
//                 <TrendingUp className="h-4 w-4 text-blue-500" />
//                 <span className="text-slate-600 dark:text-slate-400">总计:</span>
//                 <span className="font-semibold text-slate-900 dark:text-slate-100">{library.scanProgress.total}</span>
//               </div>
//               <div className="flex items-center space-x-2">
//                 <CheckCircle className="h-4 w-4 text-green-500" />
//                 <span className="text-slate-600 dark:text-slate-400">已处理:</span>
//                 <span className="font-semibold text-green-600">{library.scanProgress.processed}</span>
//               </div>
//               <div className="flex items-center space-x-2">
//                 <XCircle className="h-4 w-4 text-red-500" />
//                 <span className="text-slate-600 dark:text-slate-400">失败:</span>
//                 <span className="font-semibold text-red-600">{library.scanProgress.failed}</span>
//               </div>
//             </div>
//
//             <div className="flex items-center space-x-4 text-xs text-slate-500 dark:text-slate-400">
//               <div className="flex items-center space-x-1">
//                 <Calendar className="h-3 w-3" />
//                 <span>创建: {formatDate(library.createdAt)}</span>
//               </div>
//               <div className="flex items-center space-x-1">
//                 <RefreshCw className="h-3 w-3" />
//                 <span>扫描: {formatDate(library.lastScanAt)}</span>
//               </div>
//               <div className="flex items-center space-x-1">
//                 <Activity className="h-3 w-3" />
//                 <span>进度: {processingRate}%</span>
//               </div>
//             </div>
//           </div>
//         </div>
//
//         <div className="flex items-center space-x-2 ml-4">
//           <button
//             onClick={onRescan}
//             className="inline-flex items-center px-3 py-2 text-sm bg-slate-100/60 dark:bg-slate-600/60 text-slate-700 dark:text-slate-300 rounded-lg hover:bg-slate-200/60 dark:hover:bg-slate-600/80 transition-all duration-200"
//           >
//             <RefreshCw className="h-4 w-4 mr-1" />
//             重新扫描
//           </button>
//           <button
//             onClick={onRemove}
//             className="inline-flex items-center px-3 py-2 text-sm bg-red-100/60 dark:bg-red-900/60 text-red-700 dark:text-red-300 rounded-lg hover:bg-red-200/60 dark:hover:bg-red-900/80 transition-all duration-200"
//           >
//             <Trash2 className="h-4 w-4 mr-1" />
//             移除
//           </button>
//         </div>
//       </div>
//
//       {library.scanProgress.total > 0 && (
//         <div className="mt-4 pt-4 border-t border-slate-200/30 dark:border-slate-600/30">
//           <LibraryScanProgress libraryId={library.id} />
//         </div>
//       )}
//     </div>
//   )
// }