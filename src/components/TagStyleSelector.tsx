import { FC } from 'react'
import { EnhancedTag } from './ui/enhanced-tag'

interface TagStyleSelectorProps {
  onStyleChange: (style: 'default' | 'blur-text' | 'gradient' | 'pattern') => void
  currentStyle: 'default' | 'blur-text' | 'gradient' | 'pattern'
}

export function TagStyleSelector({ onStyleChange, currentStyle }: TagStyleSelectorProps) {
  const sampleTag = {
    name: '示例标签',
    count: 12,
    category: 'scene'
  }

  const styles = [
    { key: 'default', name: '默认样式' },
    { key: 'blur-text', name: '背景文案' },
    { key: 'gradient', name: '渐变效果' },
    { key: 'pattern', name: '图案纹理' }
  ] as const

  return (
    <div className="bg-white/60 dark:bg-slate-800/60 backdrop-blur-sm border border-slate-200/50 dark:border-slate-700/50 rounded-2xl p-6">
      <h3 className="text-lg font-semibold text-slate-900 dark:text-slate-100 mb-4">
        标签样式选择
      </h3>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {styles.map(({ key, name }) => (
          <div key={key} className="space-y-2">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium text-slate-700 dark:text-slate-300">
                {name}
              </span>
              <button
                onClick={() => onStyleChange(key)}
                className={`px-3 py-1 text-xs rounded-lg transition-colors ${
                  currentStyle === key
                    ? 'bg-[#007aff] text-white'
                    : 'bg-slate-200 dark:bg-slate-700 text-slate-600 dark:text-slate-400 hover:bg-slate-300 dark:hover:bg-slate-600'
                }`}
              >
                {currentStyle === key ? '已选择' : '选择'}
              </button>
            </div>
            
            <div className="max-w-xs">
              <EnhancedTag
                {...sampleTag}
                variant={key}
                onClick={() => onStyleChange(key)}
              />
            </div>
          </div>
        ))}
      </div>
    </div>
  )
}