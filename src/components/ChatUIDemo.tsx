/**
 * Chat UI Demo Component
 * Demonstrates the enhanced chat interface with image tool interactions
 */

import React, { useState } from 'react';
import { EnhancedImageUpload } from './EnhancedImageUpload';
import { EnhancedToolResultDisplay } from './EnhancedToolResultDisplay';
import { ImageViewer } from './ImageViewer';
import { ToolInteractionPanel } from './ToolInteractionPanel';
import { ChatMessageDisplay } from './ChatMessageDisplay';
import { Sparkles, MessageCircle } from 'lucide-react';
import { ToolCallResponse } from '../types/tools';

interface ImageData {
  url: string;
  base64: string;
  file: File;
}

interface SearchResult {
  id: string;
  url: string;
  title: string;
  similarity: number;
  tags: string[];
  description: string;
}

interface SearchParameters {
  description: string;
  limit: number;
  threshold: number;
}

export function ChatUIDemo(): JSX.Element {
  const [selectedImage, setSelectedImage] = useState<ImageData | null>(null);
  const [showImageViewer, setShowImageViewer] = useState<boolean>(false);
  const [viewerImageUrl, setViewerImageUrl] = useState<string>('');
  const [viewerImageData, setViewerImageData] = useState<ImageData | null>(null);

  const mockAnalysisResult: ToolCallResponse = {
    toolCallId: 'analysis-1',
    result: {
      success: true,
      data: {
        description: '一只橙色的猫咪坐在阳光明媚的窗台上，背景是绿色的植物',
        tags: ['猫', '橙色', '窗台', '阳光', '植物', '室内', '宠物'],
        structuredData: {
          objects: [
            { name: '猫', confidence: 0.95, attributes: ['橙色', '坐着'] },
            { name: '窗台', confidence: 0.88, attributes: ['木制', '宽敞'] },
            { name: '植物', confidence: 0.82, attributes: ['绿色', '茂盛'] }
          ],
          colors: ['橙色', '绿色', '棕色', '白色'],
          scene: '室内家居环境',
          mood: '温馨平静'
        },
        confidence: 0.92,
        analysisTime: 1450
      },
      metadata: {
        executionTime: 1450,
        toolName: 'analyze_image',
        parameters: { includeStructuredData: true }
      }
    }
  };

  const mockSearchResult: ToolCallResponse = {
    toolCallId: 'search-1',
    result: {
      success: true,
      data: {
        results: [
          {
            id: 'img_001',
            url: '/api/placeholder/300/300',
            title: '橙色猫咪在窗边',
            similarity: 0.89,
            tags: ['猫', '窗台', '橙色'],
            description: '一只可爱的橙色猫咪'
          },
          {
            id: 'img_002',
            url: '/api/placeholder/300/300',
            title: '阳光下的猫咪',
            similarity: 0.76,
            tags: ['猫', '阳光', '室内'],
            description: '享受阳光的猫咪'
          },
          {
            id: 'img_003',
            url: '/api/placeholder/300/300',
            title: '窗台上的绿植',
            similarity: 0.68,
            tags: ['窗台', '植物', '绿色'],
            description: '窗台上的绿色植物'
          }
        ],
        total: 12
      },
      metadata: {
        executionTime: 2100,
        toolName: 'find_similar_images_by_description',
        parameters: { description: '橙色猫咪在窗台', limit: 15, threshold: 0.6 }
      }
    }
  };

  const mockMessages = [
    {
      id: 1,
      type: 'user' as const,
      content: '请分析这张图片并找到相似的图片',
      timestamp: new Date(),
      image: {
        url: '/api/placeholder/200/200',
        description: '用户上传的猫咪图片'
      }
    },
    {
      id: 2,
      type: 'assistant' as const,
      content: '我已经分析了您的图片，这是一张很温馨的猫咪照片。我发现了一只橙色的猫咪坐在窗台上，背景有绿色植物。同时我也为您找到了一些相似的图片。',
      timestamp: new Date(),
      toolResults: [mockAnalysisResult, mockSearchResult]
    }
  ];

  const handleImageSelect = (imageData: ImageData): void => {
    setSelectedImage(imageData);
    console.log('Image selected:', imageData.file.name);
  };

  const handleQuickAction = (action: 'analyze' | 'search_similar' | 'search_tags', imageData: ImageData): void => {
    console.log('Quick action:', action, 'for image:', imageData.file.name);
  };

  const handleImageClick = (imageUrl: string, imageData: any): void => {
    setViewerImageUrl(imageUrl);
    setViewerImageData(imageData as ImageData);
    setShowImageViewer(true);
  };

  const handleRefineSearch = (parameters: Record<string, unknown>): void => {
    console.log('Refine search with parameters:', parameters);
  };

  const handleSaveResult = (result: Record<string, unknown>): void => {
    console.log('Save result:', result);
  };

  const handleSaveResults = (results: SearchResult[]): void => {
    console.log('Save results:', results);
  };

  const handleShareResults = (results: SearchResult[]): void => {
    console.log('Share results:', results);
  };

  const handleExportResults = (results: SearchResult[], format: 'json' | 'csv' | 'pdf'): void => {
    console.log('Export results:', results, 'as', format);
  };

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-8">
      <div className="text-center space-y-2">
        <div className="flex items-center justify-center space-x-2">
          <Sparkles className="h-8 w-8 text-blue-600" />
          <h1 className="text-3xl font-bold text-slate-900 dark:text-slate-100">
            增强聊天界面演示
          </h1>
        </div>
        <p className="text-slate-600 dark:text-slate-400">
          体验全新的图像工具交互界面
        </p>
      </div>

      <div className="space-y-4">
        <h2 className="text-xl font-semibold text-slate-900 dark:text-slate-100 flex items-center">
          <MessageCircle className="h-5 w-5 mr-2" />
          增强图像上传
        </h2>
        <div className="bg-white/60 dark:bg-slate-800/60 backdrop-blur-sm border border-slate-200/50 dark:border-slate-700/50 rounded-xl p-6">
          <EnhancedImageUpload
            onImageSelect={handleImageSelect}
            onQuickAction={handleQuickAction}
            showQuickActions={true}
            maxFileSize={10}
          />
        </div>
      </div>

      <div className="space-y-4">
        <h2 className="text-xl font-semibold text-slate-900 dark:text-slate-100">
          增强聊天消息显示
        </h2>
        <div className="bg-white/60 dark:bg-slate-800/60 backdrop-blur-sm border border-slate-200/50 dark:border-slate-700/50 rounded-xl p-6">
          <div className="space-y-6">
            {mockMessages.map((message) => (
              <ChatMessageDisplay
                key={message.id}
                message={message}
                onImageClick={handleImageClick}
                onRefineSearch={handleRefineSearch}
                onSaveResult={handleSaveResult}
                useEnhancedDisplay={true}
              />
            ))}
          </div>
        </div>
      </div>

      <div className="space-y-4">
        <h2 className="text-xl font-semibold text-slate-900 dark:text-slate-100">
          工具交互控制面板
        </h2>
        <ToolInteractionPanel
          onRefineSearch={handleRefineSearch}
          onSaveResults={handleSaveResults}
          onShareResults={handleShareResults}
          onExportResults={handleExportResults}
          currentResults={mockSearchResult.result.data.results}
          searchParameters={mockSearchResult.result.metadata?.parameters}
          isLoading={false}
        />
      </div>

      <div className="space-y-4">
        <h2 className="text-xl font-semibold text-slate-900 dark:text-slate-100">
          独立工具结果显示
        </h2>
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div>
            <h3 className="text-lg font-medium text-slate-800 dark:text-slate-200 mb-3">
              图像分析结果
            </h3>
            <EnhancedToolResultDisplay
              toolResult={mockAnalysisResult}
              onRefineSearch={handleRefineSearch}
              onSaveResult={handleSaveResult}
            />
          </div>
          <div>
            <h3 className="text-lg font-medium text-slate-800 dark:text-slate-200 mb-3">
              图像搜索结果
            </h3>
            <EnhancedToolResultDisplay
              toolResult={mockSearchResult}
              onImageClick={handleImageClick}
              onRefineSearch={handleRefineSearch}
              onSaveResult={handleSaveResult}
            />
          </div>
        </div>
      </div>

      <div className="bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 rounded-xl p-6">
        <h2 className="text-xl font-semibold text-slate-900 dark:text-slate-100 mb-4">
          ✨ 新功能亮点
        </h2>
      </div>

      {showImageViewer && viewerImageUrl && (
        <ImageViewer
          isOpen={showImageViewer}
          imageUrl={viewerImageUrl}
          imageData={viewerImageData}
          onClose={() => setShowImageViewer(false)}
        />
      )}
    </div>
  );
}