import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button.tsx'
import appIcon from './assets/logo.png'
import { GalleryPage } from './pages/GalleryPage.tsx'
import { TagsPage } from './pages/TagsPage.tsx'
import { SearchPage } from './pages/SearchPage.tsx'
// import { ChatPage } from './pages/ChatPage.tsx'
// import { SettingsPage } from './pages/SettingsPage.tsx'
// import { TestPage } from './pages/TestPage.tsx'
import ToolTestPage from './pages/ToolTestPage.tsx'
import ImageLibraryPage from './pages/ImageLibraryPage.tsx'
import DatabaseQueryPage from './pages/DatabaseQueryPage.tsx'
import { ImageRecord } from './types/database'
import {
  Search,
  MessageCircle,
  Image as ImageIcon,
  // Zap,
  Shield,
  Tag,
  Settings,
  Moon,
  Sun,
  ChevronRight,
  ChevronLeft,
  TestTube,
  FolderOpen,
  Database,
} from 'lucide-react'
import { cn } from './lib/utils.ts'
import { Toaster } from 'sonner'
import configManager from './lib/ConfigManager.ts'
import './App.css'

// 页面配置 - 单一数据源
const PAGE_CONFIGS = {
  imageLibrary: { id: 'imageLibrary', label: '图片库管理', icon: FolderOpen, isDefault: true, isDeveloperOnly: false },
  gallery: { id: 'gallery', label: '图片浏览', icon: ImageIcon, isDeveloperOnly: false },
  search: { id: 'search', label: '搜索', icon: Search, isDeveloperOnly: false },
  tags: { id: 'tags', label: '标签浏览', icon: Tag, isDeveloperOnly: false },
  chat: { id: 'chat', label: '图文问答', icon: MessageCircle, isDeveloperOnly: false },
  test: { id: 'test', label: '系统测试', icon: TestTube, isDeveloperOnly: true },
  tooltest: { id: 'tooltest', label: '工具测试', icon: Shield, isDeveloperOnly: true },
  databaseQuery: { id: 'databaseQuery', label: '数据库查询', icon: Database, isDeveloperOnly: true },
  settings: { id: 'settings', label: '设置', icon: Settings, isDeveloperOnly: false }
} as const

// 从配置中提取页面类型
type PageType = keyof typeof PAGE_CONFIGS

function App(): JSX.Element {
  const [currentPage, setCurrentPage] = useState<PageType>('imageLibrary')
  const [isSidebarCollapsed, setIsSidebarCollapsed] = useState<boolean>(false)
  const [uploadedImages, setUploadedImages] = useState<ImageData[]>([])
  const [isDarkMode, setIsDarkMode] = useState<boolean>(false)

  const [isDeveloperMode, setIsDeveloperMode] = useState<boolean>(true)

  useEffect(() => {
    // initializeSeparatedChatService()
    // 立即加载图片数据，不等待服务初始化
    // loadImagesFromDatabase()

    // 初始化开发者模式状态
    setIsDeveloperMode(configManager.developerConfig.enableConsoleLog)

    // 监听配置变更
    const unsubscribe = configManager.onConfigChange((type, config) => {
      if (type === 'developer') {
        setIsDeveloperMode(config.enableConsoleLog)
        // 如果开发者模式被关闭且当前页面是开发者专用页面，切换到默认页面
        if (!config.enableConsoleLog && PAGE_CONFIGS[currentPage]?.isDeveloperOnly) {
          setCurrentPage('imageLibrary')
        }
      }
    })

    return unsubscribe
  }, [currentPage])

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const toggleDarkMode = (): void => {
    setIsDarkMode(!isDarkMode)
    document.documentElement.classList.toggle('dark')
  }

  // 侧边栏菜单项 - 从配置自动生成，根据开发者模式过滤
  const sidebarItems = Object.values(PAGE_CONFIGS)
    // .filter(config => !config.isDeveloperOnly || isDeveloperMode)
    .map(config => ({
      id: config.id as PageType,
      label: config.label,
      icon: config.icon
    }))

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-900 dark:to-slate-800 transition-all duration-500 font-inter">
      {/* Top Header */}
      <header className="fixed top-0 left-0 right-0 z-50 bg-white/80 dark:bg-slate-900/80 backdrop-blur-md border-b border-slate-200/50 dark:border-slate-700/50">
        <div className="flex items-center justify-between px-6 py-3">
          {/* Logo */}
          <div className="flex items-center space-x-3">
            <div className="shimmer-logo">
              <img src={appIcon} alt="PicMind Logo" className="w-8 h-8" />
            </div>
            <div>
              <h1 className="text-lg font-semibold text-slate-900 dark:text-white tracking-tight">
                PicMind
              </h1>
            </div>
          </div>

          {/* Search Bar */}
          <div className="flex-1 max-w-2xl mx-8">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400" />
              <input
                type="text"
                placeholder="搜索图片..."
                className="w-full pl-10 pr-4 py-2 bg-slate-100/60 dark:bg-slate-800/60 
                           border border-slate-200/50 dark:border-slate-700/50 rounded-lg
                           text-slate-900 dark:text-white placeholder-slate-500 dark:placeholder-slate-400
                           focus:outline-none focus:ring-2 focus:ring-[#007aff]/50 focus:border-[#007aff]
                           transition-all duration-200"
              />
            </div>
          </div>

          {/* Right Actions */}
          <div className="flex items-center space-x-3">
            <Button
              variant="ghost"
              size="icon"
              onClick={toggleDarkMode}
              className="h-8 w-8 rounded-lg bg-slate-100/60 dark:bg-slate-800/60
                         hover:scale-105 transition-transform"
            >
              {isDarkMode ? <Sun className="h-4 w-4 text-yellow-500" /> :
                <Moon className="h-4 w-4 text-[#007aff]" />}
            </Button>
          </div>
        </div>
      </header>

      {/* Main Layout */}
      <div className="flex pt-16 h-screen">
        {/* Sidebar */}
        <aside className={cn(
          `fixed top-16 left-0 z-40 h-[calc(100vh-4rem)] bg-white/70 dark:bg-slate-900/70 backdrop-blur-md 
             border-r border-slate-200/50 dark:border-slate-700/50 shadow-lg transition-all duration-300`,
          isSidebarCollapsed ? 'w-16' : 'w-60'
        )}>
          <div className="h-full flex flex-col">
            {/* Navigation */}
            <nav className="flex-1 px-3 py-4 space-y-1">
              {sidebarItems.map((item) => {
                const Icon = item.icon
                return (
                  <button
                    key={item.id}
                    onClick={() => setCurrentPage(item.id)}
                    className={cn(
                      `group flex items-center w-full py-2.5 px-3 rounded-xl font-medium text-sm
                         transition-all duration-200 overflow-hidden`,
                      isSidebarCollapsed ? 'justify-center' : 'space-x-3',
                      currentPage === item.id
                        ? 'bg-[#007aff]/10 text-[#007aff] dark:bg-[#007aff]/20 dark:text-[#4da6ff] shadow-sm'
                        : 'text-slate-600 dark:text-slate-400 hover:bg-slate-100/60 dark:hover:bg-slate-800/60'
                    )}
                    title={isSidebarCollapsed ? item.label : ''}
                  >
                    <Icon className="h-5 w-5 shrink-0 transition-transform group-hover:scale-105" />
                    {!isSidebarCollapsed && <span className="truncate">{item.label}</span>}
                  </button>
                )
              })}
            </nav>

            {/* Bottom Section */}
            <div className="px-3 py-4 border-t border-slate-200/50 dark:border-slate-700/50">
              <button
                onClick={() => setIsSidebarCollapsed(!isSidebarCollapsed)}
                className="w-full flex items-center justify-center h-9 px-3
                             rounded-lg text-slate-500 dark:text-slate-400
                             hover:bg-slate-100/60 dark:hover:bg-slate-800/60 transition-all duration-200"
              >
                {isSidebarCollapsed ? <ChevronRight className="h-5 w-5" /> : <ChevronLeft className="h-5 w-5" />}
              </button>
            </div>
          </div>
        </aside>

        {/* Main Content */}
        <main
          className={`flex-1 overflow-auto transition-all duration-300 ${isSidebarCollapsed ? 'ml-16' : 'ml-60'}`}>
          {/* 内容页面根据 currentPage 渲染 */}
          {currentPage === PAGE_CONFIGS.gallery.id && (
            <GalleryPage />
          )}
          {currentPage === PAGE_CONFIGS.search.id && (
            <SearchPage />
          )}
          {currentPage === PAGE_CONFIGS.imageLibrary.id && (
            <ImageLibraryPage />
          )}
          {currentPage === PAGE_CONFIGS.tags.id && (
            <TagsPage />
          )}
          {currentPage === PAGE_CONFIGS.chat.id && (
            // <ChatPage
            //   availableImages={uploadedImages}
            // />
              <div></div>
          )}
          {currentPage === PAGE_CONFIGS.test.id && isDeveloperMode && (
              // <TestPage />
              <div></div>
          )}
          {currentPage === PAGE_CONFIGS.tooltest.id && isDeveloperMode && (
              <ToolTestPage />
          )}
          {currentPage === PAGE_CONFIGS.databaseQuery.id && (
            <DatabaseQueryPage />
          )}
          {currentPage === PAGE_CONFIGS.settings.id && (
            // <SettingsPage
            //   isDarkMode={isDarkMode}
            //   onToggleDarkMode={toggleDarkMode}
            //   isDeveloperMode={isDeveloperMode}
            //   onDeveloperModeChange={setIsDeveloperMode}
            // />
              <div></div>
          )}
        </main>
      </div>
      <Toaster />
    </div>
  )
}

export default App
