// 类型定义
// export interface ImageData {
//   id: string | number
//   url: string
//   title: string
//   tags: string[]
//   description: string
//   uploadTime: string
//   location: string
//   camera: string
//   colors: string[]
//   aiAnalysis: boolean
//   similarity: number
//   fileSize: string
//   resolution: string
//   exif: {
//     iso: number
//     aperture: string
//     shutterSpeed: string
//     focalLength: string
//   }
// }

// ReAct步骤接口
export interface ReActStep {
  id: string;
  type: 'thought' | 'action' | 'observation' | 'final';
  content: string;
  timestamp: Date;
  status: 'pending' | 'active' | 'completed' | 'error';
  toolName?: string;
  toolParams?: Record<string, unknown>;
  toolResult?: any;
  executionTime?: number;
}

export interface ChatMessage {
  id: number
  type: 'user' | 'assistant'
  content: string
  timestamp: Date
  image?: {
    url: string
    description?: string
  }
  isStreaming?: boolean // 新增：标识是否为流式消息
  isError?: boolean // 新增：标识是否为错误消息
  reactSteps?: ReActStep[] // 新增：ReAct步骤
  currentReActStep?: ReActStep // 新增：当前正在执行的ReAct步骤
  toolResults?: Array<{
    toolCallId: string
    result: {
      success: boolean
      data?: any
      error?: string
      metadata?: {
        executionTime?: number
        toolName?: string
        parameters?: any
      }
    }
  }>
}

export interface StatsData {
  totalImages: number
  analyzedImages: number
  storageUsed: string
  processingSpeed: string
  totalTags: number
  uniqueLocations: number
}

// 搜索建议
export const searchSuggestions: string[] = [
  '去年拍的蓝天白云',
  '有咖啡的照片',
  '城市夜景',
  '人物肖像',
  '自然风景',
  '美食照片',
  '建筑摄影',
  '动物照片',
  '生活记录',
  '黑白照片',
  '日落时分',
  '雨天的照片',
  '春天的花朵',
  '海边风景',
  '古建筑',
  '街头摄影',
  '微距摄影',
  '星空银河',
  '雪景照片',
  '节日庆典'
]

// 热门标签
export const popularTags: string[] = [
  '蓝天白云', '城市夜景', '美食照片', '人物肖像', 
  '自然风景', '建筑摄影', '动物照片', '生活记录',
  '春天花朵', '海边日落', '古典建筑', '街头摄影',
  '微距特写', '星空银河', '雪景冬日', '节日庆典'
]

// AI分析模拟响应
export const aiResponses: Record<string, string[]> = {
  '这是什么': [
    '根据图像分析，这是一张风景照片，包含了蓝天、白云等自然元素。图片色彩丰富，构图优美。',
    '这是一张城市夜景照片，展现了现代都市的繁华景象，灯光璀璨，建筑宏伟。',
    '这是一张美食照片，展示了精心制作的料理，色彩搭配和谐，摆盘精致。',
    '这是一张人物肖像照片，光线柔和，表情自然，构图专业。'
  ],
  '拍摄地点': [
    '根据图片的GPS信息和视觉特征分析，这张照片可能拍摄于城市公园或开阔的户外场所。',
    '从建筑风格和环境特征来看，这张照片应该拍摄于现代化的都市中心区域。',
    '根据背景环境和光线条件分析，这张照片可能拍摄于室内或有遮蔽的环境中。',
    '从自然环境特征来看，这张照片拍摄于户外自然景区或公园。'
  ],
  '相似图片': [
    '我找到了3张与此图片相似的照片，它们都包含相似的色彩搭配和构图元素。',
    '在您的相册中有5张类似风格的照片，主要相似点在于拍摄主题和色调。',
    '发现了4张相关图片，它们在拍摄时间、地点或主题上有相似性。',
    '找到了6张相似的照片，它们在视觉风格和内容主题上有共同点。'
  ],
  '图片详情': [
    '这张图片拍摄于2024年1月15日，使用iPhone 15 Pro拍摄，分辨率为4032x3024，文件大小约2.5MB。',
    '照片信息：拍摄时间2024年1月14日，设备Canon EOS R5，ISO 800，光圈f/4.0，快门1/60s。',
    '图片详情：Sony A7R IV拍摄，分辨率7952x5304，文件大小3.2MB，焦距50mm。',
    '拍摄参数：Fujifilm X-T5，ISO 200，光圈f/2.8，快门1/500s，焦距35mm。'
  ]
}

// 聊天历史模拟
export const initialChatHistory: ChatMessage[] = [
  {
    id: 1,
    type: 'assistant',
    content: '您好！我是您的本地多模态智能助理。您可以向我询问关于图片的任何问题，比如图片内容、拍摄时间、相似图片等。我会基于AI视觉分析为您提供详细解答。',
    timestamp: new Date(Date.now() - 300000) // 5分钟前
  },
  {
    id: 2,
    type: 'user',
    content: '这张蓝天白云的照片是什么时候拍的？',
    timestamp: new Date(Date.now() - 240000) // 4分钟前
  },
  {
    id: 3,
    type: 'assistant',
    content: '根据图片的EXIF信息，这张蓝天白云的照片拍摄于2024年1月15日下午2:30。照片显示了晴朗的天空和蓬松的白云，是一个天气很好的日子。拍摄设备是iPhone 15 Pro，使用了24mm焦距，光圈f/2.8。',
    timestamp: new Date(Date.now() - 230000) // 约4分钟前
  }
]

// 统计数据
export const statsData: StatsData = {
  totalImages: 8,
  analyzedImages: 8,
  storageUsed: '2.3GB',
  processingSpeed: '1.2s/张',
  totalTags: 45,
  uniqueLocations: 6
} 