import React, { useState, useEffect, useCallback } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { ChevronLeft, ChevronRight, Database, RefreshCw, AlertCircle } from 'lucide-react'
import { trpcClient } from '@/lib/trpcClient'
import { cn } from '@/lib/utils'

interface TableData {
  success: boolean
  data?: any[]
  total?: number
  page?: number
  pageSize?: number
  totalPages?: number
  error?: string
}

interface TablesResponse {
  success: boolean
  tables?: string[]
  error?: string
}

export function DatabaseQueryPage(): JSX.Element {
  const [tables, setTables] = useState<string[]>([])
  const [activeTable, setActiveTable] = useState<string>('')
  const [tableData, setTableData] = useState<TableData>({ success: false })
  const [currentPage, setCurrentPage] = useState<number>(1)
  const [pageSize] = useState<number>(20)
  const [loading, setLoading] = useState<boolean>(false)
  const [tablesLoading, setTablesLoading] = useState<boolean>(true)
  const [error, setError] = useState<string>('')

  // 加载所有表名
  const loadTables = useCallback(async (): Promise<void> => {
    setTablesLoading(true)
    setError('')

    try {
      const response: TablesResponse = await trpcClient.database.getAllTables.query()

      if (response.success && response.tables) {
        setTables(response.tables)
        if (response.tables.length > 0 && !activeTable) {
          setActiveTable(response.tables[0])
        }
      } else {
        setError(response.error || '获取表名失败')
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : '获取表名失败')
    } finally {
      setTablesLoading(false)
    }
  }, [activeTable])

  // 加载表数据
  const loadTableData = useCallback(async (tableName: string, page: number = 1): Promise<void> => {
    if (!tableName) return

    setLoading(true)
    setError('')

    try {
      const response = await trpcClient.database.getTableData.query({
        tableName,
        page,
        pageSize
      })
      setTableData(response)

      if (!response.success) {
        setError(response.error || '查询数据失败')
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : '查询数据失败')
      setTableData({ success: false, error: err instanceof Error ? err.message : '查询数据失败' })
    } finally {
      setLoading(false)
    }
  }, [pageSize])

  // 处理表切换
  const handleTableChange = (tableName: string): void => {
    setActiveTable(tableName)
    setCurrentPage(1)
    loadTableData(tableName, 1)
  }

  // 处理分页
  const handlePageChange = (page: number): void => {
    setCurrentPage(page)
    loadTableData(activeTable, page)
  }

  // 刷新数据
  const handleRefresh = (): void => {
    loadTableData(activeTable, currentPage)
  }

  // 渲染表格内容
  const renderTableContent = (): JSX.Element => {
    if (loading) {
      return (
        <div className="flex items-center justify-center py-12">
          <RefreshCw className="h-6 w-6 animate-spin text-blue-500 mr-2" />
          <span className="text-slate-600 dark:text-slate-400">加载中...</span>
        </div>
      )
    }

    if (!tableData.success || !tableData.data || tableData.data.length === 0) {
      return (
        <div className="flex items-center justify-center py-12">
          <div className="text-center">
            <Database className="h-12 w-12 text-slate-400 mx-auto mb-4" />
            <p className="text-slate-600 dark:text-slate-400">暂无数据</p>
          </div>
        </div>
      )
    }

    const data = tableData.data
    const columns = data.length > 0 ? Object.keys(data[0]) : []

    return (
      <div className="space-y-4">
        <div className="overflow-x-auto border rounded-lg">
          <Table>
            <TableHeader>
              <TableRow>
                {columns.map((column) => (
                  <TableHead key={column} className="font-semibold min-w-[120px] sticky-col">
                    <div className="truncate" title={column}>
                      {column}
                    </div>
                  </TableHead>
                ))}
              </TableRow>
            </TableHeader>
            <TableBody>
              {data.map((row, index) => (
                <TableRow key={index}>
                  {columns.map((column) => {
                    const value = row[column]
                    const displayValue = typeof value === 'object'
                      ? JSON.stringify(value)
                      : String(value ?? '')

                    return (
                      <TableCell key={column} className="font-mono text-sm min-w-[120px] max-w-[300px]">
                        <div
                          className="truncate cursor-help"
                          title={displayValue}
                        >
                          {displayValue}
                        </div>
                      </TableCell>
                    )
                  })}
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>

        {/* 分页控件 */}
        {tableData.totalPages && tableData.totalPages > 1 && (
          <div className="flex flex-col sm:flex-row items-center justify-between gap-4">
            <div className="text-sm text-slate-600 dark:text-slate-400">
              共 {tableData.total} 条记录，第 {tableData.page} / {tableData.totalPages} 页
            </div>
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => handlePageChange(currentPage - 1)}
                disabled={currentPage <= 1}
              >
                <ChevronLeft className="h-4 w-4" />
                <span className="hidden sm:inline">上一页</span>
              </Button>
              <div className="flex items-center space-x-1">
                {Array.from({ length: Math.min(3, tableData.totalPages || 1) }, (_, i) => {
                  const totalPages = tableData.totalPages || 1
                  let page: number
                  if (totalPages <= 3) {
                    page = i + 1
                  } else if (currentPage <= 2) {
                    page = i + 1
                  } else if (currentPage >= totalPages - 1) {
                    page = totalPages - 2 + i
                  } else {
                    page = currentPage - 1 + i
                  }

                  return (
                    <Button
                      key={page}
                      variant={currentPage === page ? "default" : "outline"}
                      size="sm"
                      onClick={() => handlePageChange(page)}
                      className="w-8 h-8 p-0"
                    >
                      {page}
                    </Button>
                  )
                })}
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={() => handlePageChange(currentPage + 1)}
                disabled={currentPage >= (tableData.totalPages || 1)}
              >
                <span className="hidden sm:inline">下一页</span>
                <ChevronRight className="h-4 w-4" />
              </Button>
            </div>
          </div>
        )}
      </div>
    )
  }

  useEffect(() => {
    loadTables()
  }, [loadTables])

  useEffect(() => {
    if (activeTable) {
      loadTableData(activeTable, 1)
    }
  }, [activeTable, loadTableData])

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-900 dark:to-slate-800 p-6">
      <div className="max-w-7xl mx-auto space-y-6">
        {/* 页面标题 */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-slate-900 dark:text-white">
              数据库查询
            </h1>
            <p className="text-slate-600 dark:text-slate-400 mt-2">
              查看和管理数据库表数据
            </p>
          </div>
          <Button onClick={handleRefresh} disabled={loading || !activeTable}>
            <RefreshCw className={cn("h-4 w-4 mr-2", loading && "animate-spin")} />
            刷新
          </Button>
        </div>

        {/* 错误提示 */}
        {error && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {/* 主要内容 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Database className="h-5 w-5 mr-2" />
              数据表
            </CardTitle>
          </CardHeader>
          <CardContent>
            {tablesLoading ? (
              <div className="flex items-center justify-center py-12">
                <RefreshCw className="h-6 w-6 animate-spin text-blue-500 mr-2" />
                <span className="text-slate-600 dark:text-slate-400">加载表列表...</span>
              </div>
            ) : tables.length === 0 ? (
              <div className="flex items-center justify-center py-12">
                <div className="text-center">
                  <Database className="h-12 w-12 text-slate-400 mx-auto mb-4" />
                  <p className="text-slate-600 dark:text-slate-400">未找到数据表</p>
                </div>
              </div>
            ) : (
              <Tabs value={activeTable} onValueChange={handleTableChange}>
                <TabsList className="flex flex-wrap gap-1 h-auto p-2">
                  {tables.map((table) => (
                    <TabsTrigger
                      key={table}
                      value={table}
                      className="text-sm whitespace-nowrap flex-shrink-0"
                    >
                      <span className="truncate max-w-[120px]" title={table}>
                        {table}
                      </span>
                      {tableData.success && activeTable === table && tableData.total !== undefined && (
                        <Badge variant="secondary" className="ml-2 text-xs">
                          {tableData.total}
                        </Badge>
                      )}
                    </TabsTrigger>
                  ))}
                </TabsList>

                {tables.map((table) => (
                  <TabsContent key={table} value={table} className="mt-6">
                    {renderTableContent()}
                  </TabsContent>
                ))}
              </Tabs>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  )
}

export default DatabaseQueryPage