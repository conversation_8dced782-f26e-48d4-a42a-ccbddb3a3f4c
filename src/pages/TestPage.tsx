// import React, { useState, useEffect } from 'react'
// import { ImageGrid } from '@/components/ImageGrid.tsx'
// import { TagStyleShowcase } from '@/components/TagStyleShowcase'
// import { TRPCExample } from '@/components/TRPCExample'
// import { databaseService } from '../lib/database'
// import {
//   Database,
//   Image as ImageIcon,
//   Tag,
//   RefreshCw,
//   Search,
//   Plus,
//   Trash2,
//   AlertCircle,
//   CheckCircle,
//   Loader2,
//   BarChart3,
//   Grid3X3,
//   Upload,
//   TestTube,
//   Settings,
//   X,
//   Palette,
//   Network
// } from 'lucide-react'
// import { ImageRecord } from '../types/database'
// import { ImageData } from '../data/mockData'

// interface DatabaseStats {
//   connectionStatus: boolean | null
//   totalImages: number
//   totalTags: number
//   loading: boolean
//   error?: string
//   diagnostics?: {
//     serverReachable?: boolean
//     authStatus?: string
//     collections?: string[]
//     lastTestTime?: number
//     responseTime?: number
//     milvusVersion?: string
//     serverInfo?: any
//   }
// }

// export function TestPage(): JSX.Element {
//   const [stats, setStats] = useState<DatabaseStats>({
//     connectionStatus: null,
//     totalImages: 0,
//     totalTags: 0,
//     loading: false
//   })

//   // 图片数据库相关状态
//   const [imageResults, setImageResults] = useState<ImageData[]>([])
//   const [imageLimit, setImageLimit] = useState('10')
//   const [searchQuery, setSearchQuery] = useState('')
//   const [searchLimit, setSearchLimit] = useState('5')
//   const [loadingImages, setLoadingImages] = useState(false)

//   // Tag数据库相关状态
//   const [tags, setTags] = useState<string[]>([])
//   const [tagLimit, setTagLimit] = useState('20')
//   const [newTag, setNewTag] = useState('')
//   const [tagSearchQuery, setTagSearchQuery] = useState('')
//   const [tagSearchLimit, setTagSearchLimit] = useState('10')
//   const [similarTags, setSimilarTags] = useState<Array<{tag: string, similarity: number}>>([])
//   const [loadingTags, setLoadingTags] = useState(false)

//   // 批量搜索相似标签测试
//   const [batchQueries, setBatchQueries] = useState('狗,猫,汽车')
//   const [batchTagLimit, setBatchTagLimit] = useState('5')
//   const [batchSimilarTags, setBatchSimilarTags] = useState<Map<string, Array<{
//     tag: string;
//     similarity: number;
//   }>>>(new Map())

//   // Tab切换状态
//   const [testPage, setTestPage] = useState<'image' | 'tag' | 'style' | 'trpc'>('image')

//   // 初始化时不自动连接数据库
//   useEffect(() => {
//     // 仅更新连接状态为未知，不主动连接
//     setStats(prev => ({ ...prev, connectionStatus: null }))
//   }, [])

//   // 手动测试数据库连接
//   const testConnection = async () => {
//     setStats(prev => ({ ...prev, loading: true }))
//     const startTime = Date.now()

//     try {
//       const result = await databaseService.manualTestConnection()
//       const responseTime = Date.now() - startTime

//       // 获取详细的诊断信息
//       const isConnected = result.success && result?.connected
//       let diagnostics: DatabaseStats['diagnostics'] = {
//         lastTestTime: Date.now(),
//         responseTime,
//         serverReachable: isConnected,
//         authStatus: isConnected ? '认证成功' : '认证失败'
//       }

//       // 如果连接成功，尝试获取更多信息
//       if (isConnected) {
//         try {
//           // 获取集合列表
//           const collectionsResult = await databaseService.queryImages({ limit: 1 })

//           diagnostics = {
//             ...diagnostics,
//             collections: ['image_collection', 'tag_embeddings'], // 预期的集合
//             serverInfo: collectionsResult
//           }
//         } catch (infoError) {
//           // Silently handle error
//         }
//       }

//       // setStats(prev => ({
//       //   ...prev,
//       //   connectionStatus: isConnected,
//       //   error: result.success ? undefined : result.error,
//       //   loading: false
//       // }))

//       if (isConnected) {
//         await loadDatabaseStats()
//       }
//     } catch (error) {
//       const responseTime = Date.now() - startTime

//       setStats(prev => ({
//         ...prev,
//         connectionStatus: false,
//         error: error instanceof Error ? error.message : String(error),
//         loading: false,
//         diagnostics: {
//           lastTestTime: Date.now(),
//           responseTime,
//           serverReachable: false,
//           authStatus: '连接异常'
//         }
//       }))
//     }
//   }

//   // 初始化数据库
//   const initDatabase = async () => {
//     setStats(prev => ({ ...prev, loading: true }))
//     try {
//       const result = await databaseService.manualInitCollection()
//       if (result.success) {
//         await testConnection()
//       } else {
//         setStats(prev => ({
//           ...prev,
//           error: result.error,
//           loading: false
//         }))
//       }
//     } catch (error) {
//       setStats(prev => ({
//         ...prev,
//         error: error instanceof Error ? error.message : String(error),
//         loading: false
//       }))
//     }
//   }

//   // 加载数据库统计信息
//   const loadDatabaseStats = async () => {
//     try {
//       // 查询图片总数
//       const imageResult = await databaseService.queryImages({ limit: 1 })

//       // 查询标签向量库中的标签总数
//       const tagResult = await databaseService.queryTagEmbeddings(1000)

//       setStats(prev => ({
//         ...prev,
//         totalImages: imageResult.total || 0,
//         totalTags: tagResult.tags?.length || 0
//       }))
//     } catch (error) {
//       // Silently handle error
//     }
//   }

//   // 查询最新的N张图片
//   const queryRecentImages = async () => {
//     setLoadingImages(true)
//     try {
//       const limit = parseInt(imageLimit) || 10
//       const result = await databaseService.queryImages({
//         limit,
//         expr: '' // 查询所有图片
//       })

//       if (result.error) {
//         setImageResults([])
//         return
//       }

//       if (!result.results || result.results.length === 0) {
//         setImageResults([])
//         return
//       }

//       const images: ImageData[] = result.results.map((record: ImageRecord, index: number) => {
//         return {
//           id: record.id || 'unknown',
//           url: record.imagePath || '/placeholder.jpg',
//           title: record.metadata?.filename?.replace(/\.[^/.]+$/, '') || '未知文件',
//           tags: record.tags || [],
//           description: record.description || '无描述',
//           uploadTime: record.metadata?.uploadTime?.split('T')[0] || '未知',
//           location: '数据库',
//           camera: '未知设备',
//           colors: record.tags || [],
//           aiAnalysis: true,
//           similarity: record.score || 0,
//           fileSize: record.metadata?.filesize ? `${(record.metadata.filesize / 1024 / 1024).toFixed(1)}MB` : '未知',
//           resolution: record.metadata?.dimensions || '未知',
//           exif: {
//             iso: 0,
//             aperture: 'f/0',
//             shutterSpeed: '0s',
//             focalLength: '0mm'
//           }
//         }
//       })

//       setImageResults(images)
//     } catch (error) {
//       console.error('❌ 查询图片失败:', error)
//       console.error('🔍 错误详情:', {
//         message: error instanceof Error ? error.message : String(error),
//         stack: error instanceof Error ? error.stack : undefined
//       })
//       setImageResults([])
//     } finally {
//       setLoadingImages(false)
//     }
//   }

//   // 语义搜索图片
//   const searchImages = async () => {
//     setLoadingImages(true)
//     try {
//       const limit = parseInt(searchLimit) || 5
//       const result = await databaseService.enhancedHybridSearch(
//         searchQuery,
//         limit,
//         true,
//         0.7
//       )

//       const images: ImageData[] = result.results.map((record: ImageRecord) => ({
//         id: record.id || 'unknown',
//         url: record.imagePath || '/placeholder.jpg',
//         title: record.metadata?.filename?.replace(/\.[^/.]+$/, '') || '未知文件',
//         tags: record.tags || [],
//         description: record.description || '无描述',
//         uploadTime: record.metadata?.uploadTime?.split('T')[0] || '未知',
//         location: '数据库',
//         camera: '未知设备',
//         colors: record.tags || [],
//         aiAnalysis: true,
//         similarity: record.score || 0,
//         fileSize: record.metadata?.filesize ? `${(record.metadata.filesize / 1024 / 1024).toFixed(1)}MB` : '未知',
//         resolution: record.metadata?.dimensions || '未知',
//         exif: {
//           iso: 0,
//           aperture: 'f/0',
//           shutterSpeed: '0s',
//           focalLength: '0mm'
//         }
//       }))

//       setImageResults(images)
//     } catch (error) {
//       console.error('搜索图片失败:', error)
//       setImageResults([])
//     } finally {
//       setLoadingImages(false)
//     }
//   }

//   // 清空图片数据库
//   const clearImageDatabase = async () => {
//     if (!window.confirm('确定要清空图片数据库吗？此操作不可逆！')) {
//       return
//     }

//     setStats(prev => ({ ...prev, loading: true }))
//     try {
//       const result = await databaseService.clearDatabase()
//       if (result) {
//         await loadDatabaseStats()
//         setImageResults([])
//         alert('图片数据库已清空')
//       } else {
//         alert('清空数据库失败')
//       }
//     } catch (error) {
//       console.error('清空数据库失败:', error)
//       alert('清空数据库失败')
//     } finally {
//       setStats(prev => ({ ...prev, loading: false }))
//     }
//   }

//   // 插入示例图片
//   const insertSampleImage = async () => {
//     setStats(prev => ({ ...prev, loading: true }))
//     try {
//       const sampleImage: ImageRecord = {
//         id: `test_img_${Date.now()}`,
//         imagePath: `/test/sample_${Date.now()}.jpg`,
//         description: '这是一张测试图片',
//         tags: ['测试', '示例', '图片'],
//         embedding: new Array(1024).fill(0).map(() => Math.random()),
//         metadata: {
//           filename: `sample_${Date.now()}.jpg`,
//           filesize: 1024000,
//           uploadTime: new Date().toISOString(),
//           dimensions: '1920x1080',
//           format: 'jpg'
//         }
//       }

//       const result = await databaseService.insertImages([sampleImage])
//       if (result.success) {
//         await loadDatabaseStats()
//         alert('示例图片插入成功')
//       } else {
//         alert(`插入失败: ${result.error}`)
//       }
//     } catch (error) {
//       console.error('插入示例图片失败:', error)
//       alert('插入示例图片失败')
//     } finally {
//       setStats(prev => ({ ...prev, loading: false }))
//     }
//   }

//   // 查询所有标签
//   const queryAllTags = async () => {
//     setLoadingTags(true)
//     try {
//       const limit = parseInt(tagLimit) || 20
//       const result = await databaseService.queryTagEmbeddings(limit)
//       setTags(result.tags || [])
//     } catch (error) {
//       console.error('查询标签失败:', error)
//       setTags([])
//     } finally {
//       setLoadingTags(false)
//     }
//   }

//   // 搜索相似标签
//   const searchSimilarTags = async () => {
//     setLoadingTags(true)
//     try {
//       const limit = parseInt(tagSearchLimit) || 10
//       const result = await databaseService.findSimilarTags(tagSearchQuery, {
//         limit,
//         threshold: 0.3  // 降低阈值，让更多相似标签被找到
//       })
//       setSimilarTags(result.map((item: any) => ({
//         tag: item.tag,
//         similarity: item.similarity
//       })))
//     } catch (error) {
//       console.error('搜索相似标签失败:', error)
//       setSimilarTags([])
//     } finally {
//       setLoadingTags(false)
//     }
//   }

//   // 插入新标签（构建标签向量库）
//   const insertNewTag = async () => {
//     if (!newTag.trim()) {
//       alert('请输入标签内容')
//       return
//     }

//     setLoadingTags(true)
//     try {
//       // 更新标签向量库
//       await databaseService.updateTagEmbeddings([newTag.trim()])
//       await loadDatabaseStats()
//       setNewTag('')
//       alert('标签插入成功')
//     } catch (error) {
//       console.error('插入标签失败:', error)
//       alert('插入标签失败')
//     } finally {
//       setLoadingTags(false)
//     }
//   }

//   // 重新初始化数据库（包含标签向量库）
//   const reinitializeDatabase = async () => {
//     setLoadingTags(true)
//     try {
//       const result = await databaseService.manualInitCollection()
//       if (result.success) {
//         await loadDatabaseStats()
//         alert('数据库重新初始化成功（包含标签向量库）')
//       } else {
//         alert(`数据库重新初始化失败: ${result.error}`)
//       }
//     } catch (error) {
//       console.error('重新初始化数据库失败:', error)
//       alert('重新初始化数据库失败')
//     } finally {
//       setLoadingTags(false)
//     }
//   }

//   // 批量搜索相似标签
//   const batchSearchSimilarTags = async () => {
//     setLoadingTags(true)
//     try {
//       const queries = batchQueries.split(',').map((q: any) => q.trim()).filter((q: any) => q.length > 0)
//       const limit = parseInt(batchTagLimit) || 5

//       const result = await databaseService.findSimilarTagsBatch(queries, {
//         limit,
//         threshold: 0.3
//       })

//       // 转换Map为组件可用的格式
//       const convertedResult = new Map()
//       result.forEach((tags, query) => {
//         convertedResult.set(query, tags.map((item: any) => ({
//           tag: item.tag,
//           similarity: item.similarity
//         })))
//       })

//       setBatchSimilarTags(convertedResult)
//     } catch (error) {
//       console.error('批量搜索相似标签失败:', error)
//       setBatchSimilarTags(new Map())
//     } finally {
//       setLoadingTags(false)
//     }
//   }

//   const getStatusIcon = (status: boolean | null) => {
//     if (stats.loading) return <Loader2 className="h-4 w-4 animate-spin text-blue-500" />
//     if (status === null) return <AlertCircle className="h-4 w-4 text-yellow-500" />
//     return status ? <CheckCircle className="h-4 w-4 text-green-500" /> : <AlertCircle className="h-4 w-4 text-red-500" />
//   }

//   const getStatusText = (status: boolean | null) => {
//     if (stats.loading) return '检测中...'
//     if (status === null) return '未检测'
//     return status ? '已连接' : '连接失败'
//   }

//   return (
//     <div className="p-8 space-y-8">
//       {/* 页面标题 */}
//       <div className="space-y-2">
//         <h1 className="text-3xl font-semibold text-slate-900 dark:text-slate-100">系统测试</h1>
//         <p className="text-slate-600 dark:text-slate-400">
//           数据库连接测试、数据操作和系统性能验证
//         </p>
//       </div>

//       {/* 数据库状态 */}
//       <div className="bg-white/60 dark:bg-slate-800/60 backdrop-blur-sm border border-slate-200/50 dark:border-slate-700/50 rounded-2xl p-6">
//         <div className="flex items-center justify-between mb-6">
//           <div className="flex items-center space-x-3">
//             <div className="p-2 bg-[#007aff]/10 rounded-lg">
//               <Database className="h-5 w-5 text-[#007aff]" />
//             </div>
//             <h2 className="text-xl font-semibold text-slate-900 dark:text-slate-100">数据库状态</h2>
//           </div>
//           <span className={`px-3 py-1 text-sm rounded-lg ${
//             stats.loading
//               ? 'bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-300'
//               : stats.connectionStatus === null
//                 ? 'bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400'
//                 : stats.connectionStatus
//                   ? 'bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300'
//                   : 'bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300'
//           }`}>
//             {getStatusText(stats.connectionStatus)}
//           </span>
//         </div>

//         <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
//           <div className="flex items-center space-x-3">
//             {getStatusIcon(stats.connectionStatus)}
//             <div>
//               <p className="text-sm text-slate-600 dark:text-slate-400">连接状态</p>
//               <p className="font-semibold text-slate-900 dark:text-slate-100">{getStatusText(stats.connectionStatus)}</p>
//             </div>
//           </div>
//           <div className="flex items-center space-x-3">
//             <ImageIcon className="h-4 w-4 text-blue-500" />
//             <div>
//               <p className="text-sm text-slate-600 dark:text-slate-400">图片总数</p>
//               <p className="font-semibold text-slate-900 dark:text-slate-100">{stats.totalImages}</p>
//             </div>
//           </div>
//           <div className="flex items-center space-x-3">
//             <Tag className="h-4 w-4 text-green-500" />
//             <div>
//               <p className="text-sm text-slate-600 dark:text-slate-400">标签总数</p>
//               <p className="font-semibold text-slate-900 dark:text-slate-100">{stats.totalTags}</p>
//             </div>
//           </div>
//         </div>

//         <div className="flex flex-wrap gap-3">
//           <button
//             onClick={testConnection}
//             disabled={stats.loading}
//             className="inline-flex items-center px-4 py-2 text-sm font-medium text-[#007aff] bg-[#007aff]/10 hover:bg-[#007aff]/20 rounded-lg transition-colors disabled:opacity-50"
//           >
//             <RefreshCw className={`h-4 w-4 mr-2 ${stats.loading ? 'animate-spin' : ''}`} />
//             测试连接
//           </button>
//           <button
//             onClick={initDatabase}
//             disabled={stats.loading}
//             className="inline-flex items-center px-4 py-2 text-sm font-medium text-slate-600 dark:text-slate-400 bg-white/60 dark:bg-slate-700/60 hover:bg-white/80 dark:hover:bg-slate-700/80 rounded-lg transition-colors disabled:opacity-50"
//           >
//             <Database className="h-4 w-4 mr-2" />
//             初始化数据库
//           </button>
//           <button
//             onClick={loadDatabaseStats}
//             disabled={stats.loading || !stats.connectionStatus}
//             className="inline-flex items-center px-4 py-2 text-sm font-medium text-slate-600 dark:text-slate-400 bg-white/60 dark:bg-slate-700/60 hover:bg-white/80 dark:hover:bg-slate-700/80 rounded-lg transition-colors disabled:opacity-50"
//           >
//             <BarChart3 className="h-4 w-4 mr-2" />
//             刷新统计
//           </button>
//         </div>

//         {stats.error && (
//           <div className="mt-4 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
//             <div className="flex items-start space-x-2">
//               <AlertCircle className="h-4 w-4 text-red-500 mt-0.5 flex-shrink-0" />
//               <div>
//                 <p className="text-sm font-medium text-red-600 dark:text-red-400 mb-1">连接错误</p>
//                 <p className="text-sm text-red-600 dark:text-red-400">{stats.error}</p>
//                 <div className="mt-2 text-xs text-red-500 dark:text-red-400">
//                   <p>🔧 请检查以下项目：</p>
//                   <ul className="mt-1 ml-4 space-y-1">
//                     <li>• Milvus 服务是否已启动</li>
//                     <li>• 服务器地址和端口是否正确</li>
//                     <li>• 网络连接是否正常</li>
//                     <li>• 防火墙设置是否阻止连接</li>
//                   </ul>
//                 </div>
//               </div>
//             </div>
//           </div>
//         )}

//         {/* 诊断详情 */}
//         {stats.diagnostics && (
//           <div className="mt-4 p-4 bg-slate-50 dark:bg-slate-700/30 border border-slate-200 dark:border-slate-600 rounded-lg">
//             <h3 className="text-sm font-semibold text-slate-900 dark:text-slate-100 mb-3 flex items-center">
//               <TestTube className="h-4 w-4 mr-2" />
//               诊断信息
//             </h3>
//             <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 text-xs">
//               <div>
//                 <p className="text-slate-600 dark:text-slate-400">最后测试时间</p>
//                 <p className="font-mono text-slate-900 dark:text-slate-100">
//                   {stats.diagnostics.lastTestTime ? new Date(stats.diagnostics.lastTestTime).toLocaleString() : '未测试'}
//                 </p>
//               </div>
//               <div>
//                 <p className="text-slate-600 dark:text-slate-400">响应时间</p>
//                 <p className="font-mono text-slate-900 dark:text-slate-100">
//                   {stats.diagnostics.responseTime ? `${stats.diagnostics.responseTime}ms` : 'N/A'}
//                 </p>
//               </div>
//               <div>
//                 <p className="text-slate-600 dark:text-slate-400">服务器可达性</p>
//                 <p className={`font-mono ${stats.diagnostics.serverReachable ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'}`}>
//                   {stats.diagnostics.serverReachable ? '✅ 可达' : '❌ 不可达'}
//                 </p>
//               </div>
//               <div>
//                 <p className="text-slate-600 dark:text-slate-400">认证状态</p>
//                 <p className="font-mono text-slate-900 dark:text-slate-100">
//                   {stats.diagnostics.authStatus || 'N/A'}
//                 </p>
//               </div>
//               <div>
//                 <p className="text-slate-600 dark:text-slate-400">预期集合</p>
//                 <p className="font-mono text-slate-900 dark:text-slate-100">
//                   {stats.diagnostics.collections?.join(', ') || 'N/A'}
//                 </p>
//               </div>
//               <div>
//                 <p className="text-slate-600 dark:text-slate-400">服务状态</p>
//                 <p className={`font-mono ${stats.connectionStatus ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'}`}>
//                   {stats.connectionStatus ? '🟢 正常' : '🔴 异常'}
//                 </p>
//               </div>
//             </div>
//           </div>
//         )}
//       </div>

//       {/* Tab切换 */}
//       <div className="flex space-x-2 bg-slate-100/60 dark:bg-slate-800/60 p-1 rounded-lg w-fit">
//         <button
//           onClick={() => setTestPage('image')}
//           className={`inline-flex items-center px-4 py-2 text-sm font-medium rounded-md transition-all ${
//             testPage === 'image'
//               ? 'bg-white dark:bg-slate-700 text-[#007aff] shadow-sm'
//               : 'text-slate-600 dark:text-slate-400 hover:text-slate-900 dark:hover:text-white'
//           }`}
//         >
//           <ImageIcon className="h-4 w-4 mr-2" />
//           图片数据库
//         </button>
//         <button
//           onClick={() => setTestPage('tag')}
//           className={`inline-flex items-center px-4 py-2 text-sm font-medium rounded-md transition-all ${
//             testPage === 'tag'
//               ? 'bg-white dark:bg-slate-700 text-[#007aff] shadow-sm'
//               : 'text-slate-600 dark:text-slate-400 hover:text-slate-900 dark:hover:text-white'
//           }`}
//         >
//           <Tag className="h-4 w-4 mr-2" />
//           标签数据库
//         </button>
//         <button
//           onClick={() => setTestPage('style' as any)}
//           className={`inline-flex items-center px-4 py-2 text-sm font-medium rounded-md transition-all ${
//             testPage === 'style'
//               ? 'bg-white dark:bg-slate-700 text-[#007aff] shadow-sm'
//               : 'text-slate-600 dark:text-slate-400 hover:text-slate-900 dark:hover:text-white'
//           }`}
//         >
//           <Palette className="h-4 w-4 mr-2" />
//           标签样式
//         </button>
//         <button
//           onClick={() => setTestPage('trpc')}
//           className={`inline-flex items-center px-4 py-2 text-sm font-medium rounded-md transition-all ${
//             testPage === 'trpc'
//               ? 'bg-white dark:bg-slate-700 text-[#007aff] shadow-sm'
//               : 'text-slate-600 dark:text-slate-400 hover:text-slate-900 dark:hover:text-white'
//           }`}
//         >
//           <Network className="h-4 w-4 mr-2" />
//           tRPC 测试
//         </button>
//       </div>

//       {/* 图片数据库 Tab */}
//       {testPage === 'image' && (
//         <div className="space-y-8">
//           <div className="bg-white/60 dark:bg-slate-800/60 backdrop-blur-sm border border-slate-200/50 dark:border-slate-700/50 rounded-2xl p-6">
//             <div className="flex items-center space-x-3 mb-6">
//               <div className="p-2 bg-[#007aff]/10 rounded-lg">
//                 <ImageIcon className="h-5 w-5 text-[#007aff]" />
//               </div>
//               <h2 className="text-xl font-semibold text-slate-900 dark:text-slate-100">图片数据库操作</h2>
//             </div>

//             <div className="space-y-4">
//               {/* 查询最新图片 */}
//               <div className="flex items-center space-x-3">
//                 <input
//                   type="number"
//                   placeholder="数量"
//                   value={imageLimit}
//                   onChange={(e) => setImageLimit(e.target.value)}
//                   className="w-20 px-3 py-2 text-sm bg-white/60 dark:bg-slate-800/60 border border-slate-200/50 dark:border-slate-700/50 rounded-lg focus:border-[#007aff]/50 focus:ring-[#007aff]/20 focus:ring-2 outline-none"
//                 />
//                 <button
//                   onClick={queryRecentImages}
//                   disabled={loadingImages || !stats.connectionStatus}
//                   className="inline-flex items-center px-4 py-2 text-sm font-medium text-[#007aff] bg-[#007aff]/10 hover:bg-[#007aff]/20 rounded-lg transition-colors disabled:opacity-50"
//                 >
//                   <Grid3X3 className="h-4 w-4 mr-2" />
//                   查询最新图片
//                 </button>
//               </div>

//               {/* 搜索图片 */}
//               <div className="flex items-center space-x-3">
//                 <input
//                   type="text"
//                   placeholder="输入搜索关键词..."
//                   value={searchQuery}
//                   onChange={(e) => setSearchQuery(e.target.value)}
//                   className="flex-1 px-3 py-2 text-sm bg-white/60 dark:bg-slate-800/60 border border-slate-200/50 dark:border-slate-700/50 rounded-lg focus:border-[#007aff]/50 focus:ring-[#007aff]/20 focus:ring-2 outline-none"
//                 />
//                 <input
//                   type="number"
//                   placeholder="Top N"
//                   value={searchLimit}
//                   onChange={(e) => setSearchLimit(e.target.value)}
//                   className="w-20 px-3 py-2 text-sm bg-white/60 dark:bg-slate-800/60 border border-slate-200/50 dark:border-slate-700/50 rounded-lg focus:border-[#007aff]/50 focus:ring-[#007aff]/20 focus:ring-2 outline-none"
//                 />
//                 <button
//                   onClick={searchImages}
//                   disabled={loadingImages || !stats.connectionStatus}
//                   className="inline-flex items-center px-4 py-2 text-sm font-medium text-[#007aff] bg-[#007aff]/10 hover:bg-[#007aff]/20 rounded-lg transition-colors disabled:opacity-50"
//                 >
//                   <Search className="h-4 w-4 mr-2" />
//                   语义搜索
//                 </button>
//               </div>

//               {/* 数据库操作 */}
//               <div className="flex space-x-3">
//                 <button
//                   onClick={insertSampleImage}
//                   disabled={stats.loading || !stats.connectionStatus}
//                   className="inline-flex items-center px-4 py-2 text-sm font-medium text-slate-600 dark:text-slate-400 bg-white/60 dark:bg-slate-700/60 hover:bg-white/80 dark:hover:bg-slate-700/80 rounded-lg transition-colors disabled:opacity-50"
//                 >
//                   <Plus className="h-4 w-4 mr-2" />
//                   插入示例图片
//                 </button>
//                 <button
//                   onClick={clearImageDatabase}
//                   disabled={stats.loading || !stats.connectionStatus}
//                   className="inline-flex items-center px-4 py-2 text-sm font-medium text-red-600 dark:text-red-400 bg-red-50 dark:bg-red-900/20 hover:bg-red-100 dark:hover:bg-red-900/40 rounded-lg transition-colors disabled:opacity-50"
//                 >
//                   <Trash2 className="h-4 w-4 mr-2" />
//                   清空数据库
//                 </button>
//               </div>
//             </div>
//           </div>

//           {/* 图片结果展示 */}
//           {(loadingImages || imageResults.length > 0) && (
//             <div className="bg-white/60 dark:bg-slate-800/60 backdrop-blur-sm border border-slate-200/50 dark:border-slate-700/50 rounded-2xl p-6">
//               <div className="flex items-center justify-between mb-6">
//                 <h3 className="text-lg font-semibold text-slate-900 dark:text-slate-100">
//                   图片结果 ({imageResults.length})
//                 </h3>
//                 {loadingImages && <Loader2 className="h-4 w-4 animate-spin text-[#007aff]" />}
//               </div>

//               {loadingImages ? (
//                 <div className="flex items-center justify-center py-12">
//                   <Loader2 className="h-6 w-6 animate-spin mr-3 text-[#007aff]" />
//                   <span className="text-slate-600 dark:text-slate-400">加载中...</span>
//                 </div>
//               ) : (
//                 <ImageGrid
//                   images={imageResults}
//                   onImageSelect={() => {}}
//                   viewMode="grid"
//                 />
//               )}
//             </div>
//           )}
//         </div>
//       )}

//       {/* 标签数据库 Tab */}
//       {testPage === 'tag' && (
//         <div className="space-y-8">
//           <div className="bg-white/60 dark:bg-slate-800/60 backdrop-blur-sm border border-slate-200/50 dark:border-slate-700/50 rounded-2xl p-6">
//             <div className="flex items-center space-x-3 mb-6">
//               <div className="p-2 bg-[#007aff]/10 rounded-lg">
//                 <Tag className="h-5 w-5 text-[#007aff]" />
//               </div>
//               <h2 className="text-xl font-semibold text-slate-900 dark:text-slate-100">标签数据库操作</h2>
//             </div>

//             <div className="space-y-6">
//               {/* 查询所有标签 */}
//               <div className="flex items-center space-x-3">
//                 <input
//                   type="number"
//                   placeholder="数量"
//                   value={tagLimit}
//                   onChange={(e) => setTagLimit(e.target.value)}
//                   className="w-20 px-3 py-2 text-sm bg-white/60 dark:bg-slate-800/60 border border-slate-200/50 dark:border-slate-700/50 rounded-lg focus:border-[#007aff]/50 focus:ring-[#007aff]/20 focus:ring-2 outline-none"
//                 />
//                 <button
//                   onClick={queryAllTags}
//                   disabled={loadingTags || !stats.connectionStatus}
//                   className="inline-flex items-center px-4 py-2 text-sm font-medium text-[#007aff] bg-[#007aff]/10 hover:bg-[#007aff]/20 rounded-lg transition-colors disabled:opacity-50"
//                 >
//                   <Grid3X3 className="h-4 w-4 mr-2" />
//                   查询标签列表
//                 </button>
//               </div>

//               {/* 搜索相似标签 */}
//               <div className="flex items-center space-x-3">
//                 <input
//                   type="text"
//                   placeholder="输入标签搜索词..."
//                   value={tagSearchQuery}
//                   onChange={(e) => setTagSearchQuery(e.target.value)}
//                   className="flex-1 px-3 py-2 text-sm bg-white/60 dark:bg-slate-800/60 border border-slate-200/50 dark:border-slate-700/50 rounded-lg focus:border-[#007aff]/50 focus:ring-[#007aff]/20 focus:ring-2 outline-none"
//                 />
//                 <input
//                   type="number"
//                   placeholder="Top N"
//                   value={tagSearchLimit}
//                   onChange={(e) => setTagSearchLimit(e.target.value)}
//                   className="w-20 px-3 py-2 text-sm bg-white/60 dark:bg-slate-800/60 border border-slate-200/50 dark:border-slate-700/50 rounded-lg focus:border-[#007aff]/50 focus:ring-[#007aff]/20 focus:ring-2 outline-none"
//                 />
//                 <button
//                   onClick={searchSimilarTags}
//                   disabled={loadingTags || !stats.connectionStatus}
//                   className="inline-flex items-center px-4 py-2 text-sm font-medium text-[#007aff] bg-[#007aff]/10 hover:bg-[#007aff]/20 rounded-lg transition-colors disabled:opacity-50"
//                 >
//                   <Search className="h-4 w-4 mr-2" />
//                   相似搜索
//                 </button>
//               </div>

//               {/* 插入新标签 */}
//               <div className="flex items-center space-x-3">
//                 <input
//                   type="text"
//                   placeholder="输入新标签..."
//                   value={newTag}
//                   onChange={(e) => setNewTag(e.target.value)}
//                   className="flex-1 px-3 py-2 text-sm bg-white/60 dark:bg-slate-800/60 border border-slate-200/50 dark:border-slate-700/50 rounded-lg focus:border-[#007aff]/50 focus:ring-[#007aff]/20 focus:ring-2 outline-none"
//                 />
//                 <button
//                   onClick={insertNewTag}
//                   disabled={loadingTags || !stats.connectionStatus}
//                   className="inline-flex items-center px-4 py-2 text-sm font-medium text-[#007aff] bg-[#007aff]/10 hover:bg-[#007aff]/20 rounded-lg transition-colors disabled:opacity-50"
//                 >
//                   <Plus className="h-4 w-4 mr-2" />
//                   插入标签
//                 </button>
//               </div>

//               {/* 标签数据库操作 */}
//               <div className="flex space-x-3">
//                 <button
//                   onClick={reinitializeDatabase}
//                   disabled={loadingTags || !stats.connectionStatus}
//                   className="inline-flex items-center px-4 py-2 text-sm font-medium text-slate-600 dark:text-slate-400 bg-white/60 dark:bg-slate-700/60 hover:bg-white/80 dark:hover:bg-slate-700/80 rounded-lg transition-colors disabled:opacity-50"
//                 >
//                   <Upload className="h-4 w-4 mr-2" />
//                   重新初始化数据库
//                 </button>
//               </div>

//               {/* 批量标签搜索测试 */}
//               <div className="bg-white/40 dark:bg-slate-700/40 backdrop-blur-sm border border-slate-200/50 dark:border-slate-600/50 rounded-xl p-6">
//                 <h3 className="text-lg font-semibold text-slate-900 dark:text-slate-100 mb-4">批量相似标签搜索</h3>
//                 <div className="space-y-4">
//                   <div>
//                     <label className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">查询词（用逗号分隔）:</label>
//                     <input
//                       type="text"
//                       value={batchQueries}
//                       onChange={(e) => setBatchQueries(e.target.value)}
//                       className="w-full px-3 py-2 text-sm bg-white/60 dark:bg-slate-800/60 border border-slate-200/50 dark:border-slate-700/50 rounded-lg focus:border-[#007aff]/50 focus:ring-[#007aff]/20 focus:ring-2 outline-none"
//                       placeholder="例如: 狗,猫,汽车"
//                     />
//                   </div>
//                   <div>
//                     <label className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">每个词返回标签数量:</label>
//                     <input
//                       type="number"
//                       value={batchTagLimit}
//                       onChange={(e) => setBatchTagLimit(e.target.value)}
//                       className="w-32 px-3 py-2 text-sm bg-white/60 dark:bg-slate-800/60 border border-slate-200/50 dark:border-slate-700/50 rounded-lg focus:border-[#007aff]/50 focus:ring-[#007aff]/20 focus:ring-2 outline-none"
//                       min="1"
//                       max="20"
//                     />
//                   </div>
//                   <button
//                     onClick={batchSearchSimilarTags}
//                     disabled={loadingTags}
//                     className="inline-flex items-center px-4 py-2 text-sm font-medium text-white bg-purple-500 hover:bg-purple-600 rounded-lg transition-colors disabled:opacity-50"
//                   >
//                     {loadingTags ? <Loader2 className="h-4 w-4 mr-2 animate-spin" /> : <Search className="h-4 w-4 mr-2" />}
//                     {loadingTags ? '搜索中...' : '批量搜索相似标签'}
//                   </button>
//                 </div>

//                 {batchSimilarTags.size > 0 && (
//                   <div className="mt-6">
//                     <h4 className="font-medium text-slate-900 dark:text-slate-100 mb-4">批量搜索结果:</h4>
//                     <div className="space-y-4">
//                       {Array.from(batchSimilarTags.entries()).map(([query, tags]) => (
//                         <div key={query} className="border-l-4 border-purple-400 pl-4">
//                           <div className="font-medium text-purple-700 dark:text-purple-300">查询: "{query}"</div>
//                           <div className="mt-2 text-sm text-slate-600 dark:text-slate-400">
//                             找到 {tags.length} 个相似标签:
//                           </div>
//                           <div className="mt-2 flex flex-wrap gap-2">
//                             {tags.map((item, index) => (
//                               <span
//                                 key={index}
//                                 className="px-2 py-1 bg-purple-100 dark:bg-purple-900/30 text-purple-800 dark:text-purple-300 rounded text-xs"
//                               >
//                                 {item.tag} ({item.similarity.toFixed(3)})
//                               </span>
//                             ))}
//                           </div>
//                         </div>
//                       ))}
//                     </div>
//                   </div>
//                 )}
//               </div>
//             </div>
//           </div>

//           {/* 标签结果展示 */}
//           <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
//             {/* 标签列表 */}
//             {(loadingTags || tags.length > 0) && (
//               <div className="bg-white/60 dark:bg-slate-800/60 backdrop-blur-sm border border-slate-200/50 dark:border-slate-700/50 rounded-2xl p-6">
//                 <div className="flex items-center justify-between mb-6">
//                   <h3 className="text-lg font-semibold text-slate-900 dark:text-slate-100">
//                     标签列表 ({tags.length})
//                   </h3>
//                   {loadingTags && <Loader2 className="h-4 w-4 animate-spin text-[#007aff]" />}
//                 </div>

//                 {loadingTags ? (
//                   <div className="flex items-center justify-center py-12">
//                     <Loader2 className="h-6 w-6 animate-spin mr-3 text-[#007aff]" />
//                     <span className="text-slate-600 dark:text-slate-400">加载中...</span>
//                   </div>
//                 ) : (
//                   <div className="flex flex-wrap gap-2 max-h-96 overflow-y-auto">
//                     {tags.map((tag, index) => (
//                       <span
//                         key={index}
//                         className="px-3 py-1 text-sm bg-slate-100 dark:bg-slate-700 text-slate-700 dark:text-slate-300 rounded-lg border border-slate-200/50 dark:border-slate-600/50"
//                       >
//                         {tag}
//                       </span>
//                     ))}
//                   </div>
//                 )}
//               </div>
//             )}

//             {/* 相似标签搜索结果 */}
//             {(loadingTags || similarTags.length > 0) && (
//               <div className="bg-white/60 dark:bg-slate-800/60 backdrop-blur-sm border border-slate-200/50 dark:border-slate-700/50 rounded-2xl p-6">
//                 <div className="flex items-center justify-between mb-6">
//                   <h3 className="text-lg font-semibold text-slate-900 dark:text-slate-100">
//                     相似标签 ({similarTags.length})
//                   </h3>
//                   {loadingTags && <Loader2 className="h-4 w-4 animate-spin text-[#007aff]" />}
//                 </div>

//                 {loadingTags ? (
//                   <div className="flex items-center justify-center py-12">
//                     <Loader2 className="h-6 w-6 animate-spin mr-3 text-[#007aff]" />
//                     <span className="text-slate-600 dark:text-slate-400">搜索中...</span>
//                   </div>
//                 ) : (
//                   <div className="space-y-2 max-h-96 overflow-y-auto">
//                     {similarTags.map((item, index) => (
//                       <div key={index} className="flex items-center justify-between p-3 bg-white/40 dark:bg-slate-700/40 rounded-lg border border-slate-200/50 dark:border-slate-600/50">
//                         <span className="font-medium text-slate-900 dark:text-slate-100">{item.tag}</span>
//                         <span className="px-2 py-1 text-xs bg-slate-100 dark:bg-slate-700 text-slate-600 dark:text-slate-400 rounded">
//                           {(item.similarity * 100).toFixed(1)}%
//                         </span>
//                       </div>
//                     ))}
//                   </div>
//                 )}
//               </div>
//             )}
//           </div>
//         </div>
//       )}

//       {/* 标签样式展示 Tab */}
//       {testPage === 'style' && (
//         <TagStyleShowcase />
//       )}

//       {/* tRPC 测试 Tab */}
//       {testPage === 'trpc' && (
//         <div className="space-y-8">
//           <TRPCExample />
//         </div>
//       )}
//     </div>
//   )
// }