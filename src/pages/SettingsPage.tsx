// import { <PERSON>ting<PERSON>, <PERSON>, Sun, Shield, Database, Zap, Image as ImageIcon, RefreshCw, Trash2, Info, Code, Server, Brain, Eye, Palette, Download, Upload } from 'lucide-react'
// import DatabaseStatus from '@/components/DatabaseStatus.tsx'
// import { galleryService } from '@/lib/galleryService.ts'
// import configManager from '@/lib/ConfigManager.ts'
// import { useState, useEffect } from 'react'
// import appIcon from '../assets/logo.png'
//
// interface SettingsPageProps {
//   isDarkMode: boolean
//   onToggleDarkMode: () => void
//   isDeveloperMode?: boolean
//   onDeveloperModeChange?: (enabled: boolean) => void
// }
//
// export function SettingsPage({ isDarkMode, onToggleDarkMode, isDeveloperMode: propIsDeveloperMode, onDeveloperModeChange }: SettingsPageProps): JSX.Element {
//   const [isClearing, setIsClearing] = useState(false)
//   const [clearMessage, setClearMessage] = useState<string | null>(null)
//   const [showConfigDetails, setShowConfigDetails] = useState(false)
//
//   // 使用配置管理器
//   const aiConfig = configManager.aiConfig
//   const appConfig = configManager.appConfig
//   const uiConfig = configManager.uiConfig
//   const databaseConfig = configManager.databaseConfig
//   const developerConfig = configManager.developerConfig
//
//   const [isDeveloperMode, setIsDeveloperMode] = useState(propIsDeveloperMode ?? developerConfig.enableConsoleLog)
//   const [consoleLog, setConsoleLog] = useState(developerConfig.enableConsoleLog)
//   const [performanceMonitoring, setPerformanceMonitoring] = useState(developerConfig.enablePerformanceMonitoring)
//   const [apiDebug, setApiDebug] = useState(developerConfig.enableApiDebug)
//   const [experimentalFeatures, setExperimentalFeatures] = useState(developerConfig.enableExperimentalFeatures)
//
//   useEffect(() => {
//     // 同步外部传入的开发者模式状态
//     if (propIsDeveloperMode !== undefined) {
//       setIsDeveloperMode(propIsDeveloperMode)
//     }
//
//     // 监听配置变更
//     const unsubscribe = configManager.onConfigChange((type, config) => {
//       if (type === 'developer') {
//         setIsDeveloperMode(config.enableConsoleLog)
//         setConsoleLog(config.enableConsoleLog)
//         setPerformanceMonitoring(config.enablePerformanceMonitoring)
//         setApiDebug(config.enableApiDebug)
//         setExperimentalFeatures(config.enableExperimentalFeatures)
//
//         // 通知父组件状态变更
//         if (onDeveloperModeChange) {
//           onDeveloperModeChange(config.enableConsoleLog)
//         }
//       }
//     })
//
//     return unsubscribe
//   }, [propIsDeveloperMode, onDeveloperModeChange])
//
//   const toggleDeveloperMode = () => {
//     const newMode = !isDeveloperMode
//     configManager.updateDeveloperConfig({
//       enableConsoleLog: newMode,
//       enablePerformanceMonitoring: newMode ? performanceMonitoring : false,
//       enableApiDebug: newMode ? apiDebug : false,
//       enableExperimentalFeatures: newMode ? experimentalFeatures : false
//     })
//
//     // 通知父组件状态变更
//     if (onDeveloperModeChange) {
//       onDeveloperModeChange(newMode)
//     }
//   }
//
//   const updateDeveloperOption = (key: string, value: boolean) => {
//     configManager.updateDeveloperConfig({ [key]: value })
//   }
//
//   const exportConfig = () => {
//     const configJson = configManager.exportConfig()
//     const blob = new Blob([configJson], { type: 'application/json' })
//     const url = URL.createObjectURL(blob)
//     const a = document.createElement('a')
//     a.href = url
//     a.download = `picmind-config-${new Date().toISOString().split('T')[0]}.json`
//     document.body.appendChild(a)
//     a.click()
//     document.body.removeChild(a)
//     URL.revokeObjectURL(url)
//   }
//
//   const importConfig = (event: React.ChangeEvent<HTMLInputElement>) => {
//     const file = event.target.files?.[0]
//     if (file) {
//       const reader = new FileReader()
//       reader.onload = (e) => {
//         const content = e.target?.result as string
//         if (configManager.importConfig(content)) {
//           setClearMessage('配置导入成功')
//         } else {
//           setClearMessage('配置导入失败，请检查文件格式')
//         }
//         setTimeout(() => setClearMessage(null), 3000)
//       }
//       reader.readAsText(file)
//     }
//   }
//
//   // 清除缓存
//   const handleClearCache = async () => {
//     setIsClearing(true)
//     setClearMessage(null)
//
//     try {
//       // 清除图片库服务的标签缓存
//       galleryService.clearTagsCache()
//
//       // 重置数据库管理器状态
//       // databaseManager.reset()
//
//       setClearMessage('缓存已清除，下次查询将重新获取数据')
//
//       // 3秒后清除消息
//       setTimeout(() => {
//         setClearMessage(null)
//       }, 3000)
//
//     } catch (error) {
//       setClearMessage('清除缓存失败: ' + (error instanceof Error ? error.message : String(error)))
//     } finally {
//       setIsClearing(false)
//     }
//   }
//
//   return (
//     <div className="p-8 space-y-8">
//       {/* 页面标题 */}
//       <div className="space-y-2">
//         <h1 className="text-3xl font-semibold text-slate-900 dark:text-slate-100">设置</h1>
//         <p className="text-slate-600 dark:text-slate-400">
//           配置应用偏好和系统参数
//         </p>
//       </div>
//
//       {/* 应用说明 */}
//       <div className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 border border-blue-200/50 dark:border-blue-700/50 rounded-2xl p-6">
//         <div className="flex items-center space-x-3 mb-4">
//           <div className="p-2 bg-blue-500/10 rounded-lg">
//             <img src={appIcon} alt="PicMind Logo" className="w-48 h-48"/>
//           </div>
//           <h2 className="text-xl font-semibold text-slate-900 dark:text-slate-100">关于 PicMind</h2>
//         </div>
//         <div className="space-y-3 text-slate-700 dark:text-slate-300">
//           <p className="leading-relaxed">
//             PicMind 是一款智能图片管理应用，结合了先进的 AI 技术和直观的用户界面，帮助您更好地组织、搜索和管理您的图片收藏。
//           </p>
//           <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
//             <div className="flex items-center space-x-2">
//               <Brain className="h-4 w-4 text-blue-500" />
//               <span className="text-sm">AI 智能标签和内容识别</span>
//             </div>
//             <div className="flex items-center space-x-2">
//               <Database className="h-4 w-4 text-green-500" />
//               <span className="text-sm">向量化语义搜索</span>
//             </div>
//             <div className="flex items-center space-x-2">
//               <Eye className="h-4 w-4 text-purple-500" />
//               <span className="text-sm">智能图片浏览和筛选</span>
//             </div>
//             <div className="flex items-center space-x-2">
//               <Zap className="h-4 w-4 text-orange-500" />
//               <span className="text-sm">高性能本地处理</span>
//             </div>
//           </div>
//         </div>
//       </div>
//
//       {/* 主题设置 */}
//       <div className="bg-white/60 dark:bg-slate-800/60 backdrop-blur-sm border border-slate-200/50 dark:border-slate-700/50 rounded-2xl p-6">
//         <div className="flex items-center space-x-3 mb-6">
//           <div className="p-2 bg-[#007aff]/10 rounded-lg">
//             <Palette className="h-5 w-5 text-[#007aff]" />
//           </div>
//           <h2 className="text-xl font-semibold text-slate-900 dark:text-slate-100">外观设置</h2>
//         </div>
//
//         <div className="space-y-4">
//           <div className="p-4 bg-white/40 dark:bg-slate-700/40 backdrop-blur-sm border border-slate-200/50 dark:border-slate-600/50 rounded-xl">
//             <div className="flex items-center justify-between mb-4">
//               <div>
//                 <h4 className="font-medium text-slate-900 dark:text-slate-100">主题模式</h4>
//                 <p className="text-sm text-slate-600 dark:text-slate-400">选择您偏好的界面主题</p>
//               </div>
//               <div className="flex items-center space-x-2">
//                 <span className={`text-sm font-medium ${
//                   !isDarkMode ? 'text-[#007aff]' : 'text-slate-500 dark:text-slate-400'
//                 }`}>浅色</span>
//                 <button
//                   onClick={onToggleDarkMode}
//                   className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-[#007aff] focus:ring-offset-2 ${
//                     isDarkMode ? 'bg-[#007aff]' : 'bg-slate-200'
//                   }`}
//                 >
//                   <span
//                     className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
//                       isDarkMode ? 'translate-x-6' : 'translate-x-1'
//                     }`}
//                   />
//                 </button>
//                 <span className={`text-sm font-medium ${
//                   isDarkMode ? 'text-[#007aff]' : 'text-slate-500 dark:text-slate-400'
//                 }`}>深色</span>
//               </div>
//             </div>
//
//             {/* 主题预览 */}
//             <div className="grid grid-cols-2 gap-3">
//               <div className={`p-3 rounded-lg border-2 transition-all cursor-pointer ${
//                 !isDarkMode
//                   ? 'border-[#007aff] bg-white'
//                   : 'border-slate-300 bg-slate-50 hover:border-slate-400'
//               }`} onClick={() => !isDarkMode || onToggleDarkMode()}>
//                 <div className="flex items-center space-x-2 mb-2">
//                   <Sun className="h-4 w-4 text-yellow-500" />
//                   <span className="text-sm font-medium text-slate-900">浅色主题</span>
//                 </div>
//                 <div className="space-y-1">
//                   <div className="h-2 bg-slate-200 rounded"></div>
//                   <div className="h-2 bg-slate-100 rounded w-3/4"></div>
//                 </div>
//               </div>
//
//               <div className={`p-3 rounded-lg border-2 transition-all cursor-pointer ${
//                 isDarkMode
//                   ? 'border-[#007aff] bg-slate-800'
//                   : 'border-slate-300 bg-slate-800 hover:border-slate-400'
//               }`} onClick={() => isDarkMode || onToggleDarkMode()}>
//                 <div className="flex items-center space-x-2 mb-2">
//                   <Moon className="h-4 w-4 text-blue-400" />
//                   <span className="text-sm font-medium text-white">深色主题</span>
//                 </div>
//                 <div className="space-y-1">
//                   <div className="h-2 bg-slate-600 rounded"></div>
//                   <div className="h-2 bg-slate-700 rounded w-3/4"></div>
//                 </div>
//               </div>
//             </div>
//           </div>
//         </div>
//       </div>
//
//       {/* 数据库管理 */}
//       <div className="bg-white/60 dark:bg-slate-800/60 backdrop-blur-sm border border-slate-200/50 dark:border-slate-700/50 rounded-2xl p-6">
//         <div className="flex items-center space-x-3 mb-6">
//           <div className="p-2 bg-blue-500/10 rounded-lg">
//             <Database className="h-5 w-5 text-blue-500" />
//           </div>
//           <h2 className="text-xl font-semibold text-slate-900 dark:text-slate-100">数据库管理</h2>
//         </div>
//
//         <div className="space-y-6">
//           {/* 数据库状态 */}
//           <DatabaseStatus />
//
//           {/* 缓存管理 */}
//           <div className="p-4 bg-white/40 dark:bg-slate-700/40 backdrop-blur-sm border border-slate-200/50 dark:border-slate-600/50 rounded-xl">
//             <div className="flex items-center justify-between">
//               <div className="flex items-center space-x-3">
//                 <div className="p-2 bg-orange-500/10 rounded-lg">
//                   <RefreshCw className="h-4 w-4 text-orange-500" />
//                 </div>
//                 <div>
//                   <h4 className="font-medium text-slate-900 dark:text-slate-100">清除缓存</h4>
//                   <p className="text-sm text-slate-600 dark:text-slate-400">
//                     清除标签缓存和连接状态，强制重新获取数据
//                   </p>
//                 </div>
//               </div>
//               <button
//                 onClick={handleClearCache}
//                 disabled={isClearing}
//                 className="inline-flex items-center px-4 py-2 text-sm font-medium text-white bg-orange-500 hover:bg-orange-600 disabled:opacity-50 disabled:cursor-not-allowed rounded-lg transition-colors"
//               >
//                 <Trash2 className={`h-4 w-4 mr-2 ${isClearing ? 'animate-spin' : ''}`} />
//                 {isClearing ? '清除中...' : '清除缓存'}
//               </button>
//             </div>
//
//             {clearMessage && (
//               <div className={`mt-3 p-2 rounded-lg text-sm ${
//                 clearMessage.includes('失败')
//                   ? 'bg-red-50 dark:bg-red-900/20 text-red-700 dark:text-red-400 border border-red-200 dark:border-red-800'
//                   : 'bg-green-50 dark:bg-green-900/20 text-green-700 dark:text-green-400 border border-green-200 dark:border-green-800'
//               }`}>
//                 {clearMessage}
//               </div>
//             )}
//           </div>
//         </div>
//       </div>
//
//       {/* 开发者设置 */}
//       <div className="bg-white/60 dark:bg-slate-800/60 backdrop-blur-sm border border-slate-200/50 dark:border-slate-700/50 rounded-2xl p-6">
//         <div className="flex items-center space-x-3 mb-6">
//           <div className="p-2 bg-purple-500/10 rounded-lg">
//             <Code className="h-5 w-5 text-purple-500" />
//           </div>
//           <h2 className="text-xl font-semibold text-slate-900 dark:text-slate-100">开发者设置</h2>
//         </div>
//
//         <div className="space-y-4">
//           <div className="p-4 bg-white/40 dark:bg-slate-700/40 backdrop-blur-sm border border-slate-200/50 dark:border-slate-600/50 rounded-xl">
//             <div className="flex items-center justify-between mb-4">
//               <div className="flex items-center space-x-3">
//                 <div className="p-2 bg-orange-500/10 rounded-lg">
//                   <Zap className="h-4 w-4 text-orange-500" />
//                 </div>
//                 <div>
//                   <span className="font-medium text-slate-900 dark:text-slate-100">开发者模式</span>
//                   <p className="text-sm text-slate-600 dark:text-slate-400">启用系统测试和调试功能</p>
//                 </div>
//               </div>
//               <label className="relative inline-flex items-center cursor-pointer">
//                 <input
//                   type="checkbox"
//                   checked={isDeveloperMode}
//                   onChange={toggleDeveloperMode}
//                   className="sr-only peer"
//                 />
//                 <div className="w-11 h-6 bg-slate-200 dark:bg-slate-700 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-[#007aff]/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-[#007aff]"></div>
//               </label>
//             </div>
//
//             {/* 开发者模式详细选项 */}
//              {isDeveloperMode && (
//                <div className="mt-4 pt-4 border-t border-slate-200/50 dark:border-slate-600/50 space-y-3">
//                  <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
//                    <div className="flex items-center justify-between p-3 bg-slate-50 dark:bg-slate-800/50 rounded-lg">
//                      <span className="text-sm text-slate-700 dark:text-slate-300">控制台日志</span>
//                      <label className="relative inline-flex items-center cursor-pointer">
//                        <input
//                          type="checkbox"
//                          checked={consoleLog}
//                          onChange={(e) => updateDeveloperOption('enableConsoleLog', e.target.checked)}
//                          className="sr-only peer"
//                        />
//                        <div className="w-8 h-4 bg-slate-200 dark:bg-slate-700 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-4 peer-checked:after:border-white after:content-[''] after:absolute after:top-[1px] after:left-[1px] after:bg-white after:rounded-full after:h-3 after:w-3 after:transition-all peer-checked:bg-orange-500"></div>
//                      </label>
//                    </div>
//
//                    <div className="flex items-center justify-between p-3 bg-slate-50 dark:bg-slate-800/50 rounded-lg">
//                      <span className="text-sm text-slate-700 dark:text-slate-300">性能监控</span>
//                      <label className="relative inline-flex items-center cursor-pointer">
//                        <input
//                          type="checkbox"
//                          checked={performanceMonitoring}
//                          onChange={(e) => updateDeveloperOption('enablePerformanceMonitoring', e.target.checked)}
//                          className="sr-only peer"
//                        />
//                        <div className="w-8 h-4 bg-slate-200 dark:bg-slate-700 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-4 peer-checked:after:border-white after:content-[''] after:absolute after:top-[1px] after:left-[1px] after:bg-white after:rounded-full after:h-3 after:w-3 after:transition-all peer-checked:bg-orange-500"></div>
//                      </label>
//                    </div>
//
//                    <div className="flex items-center justify-between p-3 bg-slate-50 dark:bg-slate-800/50 rounded-lg">
//                      <span className="text-sm text-slate-700 dark:text-slate-300">API 调试</span>
//                      <label className="relative inline-flex items-center cursor-pointer">
//                        <input
//                          type="checkbox"
//                          checked={apiDebug}
//                          onChange={(e) => updateDeveloperOption('enableApiDebug', e.target.checked)}
//                          className="sr-only peer"
//                        />
//                        <div className="w-8 h-4 bg-slate-200 dark:bg-slate-700 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-4 peer-checked:after:border-white after:content-[''] after:absolute after:top-[1px] after:left-[1px] after:bg-white after:rounded-full after:h-3 after:w-3 after:transition-all peer-checked:bg-orange-500"></div>
//                      </label>
//                    </div>
//
//                    <div className="flex items-center justify-between p-3 bg-slate-50 dark:bg-slate-800/50 rounded-lg">
//                      <span className="text-sm text-slate-700 dark:text-slate-300">实验性功能</span>
//                      <label className="relative inline-flex items-center cursor-pointer">
//                        <input
//                          type="checkbox"
//                          checked={experimentalFeatures}
//                          onChange={(e) => updateDeveloperOption('enableExperimentalFeatures', e.target.checked)}
//                          className="sr-only peer"
//                        />
//                        <div className="w-8 h-4 bg-slate-200 dark:bg-slate-700 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-4 peer-checked:after:border-white after:content-[''] after:absolute after:top-[1px] after:left-[1px] after:bg-white after:rounded-full after:h-3 after:w-3 after:transition-all peer-checked:bg-orange-500"></div>
//                      </label>
//                    </div>
//                  </div>
//
//                  {/* 配置管理 */}
//                  <div className="mt-4 p-3 bg-indigo-50 dark:bg-indigo-900/20 border border-indigo-200 dark:border-indigo-800 rounded-lg">
//                    <div className="flex items-center justify-between mb-3">
//                      <span className="text-sm font-medium text-indigo-800 dark:text-indigo-200">配置管理</span>
//                      <div className="flex space-x-2">
//                        <button
//                          onClick={exportConfig}
//                          className="inline-flex items-center px-2 py-1 text-xs font-medium text-indigo-700 dark:text-indigo-300 bg-indigo-100 dark:bg-indigo-800/50 hover:bg-indigo-200 dark:hover:bg-indigo-800 rounded transition-colors"
//                        >
//                          <Download className="h-3 w-3 mr-1" />
//                          导出
//                        </button>
//                        <label className="inline-flex items-center px-2 py-1 text-xs font-medium text-indigo-700 dark:text-indigo-300 bg-indigo-100 dark:bg-indigo-800/50 hover:bg-indigo-200 dark:hover:bg-indigo-800 rounded transition-colors cursor-pointer">
//                          <Upload className="h-3 w-3 mr-1" />
//                          导入
//                          <input
//                            type="file"
//                            accept=".json"
//                            onChange={importConfig}
//                            className="hidden"
//                          />
//                        </label>
//                      </div>
//                    </div>
//                    <p className="text-xs text-indigo-700 dark:text-indigo-300">
//                      导出当前配置或导入之前保存的配置文件
//                    </p>
//                  </div>
//
//                  <div className="mt-4 p-3 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg">
//                    <div className="flex items-center space-x-2">
//                      <Shield className="h-4 w-4 text-yellow-600 dark:text-yellow-400" />
//                      <span className="text-sm font-medium text-yellow-800 dark:text-yellow-200">开发者模式已启用</span>
//                    </div>
//                    <p className="text-xs text-yellow-700 dark:text-yellow-300 mt-1">
//                      某些功能可能不稳定，仅建议开发和测试时使用
//                    </p>
//                  </div>
//                </div>
//              )}
//           </div>
//         </div>
//       </div>
//
//       {/* AI 处理设置 */}
//       <div className="bg-white/60 dark:bg-slate-800/60 backdrop-blur-sm border border-slate-200/50 dark:border-slate-700/50 rounded-2xl p-6">
//         <div className="flex items-center space-x-3 mb-6">
//           <div className="p-2 bg-purple-500/10 rounded-lg">
//             <Zap className="h-5 w-5 text-purple-500" />
//           </div>
//           <h2 className="text-xl font-semibold text-slate-900 dark:text-slate-100">AI 处理设置</h2>
//         </div>
//
//         <div className="space-y-4">
//           <div className="flex items-center justify-between p-4 bg-white/40 dark:bg-slate-700/40 backdrop-blur-sm border border-slate-200/50 dark:border-slate-600/50 rounded-xl">
//             <div className="flex items-center space-x-3">
//               <div className="p-2 bg-green-500/10 rounded-lg">
//                 <ImageIcon className="h-4 w-4 text-green-500" />
//               </div>
//               <div>
//                 <span className="font-medium text-slate-900 dark:text-slate-100">自动分析新图片</span>
//                 <p className="text-sm text-slate-600 dark:text-slate-400">上传时自动进行 AI 内容识别</p>
//               </div>
//             </div>
//             <label className="relative inline-flex items-center cursor-pointer">
//               <input type="checkbox" defaultChecked className="sr-only peer" />
//               <div className="w-11 h-6 bg-slate-200 dark:bg-slate-700 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-[#007aff]/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-[#007aff]"></div>
//             </label>
//           </div>
//
//           <div className="flex items-center justify-between p-4 bg-white/40 dark:bg-slate-700/40 backdrop-blur-sm border border-slate-200/50 dark:border-slate-600/50 rounded-xl">
//             <div className="flex items-center space-x-3">
//               <div className="p-2 bg-blue-500/10 rounded-lg">
//                 <Database className="h-4 w-4 text-blue-500" />
//               </div>
//               <div>
//                 <span className="font-medium text-slate-900 dark:text-slate-100">启用向量化存储</span>
//                 <p className="text-sm text-slate-600 dark:text-slate-400">使用向量数据库进行语义搜索</p>
//               </div>
//             </div>
//             <label className="relative inline-flex items-center cursor-pointer">
//               <input type="checkbox" defaultChecked className="sr-only peer" />
//               <div className="w-11 h-6 bg-slate-200 dark:bg-slate-700 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-[#007aff]/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-[#007aff]"></div>
//             </label>
//           </div>
//         </div>
//       </div>
//
//       {/* 配置模式 */}
//       <div className="bg-white/60 dark:bg-slate-800/60 backdrop-blur-sm border border-slate-200/50 dark:border-slate-700/50 rounded-2xl p-6">
//         <div className="flex items-center justify-between mb-6">
//           <div className="flex items-center space-x-3">
//             <div className="p-2 bg-indigo-500/10 rounded-lg">
//               <Server className="h-5 w-5 text-indigo-500" />
//             </div>
//             <h2 className="text-xl font-semibold text-slate-900 dark:text-slate-100">配置模式</h2>
//           </div>
//           <button
//             onClick={() => setShowConfigDetails(!showConfigDetails)}
//             className="text-sm text-indigo-600 dark:text-indigo-400 hover:text-indigo-700 dark:hover:text-indigo-300 font-medium"
//           >
//             {showConfigDetails ? '隐藏详情' : '显示详情'}
//           </button>
//         </div>
//
//         <div className="space-y-4">
//           {/* AI 服务配置概览 */}
//           <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
//             <div className="p-4 bg-white/40 dark:bg-slate-700/40 backdrop-blur-sm border border-slate-200/50 dark:border-slate-600/50 rounded-xl">
//               <div className="flex items-center justify-between">
//                 <span className="text-sm text-slate-600 dark:text-slate-400">AI 服务状态</span>
//                 <span className="inline-flex items-center px-2 py-1 text-xs font-medium bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-400 rounded">
//                   已配置
//                 </span>
//               </div>
//             </div>
//
//             <div className="p-4 bg-white/40 dark:bg-slate-700/40 backdrop-blur-sm border border-slate-200/50 dark:border-slate-600/50 rounded-xl">
//               <div className="flex items-center justify-between">
//                 <span className="text-sm text-slate-600 dark:text-slate-400">向量化服务</span>
//                 <span className="inline-flex items-center px-2 py-1 text-xs font-medium bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-400 rounded">
//                   已启用
//                 </span>
//               </div>
//             </div>
//           </div>
//
//           {/* 详细配置信息 */}
//           {showConfigDetails && (
//             <div className="mt-6 space-y-4">
//               <div className="p-4 bg-gradient-to-r from-indigo-50 to-purple-50 dark:from-indigo-900/20 dark:to-purple-900/20 border border-indigo-200/50 dark:border-indigo-700/50 rounded-xl">
//                 <h4 className="font-medium text-slate-900 dark:text-slate-100 mb-3 flex items-center">
//                   <Brain className="h-4 w-4 mr-2 text-indigo-500" />
//                   AI 模型配置
//                 </h4>
//                 <div className="grid grid-cols-1 md:grid-cols-2 gap-3 text-sm">
//                    <div className="flex justify-between">
//                      <span className="text-slate-600 dark:text-slate-400">视觉模型:</span>
//                      <span className="font-mono text-slate-900 dark:text-slate-100 text-xs">{aiConfig.vlModel}</span>
//                    </div>
//                    <div className="flex justify-between">
//                      <span className="text-slate-600 dark:text-slate-400">对话模型:</span>
//                      <span className="font-mono text-slate-900 dark:text-slate-100 text-xs">{aiConfig.chatModel}</span>
//                    </div>
//                    <div className="flex justify-between">
//                      <span className="text-slate-600 dark:text-slate-400">嵌入模型:</span>
//                      <span className="font-mono text-slate-900 dark:text-slate-100 text-xs">{aiConfig.embeddingModel}</span>
//                    </div>
//                    <div className="flex justify-between">
//                      <span className="text-slate-600 dark:text-slate-400">向量维度:</span>
//                      <span className="font-mono text-slate-900 dark:text-slate-100 text-xs">{aiConfig.embeddingDimension}</span>
//                    </div>
//                  </div>
//               </div>
//
//               <div className="p-4 bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 border border-green-200/50 dark:border-green-700/50 rounded-xl">
//                 <h4 className="font-medium text-slate-900 dark:text-slate-100 mb-3 flex items-center">
//                   <Server className="h-4 w-4 mr-2 text-green-500" />
//                   服务端点配置
//                 </h4>
//                 <div className="space-y-3 text-sm">
//                   <div className="bg-white/30 dark:bg-slate-800/30 p-3 rounded-lg">
//                     <div className="font-medium text-slate-700 dark:text-slate-300 mb-2">VL (视觉) 服务</div>
//                     <div className="space-y-1">
//                       <div className="flex justify-between items-center">
//                         <span className="text-slate-600 dark:text-slate-400">服务地址:</span>
//                         <span className="font-mono text-slate-900 dark:text-slate-100 text-xs bg-white/50 dark:bg-slate-800/50 px-2 py-1 rounded">
//                           {aiConfig.vlBaseUrl}
//                         </span>
//                       </div>
//                     </div>
//                   </div>
//                   <div className="bg-white/30 dark:bg-slate-800/30 p-3 rounded-lg">
//                     <div className="font-medium text-slate-700 dark:text-slate-300 mb-2">Chat (对话) 服务</div>
//                     <div className="space-y-1">
//                       <div className="flex justify-between items-center">
//                         <span className="text-slate-600 dark:text-slate-400">服务地址:</span>
//                         <span className="font-mono text-slate-900 dark:text-slate-100 text-xs bg-white/50 dark:bg-slate-800/50 px-2 py-1 rounded">
//                           {aiConfig.chatBaseUrl}
//                         </span>
//                       </div>
//                     </div>
//                   </div>
//                 </div>
//               </div>
//
//               <div className="p-3 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg">
//                 <div className="flex items-center space-x-2">
//                   <Info className="h-4 w-4 text-blue-600 dark:text-blue-400" />
//                   <span className="text-sm font-medium text-blue-800 dark:text-blue-200">配置说明</span>
//                 </div>
//                 <p className="text-xs text-blue-700 dark:text-blue-300 mt-1">
//                   配置信息来自环境变量文件 (.env)，如需修改请编辑相应的配置文件并重启应用
//                 </p>
//               </div>
//             </div>
//           )}
//         </div>
//       </div>
//
//       {/* 系统信息 */}
//       <div className="bg-white/60 dark:bg-slate-800/60 backdrop-blur-sm border border-slate-200/50 dark:border-slate-700/50 rounded-2xl p-6">
//         <div className="flex items-center space-x-3 mb-6">
//           <div className="p-2 bg-green-500/10 rounded-lg">
//             <Shield className="h-5 w-5 text-green-500" />
//           </div>
//           <h2 className="text-xl font-semibold text-slate-900 dark:text-slate-100">系统信息</h2>
//         </div>
//
//         <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
//            <div className="p-4 bg-white/40 dark:bg-slate-700/40 backdrop-blur-sm border border-slate-200/50 dark:border-slate-600/50 rounded-xl">
//              <div className="flex items-center justify-between">
//                <span className="text-sm text-slate-600 dark:text-slate-400">应用版本</span>
//                <span className="font-medium text-slate-900 dark:text-slate-100">{appConfig.version}</span>
//              </div>
//            </div>
//
//            <div className="p-4 bg-white/40 dark:bg-slate-700/40 backdrop-blur-sm border border-slate-200/50 dark:border-slate-600/50 rounded-xl">
//              <div className="flex items-center justify-between">
//                <span className="text-sm text-slate-600 dark:text-slate-400">运行模式</span>
//                <span className={`inline-flex items-center px-2 py-1 text-xs font-medium rounded ${
//                  appConfig.isDevelopment
//                    ? 'bg-yellow-100 dark:bg-yellow-900/30 text-yellow-700 dark:text-yellow-400'
//                    : 'bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-400'
//                }`}>
//                  {appConfig.isDevelopment ? '开发模式' : '生产模式'}
//                </span>
//              </div>
//            </div>
//
//            <div className="p-4 bg-white/40 dark:bg-slate-700/40 backdrop-blur-sm border border-slate-200/50 dark:border-slate-600/50 rounded-xl">
//              <div className="flex items-center justify-between">
//                <span className="text-sm text-slate-600 dark:text-slate-400">向量搜索</span>
//                <span className={`inline-flex items-center px-2 py-1 text-xs font-medium rounded ${
//                  databaseConfig.enableVectorSearch
//                    ? 'bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-400'
//                    : 'bg-gray-100 dark:bg-gray-900/30 text-gray-700 dark:text-gray-400'
//                }`}>
//                  {databaseConfig.enableVectorSearch ? '已启用' : '已禁用'}
//                </span>
//              </div>
//            </div>
//
//             <div className="p-4 bg-white/40 dark:bg-slate-700/40 backdrop-blur-sm border border-slate-200/50 dark:border-slate-600/50 rounded-xl">
//               <div className="flex items-center justify-between">
//                 <span className="text-sm text-slate-600 dark:text-slate-400">当前模型</span>
//                 <span className="font-medium text-slate-900 dark:text-slate-100 text-xs">{aiConfig.vlModel.split('/')[1] || aiConfig.vlModel}</span>
//               </div>
//             </div>
//
//             <div className="p-4 bg-white/40 dark:bg-slate-700/40 backdrop-blur-sm border border-slate-200/50 dark:border-slate-600/50 rounded-xl">
//               <div className="flex items-center justify-between">
//                 <span className="text-sm text-slate-600 dark:text-slate-400">配置状态</span>
//                 <span className={`inline-flex items-center px-2 py-1 text-xs font-medium rounded ${
//                   configManager.validateConfig().isValid
//                     ? 'bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-400'
//                     : 'bg-red-100 dark:bg-red-900/30 text-red-700 dark:text-red-400'
//                 }`}>
//                   {configManager.validateConfig().isValid ? '配置完整' : '配置不完整'}
//                 </span>
//               </div>
//             </div>
//
//             <div className="p-4 bg-white/40 dark:bg-slate-700/40 backdrop-blur-sm border border-slate-200/50 dark:border-slate-600/50 rounded-xl">
//               <div className="flex items-center justify-between">
//                 <span className="text-sm text-slate-600 dark:text-slate-400">缓存大小</span>
//                 <span className="font-medium text-slate-900 dark:text-slate-100 text-xs">{databaseConfig.cacheSize} MB</span>
//               </div>
//             </div>
//          </div>
//       </div>
//     </div>
//   )
// }