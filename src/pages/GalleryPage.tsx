import { useState, useEffect } from 'react'
import {
  FolderOpen,
  Calendar,
  Clock,
  ChevronDown,
  ChevronRight,
  Image as ImageIcon,
  Loader2,
  AlertCircle
} from 'lucide-react'
import { trpcClient } from '../lib/trpcClient'
import { getImageUrl, generateImagePlaceholder, convertTimestampToDate } from '../lib/imageUrlUtils'

// 实际从tRPC返回的图片数据类型（基于数据库schema）
interface TRPCImageRecord {
  id: string
  filePath: string
  fileName: string
  description?: string
  tags?: string
  metadata?: string
  capturedAt?: number
  createdAt: number
  updatedAt: number
  // 可能还有其他字段
  [key: string]: any
}

// 图片数据接口，适配UI需求
interface GalleryImage {
  id: string
  url: string
  title: string
  capturedAt: Date
  createdAt: Date
  filePath: string
  description: string
}

// 将数据库图片记录转换为画廊显示格式
const convertToGalleryImage = (record: TRPCImageRecord): GalleryImage => {
  // 使用统一的图片URL处理工具
  const fileUrl = getImageUrl(record.filePath)

  // 使用描述作为标题，如果没有描述则使用文件名
  const fileName = record.fileName || record.filePath?.split(/[/\\]/).pop() || ''
  const title = record.description || fileName || '未命名图片'

  // 使用统一的时间戳转换工具
  const capturedAt = convertTimestampToDate(record.capturedAt, record.createdAt)
  const createdAt = convertTimestampToDate(record.createdAt)

  return {
    id: record.id,
    url: fileUrl,
    title: title.trim() || '未命名图片',
    capturedAt,
    createdAt,
    filePath: record.filePath || '',
    description: record.description || ''
  }
}

interface ViewMode {
  id: string
  name: string
  icon: React.ReactNode
}

const viewModes: ViewMode[] = [
  { id: 'time', name: '时间视图', icon: <Clock className="h-4 w-4" /> },
  { id: 'folder', name: '文件夹视图', icon: <FolderOpen className="h-4 w-4" /> }
]

interface TimelineData {
  year: number
  months: {
    month: number
    days: {
      day: number
      count: number
    }[]
  }[]
}

export function GalleryPage(): JSX.Element {
  const [currentViewMode, setCurrentViewMode] = useState<ViewMode>(viewModes[0])
  const [selectedYear, setSelectedYear] = useState<number | null>(null)
  const [selectedMonth, setSelectedMonth] = useState<number | null>(null)
  const [selectedDay, setSelectedDay] = useState<number | null>(null)
  const [images, setImages] = useState<GalleryImage[]>([])
  const [filteredImages, setFilteredImages] = useState<GalleryImage[]>([])
  const [timelineData, setTimelineData] = useState<TimelineData[]>([])
  const [expandedYears, setExpandedYears] = useState<Set<number>>(new Set())
  const [expandedMonths, setExpandedMonths] = useState<Set<string>>(new Set())
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // 获取所有图片数据
  const fetchImages = async () => {
    try {
      setLoading(true)
      setError(null)

      const result = await trpcClient.image.queryImages.query({
        limit: 1000, // 可以根据需要调整
        sortBy: 'capturedAt',
        sortOrder: 'desc'
      })

      if (result.error) {
        setError(result.error)
        return
      }

      const galleryImages = (result.results as TRPCImageRecord[]).map(convertToGalleryImage)
      setImages(galleryImages)
    } catch (err) {
      setError(err instanceof Error ? err.message : '获取图片失败')
    } finally {
      setLoading(false)
    }
  }

  // 初始化加载图片
  useEffect(() => {
    fetchImages()
  }, [])

  // 生成时间轴数据
  useEffect(() => {
    if (images.length === 0) {
      setTimelineData([])
      return
    }

    const timeline: { [year: number]: { [month: number]: { [day: number]: number } } } = {}

    images.forEach(image => {
      const date = image.capturedAt
      const year = date.getFullYear()
      const month = date.getMonth() + 1
      const day = date.getDate()

      if (!timeline[year]) timeline[year] = {}
      if (!timeline[year][month]) timeline[year][month] = {}
      if (!timeline[year][month][day]) timeline[year][month][day] = 0
      timeline[year][month][day]++
    })

    const timelineArray: TimelineData[] = Object.entries(timeline)
      .map(([year, months]) => ({
        year: parseInt(year),
        months: Object.entries(months).map(([month, days]) => ({
          month: parseInt(month),
          days: Object.entries(days).map(([day, count]) => ({
            day: parseInt(day),
            count
          })).sort((a, b) => b.day - a.day)
        })).sort((a, b) => b.month - a.month)
      }))
      .sort((a, b) => b.year - a.year)

    setTimelineData(timelineArray)

    // 默认展开最新的年份
    if (timelineArray.length > 0) {
      setExpandedYears(new Set([timelineArray[0].year]))
    }
  }, [images])

  // 过滤图片
  useEffect(() => {
    let filtered = images

    if (selectedYear) {
      filtered = filtered.filter(img => img.capturedAt.getFullYear() === selectedYear)
    }
    if (selectedMonth) {
      filtered = filtered.filter(img => img.capturedAt.getMonth() + 1 === selectedMonth)
    }
    if (selectedDay) {
      filtered = filtered.filter(img => img.capturedAt.getDate() === selectedDay)
    }

    setFilteredImages(filtered)
  }, [images, selectedYear, selectedMonth, selectedDay])

  const toggleYear = (year: number) => {
    const newExpanded = new Set(expandedYears)
    if (newExpanded.has(year)) {
      newExpanded.delete(year)
    } else {
      newExpanded.add(year)
    }
    setExpandedYears(newExpanded)
  }

  const toggleMonth = (year: number, month: number) => {
    const key = `${year}-${month}`
    const newExpanded = new Set(expandedMonths)
    if (newExpanded.has(key)) {
      newExpanded.delete(key)
    } else {
      newExpanded.add(key)
    }
    setExpandedMonths(newExpanded)
  }

  const selectTimeFilter = (year?: number, month?: number, day?: number) => {
    setSelectedYear(year || null)
    setSelectedMonth(month || null)
    setSelectedDay(day || null)
  }

  const clearTimeFilter = () => {
    setSelectedYear(null)
    setSelectedMonth(null)
    setSelectedDay(null)
  }

  const getMonthName = (month: number) => {
    const months = ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月']
    return months[month - 1]
  }

  return (
    <div className="h-full flex bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-900 dark:to-slate-800">
      {/* 主内容区域 */}
      <div className="flex-1 flex flex-col">
        {/* 顶部工具栏 */}
        <div className="p-6 bg-white/60 dark:bg-slate-800/60 backdrop-blur-sm border-b border-slate-200/50 dark:border-slate-700/50">
          <div className="flex items-center justify-between">
            {/* 左侧：视图切换 */}
            <div className="flex items-center space-x-4">
              <div className="relative">
                <button className="flex items-center space-x-2 px-4 py-2 bg-white dark:bg-slate-700 border border-slate-200 dark:border-slate-600 rounded-lg hover:bg-slate-50 dark:hover:bg-slate-600 transition-colors">
                  {currentViewMode.icon}
                  <span className="text-sm font-medium text-slate-700 dark:text-slate-300">
                    {currentViewMode.name}
                  </span>
                  <ChevronDown className="h-4 w-4 text-slate-500" />
                </button>
              </div>
              
              {/* 当前筛选状态 */}
              {(selectedYear || selectedMonth || selectedDay) && (
                <div className="flex items-center space-x-2">
                  <div className="flex items-center space-x-1 px-3 py-1 bg-blue-100 dark:bg-blue-900/50 text-blue-700 dark:text-blue-300 rounded-lg text-sm">
                    <Calendar className="h-3 w-3" />
                    <span>
                      {selectedYear && `${selectedYear}年`}
                      {selectedMonth && `${selectedMonth}月`}
                      {selectedDay && `${selectedDay}日`}
                    </span>
                  </div>
                  <button
                    onClick={clearTimeFilter}
                    className="text-xs text-slate-500 hover:text-slate-700 dark:hover:text-slate-300"
                  >
                    清除
                  </button>
                </div>
              )}
            </div>

            {/* 右侧：图片数量和刷新按钮 */}
            <div className="flex items-center space-x-4">
              <div className="text-sm text-slate-600 dark:text-slate-400">
                {loading ? '加载中...' : `共 ${filteredImages.length} 张图片`}
                {!loading && images.length > 0 && filteredImages.length !== images.length && (
                  <span className="text-xs text-slate-500 ml-1">
                    (总共 {images.length} 张)
                  </span>
                )}
              </div>
              <button
                onClick={fetchImages}
                disabled={loading}
                className="p-2 text-slate-500 hover:text-slate-700 dark:hover:text-slate-300 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                title="刷新图片"
              >
                <svg className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                </svg>
              </button>
            </div>
          </div>
        </div>

        {/* 图片展示区域 */}
        <div className="flex-1 p-6 overflow-auto">
          {loading ? (
            <div className="flex flex-col items-center justify-center h-full text-slate-500 dark:text-slate-400">
              <Loader2 className="h-16 w-16 mb-4 opacity-50 animate-spin" />
              <p className="text-lg font-medium mb-2">加载中...</p>
              <p className="text-sm">正在获取图片数据</p>
            </div>
          ) : error ? (
            <div className="flex flex-col items-center justify-center h-full text-red-500 dark:text-red-400">
              <AlertCircle className="h-16 w-16 mb-4 opacity-50" />
              <p className="text-lg font-medium mb-2">加载失败</p>
              <p className="text-sm mb-4">{error}</p>
              <button
                onClick={fetchImages}
                className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
              >
                重试
              </button>
            </div>
          ) : filteredImages.length > 0 ? (
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 2xl:grid-cols-6 gap-4">
              {filteredImages.map((image) => (
                <div
                  key={image.id}
                  className="group relative bg-white dark:bg-slate-800 rounded-xl overflow-hidden shadow-sm hover:shadow-lg transition-all duration-300 hover:scale-105"
                >
                  <div className="aspect-square overflow-hidden">
                    <img
                      src={image.url}
                      alt={image.title}
                      className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-300"
                      onError={(e) => {
                        // 如果图片加载失败，显示占位符
                        const target = e.target as HTMLImageElement
                        if (!target.dataset.errorHandled) {
                          target.dataset.errorHandled = 'true'
                          target.src = generateImagePlaceholder(400, 400)
                          target.alt = '图片加载失败'
                        }
                      }}
                    />
                  </div>
                  <div className="p-3">
                    <h3 className="text-sm font-medium text-slate-900 dark:text-slate-100 truncate" title={image.title}>
                      {image.title}
                    </h3>
                    <p className="text-xs text-slate-500 dark:text-slate-400 mt-1">
                      {image.capturedAt.toLocaleDateString('zh-CN')}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="flex flex-col items-center justify-center h-full text-slate-500 dark:text-slate-400">
              <ImageIcon className="h-16 w-16 mb-4 opacity-50" />
              <p className="text-lg font-medium mb-2">暂无图片</p>
              <p className="text-sm">
                {images.length === 0 ? '数据库中没有图片' : '请选择其他时间范围或清除筛选条件'}
              </p>
            </div>
          )}
        </div>
      </div>

      {/* 右侧时间轴 */}
      <div className="w-80 bg-white/60 dark:bg-slate-800/60 backdrop-blur-sm border-l border-slate-200/50 dark:border-slate-700/50 overflow-auto">
        <div className="p-6">
          <h2 className="text-lg font-semibold text-slate-900 dark:text-slate-100 mb-4 flex items-center">
            <Calendar className="h-5 w-5 mr-2 text-blue-500" />
            时间轴
          </h2>
          
          <div className="space-y-2">
            {timelineData.map((yearData) => (
              <div key={yearData.year} className="border border-slate-200 dark:border-slate-700 rounded-lg overflow-hidden">
                {/* 年份 */}
                <button
                  onClick={() => {
                    toggleYear(yearData.year)
                    selectTimeFilter(yearData.year)
                  }}
                  className={`w-full flex items-center justify-between p-3 text-left hover:bg-slate-50 dark:hover:bg-slate-700/50 transition-colors ${
                    selectedYear === yearData.year ? 'bg-blue-50 dark:bg-blue-900/20' : ''
                  }`}
                >
                  <div className="flex items-center space-x-2">
                    {expandedYears.has(yearData.year) ? (
                      <ChevronDown className="h-4 w-4 text-slate-500" />
                    ) : (
                      <ChevronRight className="h-4 w-4 text-slate-500" />
                    )}
                    <span className="font-medium text-slate-900 dark:text-slate-100">
                      {yearData.year}年
                    </span>
                  </div>
                  <span className="text-sm text-slate-500 dark:text-slate-400">
                    {yearData.months.reduce((total, month) => 
                      total + month.days.reduce((dayTotal, day) => dayTotal + day.count, 0), 0
                    )} 张
                  </span>
                </button>

                {/* 月份 */}
                {expandedYears.has(yearData.year) && (
                  <div className="border-t border-slate-200 dark:border-slate-700">
                    {yearData.months.map((monthData) => (
                      <div key={monthData.month}>
                        <button
                          onClick={() => {
                            toggleMonth(yearData.year, monthData.month)
                            selectTimeFilter(yearData.year, monthData.month)
                          }}
                          className={`w-full flex items-center justify-between p-3 pl-8 text-left hover:bg-slate-50 dark:hover:bg-slate-700/50 transition-colors ${
                            selectedYear === yearData.year && selectedMonth === monthData.month ? 'bg-blue-50 dark:bg-blue-900/20' : ''
                          }`}
                        >
                          <div className="flex items-center space-x-2">
                            {expandedMonths.has(`${yearData.year}-${monthData.month}`) ? (
                              <ChevronDown className="h-3 w-3 text-slate-500" />
                            ) : (
                              <ChevronRight className="h-3 w-3 text-slate-500" />
                            )}
                            <span className="text-sm text-slate-700 dark:text-slate-300">
                              {getMonthName(monthData.month)}
                            </span>
                          </div>
                          <span className="text-xs text-slate-500 dark:text-slate-400">
                            {monthData.days.reduce((total, day) => total + day.count, 0)} 张
                          </span>
                        </button>

                        {/* 日期 */}
                        {expandedMonths.has(`${yearData.year}-${monthData.month}`) && (
                          <div className="bg-slate-50 dark:bg-slate-800/50">
                            {monthData.days.map((dayData) => (
                              <button
                                key={dayData.day}
                                onClick={() => selectTimeFilter(yearData.year, monthData.month, dayData.day)}
                                className={`w-full flex items-center justify-between p-2 pl-12 text-left hover:bg-slate-100 dark:hover:bg-slate-700/50 transition-colors ${
                                  selectedYear === yearData.year && selectedMonth === monthData.month && selectedDay === dayData.day ? 'bg-blue-100 dark:bg-blue-900/30' : ''
                                }`}
                              >
                                <span className="text-sm text-slate-600 dark:text-slate-400">
                                  {dayData.day}日
                                </span>
                                <span className="text-xs text-slate-500 dark:text-slate-400">
                                  {dayData.count} 张
                                </span>
                              </button>
                            ))}
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  )
}