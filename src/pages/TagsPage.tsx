import { useState, useEffect, useCallback, useMemo } from 'react'
import { trpcClient } from '@/lib/trpcClient.ts'
import { EnhancedTag } from '@/components/ui/enhanced-tag'
import { ImageCategory } from '@/types/database'
import {
  Tag,
  Search,
  Filter,
  TrendingUp,
  Grid3X3,
  Image as ImageIcon,
  Loader2,
  Hash,
  Palette,
  MapPin,
  Camera,
  Activity,
  MoreHorizontal,
  X
} from 'lucide-react'

interface TagInfo {
  name: string
  count: number
  category: ImageCategory
  color: string
}

export function TagsPage(): JSX.Element {
  const [allTags, setAllTags] = useState<TagInfo[]>([])
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedCategory, setSelectedCategory] = useState<ImageCategory | 'all'>('all')
  const [selectedTag, setSelectedTag] = useState<string>('')
  const [loading, setLoading] = useState(true)



  // 标签分类映射
  const tagCategories = useMemo(() => ({
    all: '全部',
    color: '颜色',
    scene: '场景',
    mood: '氛围',
    time: '时间',
    style: '风格',
    object: '物体',
    action: '动作',
    clothing: '衣着',
    relationship: '关系',
    domain: '领域',
    text: '文字',
    location: '地点',
    other: '其他',
    general: '通用'
  }), [])

  // 获取标签颜色
  const getTagColor = (category: ImageCategory): string => {
    const colors = {
      'color': 'bg-pink-500/10 text-pink-600 border-pink-200/50',
      'scene': 'bg-blue-500/10 text-blue-600 border-blue-200/50',
      'mood': 'bg-purple-500/10 text-purple-600 border-purple-200/50',
      'time': 'bg-orange-500/10 text-orange-600 border-orange-200/50',
      'style': 'bg-indigo-500/10 text-indigo-600 border-indigo-200/50',
      'object': 'bg-green-500/10 text-green-600 border-green-200/50',
      'action': 'bg-red-500/10 text-red-600 border-red-200/50',
      'clothing': 'bg-teal-500/10 text-teal-600 border-teal-200/50',
      'relationship': 'bg-cyan-500/10 text-cyan-600 border-cyan-200/50',
      'domain': 'bg-amber-500/10 text-amber-600 border-amber-200/50',
      'text': 'bg-emerald-500/10 text-emerald-600 border-emerald-200/50',
      'location': 'bg-orange-500/10 text-orange-600 border-orange-200/50',
      'other': 'bg-gray-500/10 text-gray-600 border-gray-200/50',
      'general': 'bg-gray-500/10 text-gray-600 border-gray-200/50'
    }
    return colors[category] || 'bg-gray-500/10 text-gray-600 border-gray-200/50'
  }

  // 获取分类图标
  const getCategoryIcon = (category: ImageCategory | 'all') => {
    const icons = {
      all: Filter,
      color: Palette,
      scene: Grid3X3,
      mood: Camera,
      time: Camera,
      style: Camera,
      object: Hash,
      action: Activity,
      clothing: Hash,
      relationship: Hash,
      domain: Hash,
      text: Hash,
      location: MapPin,
      other: MoreHorizontal,
      general: MoreHorizontal
    }
    return icons[category] || Hash
  }

  // 加载所有标签
  const loadAllTags = useCallback(async () => {
    setLoading(true)
    try {
      const tags = await trpcClient.tag.getAllTags.query()

      // 创建标签信息
      const tagInfo: TagInfo[] = tags.tags.map((tag) => ({
        name: tag.tagText,
        count: tag.frequency,
        category: tag.category || 'general',
        color: getTagColor(tag.category || 'general')
      })).sort((a, b) => b.count - a.count)

      setAllTags(tagInfo)
    } catch (error) {
      console.error('加载标签失败:', error)
    } finally {
      setLoading(false)
    }
  }, [])

  // 处理标签点击
  const handleTagClick = (tag: string) => {
    setSelectedTag(tag)
  }

  // 筛选标签
  const filteredTags = useMemo(() => {
    return allTags.filter((tag) => {
      const matchesSearch = !searchQuery.trim() || tag.name.toLowerCase().includes(searchQuery.toLowerCase())
      const matchesCategory = selectedCategory === 'all' || tag.category === selectedCategory
      return matchesSearch && matchesCategory
    })
  }, [allTags, searchQuery, selectedCategory])

  // 获取分类统计
  const categoryStats = useMemo(() => {
    return Object.entries(tagCategories).map(([key, name]) => {
      if (key === 'all') return { key, name, count: allTags.length }
      return {
        key,
        name,
        count: allTags.filter((tag) => tag.category === key).length
      }
    })
  }, [allTags, tagCategories])

  useEffect(() => {
    loadAllTags()
  }, [loadAllTags])

  return (
    <div className="p-8 space-y-8">
      {/* 页面标题 */}
      <div className="space-y-2">
        <h1 className="text-3xl font-semibold text-slate-900 dark:text-slate-100">标签浏览</h1>
        <p className="text-slate-600 dark:text-slate-400">
          浏览和搜索您的图片标签，发现更多内容
        </p>
      </div>

      {/* 统计信息 */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-white/60 dark:bg-slate-800/60 backdrop-blur-sm border border-slate-200/50 dark:border-slate-700/50 rounded-2xl p-6">
          <div className="flex items-center space-x-3">
            <div className="p-3 bg-[#007aff]/10 rounded-full">
              <Tag className="h-6 w-6 text-[#007aff]" />
            </div>
            <div>
              <p className="text-2xl font-semibold text-slate-900 dark:text-slate-100">{allTags.length}</p>
              <p className="text-sm text-slate-600 dark:text-slate-400">总标签数</p>
            </div>
          </div>
        </div>

        <div className="bg-white/60 dark:bg-slate-800/60 backdrop-blur-sm border border-slate-200/50 dark:border-slate-700/50 rounded-2xl p-6">
          <div className="flex items-center space-x-3">
            <div className="p-3 bg-green-500/10 rounded-full">
              <Filter className="h-6 w-6 text-green-500" />
            </div>
            <div>
              <p className="text-2xl font-semibold text-slate-900 dark:text-slate-100">{filteredTags.length}</p>
              <p className="text-sm text-slate-600 dark:text-slate-400">筛选结果</p>
            </div>
          </div>
        </div>


      </div>

      {/* 搜索和筛选 */}
      <div className="bg-white/60 dark:bg-slate-800/60 backdrop-blur-sm border border-slate-200/50 dark:border-slate-700/50 rounded-2xl p-6">
        <div className="flex items-center space-x-3 mb-6">
          <div className="p-2 bg-[#007aff]/10 rounded-lg">
            <Search className="h-5 w-5 text-[#007aff]" />
          </div>
          <h2 className="text-xl font-semibold text-slate-900 dark:text-slate-100">搜索与筛选</h2>
        </div>

        <div className="space-y-6">
          {/* 搜索框 */}
          <div className="relative">
            <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-slate-400" />
            <input
              type="text"
              placeholder="搜索标签..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full pl-12 pr-4 h-12 text-base bg-white/60 dark:bg-slate-800/60 backdrop-blur-sm border border-slate-200/50 dark:border-slate-700/50 rounded-xl focus:border-[#007aff]/50 focus:ring-[#007aff]/20 focus:ring-2 outline-none transition-all duration-200"
            />
          </div>

          {/* 分类筛选 */}
          <div className="space-y-3">
            <h4 className="text-sm font-medium text-slate-700 dark:text-slate-300">标签分类</h4>
            <div className="flex flex-wrap gap-2">
              {categoryStats.map(({ key, name, count }) => {
                const Icon = key === 'all' ? Filter : getCategoryIcon(key as ImageCategory)
                const isSelected = selectedCategory === key
                return (
                  <button
                    key={key}
                    onClick={() => setSelectedCategory(key as ImageCategory | 'all')}
                    className={`inline-flex items-center px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 ${
                      isSelected
                        ? 'bg-[#007aff] text-white shadow-md'
                        : 'bg-white/40 dark:bg-slate-700/40 text-slate-700 dark:text-slate-300 hover:bg-white/60 dark:hover:bg-slate-700/60'
                    }`}
                  >
                    <Icon className="h-4 w-4 mr-2" />
                    {name}
                    <span className={`ml-2 px-1.5 py-0.5 rounded text-xs ${
                      isSelected ? 'bg-white/20' : 'bg-slate-200/50 dark:bg-slate-600/50'
                    }`}>
                      {count}
                    </span>
                  </button>
                )
              })}
            </div>
          </div>
        </div>
      </div>

      {/* 标签展示 */}
      <div className="bg-white/60 dark:bg-slate-800/60 backdrop-blur-sm border border-slate-200/50 dark:border-slate-700/50 rounded-2xl p-6">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-[#007aff]/10 rounded-lg">
              <TrendingUp className="h-5 w-5 text-[#007aff]" />
            </div>
            <h2 className="text-xl font-semibold text-slate-900 dark:text-slate-100">标签列表</h2>
          </div>
          <span className="px-3 py-1 text-sm bg-slate-100 dark:bg-slate-700 text-slate-600 dark:text-slate-400 rounded-lg">
            {filteredTags.length} 个标签
          </span>
        </div>

        {loading ? (
          <div className="flex items-center justify-center py-12">
            <Loader2 className="h-6 w-6 animate-spin mr-3 text-[#007aff]" />
            <span className="text-slate-600 dark:text-slate-400">加载标签中...</span>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {filteredTags.map((tag) => (
              <EnhancedTag
                key={tag.name}
                name={tag.name}
                count={tag.count}
                category={tag.category}
                variant="blur-text"
                selected={selectedTag === tag.name}
                onClick={() => handleTagClick(tag.name)}
              />
            ))}
          </div>
        )}
      </div>

      {/* 选中标签的图片展示 */}
      {/*{selectedTag && (*/}
      {/*  <div className="bg-white/60 dark:bg-slate-800/60 backdrop-blur-sm border border-slate-200/50 dark:border-slate-700/50 rounded-2xl p-6">*/}
      {/*    <div className="flex items-center justify-between mb-6">*/}
      {/*      <div className="flex items-center space-x-3">*/}
      {/*        <div className="p-2 bg-[#007aff]/10 rounded-lg">*/}
      {/*          <ImageIcon className="h-5 w-5 text-[#007aff]" />*/}
      {/*        </div>*/}
      {/*        <h2 className="text-xl font-semibold text-slate-900 dark:text-slate-100">*/}
      {/*          标签 "{selectedTag}" 的相关图片*/}
      {/*        </h2>*/}
      {/*        <span className="px-3 py-1 text-sm bg-slate-100 dark:bg-slate-700 text-slate-600 dark:text-slate-400 rounded-lg">*/}
      {/*          {tagImages.length} 张*/}
      {/*        </span>*/}
      {/*      </div>*/}
      {/*      <button*/}
      {/*        onClick={() => setSelectedTag('')}*/}
      {/*        className="p-2 text-slate-400 hover:text-slate-600 dark:hover:text-slate-300 transition-colors"*/}
      {/*      >*/}
      {/*        <X className="h-5 w-5" />*/}
      {/*      </button>*/}
      {/*    </div>*/}

      {/*    {loadingImages ? (*/}
      {/*      <div className="flex items-center justify-center py-12">*/}
      {/*        <Loader2 className="h-6 w-6 animate-spin mr-3 text-[#007aff]" />*/}
      {/*        <span className="text-slate-600 dark:text-slate-400">加载图片中...</span>*/}
      {/*      </div>*/}
      {/*    ) : tagImages.length > 0 ? (*/}
      {/*      <ImageGrid*/}
      {/*        images={tagImages}*/}
      {/*        onImageSelect={(image) => console.log('选中图片:', image)}*/}
      {/*      />*/}
      {/*    ) : (*/}
      {/*      <div className="text-center py-12 text-slate-500 dark:text-slate-400">*/}
      {/*        暂无相关图片*/}
      {/*      </div>*/}
      {/*    )}*/}
      {/*  </div>*/}
      {/*)}*/}
    </div>
  )
}