import { useState, useEffect, useCallback, useRef } from 'react'
import { AdvancedFolderSelector } from '../components/AdvancedFolderSelector'
import { Button } from '@/components/ui/button'
import { RefreshCw, Folder, Plus, Clock, CheckCircle, AlertCircle, Loader2, Eye, Trash2, Edit3, Play } from 'lucide-react'
import { toast } from 'sonner'
import { trpcClient } from '../lib/trpcClient'
import { LibraryConfig, LibraryScanProgress, LibraryStatistics } from '@shared/types/database'

// 扩展类型以包含前端需要的计算字段和进度信息
interface ExtendedLibraryConfig extends LibraryConfig {
  imageCount?: number
  totalSize?: number
  lastScanTime?: string
  parsedScanProgress?: {
    status?: string
    processed?: number
    total?: number
    error?: string
  }
}

export default function ImageLibraryPage() {
  const [libraries, setLibraries] = useState<ExtendedLibraryConfig[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [showFolderSelector, setShowFolderSelector] = useState(false)
  const [refreshing, setRefreshing] = useState(false)
  const [editingLibrary, setEditingLibrary] = useState<string | null>(null)
  const [editName, setEditName] = useState('')
  const [scanningLibraries, setScanningLibraries] = useState<Set<string>>(new Set())

  // 轮询相关状态
  const pollingIntervalRef = useRef<NodeJS.Timeout | null>(null)
  const [isPolling, setIsPolling] = useState(false)
  
  const loadLibraries = useCallback(async (showLoadingIndicator = true) => {
    try {
      if (showLoadingIndicator) {
        setIsLoading(true)
      }
      const response = await trpcClient.library.getAllLibraries.query()

      if (response.success && response.libraries) {
        // 扩展库配置，添加计算字段
        // 注意：DAO层已经解析了JSON字段，所以这里直接使用
        const extendedLibraries = response.libraries.map(lib => {
          const statistics = lib.statistics as LibraryStatistics || {}
          const parsedScanProgress = lib.scanProgress as LibraryScanProgress || {}

          return {
            ...lib,
            imageCount: statistics?.totalImages || 0,
            totalSize: statistics?.totalSize || 0,
            lastScanTime: lib.lastScanAt,
            parsedScanProgress
          }
        })

        setLibraries(extendedLibraries)
        console.log('图片库加载完成，共', extendedLibraries.length, '个库')
      } else {
        console.error('加载图片库失败:', response.error)
        toast.error(`加载图片库失败: ${response.error}`)
      }
    } catch (error) {
      console.error('加载图片库失败:', error)
      toast.error('加载图片库失败')
    } finally {
      if (showLoadingIndicator) {
        setIsLoading(false)
      }
    }
  }, [])
  
  // 使用 ref 来保存最新的 loadLibraries 函数，避免轮询回调中的依赖问题
  const loadLibrariesRef = useRef(loadLibraries)
  loadLibrariesRef.current = loadLibraries

  // 轮询管理函数
  const startPolling = useCallback(() => {
    if (pollingIntervalRef.current) return // 避免重复启动

    setIsPolling(true)
    pollingIntervalRef.current = setInterval(() => {
      loadLibrariesRef.current(false) // 轮询时不显示加载指示器
    }, 2000) // 每2秒轮询一次

    console.log('开始轮询任务状态')
  }, [])

  const stopPolling = useCallback(() => {
    if (pollingIntervalRef.current) {
      clearInterval(pollingIntervalRef.current)
      pollingIntervalRef.current = null
    }
    setIsPolling(false)
    console.log('停止轮询任务状态')
  }, [])

  // 初始加载 - 只在组件挂载时执行一次
  useEffect(() => {
    loadLibraries()
  }, []) // 空依赖数组，只在挂载时执行

  // 轮询状态管理 - 根据库的扫描状态决定是否需要轮询
  useEffect(() => {
    const hasActiveScans = libraries.some(lib =>
      lib.parsedScanProgress?.status === 'pending' ||
      lib.parsedScanProgress?.status === 'scanning' ||
      lib.parsedScanProgress?.status === 'running' ||
      lib.parsedScanProgress?.status === 'processing'
    )

    if (hasActiveScans && !isPolling) {
      startPolling()
    } else if (!hasActiveScans && isPolling) {
      stopPolling()
    }
  }, [libraries, isPolling, startPolling, stopPolling])

  // 组件卸载时清理轮询
  useEffect(() => {
    return () => {
      if (pollingIntervalRef.current) {
        clearInterval(pollingIntervalRef.current)
        pollingIntervalRef.current = null
      }
    }
  }, [])
  

  
  const handleAddLibrary = () => {
    setShowFolderSelector(true)
  }
  
  const handleFolderSelected = async (folderPath: string) => {
    try {
      const response = await trpcClient.library.createLibrary.mutate({
        name: folderPath.split('/').pop() || 'New Library',
        rootPath: folderPath,
        type: 'local',
        description: `本地图片库: ${folderPath}`
      })
      
      if (response.success) {
        toast.success('图片库已创建')
        loadLibraries()
      } else {
        toast.error(`创建失败: ${response.error}`)
      }
    } catch (error: any) {
      console.error('创建图片库失败:', error)
      toast.error(`创建失败: ${error.message}`)
    } finally {
      setShowFolderSelector(false)
    }
  }
  
  const handleScanLibrary = async (libraryId: string) => {
    try {
      setScanningLibraries(prev => new Set(prev).add(libraryId))
      const response = await trpcClient.library.startScan.mutate(libraryId)
      if (response.success) {
        toast.success('开始扫描图片库')
        // 立即刷新以获取最新状态，并启动轮询
        await loadLibraries(false)
        if (!isPolling) {
          startPolling()
        }
      } else {
        toast.error(`扫描失败: ${response.error}`)
      }
    } catch (error: any) {
      console.error('扫描失败:', error)
      toast.error(`扫描失败: ${error.message}`)
    } finally {
      setScanningLibraries(prev => {
        const newSet = new Set(prev)
        newSet.delete(libraryId)
        return newSet
      })
    }
  }
  
  const handleUpdateLibraryName = async (libraryId: string, newName: string) => {
    try {
      const response = await trpcClient.library.updateLibraryName.mutate({
        id: libraryId,
        newName
      })
      if (response.success) {
        toast.success('图片库名称已更新')
        setEditingLibrary(null)
        loadLibraries()
      } else {
        toast.error(`更新失败: ${response.error}`)
      }
    } catch (error: any) {
      console.error('更新图片库失败:', error)
      toast.error(`更新失败: ${error.message}`)
    }
  }
  
  const handleDeleteLibrary = async (libraryId: string, libraryName: string) => {
    if (!confirm(`确定要删除图片库 "${libraryName}" 吗？这将删除所有图像记录但不会删除实际文件。`)) {
      return
    }
    
    try {
      const response = await trpcClient.library.deleteLibrary.mutate(libraryId)
      if (response.success) {
        toast.success('图片库已删除')
        loadLibraries()
      } else {
        toast.error(`删除失败: ${response.error}`)
      }
    } catch (error: any) {
      console.error('删除图片库失败:', error)
      toast.error(`删除失败: ${error.message}`)
    }
  }
  
  const handleRefresh = async () => {
    setRefreshing(true)
    await loadLibraries(true) // 手动刷新时显示加载指示器
    setRefreshing(false)
    toast.success('刷新完成')
  }
  
  const getStatusIcon = (library: ExtendedLibraryConfig) => {
    const scanProgress = library.parsedScanProgress
    const isScanning = scanProgress?.status === 'running' || scanProgress?.status === 'processing'

    if (isScanning) return <Loader2 className="h-5 w-5 text-blue-500 animate-spin" />
    if (scanProgress?.status === 'error') return <AlertCircle className="h-5 w-5 text-red-500" />

    switch (library.status) {
      case 'active': return <CheckCircle className="h-5 w-5 text-green-500" />
      case 'offline': return <AlertCircle className="h-5 w-5 text-orange-500" />
      case 'removed': return <AlertCircle className="h-5 w-5 text-red-500" />
      default: return <AlertCircle className="h-5 w-5 text-gray-500" />
    }
  }

  const getStatusText = (library: ExtendedLibraryConfig) => {
    const scanProgress = library.parsedScanProgress

    if (scanProgress?.status === 'running' || scanProgress?.status === 'processing') {
      const processed = scanProgress.processed || 0
      const total = scanProgress.total || 0
      return total > 0 ? `扫描中 (${processed}/${total})` : '扫描中'
    }

    if (scanProgress?.status === 'error') {
      return `扫描错误: ${scanProgress.error || '未知错误'}`
    }

    if (scanProgress?.status === 'completed') {
      return '扫描完成'
    }

    switch (library.status) {
      case 'active': return '活跃'
      case 'offline': return '离线'
      case 'removed': return '已移除'
      default: return '未知'
    }
  }
  
  const formatLastScanTime = (lastScanTime?: string) => {
    if (!lastScanTime) return '未扫描'
    
    const lastScan = new Date(lastScanTime)
    const now = new Date()
    const diff = now.getTime() - lastScan.getTime()
    const hours = Math.floor(diff / (1000 * 60 * 60))
    const days = Math.floor(hours / 24)
    
    if (days > 0) {
      return `${days}天前`
    } else if (hours > 0) {
      return `${hours}小时前`
    } else {
      return '刚刚'
    }
  }
  
  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 B'
    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const startEdit = (library: ExtendedLibraryConfig) => {
    setEditingLibrary(library.id)
    setEditName(library.name)
  }

  const saveEdit = () => {
    if (editingLibrary && editName.trim()) {
      handleUpdateLibraryName(editingLibrary, editName.trim())
    }
  }

  const cancelEdit = () => {
    setEditingLibrary(null)
    setEditName('')
  }

  return (
    <div className="p-8 space-y-8">
      {/* 页面标题 */}
      <div className="space-y-2">
        <h1 className="text-3xl font-semibold text-slate-900 dark:text-slate-100">图片库管理</h1>
        <p className="text-slate-600 dark:text-slate-400">
          管理您的图片库，添加新的文件夹或扫描现有内容
        </p>
      </div>

      {/* 统计信息 */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white/60 dark:bg-slate-800/60 backdrop-blur-sm border border-slate-200/50 dark:border-slate-700/50 rounded-2xl p-6">
          <div className="flex items-center space-x-3">
            <div className="p-3 bg-[#007aff]/10 rounded-full">
              <Folder className="h-6 w-6 text-[#007aff]" />
            </div>
            <div>
              <p className="text-2xl font-semibold text-slate-900 dark:text-slate-100">{libraries.length}</p>
              <p className="text-sm text-slate-600 dark:text-slate-400">图片库数量</p>
            </div>
          </div>
        </div>
        
        <div className="bg-white/60 dark:bg-slate-800/60 backdrop-blur-sm border border-slate-200/50 dark:border-slate-700/50 rounded-2xl p-6">
          <div className="flex items-center space-x-3">
            <div className="p-3 bg-green-500/10 rounded-full">
              <CheckCircle className="h-6 w-6 text-green-500" />
            </div>
            <div>
              <p className="text-2xl font-semibold text-slate-900 dark:text-slate-100">
                {libraries.filter(lib => lib.status === 'active').length}
              </p>
              <p className="text-sm text-slate-600 dark:text-slate-400">活跃图片库</p>
            </div>
          </div>
        </div>
        
        <div className="bg-white/60 dark:bg-slate-800/60 backdrop-blur-sm border border-slate-200/50 dark:border-slate-700/50 rounded-2xl p-6">
          <div className="flex items-center space-x-3">
            <div className="p-3 bg-purple-500/10 rounded-full">
              <Eye className="h-6 w-6 text-purple-500" />
            </div>
            <div>
              <p className="text-2xl font-semibold text-slate-900 dark:text-slate-100">
                {libraries.reduce((total, lib) => total + (lib.imageCount || 0), 0)}
              </p>
              <p className="text-sm text-slate-600 dark:text-slate-400">总图片数量</p>
            </div>
          </div>
        </div>
        
        <div className="bg-white/60 dark:bg-slate-800/60 backdrop-blur-sm border border-slate-200/50 dark:border-slate-700/50 rounded-2xl p-6">
          <div className="flex items-center space-x-3">
            <div className="p-3 bg-orange-500/10 rounded-full">
              <Loader2 className="h-6 w-6 text-orange-500" />
            </div>
            <div>
              <p className="text-2xl font-semibold text-slate-900 dark:text-slate-100">
                {libraries.filter(lib =>
                  lib.parsedScanProgress?.status === 'running' ||
                  lib.parsedScanProgress?.status === 'processing'
                ).length}
              </p>
              <p className="text-sm text-slate-600 dark:text-slate-400">扫描中</p>
            </div>
          </div>
        </div>
      </div>

      {/* 操作区域 */}
      <div className="bg-white/60 dark:bg-slate-800/60 backdrop-blur-sm border border-slate-200/50 dark:border-slate-700/50 rounded-2xl p-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-[#007aff]/10 rounded-lg">
              <Plus className="h-5 w-5 text-[#007aff]" />
            </div>
            <h2 className="text-xl font-semibold text-slate-900 dark:text-slate-100">图片库操作</h2>
          </div>
          <div className="flex items-center space-x-3">
            <Button
              variant="outline"
              size="sm"
              onClick={handleRefresh}
              disabled={refreshing}
              className="flex items-center space-x-2"
            >
              <RefreshCw className={`h-4 w-4 ${refreshing ? 'animate-spin' : ''}`} />
              <span>手动刷新</span>
            </Button>
            {isPolling && (
              <div className="flex items-center space-x-2 text-sm text-slate-600 dark:text-slate-400">
                <Loader2 className="h-4 w-4 animate-spin" />
                <span>自动更新中</span>
              </div>
            )}
            <Button
              onClick={handleAddLibrary}
              className="flex items-center space-x-2 bg-[#007aff] hover:bg-[#0056cc]"
            >
              <Plus className="h-4 w-4" />
              <span>添加图片库</span>
            </Button>
          </div>
        </div>
      </div>

      {/* 图片库列表 */}
      <div className="bg-white/60 dark:bg-slate-800/60 backdrop-blur-sm border border-slate-200/50 dark:border-slate-700/50 rounded-2xl p-6">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-[#007aff]/10 rounded-lg">
              <Folder className="h-5 w-5 text-[#007aff]" />
            </div>
            <h2 className="text-xl font-semibold text-slate-900 dark:text-slate-100">图片库列表</h2>
          </div>
          <span className="px-3 py-1 text-sm bg-slate-100 dark:bg-slate-700 text-slate-600 dark:text-slate-400 rounded-lg">
            {libraries.length} 个库
          </span>
        </div>

        {isLoading ? (
          <div className="flex items-center justify-center py-12">
            <Loader2 className="h-6 w-6 animate-spin mr-3 text-[#007aff]" />
            <span className="text-slate-600 dark:text-slate-400">加载图片库中...</span>
          </div>
        ) : libraries.length === 0 ? (
          <div className="text-center py-12">
            <div className="p-4 bg-slate-100/50 dark:bg-slate-700/50 rounded-2xl inline-block mb-4">
              <Folder className="h-12 w-12 text-slate-400 dark:text-slate-500" />
            </div>
            <h3 className="text-lg font-medium text-slate-700 dark:text-slate-300 mb-2">暂无图片库</h3>
            <p className="text-slate-500 dark:text-slate-400 mb-6">点击上方"添加图片库"开始使用</p>
            <Button
              onClick={handleAddLibrary}
              className="bg-[#007aff] hover:bg-[#0056cc]"
            >
              <Plus className="h-4 w-4 mr-2" />
              添加第一个图片库
            </Button>
          </div>
        ) : (
          <div className="space-y-4">
            {libraries.map((library) => {
              const isScanning = library.parsedScanProgress?.status === 'running' ||
                                library.parsedScanProgress?.status === 'processing' ||
                                scanningLibraries.has(library.id)
              const isEditing = editingLibrary === library.id
              
              return (
                <div
                  key={library.id}
                  className="p-6 bg-white/40 dark:bg-slate-700/40 backdrop-blur-sm border border-slate-200/50 dark:border-slate-600/50 rounded-xl hover:bg-white/60 dark:hover:bg-slate-700/60 transition-all duration-200"
                >
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <div className="flex items-center space-x-3 mb-2">
                        <Folder className="h-5 w-5 text-slate-600 dark:text-slate-400" />
                        {isEditing ? (
                          <div className="flex items-center space-x-2">
                            <input
                              type="text"
                              value={editName}
                              onChange={(e) => setEditName(e.target.value)}
                              className="px-2 py-1 text-lg font-semibold bg-white dark:bg-slate-800 border rounded"
                              onKeyDown={(e) => {
                                if (e.key === 'Enter') saveEdit()
                                if (e.key === 'Escape') cancelEdit()
                              }}
                              autoFocus
                            />
                            <Button size="sm" onClick={saveEdit}>保存</Button>
                            <Button size="sm" variant="outline" onClick={cancelEdit}>取消</Button>
                          </div>
                        ) : (
                          <>
                            <h3 className="text-lg font-semibold text-slate-900 dark:text-slate-100">
                              {library.name}
                            </h3>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => startEdit(library)}
                              className="p-1 h-auto"
                            >
                              <Edit3 className="h-4 w-4" />
                            </Button>
                          </>
                        )}
                        <div className="flex items-center space-x-2">
                          {getStatusIcon(library)}
                          <span className="text-sm text-slate-600 dark:text-slate-400">
                            {getStatusText(library)}
                          </span>
                        </div>
                        <span className="px-2 py-1 text-xs bg-slate-100 dark:bg-slate-600 text-slate-600 dark:text-slate-300 rounded-lg">
                          {library.imageCount || 0} 张
                        </span>
                        {library.totalSize && library.totalSize > 0 && (
                          <span className="px-2 py-1 text-xs bg-blue-100 dark:bg-blue-800 text-blue-600 dark:text-blue-300 rounded-lg">
                            {formatFileSize(library.totalSize)}
                          </span>
                        )}
                      </div>
                      
                      <div className="text-sm text-slate-500 dark:text-slate-400 mb-2">
                        {library.rootPath}
                      </div>
                      
                      {library.description && (
                        <div className="text-sm text-slate-600 dark:text-slate-300 mb-2">
                          {library.description}
                        </div>
                      )}
                      
                      <div className="flex items-center space-x-4 text-xs text-slate-500 dark:text-slate-400">
                        <div className="flex items-center space-x-1">
                          <Clock className="h-3 w-3" />
                          <span>最后扫描: {formatLastScanTime(library.lastScanTime)}</span>
                        </div>
                        <div className="flex items-center space-x-1">
                          <span>类型: {library.type}</span>
                        </div>
                        {library.parsedScanProgress?.status === 'running' && library.parsedScanProgress.total && (
                          <div className="flex items-center space-x-1">
                            <span>进度: {library.parsedScanProgress.processed || 0}/{library.parsedScanProgress.total}</span>
                          </div>
                        )}
                      </div>
                    </div>
                    
                    <div className="flex items-center space-x-2 ml-4">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleScanLibrary(library.id)}
                        disabled={isScanning}
                        className="flex items-center space-x-1"
                      >
                        {isScanning ? (
                          <Loader2 className="h-4 w-4 animate-spin" />
                        ) : (
                          <Play className="h-4 w-4" />
                        )}
                        <span>{isScanning ? '扫描中' : '开始扫描'}</span>
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleDeleteLibrary(library.id, library.name)}
                        className="flex items-center space-x-1 text-red-600 hover:text-red-700"
                      >
                        <Trash2 className="h-4 w-4" />
                        <span>删除</span>
                      </Button>
                    </div>
                  </div>
                </div>
              )
            })}
          </div>
        )}
      </div>

      {/* 高级文件夹选择器 */}
      <AdvancedFolderSelector
        isVisible={showFolderSelector}
        onFolderSelected={handleFolderSelected}
        onCancel={() => setShowFolderSelector(false)}
      />
    </div>
  )
}