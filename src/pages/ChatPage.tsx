// import { useState, useCallback, useEffect, useRef } from 'react'
// // import { ImageSelector } from '@/components/ImageSelector.tsx'
// import { ToolCallStatusList } from '@/components/ToolCallStatus.tsx'
// import { ChatMessageDisplay } from '@/components/ChatMessageDisplay.tsx'
// import { ImageModal } from '@/components/ImageModal.tsx'
// import { useToolCallState } from '@/hooks/useToolCallState.ts'
// import { Brain, Image as ImageIcon, Send, Sparkles} from 'lucide-react'
// import {type ChatMessage, type ImageData, initialChatHistory} from '@/data/mockData.ts'
// import {ChatResponse, sendImageMessageByPath, sendTextMessage, setReActStepCallback, ReActStep} from "@/lib/ChatServiceWithTools.ts";
//
// interface ChatPageProps {
//   availableImages?: ImageData[]
// }
// export function ChatPage({
//                            availableImages
// }: ChatPageProps): JSX.Element {
//   const [chatMessage, setChatMessage] = useState<string>('')
//   const [chatHistory, setChatHistory] = useState<ChatMessage[]>(initialChatHistory)
//   const [showImageSelector, setShowImageSelector] = useState<boolean>(false)
//   const [selectedImageForModal, setSelectedImageForModal] = useState<ImageData | null>(null)
//   const [isTyping, setIsTyping] = useState<boolean>(false)
//
//   // 使用useRef避免依赖项问题
//   const currentMessageIdRef = useRef<number | null>(null)
//
//   // Tool call state management
//   const { activeSessions, hasActiveToolCalls } = useToolCallState();
//
//   // ReAct步骤回调 - 优化为不依赖currentMessage
//   const handleReActStep = useCallback((step: ReActStep) => {
//     console.log('🎯 收到ReAct步骤:', step.type, step.status, step.content.substring(0, 50) + '...');
//
//     const currentMessageId = currentMessageIdRef.current;
//     if (currentMessageId) {
//       // 立即更新聊天历史中的对应消息
//       setChatHistory(prev => prev.map(msg => {
//         if (msg.id === currentMessageId) {
//           const updatedSteps = [...(msg.reactSteps || [])];
//
//           // 查找是否已存在相同ID的步骤
//           const existingIndex = updatedSteps.findIndex(s => s.id === step.id);
//           if (existingIndex >= 0) {
//             // 更新现有步骤
//             updatedSteps[existingIndex] = step;
//           } else {
//             // 添加新步骤
//             updatedSteps.push(step);
//           }
//
//           const updatedMessage: ChatMessage = {
//             ...msg,
//             reactSteps: updatedSteps,
//             currentReActStep: step.status === 'active' ? step : undefined,
//             isStreaming: step.type !== 'final' // 如果是final步骤，停止streaming
//           };
//
//           console.log('✅ 更新消息，当前步骤数:', updatedSteps.length, '当前步骤:', step.type);
//           return updatedMessage;
//         }
//         return msg;
//       }));
//     } else {
//       console.warn('⚠️ 当前消息ID为空，无法更新ReAct步骤');
//     }
//   }, []); // 移除依赖项
//
//   // 设置ReAct步骤回调 - 只设置一次
//   useEffect(() => {
//     console.log('🔧 设置ReAct步骤回调');
//     setReActStepCallback(handleReActStep);
//   }, [handleReActStep]);
//
//   const handleImageSelect = (imageData: { url: string; file: File }): void => {
//     handleImageSend(imageData, chatMessage)
//     setChatMessage('')
//     setShowImageSelector(false)
//   }
//
//   const handleLibraryImageSelect = (image: ImageData): void => {
//     handleLibraryImageSend(image, chatMessage)
//     setChatMessage('')
//     setShowImageSelector(false)
//   }
//
//   const handleImageSend = async (imageData: { url: string; file: File }, message?: string): Promise<void> => {
//     const userMessage: ChatMessage = {
//       id: Date.now(),
//       type: 'user',
//       content: message || '请分析这张图片',
//       timestamp: new Date(),
//       image: {
//         url: imageData.url,
//         description: imageData.file.name
//       }
//     }
//
//     setChatHistory(prev => [...prev, userMessage])
//     setIsTyping(true)
//     setChatMessage('')
//
//     // 创建AI回复消息
//     const aiMessageId = Date.now() + 1;
//     const aiMessage: ChatMessage = {
//       id: aiMessageId,
//       type: 'assistant',
//       content: '',
//       timestamp: new Date(),
//       isStreaming: true,
//       reactSteps: [],
//       currentReActStep: undefined
//     };
//
//     // 设置当前消息ID到ref
//     currentMessageIdRef.current = aiMessageId;
//     console.log('🚀 开始处理图片，消息ID:', aiMessageId);
//
//     // 添加到聊天历史
//     setChatHistory(prev => [...prev, aiMessage]);
//
//     try {
//       const response: ChatResponse = await sendImageMessageByPath(imageData.url, message || '请详细描述这张图片的内容')
//
//       // 更新AI回复消息的最终内容
//       setChatHistory(prev => prev.map(msg => {
//         if (msg.id === aiMessageId) {
//           return {
//             ...msg,
//             content: response.content,
//             isStreaming: false,
//             toolResults: response.toolResults,
//             // 保留已有的reactSteps，不要覆盖
//             reactSteps: response.reactSteps && response.reactSteps.length > 0 ? response.reactSteps : msg.reactSteps,
//             currentReActStep: undefined
//           };
//         }
//         return msg;
//       }));
//
//       // 清除当前消息ID引用
//       currentMessageIdRef.current = null;
//       console.log('✅ 图片处理完成');
//
//       if (response.error) {
//         console.error('Chat服务错误:', response.error)
//       }
//     } catch (error) {
//       console.error('发送图片消息失败:', error)
//
//       setChatHistory(prev => prev.map(msg => {
//         if (msg.id === aiMessageId) {
//           return {
//             ...msg,
//             content: '抱歉，图片分析服务暂时不可用。',
//             isStreaming: false,
//             isError: true
//           };
//         }
//         return msg;
//       }));
//
//       // 清除当前消息ID引用
//       currentMessageIdRef.current = null;
//     } finally {
//       setIsTyping(false)
//     }
//   }
//
//   const handleLibraryImageSend = async (image: ImageData, message?: string): Promise<void> => {
//     const userMessage: ChatMessage = {
//       id: Date.now(),
//       type: 'user',
//       content: message || '请分析这张图片',
//       timestamp: new Date(),
//       image: {
//         url: image.url,
//         description: image.title
//       }
//     }
//
//     setChatHistory(prev => [...prev, userMessage])
//     setIsTyping(true)
//     setChatMessage('')
//
//     // 创建AI回复消息
//     const aiMessageId = Date.now() + 1;
//     const aiMessage: ChatMessage = {
//       id: aiMessageId,
//       type: 'assistant',
//       content: '',
//       timestamp: new Date(),
//       isStreaming: true,
//       reactSteps: [],
//       currentReActStep: undefined
//     };
//
//     // 设置当前消息ID到ref
//     currentMessageIdRef.current = aiMessageId;
//     console.log('🚀 开始处理图库图片，消息ID:', aiMessageId);
//
//     // 添加到聊天历史
//     setChatHistory(prev => [...prev, aiMessage]);
//
//     try {
//       // 尝试从图片URL中提取文件路径
//       let imagePath = image.url;
//
//       // 如果是本地图片库的图片，URL格式可能是 /api/image/路径
//       if (image.url.startsWith('/api/image/')) {
//         // 提取实际文件路径
//         imagePath = image.url.replace('/api/image/', '');
//       }
//
//       // 如果已经是app://格式，保持不变
//       if (!imagePath.startsWith('app://')) {
//         // 构建app://格式的路径
//         imagePath = `app://${imagePath.replace(/\\/g, '/').replace(/^([A-Za-z]):/, '$1')}`;
//       }
//
//       console.log('使用路径发送图片消息:', imagePath);
//
//       const response: ChatResponse = await sendImageMessageByPath(imagePath, message || `请分析这张图片：${image.title}`)
//
//       // 更新AI回复消息的最终内容
//       setChatHistory(prev => prev.map(msg => {
//         if (msg.id === aiMessageId) {
//           return {
//             ...msg,
//             content: response.content,
//             isStreaming: false,
//             toolResults: response.toolResults,
//             // 保留已有的reactSteps，不要覆盖
//             reactSteps: response.reactSteps && response.reactSteps.length > 0 ? response.reactSteps : msg.reactSteps,
//             currentReActStep: undefined
//           };
//         }
//         return msg;
//       }));
//
//       // 清除当前消息ID引用
//       currentMessageIdRef.current = null;
//       console.log('✅ 图库图片处理完成');
//
//       if (response.error) {
//         console.error('分离聊天服务错误:', response.error)
//       }
//     } catch (error) {
//       console.error('图片转换失败:', error)
//
//       setChatHistory(prev => prev.map(msg => {
//         if (msg.id === aiMessageId) {
//           return {
//             ...msg,
//             content: '抱歉，图片加载失败，请稍后重试。',
//             isStreaming: false,
//             isError: true
//           };
//         }
//         return msg;
//       }));
//
//       // 清除当前消息ID引用
//       currentMessageIdRef.current = null;
//     } finally {
//       setIsTyping(false)
//     }
//   }
//
//   const handleSendMessage = async (): Promise<void> => {
//     if (!chatMessage.trim()) return
//
//     const userMessage: ChatMessage = {
//       id: Date.now(),
//       type: 'user',
//       content: chatMessage,
//       timestamp: new Date()
//     }
//
//     setChatHistory(prev => [...prev, userMessage])
//     setIsTyping(true)
//     const currentMessageText = chatMessage
//     setChatMessage('')
//
//     // 创建AI回复消息
//     const aiMessageId = Date.now() + 1;
//     const aiMessage: ChatMessage = {
//       id: aiMessageId,
//       type: 'assistant',
//       content: '',
//       timestamp: new Date(),
//       isStreaming: true,
//       reactSteps: [],
//       currentReActStep: undefined
//     };
//
//     // 设置当前消息ID到ref
//     currentMessageIdRef.current = aiMessageId;
//     console.log('🚀 开始处理文本消息，消息ID:', aiMessageId);
//
//     // 添加到聊天历史
//     setChatHistory(prev => [...prev, aiMessage]);
//
//     try {
//       const response: ChatResponse = await sendTextMessage(currentMessageText)
//
//       // 更新AI回复消息的最终内容
//       setChatHistory(prev => prev.map(msg => {
//         if (msg.id === aiMessageId) {
//           return {
//             ...msg,
//             content: response.content,
//             isStreaming: false,
//             toolResults: response.toolResults,
//             // 保留已有的reactSteps，不要覆盖
//             reactSteps: response.reactSteps && response.reactSteps.length > 0 ? response.reactSteps : msg.reactSteps,
//             currentReActStep: undefined
//           };
//         }
//         return msg;
//       }));
//
//       // 清除当前消息ID引用
//       currentMessageIdRef.current = null;
//       console.log('✅ 文本消息处理完成');
//
//       if (response.error) {
//         console.error('Chat服务错误:', response.error)
//       }
//     } catch (error) {
//       console.error('发送消息失败:', error)
//
//       setChatHistory(prev => prev.map(msg => {
//         if (msg.id === aiMessageId) {
//           return {
//             ...msg,
//             content: '抱歉，服务暂时不可用。',
//             isStreaming: false,
//             isError: true
//           };
//         }
//         return msg;
//       }));
//
//       // 清除当前消息ID引用
//       currentMessageIdRef.current = null;
//     } finally {
//       setIsTyping(false)
//     }
//   }
//
//   const handleImageClick = (imageUrl: string, imageData: any): void => {
//     // 如果是 ImageData 类型，直接使用
//     if (imageData && typeof imageData === 'object' && imageData.id) {
//       setSelectedImageForModal(imageData as ImageData)
//     } else {
//       // 如果只有 URL，创建一个简单的 ImageData 对象
//       const simpleImageData: ImageData = {
//         id: Date.now(),
//         url: imageUrl,
//         title: '聊天图片',
//         tags: [],
//         description: '来自聊天的图片',
//         uploadTime: new Date().toLocaleDateString(),
//         location: '',
//         camera: '',
//         colors: [],
//         aiAnalysis: false,
//         similarity: 0,
//         fileSize: '未知',
//         resolution: '未知',
//         exif: {
//           iso: 0,
//           aperture: '',
//           shutterSpeed: '',
//           focalLength: ''
//         }
//       }
//       setSelectedImageForModal(simpleImageData)
//     }
//   }
//
//   const handleKeyPress = (e: React.KeyboardEvent<HTMLInputElement>): void => {
//     if (e.key === 'Enter') {
//       handleSendMessage()
//     }
//   }
//
//   const quickQuestions = ['这是什么？', '拍摄地点？', '相似图片', '图片详情', '分析场景', '识别物体']
//
//   return (
//         <div className="p-8 space-y-8">
//           {/* 页面标题 */}
//           <div className="space-y-2">
//             <h1 className="text-3xl font-semibold text-slate-900 dark:text-slate-100">图文问答</h1>
//             <p className="text-slate-600 dark:text-slate-400">
//               上传图片并与AI进行对话，获取智能分析和回答
//             </p>
//           </div>
//
//
//
//           {/* 聊天界面 */}
//           <div className="bg-white/60 dark:bg-slate-800/60 backdrop-blur-sm border border-slate-200/50 dark:border-slate-700/50 rounded-2xl overflow-hidden flex flex-col">
//
//             {/* 聊天消息区 */}
//             <div className="min-h-[40vh] max-h-[70vh] p-6 overflow-y-auto bg-gradient-to-b from-slate-50/50 to-white/50 dark:from-slate-800/50 dark:to-slate-900/50 flex-1">
//               <div className="space-y-4">
//                 {chatHistory.length === 0 && !isTyping && (
//                     <div className="flex items-start space-x-3">
//                       <div className="p-2 bg-[#007aff] rounded-full shadow-md flex-shrink-0">
//                         <Brain className="h-4 w-4 text-white" />
//                       </div>
//                       <div className="bg-white/80 dark:bg-slate-700/80 backdrop-blur-sm border border-slate-200/50 dark:border-slate-600/50 rounded-2xl p-4 max-w-[90%] sm:max-w-[80%] lg:max-w-[70%] xl:max-w-[700px]">
//                         <div className="space-y-3">
//                           <div className="flex items-center space-x-2">
//                             <Sparkles className="h-4 w-4 text-[#007aff]" />
//                             <span className="text-sm font-medium text-[#007aff]">AI助理</span>
//                           </div>
//                           <p className="text-sm leading-relaxed text-slate-700 dark:text-slate-300">
//                             您好！我是您的本地多模态智能助理。您可以向我询问关于图片的任何问题，比如图片内容、拍摄时间、相似图片等。我会基于AI视觉分析为您提供详细解答。
//                           </p>
//                           <div className="flex items-center space-x-2 text-xs text-slate-500 dark:text-slate-400">
//                             <div className="w-2 h-2 bg-green-500 rounded-full"></div>
//                             <span>AI服务已就绪</span>
//                           </div>
//                         </div>
//                       </div>
//                     </div>
//                 )}
//                 {chatHistory.map((message) => (
//                     <ChatMessageDisplay
//                         key={message.id}
//                         message={message}
//                         onImageClick={handleImageClick}
//                     />
//                 ))}
//
//                 {isTyping && (
//                     <div className="flex items-start space-x-3">
//                       <div className="p-2 bg-[#007aff] rounded-full shadow-md flex-shrink-0">
//                         <Brain className="h-4 w-4 text-white" />
//                       </div>
//                       <div className="bg-white/80 dark:bg-slate-700/80 backdrop-blur-sm border border-slate-200/50 dark:border-slate-600/50 rounded-2xl p-4 max-w-[90%] sm:max-w-[80%] lg:max-w-[70%] xl:max-w-[700px]">
//                         <div className="flex items-center space-x-3">
//                           <div className="flex space-x-1">
//                             <div className="w-2 h-2 bg-[#007aff] rounded-full animate-bounce"></div>
//                             <div className="w-2 h-2 bg-[#007aff] rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
//                             <div className="w-2 h-2 bg-[#007aff] rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
//                           </div>
//                           <span className="text-sm text-slate-600 dark:text-slate-400">AI正在思考...</span>
//                           <div className="flex-1"></div>
//                           <div className="w-4 h-4 border-2 border-[#007aff]/30 border-t-[#007aff] rounded-full animate-spin"></div>
//                         </div>
//                         <div className="mt-2 h-1 bg-slate-200 dark:bg-slate-600 rounded-full overflow-hidden">
//                           <div className="h-full bg-gradient-to-r from-[#007aff] to-blue-400 rounded-full animate-pulse" style={{ width: '60%' }}></div>
//                         </div>
//                       </div>
//                     </div>
//                 )}
//
//                 {/* Tool call status display */}
//                 {hasActiveToolCalls() && (
//                     <div className="flex items-start space-x-3">
//                       <div className="p-2 bg-[#007aff] rounded-full shadow-md flex-shrink-0">
//                         <Brain className="h-4 w-4 text-white" />
//                       </div>
//                       <div className="flex-1 max-w-[90%] sm:max-w-[80%] lg:max-w-[70%] xl:max-w-[700px]">
//                         <ToolCallStatusList sessions={activeSessions} />
//                       </div>
//                     </div>
//                 )}
//               </div>
//             </div>
//
//
//
//             {/* 图片选择区 */}
//             {/*{showImageSelector && (*/}
//             {/*    <div className="px-6 pb-4">*/}
//             {/*      <div className="p-4 bg-white/40 dark:bg-slate-700/40 backdrop-blur-sm border border-slate-200/50 dark:border-slate-600/50 rounded-xl">*/}
//             {/*        <ImageSelector*/}
//             {/*            onImageSelect={handleImageSelect}*/}
//             {/*            onLibraryImageSelect={handleLibraryImageSelect}*/}
//             {/*            availableImages={availableImages}*/}
//             {/*            disabled={isTyping}*/}
//             {/*            className="w-full"*/}
//             {/*        />*/}
//             {/*      </div>*/}
//             {/*    </div>*/}
//             {/*)}*/}
//
//             {/* 输入区域 */}
//             <div className="p-6 bg-white/40 dark:bg-slate-800/40 backdrop-blur-sm border-t border-slate-200/50 dark:border-slate-700/50">
//               <div className="flex space-x-3 mb-4">
//                 <button
//                     onClick={() => setShowImageSelector(!showImageSelector)}
//                     disabled={isTyping}
//                     className={`group p-3 rounded-xl transition-all duration-200 ${
//                         showImageSelector
//                             ? 'bg-[#007aff] text-white shadow-md scale-105'
//                             : 'bg-white/60 dark:bg-slate-700/60 text-slate-600 dark:text-slate-400 hover:bg-white/80 dark:hover:bg-slate-700/80 hover:scale-105'
//                     } disabled:opacity-50 disabled:scale-100`}
//                     title={showImageSelector ? '关闭图片选择' : '选择图片'}
//                 >
//                   <ImageIcon className="h-5 w-5 group-hover:scale-110 transition-transform duration-200" />
//                 </button>
//                 <div className="flex-1 relative">
//                   <input
//                       type="text"
//                       placeholder="询问关于图片的任何问题..."
//                       value={chatMessage}
//                       onChange={(e) => setChatMessage(e.target.value)}
//                       onKeyPress={handleKeyPress}
//                       disabled={isTyping}
//                       className="w-full pl-4 pr-16 h-12 text-base bg-white/60 dark:bg-slate-800/60 backdrop-blur-sm border border-slate-200/50 dark:border-slate-700/50 rounded-xl focus:border-[#007aff]/50 focus:ring-[#007aff]/20 focus:ring-2 outline-none transition-all duration-200 placeholder:text-slate-400 dark:placeholder:text-slate-500 disabled:opacity-50"
//                   />
//                   <button
//                       onClick={handleSendMessage}
//                       disabled={!chatMessage.trim() || isTyping}
//                       className="absolute right-2 top-1/2 transform -translate-y-1/2 p-2 bg-[#007aff] text-white rounded-lg hover:bg-[#0056b3] hover:scale-105 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed disabled:scale-100 group"
//                       title="发送消息"
//                   >
//                     <Send className="h-4 w-4 group-hover:translate-x-0.5 transition-transform duration-200" />
//                   </button>
//                 </div>
//               </div>
//
//               {/* 快捷问题 */}
//               <div className="space-y-4">
//                 <div className="flex items-center space-x-2">
//                   <Sparkles className="h-4 w-4 text-[#007aff]" />
//                   <span className="text-sm font-medium text-slate-700 dark:text-slate-300">快速提问</span>
//                   <div className="flex-1 h-px bg-gradient-to-r from-slate-200 to-transparent dark:from-slate-600"></div>
//                 </div>
//                 <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
//                   {quickQuestions.map((question, index) => (
//                       <button
//                           key={question}
//                           onClick={() => setChatMessage(question)}
//                           disabled={isTyping}
//                           className="group px-4 py-3 text-sm font-medium text-slate-600 dark:text-slate-400 bg-white/40 dark:bg-slate-700/40 hover:bg-white/60 dark:hover:bg-slate-700/60 hover:text-[#007aff] dark:hover:text-blue-400 rounded-xl transition-all duration-200 text-left border border-slate-200/30 dark:border-slate-600/30 hover:border-[#007aff]/30 hover:shadow-md disabled:opacity-50 disabled:cursor-not-allowed"
//                           style={{ animationDelay: `${index * 50}ms` }}
//                       >
//                         <div className="flex items-center space-x-2">
//                           <span className="group-hover:scale-110 transition-transform duration-200">💭</span>
//                           <span>{question}</span>
//                         </div>
//                       </button>
//                   ))}
//                 </div>
//               </div>
//             </div>
//           </div>
//
//           {/* 图片详情模态框 */}
//           <ImageModal
//               image={selectedImageForModal}
//               isOpen={!!selectedImageForModal}
//               onClose={() => setSelectedImageForModal(null)}
//               onImageClick={handleImageClick}
//           />
//         </div>
//   )
// }