import { useState, useEffect, useRef } from 'react';
import { ToolCallSession, toolCallStateManager } from '../lib/toolCallStateManager';

export function useToolCallState() {
  const [sessions, setSessions] = useState<ToolCallSession[]>([]);
  const [activeSessions, setActiveSessions] = useState<ToolCallSession[]>([]);
  const unsubscribeRef = useRef<(() => void) | null>(null);

  useEffect(() => {
    // Initialize with existing sessions
    const initialSessions = toolCallStateManager.getActiveSessions();
    setSessions(initialSessions);
    setActiveSessions(initialSessions);

    // Subscribe to updates
    unsubscribeRef.current = toolCallStateManager.subscribe((updatedSession) => {
      setSessions(prevSessions => {
        const existingIndex = prevSessions.findIndex(s => s.id === updatedSession.id);
        if (existingIndex >= 0) {
          const newSessions = [...prevSessions];
          newSessions[existingIndex] = updatedSession;
          return newSessions;
        } else {
          return [...prevSessions, updatedSession];
        }
      });

      // Update active sessions
      setActiveSessions(toolCallStateManager.getActiveSessions());
    });

    return () => {
      if (unsubscribeRef.current) {
        unsubscribeRef.current();
      }
    };
  }, []);

  const getActiveSessionsCount = () => activeSessions.length;
  
  const hasActiveToolCalls = () => activeSessions.length > 0;

  const getSessionById = (sessionId: string) => {
    return sessions.find((s: any) => s.id === sessionId);
  };

  const getCompletedSessions = () => {
    return sessions.filter((s: any) => s.status === 'completed' || s.status === 'failed');
  };

  const getStatistics = () => {
    return toolCallStateManager.getStatistics();
  };

  const getHistory = (limit: number = 50) => {
    return toolCallStateManager.getHistory(limit);
  };

  return {
    sessions,
    activeSessions,
    getActiveSessionsCount,
    hasActiveToolCalls,
    getSessionById,
    getCompletedSessions,
    getStatistics,
    getHistory
  };
}