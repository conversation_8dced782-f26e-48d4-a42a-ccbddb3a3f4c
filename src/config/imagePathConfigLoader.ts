// /**
//  * 图片路径配置加载器
//  *
//  * 负责从配置文件加载图片路径配置，并提供配置初始化功能
//  */
//
// import * as fs from 'fs'
// import * as path from 'path'
// import { app } from 'electron'
// import { imagePathConfig, updateImagePathConfig, ImagePathConfig } from './imagePathConfig'
//
// /**
//  * 初始化图片路径配置
//  * 从配置文件加载配置，并设置运行时配置项
//  */
// export async function initializeImagePathConfig(): Promise<void> {
//   try {
//     // 加载配置文件
//     const configPath = path.join(app.getPath('userData'), 'config', 'imagePathConfig.json')
//
//     // 确保配置目录存在
//     const configDir = path.dirname(configPath)
//     if (!fs.existsSync(configDir)) {
//       fs.mkdirSync(configDir, { recursive: true })
//     }
//
//     // 检查配置文件是否存在
//     if (fs.existsSync(configPath)) {
//       // 读取配置文件
//       const configData = fs.readFileSync(configPath, 'utf-8')
//       const userConfig = JSON.parse(configData) as Partial<ImagePathConfig>
//
//       // 更新配置
//       updateImagePathConfig(userConfig)
//
//       console.log('已加载图片路径配置:', configPath)
//     } else {
//       // 创建默认配置文件
//       await saveImagePathConfig()
//       console.log('已创建默认图片路径配置:', configPath)
//     }
//
//     // 设置运行时配置项
//     setupRuntimeConfig()
//
//   } catch (error) {
//     console.error('加载图片路径配置失败:', error)
//   }
// }
//
// /**
//  * 保存当前配置到配置文件
//  */
// export async function saveImagePathConfig(): Promise<void> {
//   try {
//     const configPath = path.join(app.getPath('userData'), 'config', 'imagePathConfig.json')
//
//     // 确保配置目录存在
//     const configDir = path.dirname(configPath)
//     if (!fs.existsSync(configDir)) {
//       fs.mkdirSync(configDir, { recursive: true })
//     }
//
//     // 写入配置文件
//     fs.writeFileSync(configPath, JSON.stringify(imagePathConfig, null, 2), 'utf-8')
//
//     console.log('已保存图片路径配置:', configPath)
//   } catch (error) {
//     console.error('保存图片路径配置失败:', error)
//   }
// }
//
// /**
//  * 设置运行时配置项
//  * 这些配置项需要在运行时动态设置，不能直接从配置文件加载
//  */
// function setupRuntimeConfig(): void {
//   // 设置允许访问的目录
//   if (imagePathConfig.security.allowedDirectories.length === 0) {
//     imagePathConfig.security.allowedDirectories = [
//       path.join(app.getPath('userData'), 'images'),
//       path.join(app.getPath('pictures')),
//       path.join(app.getPath('documents'))
//     ]
//   }
//
//   // 设置占位图路径
//   const placeholdersDir = path.join(app.getPath('userData'), 'placeholders')
//
//   // 确保占位图目录存在
//   if (!fs.existsSync(placeholdersDir)) {
//     fs.mkdirSync(placeholdersDir, { recursive: true })
//   }
//
//   // 设置占位图路径
//   imagePathConfig.placeholder.defaultImagePath = path.join(placeholdersDir, 'default.png')
//   imagePathConfig.placeholder.notFoundImagePath = path.join(placeholdersDir, 'not_found.png')
//   imagePathConfig.placeholder.forbiddenImagePath = path.join(placeholdersDir, 'forbidden.png')
//   imagePathConfig.placeholder.errorImagePath = path.join(placeholdersDir, 'error.png')
// }