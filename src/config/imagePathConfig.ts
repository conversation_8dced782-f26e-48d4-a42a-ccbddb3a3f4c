/**
 * 图片路径优化配置文件
 * 
 * 本配置文件定义了图片路径优化相关的所有配置选项，包括：
 * - 缓存策略配置
 * - 缩略图配置
 * - 并发处理配置
 * - 安全配置
 * - 占位图配置
 */

// 智能缓存配置
export interface SmartCacheConfig {
  maxSize: number        // 最大缓存大小（字节）
  maxEntries: number     // 最大条目数
  ttl: number           // 生存时间（毫秒）
  cleanupInterval: number // 清理间隔（毫秒）
  useSmartCache: boolean // 是否使用智能缓存
}

// 缩略图配置
export interface ThumbnailConfig {
  enabled: boolean       // 是否启用缩略图
  sizes: Array<{ width: number; height?: number; quality?: number }> // 默认缩略图尺寸
  useWebP: boolean       // 是否使用WebP格式
  webPQuality: number    // WebP质量（1-100）
  maxCacheSize: number   // 最大缓存大小（字节）
  maxEntries: number     // 最大缓存条目数
  ttl: number           // 缓存生存时间（毫秒）
}

// 并发处理配置
export interface ConcurrentProcessingConfig {
  maxConcurrent: number  // 最大并发处理数
  defaultTimeout: number // 默认超时时间（毫秒）
  retryCount: number     // 重试次数
  retryDelay: number     // 重试延迟（毫秒）
}

// 安全配置
export interface SecurityConfig {
  allowedDirectories: string[] // 允许访问的目录
  maxFileSize: number         // 最大文件大小（字节）
  allowedFormats: string[]    // 允许的文件格式
  securityLevel: 'strict' | 'normal' | 'permissive' // 安全级别
}

// 占位图配置
export interface PlaceholderConfig {
  enabled: boolean       // 是否启用占位图
  defaultImagePath: string // 默认占位图路径
  notFoundImagePath: string // 文件不存在占位图路径
  forbiddenImagePath: string // 禁止访问占位图路径
  errorImagePath: string  // 错误占位图路径
}

// 图片路径配置
export interface ImagePathConfig {
  smartCache: SmartCacheConfig
  thumbnail: ThumbnailConfig
  concurrentProcessing: ConcurrentProcessingConfig
  security: SecurityConfig
  placeholder: PlaceholderConfig
  legacySupport: {
    enableBase64Fallback: boolean // 是否启用base64回退
    disablePathMode: boolean     // 是否禁用路径模式（仅使用base64）
  }
}

// 默认配置
export const defaultImagePathConfig: ImagePathConfig = {
  smartCache: {
    maxSize: 100 * 1024 * 1024, // 100MB
    maxEntries: 500,
    ttl: 30 * 60 * 1000,        // 30分钟
    cleanupInterval: 5 * 60 * 1000, // 5分钟
    useSmartCache: true
  },
  thumbnail: {
    enabled: true,
    sizes: [
      { width: 100, quality: 80 },
      { width: 200, quality: 80 },
      { width: 400, quality: 85 }
    ],
    useWebP: true,
    webPQuality: 80,
    maxCacheSize: 50 * 1024 * 1024, // 50MB
    maxEntries: 300,
    ttl: 60 * 60 * 1000 // 1小时
  },
  concurrentProcessing: {
    maxConcurrent: 5,
    defaultTimeout: 30000, // 30秒
    retryCount: 2,
    retryDelay: 1000 // 1秒
  },
  security: {
    allowedDirectories: [], // 默认为空，需要在运行时设置
    maxFileSize: 10 * 1024 * 1024, // 10MB
    allowedFormats: ['jpg', 'jpeg', 'png', 'webp', 'gif', 'bmp'],
    securityLevel: 'normal'
  },
  placeholder: {
    enabled: true,
    defaultImagePath: '', // 需要在运行时设置
    notFoundImagePath: '', // 需要在运行时设置
    forbiddenImagePath: '', // 需要在运行时设置
    errorImagePath: '' // 需要在运行时设置
  },
  legacySupport: {
    enableBase64Fallback: true,
    disablePathMode: false
  }
}

// 当前配置（可在运行时修改）
export let imagePathConfig: ImagePathConfig = { ...defaultImagePathConfig }

/**
 * 更新图片路径配置
 * @param newConfig 新配置（部分）
 */
export function updateImagePathConfig(newConfig: Partial<ImagePathConfig>): void {
  // 深度合并配置
  imagePathConfig = mergeConfigs(imagePathConfig, newConfig)
}

/**
 * 重置为默认配置
 */
export function resetImagePathConfig(): void {
  imagePathConfig = { ...defaultImagePathConfig }
}

/**
 * 深度合并配置对象
 */
function mergeConfigs<T>(target: T, source: Partial<T>): T {
  const result = { ...target }
  
  if (source && typeof source === 'object') {
    Object.keys(source).forEach(key => {
      const sourceValue = source[key as keyof Partial<T>]
      const targetValue = target[key as keyof T]
      
      if (
        sourceValue && 
        typeof sourceValue === 'object' && 
        targetValue && 
        typeof targetValue === 'object' &&
        !Array.isArray(sourceValue) && 
        !Array.isArray(targetValue)
      ) {
        // 递归合并对象
        result[key as keyof T] = mergeConfigs(
          targetValue as any, 
          sourceValue as any
        ) as any
      } else {
        // 直接替换值
        result[key as keyof T] = sourceValue as any
      }
    })
  }
  
  return result
}