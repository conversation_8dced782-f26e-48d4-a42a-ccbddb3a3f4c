// import type { ImageR<PERSON>ord, <PERSON>ry<PERSON><PERSON><PERSON>, InsertResult, QueryR<PERSON>ult } from '../../database'
// import type { ImageAnalysisResult, EmbeddingResult, ProcessImageRequest, ProcessImageResult, AIServiceStatus } from '../ai'
// import type { ConnectionTestResponse, DatabaseInitResponse } from '../ipc-responses'
//
// // Electron API 类型声明
// export interface ElectronAPI {
//     fileSystem: FileSystemAPI
//     ai: AIAPI
//     database: DatabaseAPI
//     imageLibrary: ImageLibraryAPI
//     progress: ProgressAPI
//     service: ServiceAPI
//     ipcRenderer: IpcRendererAPI
//     selectFolder(): Promise<{
//         success: boolean
//         folderPath?: string
//         canceled?: boolean
//         error?: string
//     }>
// }
//
// export interface FileSystemAPI {
//     saveImage(params: { base64Data: string; filename: string }): Promise<{
//         success: boolean
//         filePath?: string
//         relativePath?: string
//         error?: string
//     }>
//     getImageInfo(filePath: string): Promise<{
//         success: boolean
//         info: {
//             size?: number
//             createdAt?: string
//             modifiedAt?: string
//             exists: boolean
//         }
//         error?: string
//     }>
//     deleteImage(filePath: string): Promise<{
//         success: boolean
//         error?: string
//     }>
//     getImagesDir(): Promise<string>
//     getImageData(relativePath: string): Promise<{
//         success: boolean
//         data?: string
//         error?: string
//     }>
//     getImageBlob(imagePath: string): Promise<{
//         success: boolean
//         data?: {
//             buffer: number[]
//             type: string
//             size: number
//         }
//         error?: string
//     }>
//     validateImagePath(imagePath: string): Promise<{
//         success: boolean
//         data?: {
//             isValid: boolean
//             sanitizedPath?: string
//             errors: string[]
//             warnings: string[]
//         }
//         error?: string
//     }>
//     getImageMetadata(imagePath: string): Promise<{
//         success: boolean
//         data?: {
//             format: string
//             size: number
//             dimensions: { width: number; height: number }
//             createdAt: string
//             modifiedAt: string
//             checksum: string
//         }
//         error?: string
//     }>
//     selectFolderAndGetImages(): Promise<{
//         success: boolean
//         folderPath?: string
//         imageFiles?: Array<{
//             path: string
//             relativePath: string
//             name: string
//             size: number
//         }>
//         error?: string
//     }>
//     selectFolder(): Promise<{
//         success: boolean
//         folderPath?: string
//         canceled?: boolean
//         error?: string
//     }>
//     getFileMd5(filePath: string): Promise<{
//         success: boolean
//         md5?: string
//         error?: string
//     }>
//     openFileLocation(filePath: string): Promise<{
//         success: boolean
//         error?: string
//     }>
// }
//
// export interface AIAPI {
//     testConnection(): Promise<boolean>
//     analyzeImage(imageBase64: string): Promise<ImageAnalysisResult & { error?: string }>
//     analyzeImageByPath(imagePath: string): Promise<ImageAnalysisResult & { error?: string }>
//     generateEmbedding(text: string): Promise<EmbeddingResult & { error?: string }>
//     processImage(request: ProcessImageRequest): Promise<ProcessImageResult>
//     processImageByPath(request: { imagePath: string; filename?: string }): Promise<ProcessImageResult>
//     getStatus(): Promise<AIServiceStatus>
//     manualTestConnection(): Promise<ConnectionTestResponse>
//     resetConnection(): Promise<{ success: boolean; error?: string }>
//     getConnectionStatus(): Promise<{ tested: boolean; connected: boolean; error?: string; success?: boolean }>
//     parseSearchQuery(query: string): Promise<{ keywords?: string[]; error?: string }>
// }
//
// export interface DatabaseAPI {
//     clearDatabase(): Promise<boolean>
//     insertImages(images: ImageRecord[]): Promise<InsertResult>
//     queryImages(params: QueryParams): Promise<QueryResult>
//     checkImageExists(filePath: string, md5?: string): Promise<{ success: boolean; exists: boolean; error?: string }>
//     vectorSimilaritySearch(embedding: number[], limit?: number, threshold?: number): Promise<QueryResult>
//     close(): Promise<void>
//     manualTestConnection(): Promise<ConnectionTestResponse>
//     manualInitCollection(): Promise<DatabaseInitResponse>
//     queryAllTags(): Promise<{ tags: string[]; error?: string }>
//     getAllTables(): Promise<{ success: boolean; tables: string[]; error?: string }>
//     getTableData(tableName: string, page?: number, pageSize?: number): Promise<{ success: boolean; data: any[]; total: number; error?: string }>
//     enhancedHybridSearch(params: {
//         query: string
//         limit?: number
//         expandKeywords?: boolean
//         similarityThreshold?: number
//     }): Promise<{
//         query: string
//         originalKeywords: string[]
//         expandedKeywords: string[]
//         results: ImageRecord[]
//         totalResults: number
//         error?: string
//     }>
//     findSimilarTags(query: string, options?: {
//         limit?: number
//         threshold?: number
//         category?: string
//         includeFrequency?: boolean
//     }): Promise<Array<{
//         tag: string
//         similarity: number
//         frequency: number
//         category: string
//         score: number
//     }>>
//     findSimilarTagsBatch(queries: string[], options?: {
//         limit?: number
//         threshold?: number
//         category?: string
//     }): Promise<Map<string, Array<{
//         tag: string
//         similarity: number
//         frequency: number
//         category: string
//         score: number
//     }>>>
//     updateTagEmbeddings(newTags: string[]): Promise<void>
//     queryTagEmbeddings(limit?: number): Promise<{ tags: string[]; error?: string }>
//     queryImagesByLocation(params: {
//         latitude: number
//         longitude: number
//         radius?: number
//         limit?: number
//     }): Promise<{ results: ImageRecord[]; error?: string }>
//     queryImagesByLocationName(params: {
//         locationName: string
//         limit?: number
//     }): Promise<{ results: ImageRecord[]; error?: string }>
//     queryImagesByTimeRange(params: {
//         startTime: string
//         endTime: string
//         limit?: number
//     }): Promise<{ results: ImageRecord[]; error?: string }>
//     queryImagesByDate(params: {
//         date: string
//         limit?: number
//     }): Promise<{ results: ImageRecord[]; error?: string }>
//     getImageExifData(imagePath: string): Promise<{
//         success: boolean
//         data?: {
//             latitude?: number
//             longitude?: number
//             altitude?: number
//             capturedAt?: string
//             cameraInfo?: string
//             shootingParams?: string
//         }
//         error?: string
//     }>
//     updateImageLocation(params: {
//         imagePath: string
//         latitude?: number
//         longitude?: number
//         altitude?: number
//         locationAddress?: string
//         locationCity?: string
//         locationCountry?: string
//         locationSource?: string
//     }): Promise<{ success: boolean; error?: string }>
// }
//
// export interface ImageLibraryAPI {
//     /**
//      * 创建新的图像库
//      */
//     createLibrary(params: {
//         name: string
//         path: string
//         description?: string,
//         options?: {
//             forceRescan?: boolean // 强制重新扫描已存在的图像
//             maxDepth?: number // 最大扫描深度
//             includeSubfolders?: boolean // 是否包含子文件夹
//             fileTypes?: string[] // 支持的文件类型
//         }
//     }): Promise<{
//         success: boolean
//         libraryId?: string
//         error?: string
//     }>
//
//     /**
//      * 获取所有图像库列表
//      */
//     getLibraries(): Promise<{
//         success: boolean
//         libraries?: Array<{
//             id: string
//             name: string
//             path: string
//             description?: string
//             imageCount: number
//             lastScanTime?: string
//             status: 'active' | 'scanning' | 'error' | 'inactive'
//             totalSize: number
//             duplicateCount?: number
//             createdAt: string
//             updatedAt: string
//         }>
//         error?: string
//     }>
//
//     /**
//      * 根据ID获取单个图像库详情
//      */
//     getLibrary(libraryId: string): Promise<{
//         success: boolean
//         library?: {
//             id: string
//             name: string
//             path: string
//             description?: string
//             imageCount: number
//             lastScanTime?: string
//             status: 'active' | 'scanning' | 'error' | 'inactive'
//             totalSize: number
//             duplicateCount?: number
//             createdAt: string
//             updatedAt: string
//         }
//         error?: string
//     }>
//
//     /**
//      * 更新图像库信息
//      */
//     updateLibrary(libraryId: string, params: {
//         name?: string
//         description?: string
//         // 路径不允许修改，需要删除重建
//     }): Promise<{
//         success: boolean
//         error?: string
//     }>
//
//     /**
//      * 删除图像库
//      */
//     deleteLibrary(libraryId: string, options?: {
//         deleteImages?: boolean // 是否同时删除图像记录
//     }): Promise<{
//         success: boolean
//         error?: string
//     }>
//
//     // ========== 扫描操作 ==========
//
//     /**
//      * 开始扫描图像库
//      */
//     scanLibrary(libraryId: string, options?: {
//         forceRescan?: boolean // 强制重新扫描已存在的图像
//         maxDepth?: number // 最大扫描深度
//         includeSubfolders?: boolean // 是否包含子文件夹
//         fileTypes?: string[] // 支持的文件类型
//     }): Promise<{
//         success: boolean
//         scanId?: string // 扫描任务ID
//         error?: string
//     }>
//
//     /**
//      * 停止扫描
//      */
//     stopScan(libraryId: string): Promise<{
//         success: boolean
//         error?: string
//     }>
//
//     /**
//      * 暂停扫描
//      */
//     pauseScan(libraryId: string): Promise<{
//         success: boolean
//         error?: string
//     }>
//
//     /**
//      * 恢复扫描
//      */
//     resumeScan(libraryId: string): Promise<{
//         success: boolean
//         error?: string
//     }>
//
//     // ========== 扫描进度查询 ==========
//
//     /**
//      * 获取扫描进度
//      */
//     getScanProgress(libraryId: string): Promise<{
//         success: boolean
//         progress?: {
//             libraryId: string
//             scanId: string
//             status: 'pending' | 'scanning' | 'paused' | 'completed' | 'error' | 'cancelled'
//             totalFiles: number
//             processedFiles: number
//             failedFiles: number
//             skippedFiles: number
//             currentFile?: string
//             startTime: string
//             estimatedEndTime?: string
//             elapsedTime: number
//             processingSpeed: number // files per second
//             errors: string[]
//         }
//         error?: string
//     }>
//
//     // ========== 文件夹分析 ==========
//
//     /**
//      * 分析文件夹（在创建图像库前预览）
//      */
//     analyzeFolder(params: {
//         folderPath: string
//         options?: {
//             maxDepth?: number
//             includeSubfolders?: boolean
//             fileTypes?: string[]
//         }
//     }): Promise<{
//         success: boolean
//         data?: {
//             estimatedFiles: number
//             estimatedSize: string
//             deepestLevel: number
//             formatBreakdown: Record<string, number>
//             sampleImages: string[]
//             hasSubfolders: boolean
//             accessPermissions: {
//                 readable: boolean
//                 writable: boolean
//             }
//         }
//         error?: string
//     }>
// }
//
// export interface ProgressAPI {
//     getActiveProgresses(): Promise<any[]>
//     getProgress(progressId: string): Promise<any>
//     pauseProgress(progressId: string): Promise<{ success: boolean; error?: string }>
//     resumeProgress(progressId: string): Promise<{ success: boolean; error?: string }>
//     cancelProgress(progressId: string): Promise<{ success: boolean; error?: string }>
//     onProgressUpdate(callback: (progress: any) => void): void
//     removeProgressListener(callback: (progress: any) => void): void
// }
//
// export interface ServiceAPI {
//     // 获取当前服务状态
//     getStates(): Promise<{
//         success: boolean
//         data?: any
//         error?: string
//     }>
//
//     // 获取启动性能指标
//     getMetrics(): Promise<{
//         success: boolean
//         data?: any
//         error?: string
//     }>
//
//     // 重试失败的服务
//     retry(serviceName: string): Promise<{
//         success: boolean
//         error?: string
//         message?: string
//     }>
//
//     // 等待特定服务就绪
//     waitFor(serviceName: string, timeout?: number): Promise<{
//         success: boolean
//         data?: { isReady: boolean }
//         error?: string
//     }>
//
//     // 检查服务是否就绪
//     isReady(serviceName: string): Promise<{
//         success: boolean
//         data?: { isReady: boolean }
//         error?: string
//     }>
//
//     // 检查所有服务是否就绪
//     areAllReady(): Promise<{
//         success: boolean
//         data?: { allReady: boolean }
//         error?: string
//     }>
//
//     // 获取服务重试信息
//     getRetryInfo(serviceName?: string): Promise<{
//         success: boolean
//         data?: { retryInfo?: any; allRetryInfo?: any }
//         error?: string
//     }>
//
//     // 检查服务是否正在重试
//     isRetrying(serviceName: string): Promise<{
//         success: boolean
//         data?: { isRetrying: boolean }
//         error?: string
//     }>
//
//     // 获取重试状态摘要
//     getRetryStatusSummary(): Promise<{
//         success: boolean
//         data?: {
//             hasRetryingServices: boolean
//             retryingServices: string[]
//             failedServices: string[]
//             canRetryServices: string[]
//         }
//         error?: string
//     }>
//
//     // 重试失败的工具
//     retryFailedTools(): Promise<{
//         success: boolean
//         error?: string
//         message?: string
//     }>
//
//     // 监听服务状态变化事件
//     onStateChange(callback: (event: any) => void): void
//
//     // 监听所有服务就绪事件
//     onAllReady(callback: (metrics: any) => void): void
//
//     // 监听初始服务状态事件
//     onInitialStates(callback: (states: any) => void): void
//
//     // 移除事件监听器
//     removeListener(channel: string, callback?: any): void
// }
//
// export interface IpcRendererAPI {
//     on(channel: string, listener: (event: any, ...args: unknown[]) => void): void
//     off(channel: string, ...args: unknown[]): void
//     send(channel: string, ...args: unknown[]): void
//     invoke(channel: string, ...args: unknown[]): Promise<unknown>
// }