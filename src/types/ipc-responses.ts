/**
 * IPC 响应类型定义
 * 用于确保前后端返回格式一致
 */

// 基础响应类型
export interface BaseResponse {
  success: boolean;
  error?: string;
}

// 连接测试响应
export interface ConnectionTestResponse extends BaseResponse {
  connected?: boolean;
}

// 数据库初始化响应
export interface DatabaseInitResponse extends BaseResponse {
  // 可以添加其他字段
}

// AI 服务状态响应
export interface AIServiceStatusResponse extends BaseResponse {
  status?: 'connected' | 'disconnected' | 'error';
  // 可以添加其他状态字段
}

// 查询响应
export interface QueryResponse<T = any> extends BaseResponse {
  results?: T[];
  total?: number;
}

// 插入响应
export interface InsertResponse extends BaseResponse {
  insertedIds?: string[];
}

// 存在性检查响应
export interface ExistsResponse extends BaseResponse {
  exists?: boolean;
}