export interface ImageAnalysisResult {
  description: string;
  tags: string[];
  tags_flat?: string[]; // 扁平化标签数组
  confidence?: number; // 添加置信度字段
  model?: string; // 添加模型名称字段
  structured_data?: {
    theme?: {
      dominant_colors?: string[];
      scene?: string;
      mood?: string;
      time?: string;
      style?: string;
    };
    tags?: {
      objects?: string[];
      actions?: string[];
      clothing?: string[];
      relationships?: string[];
      activity_domain?: string[];
      text_overlay?: string[];
    };
    objects?: Array<{
      name: string;
      attributes: string[];
    }>;
  };
}

export interface EmbeddingResult {
  embedding: number[];
  dimensions: number;
}

export interface ProcessImageRequest {
  imageBase64: string;
  filename?: string;
}

export interface ProcessImageResult {
  analysis: ImageAnalysisResult;
  embedding: EmbeddingResult;
  error?: string;
}

export interface AIServiceStatus {
  connected: boolean;
  vlModel: string;
  embeddingModel: string;
  error?: string;
} 