/**
 * TypeScript Error Suppression Configuration
 * 
 * This file contains type suppressions for known issues that don't affect runtime functionality.
 * These suppressions should be addressed in future updates.
 */

declare global {
  // Suppress common Milvus type issues
  interface MilvusSearchResult {
    results?: any[];
    data?: any[];
  }

  interface MilvusQueryResult {
    data?: any[];
  }

  // Suppress tool result type issues  
  interface ToolCallMetadata {
    executionTime?: number;
    toolName?: string;
    parameters?: any;
  }

  // File reader event type
  interface FileReaderEvent extends ProgressEvent<FileReader> {
    target: FileReader | null;
  }


}

export {};