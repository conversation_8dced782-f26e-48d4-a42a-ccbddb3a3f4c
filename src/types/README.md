# TypeScript 类型声明目录

此目录包含项目的所有 TypeScript 类型声明文件，采用模块化组织结构。

## 目录结构

- **index.d.ts** - 类型声明主入口，引用所有其他类型文件
- **global.d.ts** - 全局类型声明
- **electron/** - Electron 相关类型
  - `index.d.ts` - Electron 类型主入口
  - `api.d.ts` - Electron IPC API 接口定义
  - `env.d.ts` - NodeJS 进程环境变量类型
- **vite/** - Vite 相关类型
  - `env.d.ts` - Vite 客户端类型引用
- **modules/** - 第三方模块和临时类型修复
  - `suppressions.d.ts` - 类型错误抑制声明

## 使用方法

### 1. 全局类型自动可用

由于 `tsconfig.json` 包含了 src 目录，所有全局类型声明会自动生效：

```typescript
// 直接使用，无需导入
const api = window.electronAPI
```

### 2. 导入特定类型

```typescript
// 从模块导入特定接口
import type { ElectronAPI, AIAPI } from '@types/electron/api'

// 或使用索引导入
import type { ElectronAPI } from '@types/electron'
```

### 3. 添加新类型

- 全局类型：添加到 `global.d.ts`
- Electron 相关：添加到 `electron/` 目录
- 第三方库类型修复：添加到 `modules/` 目录

## 最佳实践

1. **保持模块化**：每个文件专注于特定功能域
2. **避免循环依赖**：使用 `import type` 而非 `import`
3. **明确导出**：在模块文件中使用 `export` 声明
4. **文档化**：为复杂类型添加 JSDoc 注释
5. **版本控制**：记录第三方类型修复的原因和版本