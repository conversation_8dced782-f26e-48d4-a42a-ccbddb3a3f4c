// 路径验证结果接口
export interface ValidationResult {
  isValid: boolean
  sanitizedPath?: string
  errors: string[]
  warnings: string[]
}

// 图片元数据接口
export interface BackendImageMetadata {
  format: string
  size: number
  dimensions: { width: number; height: number }
  createdAt: string
  modifiedAt: string
  checksum: string
}

// 图片Blob数据接口
export interface ImageBlobData {
  buffer: number[]
  type: string
  size: number
}

// 后端服务通用结果接口
export interface BackendServiceResult<T> {
  success: boolean
  data?: T
  error?: string
}

// 路径验证请求结果
export interface PathValidationResult extends BackendServiceResult<ValidationResult> {}

// 图片元数据请求结果
export interface ImageMetadataResult extends BackendServiceResult<BackendImageMetadata> {}

// 图片Blob请求结果
export interface ImageBlobResult extends BackendServiceResult<ImageBlobData> {}