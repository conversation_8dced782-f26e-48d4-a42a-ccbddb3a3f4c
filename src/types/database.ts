// 图片库配置的接口定义

// 基于OpenAIService.ts中结构化分析结果的分类枚举
export type ImageCategory = 
  // 主题相关分类
  | 'color'           // 颜色 (dominant_colors)
  | 'scene'           // 场景 (scene)
  | 'mood'            // 氛围 (mood)
  | 'time'            // 时间/天气 (time)
  | 'style'           // 风格 (style)
  
  // 标签相关分类
  | 'object'          // 物体 (objects)
  | 'action'          // 动作 (actions)
  | 'clothing'        // 衣着 (clothing)
  | 'relationship'    // 关系 (relationships)
  | 'domain'          // 活动领域 (activity_domain)
  | 'text'            // 文字覆盖 (text_overlay)
  
  // 位置相关分类
  | 'location'        // 地点
  
  // 其他分类
  | 'other'           // 其他/未分类
  | 'general';        // 通用分类

export interface LibraryConfig {
  id: string;
  name: string;
  rootPath: string;
  type?: 'local' | 'cloud' | 'network';
  status: 'active' | 'offline' | 'removed'; // 数据库约束：只允许这3个状态
  settings: {
    recursive: boolean;
    includeHidden: boolean;
    maxDepth: number;
    supportedFormats: string[];
    enableAIAnalysis: boolean;
    autoScanOnStartup: boolean;
    scanInterval?: number; // 定期扫描间隔（分钟）
    includePatterns?: string[]; // 包含的文件模式
    excludePatterns?: string[]; // 排除的文件模式
  };
  scanProgress: {
    // 基础进度信息
    total: number;
    processed: number;
    failed: number;
    skipped: number;
    duplicates: number;

    // 扫描控制信息
    scanId?: string; // 当前扫描任务ID
    status?: 'pending' | 'scanning' | 'paused' | 'completed' | 'error' | 'cancelled'; // 详细扫描状态
    currentFile?: string; // 当前处理的文件
    startTime?: string; // 扫描开始时间
    estimatedEndTime?: string; // 预计结束时间
    elapsedTime?: number; // 已用时间（秒）
    processingSpeed?: number; // 处理速度（文件/秒）
    errors?: string[]; // 错误信息列表
    scanOptions?: {
      forceRescan?: boolean;
      maxDepth?: number;
      includeSubfolders?: boolean;
      fileTypes?: string[];
    }; // 扫描选项
  };
  statistics: {
    totalImages: number;
    totalSize: number;
    lastFullScan?: string;
    avgProcessingTime?: number;
  };
  description?: string;
  tags?: string[];
  metadata?: Record<string, any>; // 额外的配置信息
  createdAt: string;
  lastScanAt: string;
  updatedAt?: string;
}

// 图片库配置操作的参数和结果类型
export interface LibraryConfigInsertResult extends BaseResult {
  insertedIds: string[];
  insertedCount: number;
  ids: string[];
}

export interface LibraryConfigQueryParams {
  status?: 'active' | 'offline' | 'removed';
  type?: 'local' | 'cloud' | 'network';
  name?: string;
  limit?: number;
  offset?: number;
  expr?: string;
  outputFields?: string[];
}

export interface LibraryConfigQueryResult extends BaseResult {
  results: LibraryConfig[];
  total: number;
  count: number;
}

// 结构化元数据的接口定义
export interface StructuredMetadata {
  theme?: {
    dominant_colors?: string[];
    scene?: string;
    mood?: string;
    time?: string;
    style?: string;
  };
  tags?: {
    objects?: string[];
    actions?: string[];
    clothing?: string[];
    relationships?: string[];
    activity_domain?: string[];
    text_overlay?: string[];
  };
  objects?: Array<{
    name: string;
    attributes: string[];
  }>;
}

// EXIF元数据接口
export interface ExifMetadata {
  // 拍摄时间信息
  dateTimeOriginal?: string;      // 原始拍摄时间 (YYYY:MM:DD HH:MM:SS)
  dateTimeDigitized?: string;     // 数字化时间
  dateTime?: string;              // 文件修改时间

  // GPS位置信息
  gps?: {
    latitude?: number;            // 纬度
    longitude?: number;           // 经度
    altitude?: number;            // 海拔高度（米）
    latitudeRef?: 'N' | 'S';     // 纬度参考（北纬/南纬）
    longitudeRef?: 'E' | 'W';    // 经度参考（东经/西经）
    altitudeRef?: 0 | 1;         // 海拔参考（0=海平面以上，1=海平面以下）
    timestamp?: string;           // GPS时间戳
    datestamp?: string;           // GPS日期戳
    processingMethod?: string;    // GPS处理方法
    areaInformation?: string;     // 区域信息
  };

  // 相机设备信息
  camera?: {
    make?: string;                // 相机制造商
    model?: string;               // 相机型号
    software?: string;            // 软件版本
    artist?: string;              // 拍摄者
    copyright?: string;           // 版权信息
  };

  // 拍摄参数
  settings?: {
    exposureTime?: string;        // 曝光时间
    fNumber?: number;             // 光圈值
    iso?: number;                 // ISO感光度
    focalLength?: number;         // 焦距
    flash?: number;               // 闪光灯状态
    whiteBalance?: number;        // 白平衡
    exposureMode?: number;        // 曝光模式
    meteringMode?: number;        // 测光模式
    orientation?: number;         // 图像方向
  };

  // 图像技术信息
  technical?: {
    colorSpace?: number;          // 色彩空间
    pixelXDimension?: number;     // 图像宽度
    pixelYDimension?: number;     // 图像高度
    compression?: number;         // 压缩方式
    photometricInterpretation?: number; // 光度解释
    samplesPerPixel?: number;     // 每像素样本数
    bitsPerSample?: number[];     // 每样本位数
  };
}

export interface ImageMetadata {
  filename: string;
  filesize: number;
  uploadTime: string; // ISO 字符串
  dimensions: string;
  format: string;
  // 新增路径相关的元数据
  fileChecksum?: string; // 文件校验和，用于检测文件变化
  createdAt?: Date;
  modifiedAt?: Date;

  // 新增EXIF元数据
  exif?: ExifMetadata;

  // 位置信息（从EXIF提取的简化版本）
  location?: {
    latitude?: number;
    longitude?: number;
    address?: string;             // 地址信息（可通过逆地理编码获取）
    city?: string;                // 城市
    country?: string;             // 国家
  };

  // 拍摄时间（从EXIF提取的简化版本）
  capturedAt?: Date;              // 实际拍摄时间

  // 搜索相关的动态属性
  similarityScore?: number;       // 相似度分数（搜索结果中使用）
}

// 路径优化后的ImageRecord模型
export interface PathBasedImageRecord {
  id: string;
  filePath: string;           // 本地文件相对路径
  filename: string;           // 原始文件名
  fileChecksum: string;       // 文件校验和

  // 图片元数据
  format: string;
  size: number;
  dimensions: { width: number; height: number };
  createdAt: Date;
  modifiedAt: Date;

  // 新增EXIF元数据
  exif?: ExifMetadata;

  // 位置信息（从EXIF提取或用户添加）
  location?: {
    latitude?: number;
    longitude?: number;
    address?: string;           // 地址信息
    city?: string;              // 城市
    country?: string;           // 国家
    locationSource?: 'exif' | 'manual' | 'geocoding'; // 位置信息来源
  };

  // 时间信息
  capturedAt?: Date;          // 实际拍摄时间（从EXIF提取）

  // AI分析结果
  description: string;
  tags: string[];
  tags_flat?: string[];       // 扁平化标签数组，用于高效过滤
  embedding: number[];        // 描述向量

  // 结构化元数据
  structured_metadata?: StructuredMetadata;

  // 其他字段
  category?: ImageCategory;
  metadata?: Record<string, any>;

  // 搜索相关分数
  score?: number;             // 向量检索的相似度分数
  finalScore?: number;        // 混合搜索的最终综合得分
  keywordScore?: number;      // 关键词匹配得分
  vectorScore?: number;       // 向量相似度得分
}

// 保持向后兼容的ImageRecord接口
export interface ImageRecord {
  id: string;
  imagePath: string;          // 兼容旧的字段名
  description: string;
  tags: string[];
  tags_flat?: string[];       // 扁平化标签数组，用于高效过滤
  embedding: number[];
  description_vector?: number[]; // 添加缺失的属性
  structured_metadata?: StructuredMetadata; // 结构化元数据
  metadata: ImageMetadata;

  // 位置信息字段（与数据库字段一致）
  latitude?: number;
  longitude?: number;
  altitude?: number;
  locationAddress?: string;
  locationCity?: string;
  locationCountry?: string;
  locationSource?: string;

  // 时间信息字段（与数据库字段一致）
  capturedAt?: string;        // ISO字符串格式
  cameraInfo?: string;        // 相机信息
  shootingParams?: string;    // 拍摄参数

  score?: number;             // 向量检索的相似度分数
  similarity_score?: number;  // 向量搜索时的相似度分数 (别名)
  finalScore?: number;        // 混合搜索的最终综合得分
  keywordScore?: number;      // 关键词匹配得分
  vectorScore?: number;       // 向量相似度得分
}

// ==================== Standard Result Types ====================
/**
 * 基础操作结果类型
 */
export interface BaseResult {
  success: boolean;
  error?: string;
}

/**
 * 存在性检查结果类型
 */
export interface ExistsResult extends BaseResult {
  exists: boolean;
  imageId?: string;
}

/**
 * 连接测试结果类型
 */
export interface ConnectionResult extends BaseResult {
  connected?: boolean;
}

export interface InsertResult extends BaseResult {
  insertedIds: string[];
}

export interface QueryParams {
  embedding?: number[];
  tags?: string[];
  limit?: number;
  offset?: number;
  expr?: string; // 兼容 Milvus 的表达式

  // 时间范围查询
  startTime?: number;
  endTime?: number;

  // 位置查询
  location_city?: string;
  location_country?: string;
}

export interface QueryResult {
  results: ImageRecord[];
  total: number;
  error?: string;
}

// 路径优化后的查询结果接口
export interface PathBasedQueryResult {
  results: PathBasedImageRecord[];
  total: number;
  error?: string;
}

// 文件路径映射接口
export interface FilePathMapping {
  relativePath: string;
  absolutePath: string;
  exists: boolean;
  checksum?: string;
  lastModified?: Date;
}

// 路径验证结果
export interface PathValidationResult {
  isValid: boolean;
  sanitizedPath?: string;
  errors: string[];
  warnings: string[];
}