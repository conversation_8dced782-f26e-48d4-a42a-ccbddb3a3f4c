// 通用进度状态
export interface BaseProgress {
  id: string
  type: 'scan' | 'import' | 'analysis'
  status: 'pending' | 'running' | 'completed' | 'failed' | 'paused' | 'cancelled'
  startTime: Date
  endTime?: Date
  error?: string
}

// 阶段进度
export interface PhaseProgress {
  name: string
  status: 'pending' | 'running' | 'completed' | 'failed'
  progress: number  // 0-100
  message?: string
}

// 扫描进度
export interface ScanProgress extends BaseProgress {
  type: 'scan'
  libraryId: string
  libraryName: string
  phases: {
    discovery: PhaseProgress    // 文件发现阶段
    processing: PhaseProgress   // 文件处理阶段
    analysis: PhaseProgress     // AI分析阶段
  }
  currentFile?: string
  totalFiles: number
  processedFiles: number
  failedFiles: number
  skippedFiles: number
  duplicateFiles: number
  estimatedTimeRemaining?: number
}

// 导入进度
export interface ImportProgress extends BaseProgress {
  type: 'import'
  sourceFolder: string
  totalFiles: number
  processedFiles: number
  successfulFiles: number
  failedFiles: number
  currentFile?: string
  fileResults: FileProcessResult[]
}

// 文件处理结果
export interface FileProcessResult {
  filePath: string
  success: boolean
  error?: string
  processedAt: Date
}

// AI分析进度
export interface AIAnalysisProgress extends BaseProgress {
  type: 'analysis'
  totalImages: number
  analyzedImages: number
  pendingImages: number
  failedImages: number
  currentImage?: string
  queueStatus: {
    pending: number
    processing: number
    completed: number
  }
}

// 进度事件类型
export type ProgressEvent = {
  type: 'progress'
  data: BaseProgress
} | {
  type: 'phaseChange'
  data: {
    progressId: string
    phase: string
    phaseProgress: PhaseProgress
  }
} | {
  type: 'error'
  data: {
    progressId: string
    error: Error
  }
}

// 进度查询参数
export interface ProgressQueryParams {
  type?: 'scan' | 'import' | 'analysis'
  status?: 'pending' | 'running' | 'completed' | 'failed' | 'paused' | 'cancelled'
  libraryId?: string
}

// API 响应类型
export interface ProgressAPIResponse<T = any> {
  success: boolean
  data?: T
  error?: string
}

// 进度控制操作
export type ProgressControlAction = 'pause' | 'resume' | 'cancel'

// 库扫描进度（兼容性）
export interface ScanProgress2 {
  libraryId: string
  total: number
  processed: number
  failed: number
  currentFile?: string
  isScanning: boolean
}

// 进度订阅回调
export type ProgressCallback = (progress: BaseProgress) => void 