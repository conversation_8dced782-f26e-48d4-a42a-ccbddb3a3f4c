// Tool system type definitions

export interface ToolParameter {
  type: 'string' | 'number' | 'boolean' | 'array' | 'object';
  description: string;
  required?: boolean;
  enum?: string[];
  items?: ToolParameter; // For array types
  properties?: Record<string, ToolParameter>; // For object types
  minimum?: number;
  maximum?: number;
  minLength?: number;
  maxLength?: number;
}

export interface ToolParameters {
  type: 'object';
  properties: Record<string, ToolParameter>;
  required?: string[];
}

export interface ToolResult {
  success: boolean;
  data?: any;
  error?: string;
  metadata?: {
    executionTime: number;
    toolName: string;
    parameters: any;
  };
}

export interface Tool {
  name: string;
  description: string;
  parameters: ToolParameters;
  execute: (params: any) => Promise<ToolResult>;
}

export interface ToolCall {
  id: string;
  name: string;
  parameters: Record<string, any>;
}

export interface ToolCallResponse {
  toolCallId: string;
  result: ToolResult;
}

// Error types for tool execution
export type ToolErrorType = 'NOT_FOUND' | 'INVALID_PARAMS' | 'EXECUTION_ERROR' | 'SERVICE_UNAVAILABLE';

export class ToolExecutionError extends Error {
  constructor(
    public toolName: string,
    public errorType: ToolErrorType,
    message: string,
    public originalError?: Error
  ) {
    super(message);
    this.name = 'ToolExecutionError';
  }
}

// Validation result interface
export interface ValidationResult {
  valid: boolean;
  errors: string[];
}