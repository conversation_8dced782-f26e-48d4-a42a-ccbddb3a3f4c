// Enhanced search functionality types

// Search method types
export type SearchMethod = 'auto' | 'hybrid' | 'vector';

// Search method information for UI display
export interface SearchMethodInfo {
  id: SearchMethod;
  name: string;
  description: string;
  icon: string;
}

// Search metadata containing performance and method information
export interface SearchMetadata {
  method: SearchMethod; // User selected method
  actualMethod: 'hybrid' | 'vector'; // What was actually used
  fallbackOccurred: boolean; // Whether fallback from hybrid to vector occurred
  duration: number; // Search duration in milliseconds
  threshold?: number; // Similarity threshold used (for vector search)
  totalResults: number; // Total number of results found
  averageSimilarity?: number; // Average similarity score of results
  keywordMatches?: number; // Number of keyword matches (for hybrid search)
  vectorMatches?: number; // Number of vector matches (for hybrid search)
  timestamp: string; // When the search was performed
}

// Enhanced gallery query result with search metadata
export interface EnhancedGalleryQueryResult {
  images: EnhancedImageData[];
  total: number;
  loading: boolean;
  error?: string;
  metadata?: SearchMetadata;
  searchError?: SearchError; // Detailed error information for UI
}

// Enhanced image data with search-specific metadata
export interface EnhancedImageData {
  id: string | number;
  url: string;
  title: string;
  tags: string[];
  description: string;
  uploadTime: string;
  location: string;
  camera: string;
  colors: string[];
  aiAnalysis: boolean;
  similarity: number;
  fileSize: string;
  resolution: string;
  exif: {
    iso: number;
    aperture: string;
    shutterSpeed: string;
    focalLength: string;
  };
  // Search-specific metadata
  searchMetadata?: {
    matchType: 'keyword' | 'vector' | 'hybrid';
    similarity?: number;
    keywordScore?: number;
    vectorScore?: number;
    relevanceReason?: string; // Why this result was considered relevant
  };
}

// Search preferences for persistence
export interface SearchPreferences {
  method: SearchMethod;
  threshold: number;
  lastUpdated: string;
}

// Similarity threshold configuration
export interface SimilarityThresholdConfig {
  value: number;
  label: string;
  description: string;
  precision: 'high' | 'medium' | 'low';
}

// Search method configurations
export const SEARCH_METHODS: SearchMethodInfo[] = [
  {
    id: 'auto',
    name: '自动选择',
    description: '智能选择最佳搜索方法，优先使用混合搜索，必要时回退到向量搜索',
    icon: '🤖'
  },
  {
    id: 'hybrid',
    name: '智能混合搜索',
    description: '结合关键词匹配和语义理解，提供最准确的搜索结果',
    icon: '⚡'
  },
  {
    id: 'vector',
    name: '语义向量搜索',
    description: '基于AI语义理解的向量相似度搜索，可调节相似度阈值',
    icon: '🎯'
  }
];

// Predefined similarity threshold configurations
export const SIMILARITY_THRESHOLDS: SimilarityThresholdConfig[] = [
  {
    value: 0.8,
    label: '高精度',
    description: '只显示高度相关的结果',
    precision: 'high'
  },
  {
    value: 0.6,
    label: '中等精度',
    description: '平衡相关性和结果数量',
    precision: 'medium'
  },
  {
    value: 0.4,
    label: '低精度',
    description: '显示更多可能相关的结果',
    precision: 'low'
  }
];

// Helper function to get precision level from threshold
export function getPrecisionLevel(threshold: number): 'high' | 'medium' | 'low' {
  if (threshold >= 0.7) return 'high';
  if (threshold >= 0.4) return 'medium';
  return 'low';
}

// Helper function to get precision label
export function getPrecisionLabel(threshold: number): string {
  const precision = getPrecisionLevel(threshold);
  switch (precision) {
    case 'high': return '高精度';
    case 'medium': return '中等精度';
    case 'low': return '低精度';
  }
}

// Default search preferences
export const DEFAULT_SEARCH_PREFERENCES: SearchPreferences = {
  method: 'auto',
  threshold: 0.6,
  lastUpdated: new Date().toISOString()
};

// Search performance thresholds for UI feedback
export const SEARCH_PERFORMANCE_THRESHOLDS = {
  FAST: 1000, // < 1s is considered fast
  NORMAL: 3000, // 1-3s is normal
  SLOW: 5000 // > 5s is slow
};

// Search result quality indicators
export interface SearchQualityIndicators {
  hasHighSimilarity: boolean; // Any results with >80% similarity
  hasGoodCoverage: boolean; // Results span different similarity ranges
  hasRecentResults: boolean; // Results include recent images
  diversityScore: number; // 0-1, how diverse the results are
}

// Enhanced error handling types
export type SearchErrorType =
  | 'INVALID_QUERY'
  | 'HYBRID_SEARCH_FAILED'
  | 'VECTOR_SEARCH_FAILED'
  | 'EMBEDDING_GENERATION_FAILED'
  | 'DATABASE_CONNECTION_FAILED'
  | 'INVALID_THRESHOLD'
  | 'NETWORK_ERROR'
  | 'UNKNOWN_ERROR';

export interface SearchError {
  type: SearchErrorType;
  message: string;
  originalError?: Error;
  method: SearchMethod;
  suggestions: readonly string[];
  retryable: boolean;
  fallbackAvailable: boolean;
}

// Error message templates
export const ERROR_MESSAGES = {
  INVALID_QUERY: {
    title: '无效的搜索查询',
    message: '请输入有效的搜索查询。',
    suggestions: ['请检查输入的搜索查询是否正确。'],
  },
  HYBRID_SEARCH_FAILED: {
    title: '智能混合搜索失败',
    message: '混合搜索服务暂时不可用',
    suggestions: [
      '尝试切换到语义向量搜索',
      '检查网络连接',
      '稍后重试'
    ]
  },
  VECTOR_SEARCH_FAILED: {
    title: '语义向量搜索失败',
    message: '向量搜索服务暂时不可用',
    suggestions: [
      '检查AI服务连接',
      '尝试使用不同的搜索词',
      '调整相似度阈值'
    ]
  },
  EMBEDDING_GENERATION_FAILED: {
    title: '文本向量化失败',
    message: 'AI文本理解服务暂时不可用',
    suggestions: [
      '检查AI服务状态',
      '尝试使用更简单的搜索词',
      '稍后重试'
    ]
  },
  DATABASE_CONNECTION_FAILED: {
    title: '数据库连接失败',
    message: '无法连接到图片数据库',
    suggestions: [
      '检查数据库服务状态',
      '重启应用程序',
      '联系技术支持'
    ]
  },
  INVALID_THRESHOLD: {
    title: '相似度阈值无效',
    message: '相似度阈值必须在0.1到1.0之间',
    suggestions: [
      '使用推荐的阈值设置',
      '高精度：0.8以上',
      '中等精度：0.4-0.7',
      '低精度：0.1-0.4'
    ]
  },
  NETWORK_ERROR: {
    title: '网络连接错误',
    message: '网络连接不稳定或已断开',
    suggestions: [
      '检查网络连接',
      '重新连接网络',
      '稍后重试'
    ]
  },
  UNKNOWN_ERROR: {
    title: '未知错误',
    message: '发生了未知错误',
    suggestions: [
      '重试操作',
      '重启应用程序',
      '联系技术支持'
    ]
  }
} as const;

// Threshold validation configuration
export interface ThresholdValidation {
  isValid: boolean;
  warning?: string;
  suggestion?: string;
  autoCorrect?: number;
}

// Threshold guidance based on use case
export const THRESHOLD_GUIDANCE = {
  HIGH_PRECISION: {
    range: [0.8, 1.0],
    label: '高精度搜索',
    description: '只显示高度相关的结果，适合精确查找',
    warning: '可能会遗漏一些相关结果'
  },
  MEDIUM_PRECISION: {
    range: [0.4, 0.79],
    label: '平衡搜索',
    description: '平衡相关性和结果数量，推荐使用',
    warning: null
  },
  LOW_PRECISION: {
    range: [0.1, 0.39],
    label: '广泛搜索',
    description: '显示更多可能相关的结果，适合探索发现',
    warning: '可能包含不太相关的结果'
  },
  EXTREME_HIGH: {
    range: [0.95, 1.0],
    label: '极高精度',
    description: '只显示几乎完全匹配的结果',
    warning: '结果可能非常少，建议降低阈值'
  },
  EXTREME_LOW: {
    range: [0.1, 0.2],
    label: '极低精度',
    description: '显示大量可能不相关的结果',
    warning: '结果质量可能较差，建议提高阈值'
  }
} as const;

// Utility functions for error handling
export function createSearchError(
  type: SearchErrorType,
  method: SearchMethod,
  originalError?: Error,
  customMessage?: string
): SearchError {
  const errorTemplate = ERROR_MESSAGES[type];
  
  return {
    type,
    message: customMessage || errorTemplate.message,
    originalError,
    method,
    suggestions: errorTemplate.suggestions,
    retryable: type !== 'DATABASE_CONNECTION_FAILED' && type !== 'INVALID_THRESHOLD',
    fallbackAvailable: method === 'auto' || (method === 'hybrid' && type === 'HYBRID_SEARCH_FAILED')
  };
}

export function getErrorTypeFromError(error: Error, method: SearchMethod): SearchErrorType {
  const errorMessage = error.message.toLowerCase();
  
  if (errorMessage.includes('embedding') || errorMessage.includes('向量生成')) {
    return 'EMBEDDING_GENERATION_FAILED';
  }
  if (errorMessage.includes('database') || errorMessage.includes('数据库')) {
    return 'DATABASE_CONNECTION_FAILED';
  }
  if (errorMessage.includes('network') || errorMessage.includes('网络')) {
    return 'NETWORK_ERROR';
  }
  if (errorMessage.includes('threshold') || errorMessage.includes('阈值')) {
    return 'INVALID_THRESHOLD';
  }
  if (method === 'hybrid' && (errorMessage.includes('hybrid') || errorMessage.includes('混合'))) {
    return 'HYBRID_SEARCH_FAILED';
  }
  if (method === 'vector' && (errorMessage.includes('vector') || errorMessage.includes('向量'))) {
    return 'VECTOR_SEARCH_FAILED';
  }
  
  return 'UNKNOWN_ERROR';
}

// Utility functions for threshold validation
export function validateThreshold(threshold: number): ThresholdValidation {
  if (threshold < 0.1 || threshold > 1.0) {
    return {
      isValid: false,
      warning: '相似度阈值必须在0.1到1.0之间',
      suggestion: '请使用0.1到1.0之间的数值',
      autoCorrect: Math.max(0.1, Math.min(1.0, threshold))
    };
  }
  
  if (threshold >= 0.95) {
    return {
      isValid: true,
      warning: THRESHOLD_GUIDANCE.EXTREME_HIGH.warning,
      suggestion: '建议使用0.8-0.9之间的值以获得更多结果'
    };
  }
  
  if (threshold <= 0.2) {
    return {
      isValid: true,
      warning: THRESHOLD_GUIDANCE.EXTREME_LOW.warning,
      suggestion: '建议使用0.4-0.6之间的值以获得更好的结果质量'
    };
  }
  
  return { isValid: true };
}

export function getThresholdGuidance(threshold: number) {
  if (threshold >= 0.95) return THRESHOLD_GUIDANCE.EXTREME_HIGH;
  if (threshold >= 0.8) return THRESHOLD_GUIDANCE.HIGH_PRECISION;
  if (threshold >= 0.4) return THRESHOLD_GUIDANCE.MEDIUM_PRECISION;
  if (threshold >= 0.2) return THRESHOLD_GUIDANCE.LOW_PRECISION;
  return THRESHOLD_GUIDANCE.EXTREME_LOW;
}

export function getRecommendedThreshold(useCase: 'precise' | 'balanced' | 'exploratory'): number {
  switch (useCase) {
    case 'precise': return 0.8;
    case 'balanced': return 0.6;
    case 'exploratory': return 0.4;
    default: return 0.6;
  }
}