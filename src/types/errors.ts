/**
 * 图片路径错误类型定义
 */
export enum ImagePathErrorType {
  PATH_NOT_FOUND = 'PATH_NOT_FOUND',
  PATH_NOT_ALLOWED = 'PATH_NOT_ALLOWED',
  INVALID_FORMAT = 'INVALID_FORMAT',
  FILE_TOO_LARGE = 'FILE_TOO_LARGE',
  PERMISSION_DENIED = 'PERMISSION_DENIED',
  CACHE_ERROR = 'CACHE_ERROR',
  CONVERSION_ERROR = 'CONVERSION_ERROR',
  NETWORK_ERROR = 'NETWORK_ERROR',
  TIMEOUT_ERROR = 'TIMEOUT_ERROR',
  UNKNOWN_ERROR = 'UNKNOWN_ERROR'
}

/**
 * 图片路径错误类
 * 用于统一处理图片路径相关的错误
 */
export class ImagePathError extends Error {
  constructor(
    public type: ImagePathErrorType,
    message: string,
    public originalPath?: string,
    public cause?: Error
  ) {
    super(message)
    this.name = 'ImagePathError'
  }

  /**
   * 获取错误详情
   */
  getDetails(): Record<string, any> {
    return {
      type: this.type,
      message: this.message,
      originalPath: this.originalPath,
      cause: this.cause ? this.cause.message : undefined
    }
  }

  /**
   * 从普通错误创建ImagePathError
   */
  static fromError(error: Error, path?: string): ImagePathError {
    // 根据错误消息判断错误类型
    let type = ImagePathErrorType.UNKNOWN_ERROR
    const message = error.message.toLowerCase()

    if (message.includes('not found') || message.includes('不存在')) {
      type = ImagePathErrorType.PATH_NOT_FOUND
    } else if (message.includes('permission') || message.includes('权限')) {
      type = ImagePathErrorType.PERMISSION_DENIED
    } else if (message.includes('format') || message.includes('格式')) {
      type = ImagePathErrorType.INVALID_FORMAT
    } else if (message.includes('too large') || message.includes('太大')) {
      type = ImagePathErrorType.FILE_TOO_LARGE
    } else if (message.includes('cache')) {
      type = ImagePathErrorType.CACHE_ERROR
    } else if (message.includes('convert') || message.includes('转换')) {
      type = ImagePathErrorType.CONVERSION_ERROR
    } else if (message.includes('network') || message.includes('网络')) {
      type = ImagePathErrorType.NETWORK_ERROR
    } else if (message.includes('timeout') || message.includes('超时')) {
      type = ImagePathErrorType.TIMEOUT_ERROR
    }

    return new ImagePathError(type, error.message, path, error)
  }
}