#!/usr/bin/env node

/**
 * 运行简化版任务服务测试
 */

import { spawn } from 'child_process'
import path from 'path'
import { fileURLToPath } from 'url'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

console.log('🚀 运行简化版任务调度测试')
console.log('📁 测试目录: C:\\Users\\<USER>\\Pictures\\测试图片2')
console.log('')

// 运行简化测试
const testProcess = spawn('npx', [
  'vitest', 
  'run', 
  'test/services/task-service-simple.test.ts',
  '--reporter=verbose',
  '--no-coverage'
], {
  cwd: __dirname,
  stdio: 'inherit',
  shell: true
})

testProcess.on('close', (code) => {
  if (code === 0) {
    console.log('\n✅ 简化测试完成！')
  } else {
    console.log(`\n❌ 测试失败，退出码: ${code}`)
  }
  process.exit(code)
})

testProcess.on('error', (error) => {
  console.error('❌ 启动测试失败:', error)
  process.exit(1)
})