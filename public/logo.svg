<?xml version="1.0" encoding="UTF-8"?>
<svg width="1024px" height="1024px" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <!-- Generator: Sketch 63.1 (92452) - https://sketch.com -->
    <title>App Icon / Apple / Photos /@SVG</title>
    <desc>Created with Sketch.</desc>
    <defs>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-1">
            <stop stop-color="#F89B35" offset="0%"></stop>
            <stop stop-color="#FEC229" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-2">
            <stop stop-color="#FFE836" offset="0%"></stop>
            <stop stop-color="#DFDD31" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-3">
            <stop stop-color="#C7DB43" offset="0%"></stop>
            <stop stop-color="#96CC6A" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-4">
            <stop stop-color="#69B7E5" offset="0%"></stop>
            <stop stop-color="#87A1D3" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-5">
            <stop stop-color="#968EC4" offset="0%"></stop>
            <stop stop-color="#B58EC1" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-6">
            <stop stop-color="#F26568" offset="0%"></stop>
            <stop stop-color="#F37D4F" offset="100%"></stop>
        </linearGradient>
        <clipPath id="roundedRect">
            <rect x="0" y="0" width="1024" height="1024" rx="180" ry="180"/>
        </clipPath>
    </defs>
    <g id="App-Icon-/-Apple-/-Photos-/" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <!-- Apple 标准圆角矩形背景 -->
        <rect id="App-Background" fill="#FFFFFF" x="0" y="0" width="1024" height="1024" rx="180" ry="180"></rect>
        <g clip-path="url(#roundedRect)">
            <g id="The-Petals" transform="translate(67.500000, 68.000000)">
                <rect id="Orange" fill="url(#linearGradient-1)" style="mix-blend-mode: multiply;" x="307.5" y="0" width="274" height="418" rx="137"></rect>
                <rect id="Orange" fill="url(#linearGradient-2)" style="mix-blend-mode: multiply;" transform="translate(648.256, 326.75) rotate(60.000000) translate(-648.256, -326.75) " x="511.256" y="117.75" width="274" height="418" rx="137"></rect>
                <rect id="Orange" fill="url(#linearGradient-3)" style="mix-blend-mode: multiply;" transform="translate(648.256, 562.25) rotate(120.000000) translate(-648.256, -562.25) " x="511.256" y="353.25" width="274" height="418" rx="137"></rect>
                <rect id="Orange" fill="url(#linearGradient-4)" style="mix-blend-mode: multiply;" transform="translate(444.5, 680.0) rotate(180.000000) translate(-444.5, -680.0) " x="307.5" y="471" width="274" height="418" rx="137"></rect>
                <rect id="Orange" fill="url(#linearGradient-5)" style="mix-blend-mode: multiply;" transform="translate(240.744, 562.25) rotate(240.000000) translate(-240.744, -562.25) " x="103.744" y="353.25" width="274" height="418" rx="137"></rect>
                <rect id="Orange" fill="url(#linearGradient-6)" style="mix-blend-mode: multiply;" transform="translate(240.744, 326.75) rotate(300.000000) translate(-240.744, -326.75) " x="103.744" y="117.75" width="274" height="418" rx="137"></rect>
            </g>
        </g>
    </g>
</svg>