#!/usr/bin/env node

const { spawn } = require('child_process')
const path = require('path')

console.log('🚀 运行修复后的 ImageLibraryTaskService 测试')
console.log('📁 当前目录:', process.cwd())

// 设置环境变量
process.env.NODE_ENV = 'test'

console.log('\n🔍 检查文件是否存在...')
const fs = require('fs')
const testFile = 'test/services/task-service-simple.test.ts'
if (fs.existsSync(testFile)) {
  console.log(`✅ 测试文件存在: ${testFile}`)
} else {
  console.log(`❌ 测试文件不存在: ${testFile}`)
  process.exit(1)
}

// 运行测试
console.log('\n🏃 启动测试...')
const testProcess = spawn('npx', [
  'vitest', 
  'run', 
  'test/services/task-service-simple.test.ts',
  '--reporter=verbose',
  '--no-coverage'
], {
  stdio: 'inherit',
  shell: true,
  cwd: process.cwd()
})

testProcess.on('close', (code) => {
  if (code === 0) {
    console.log('\n✅ 测试运行完成')
  } else {
    console.log(`\n❌ 测试失败，退出码: ${code}`)
    
    console.log('\n🔧 如果遇到 better-sqlite3 编译问题，请尝试:')
    console.log('   1. node rebuild-sqlite.js')
    console.log('   2. npm rebuild better-sqlite3')
    console.log('   3. npm install --build-from-source better-sqlite3')
  }
  
  process.exit(code)
})

testProcess.on('error', (error) => {
  console.error('❌ 启动测试进程失败:', error.message)
  process.exit(1)
})