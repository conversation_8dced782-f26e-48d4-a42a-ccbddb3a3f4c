# ImageLibraryTaskService 测试说明

## 概述

本目录包含了 `ImageLibraryTaskService` 的完整测试套件，测试任务调度系统的各个功能。

## 测试文件

### `image-library-task-service.test.ts`

这是 `ImageLibraryTaskService` 的完整测试文件，包含以下测试用例：

1. **完整任务调度流程测试**
   - 添加图片库
   - 创建扫描任务
   - 监控任务状态
   - 验证扫描结果
   - 测试搜索功能
   - 清理测试数据

2. **任务状态查询和取消功能测试**
   - 测试任务状态查询
   - 测试任务取消功能

3. **任务错误处理和重试机制测试**
   - 测试错误处理
   - 测试重试机制

### `task-service-simple.test.ts`

这是简化版的测试文件，专注于核心流程，适合快速验证功能：

1. **核心流程测试**
   - 添加图片库
   - 创建扫描任务
   - 监控任务状态
   - 验证扫描结果
   - 测试搜索功能
   - 清理测试数据

2. **快速测试**
   - 仅扫描文件（不进行AI分析）
   - 快速验证基本功能

## 运行前准备

### 1. 环境配置

确保 `.env` 文件中配置了正确的 AI 服务地址：

```env
AI_VL_BASE_URL=http://your-ai-service-url
AI_CHAT_BASE_URL=http://your-chat-service-url
AI_VL_API_KEY=-
AI_CHAT_API_KEY=-
AI_VL_MODEL=Qwen2.5-VL-7B-Instruct-GGUF
AI_CHAT_MODEL=llama3.1:8b
AI_EMBEDDING_MODEL=nomic-embed-text-v1-GGUF
AI_EMBEDDING_BASE_URL=http://your-embedding-service-url
AI_EMBEDDING_KEY=-
```

### 2. 测试目录

确保测试目录存在且包含图片文件：
- 测试目录：`C:\Users\<USER>\Pictures\测试图片2`
- 支持的格式：`.jpg`, `.jpeg`, `.png`, `.webp`, `.gif`, `.bmp`, `.tiff`

### 3. 依赖安装

```bash
yarn install
```

## 运行测试

### 方法1：使用 vitest 直接运行

```bash
# 运行所有测试
yarn test:run

# 运行完整测试文件
npx vitest run test/services/image-library-task-service.test.ts

# 运行简化测试文件（推荐）
npx vitest run test/services/task-service-simple.test.ts

# 运行测试并显示详细输出
npx vitest run test/services/task-service-simple.test.ts --reporter=verbose
```

### 方法2：使用提供的脚本

```bash
# 运行完整测试
node run-task-test.js

# 运行简化测试（推荐）
node run-simple-test.js
```

### 方法3：使用 UI 界面

```bash
yarn test:ui
```

## 测试特点

### 真实环境测试
- 使用真实的数据库（SQLite + sqlite-vec）
- 使用真实的 AI 服务（不使用 mock）
- 使用真实的文件系统操作

### 完整流程覆盖
- 图片库管理
- 任务创建和调度
- 任务状态监控
- AI 分析和元数据提取
- 搜索功能验证
- 错误处理和重试

### 性能考虑
- 限制处理的图片数量（避免测试时间过长）
- 合理的超时设置（5分钟）
- 自动清理测试数据

## 测试输出

测试运行时会显示详细的进度信息：

```
🚀 开始任务调度流程测试
📁 步骤1：添加图片库
✅ 图片库创建成功，ID: xxx
⚡ 步骤2：创建扫描任务
✅ 扫描任务创建成功，任务ID: xxx
👀 步骤3：监控任务状态
   扫描任务状态: processing, 进度: 1/5
   任务统计: 待处理=3, 处理中=1, 已完成=1, 失败=0
✅ 所有任务处理完成
🔍 步骤4：验证扫描结果
✅ 找到 5 个图片记录
📊 AI分析结果统计:
   - 有描述的图片: 5/5
   - 有标签的图片: 5/5
   - 有元数据的图片: 5/5
```

## 故障排除

### 常见问题

1. **AI 服务连接失败**
   - 检查 `.env` 文件中的 AI 服务配置
   - 确保 AI 服务正在运行
   - 检查网络连接

2. **测试目录不存在**
   - 确保 `C:\Users\<USER>\Pictures\测试图片2` 目录存在
   - 确保目录中有图片文件

3. **数据库错误**
   - 检查是否有足够的磁盘空间
   - 确保没有其他进程占用数据库文件

4. **测试超时**
   - AI 分析可能需要较长时间
   - 可以调整测试超时时间
   - 检查 AI 服务的响应速度

### 调试技巧

1. **启用详细日志**
   ```bash
   DEBUG=* npx vitest run test/services/image-library-task-service.test.ts
   ```

2. **单独运行测试用例**
   ```bash
   npx vitest run test/services/image-library-task-service.test.ts -t "完整任务调度流程测试"
   ```

3. **查看测试数据库**
   - 测试数据库文件会在测试完成后自动清理
   - 如需保留，可以注释掉 `afterEach` 中的清理代码

## 注意事项

1. **测试时间**：由于使用真实 AI 服务，完整测试可能需要几分钟时间
2. **资源消耗**：测试会消耗 AI 服务的 API 调用次数
3. **并发限制**：避免同时运行多个测试实例
4. **数据安全**：测试会自动清理所有数据，不会影响生产环境