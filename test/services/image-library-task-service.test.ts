import { describe, it, expect, beforeEach, afterEach } from 'vitest'
import * as fs from 'node:fs'
import * as path from 'node:path'
import { createTestDatabase } from '../testDatabase'
import { LibraryService } from '@electron/services/LibraryService'
import { ImageService } from '@electron/services/ImageService'
import { AIAnalysisService } from '@electron/services/AIAnalysisService'
import { ImageLibraryTaskService, TaskType, TaskStatus } from '@electron/services/ImageLibraryTaskService'
import { SQLiteLibraryDAO } from '@electron/dao/sqlite/SQLiteLibraryDAO'
import { SqliteVecImageDAO } from '@electron/dao/sqlite-vec/SqliteVecImageDAO'
import { SqliteVecTagDAO } from '@electron/dao/sqlite-vec/SqliteVecTagDAO'
import { OpenAIService } from '@electron/ai/OpenAIService'
import Database from 'better-sqlite3'
import dotenv from 'dotenv'

// 加载环境变量
dotenv.config()

describe('ImageLibraryTaskService 调度任务测试（真实AI）', () => {
  let testDb: { dbPath: string; vectorDbPath: string; cleanup: () => void }
  const testLibraryPath = 'C:\\Users\\<USER>\\Pictures\\测试图片2' // 使用指定的测试目录
  let libraryService: LibraryService
  let imageService: ImageService
  let aiAnalysisService: AIAnalysisService
  let taskService: ImageLibraryTaskService

  beforeEach(async () => {
    // 1. 创建测试数据库
    testDb = createTestDatabase('task-service-test')
    
    // 2. 验证测试目录存在
    if (!fs.existsSync(testLibraryPath)) {
      throw new Error(`测试目录不存在: ${testLibraryPath}`)
    }
    
    console.log(`使用测试目录: ${testLibraryPath}`)
    
    // 3. 检查AI服务配置
    if (!process.env.AI_VL_BASE_URL || !process.env.AI_CHAT_BASE_URL) {
      throw new Error('需要配置 AI 服务 URL (AI_VL_BASE_URL 和 AI_CHAT_BASE_URL)')
    }
    
    // 4. 创建真实的 AI 服务实例
    const aiService = new OpenAIService({
      vlBaseURL: process.env.AI_VL_BASE_URL,
      vlApiKey: process.env.AI_VL_API_KEY || '-',
      vlModel: process.env.AI_VL_MODEL || 'Qwen2.5-VL-7B-Instruct-GGUF',
      chatBaseURL: process.env.AI_CHAT_BASE_URL,
      chatApiKey: process.env.AI_CHAT_API_KEY || '-',
      chatModel: process.env.AI_CHAT_MODEL || 'llama3.1:8b',
      embeddingModel: process.env.AI_EMBEDDING_MODEL || 'nomic-embed-text-v1-GGUF',
      embeddingBaseURL: process.env.AI_EMBEDDING_BASE_URL,
      embeddingApiKey: process.env.AI_EMBEDDING_KEY,
      dangerouslyAllowBrowser: true
    })
    
    // 5. 测试 AI 服务连接
    const aiConnected = await aiService.testConnection()
    if (!aiConnected) {
      throw new Error('AI 服务连接失败，请检查配置')
    }
    
    console.log('✅ AI 服务连接成功')
    
    // 6. 创建 DAO 实例
    const libraryDAO = new SQLiteLibraryDAO(new Database(testDb.dbPath))
    const imageDAO = new SqliteVecImageDAO(testDb.vectorDbPath)
    const tagDAO = new SqliteVecTagDAO(testDb.vectorDbPath)
    
    // 7. 创建 Service 实例
    aiAnalysisService = new AIAnalysisService(aiService)
    taskService = new ImageLibraryTaskService(imageDAO, aiAnalysisService)
    libraryService = new LibraryService(libraryDAO, taskService)
    imageService = new ImageService(imageDAO, tagDAO, aiAnalysisService)
  })

  afterEach(async () => {
    // 停止任务服务
    if (taskService) {
      taskService.stop()
    }
    
    // 延迟清理，确保所有任务都完成
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    // 清理测试数据库
    testDb.cleanup()
  })

  it('完整任务调度流程测试：添加库→创建扫描任务→监控任务状态→验证结果', async () => {
    console.log('🚀 开始任务调度流程测试')
    
    // ============= 1. 添加图片库 =============
    console.log('📁 步骤1：添加图片库')
    
    const libraryId = await libraryService.createLibrary({
      name: '任务调度测试库',
      rootPath: testLibraryPath,
      description: '用于测试任务调度功能的图片库'
    })
    
    expect(libraryId).toBeDefined()
    expect(typeof libraryId).toBe('string')
    
    // 验证图片库已创建
    const library = await libraryService.getLibrary(libraryId)
    expect(library).toBeDefined()
    expect(library!.name).toBe('任务调度测试库')
    expect(library!.rootPath).toBe(testLibraryPath)
    
    console.log(`✅ 图片库创建成功，ID: ${libraryId}`)

    // ============= 2. 创建扫描任务 =============
    console.log('⚡ 步骤2：创建扫描任务')
    
    // 使用 TaskService 创建扫描任务
    const scanTaskId = await taskService.scanLibrary(libraryId, testLibraryPath, {
      recursive: true,
      includeHidden: false,
      maxDepth: 3,
      forceRescan: false,
      enableAIAnalysis: true,
      analysisTypes: ['tags', 'description']
    })
    
    expect(scanTaskId).toBeDefined()
    expect(typeof scanTaskId).toBe('string')
    
    console.log(`✅ 扫描任务创建成功，任务ID: ${scanTaskId}`)

    // ============= 3. 监控任务状态 =============
    console.log('👀 步骤3：监控任务状态')
    
    let scanTaskCompleted = false
    let allTasksCompleted = false
    let statusCheckCount = 0
    const maxStatusChecks = 120 // 最多检查2分钟
    const statusCheckInterval = 1000 // 每秒检查一次
    
    while (!allTasksCompleted && statusCheckCount < maxStatusChecks) {
      statusCheckCount++
      
      // 检查扫描任务状态
      const scanTaskStatus = taskService.getTaskStatus(scanTaskId)
      if (scanTaskStatus) {
        console.log(`   扫描任务状态: ${scanTaskStatus.status}, 进度: ${scanTaskStatus.progress.current}/${scanTaskStatus.progress.total}`)
        
        if (scanTaskStatus.status === TaskStatus.COMPLETED && !scanTaskCompleted) {
          scanTaskCompleted = true
          console.log('✅ 扫描任务完成')
        } else if (scanTaskStatus.status === TaskStatus.FAILED) {
          throw new Error(`扫描任务失败: ${scanTaskStatus.error}`)
        }
      }
      
      // 获取库的所有任务
      const libraryTasks = taskService.getLibraryTasks(libraryId)
      const pendingTasks = libraryTasks.filter(task => task.status === TaskStatus.PENDING)
      const processingTasks = libraryTasks.filter(task => task.status === TaskStatus.PROCESSING)
      const completedTasks = libraryTasks.filter(task => task.status === TaskStatus.COMPLETED)
      const failedTasks = libraryTasks.filter(task => task.status === TaskStatus.FAILED)
      
      console.log(`   任务统计: 待处理=${pendingTasks.length}, 处理中=${processingTasks.length}, 已完成=${completedTasks.length}, 失败=${failedTasks.length}`)
      
      // 显示正在处理的任务详情
      if (processingTasks.length > 0) {
        processingTasks.forEach(task => {
          console.log(`     处理中: ${task.type} - ${task.progress.message || '处理中...'} (${task.progress.current}/${task.progress.total})`)
        })
      }
      
      // 检查是否所有任务都完成
      if (pendingTasks.length === 0 && processingTasks.length === 0) {
        allTasksCompleted = true
        console.log('✅ 所有任务处理完成')
        console.log(`📊 最终统计: 完成=${completedTasks.length}, 失败=${failedTasks.length}`)
        
        // 显示失败任务的错误信息
        if (failedTasks.length > 0) {
          console.log('❌ 失败任务详情:')
          failedTasks.forEach(task => {
            console.log(`   - ${task.type}: ${task.error}`)
          })
        }
        break
      }
      
      // 等待下次检查
      await new Promise(resolve => setTimeout(resolve, statusCheckInterval))
    }
    
    if (!allTasksCompleted) {
      throw new Error(`任务处理超时，检查了 ${statusCheckCount} 次仍未完成`)
    }

    // ============= 4. 验证扫描结果 =============
    console.log('🔍 步骤4：验证扫描结果')
    
    // 查询所有图片记录
    const allImages = await imageService.queryImages({})
    const imageRecords = allImages.results || []
    
    expect(imageRecords.length).toBeGreaterThan(0)
    console.log(`✅ 找到 ${imageRecords.length} 个图片记录`)
    
    // 验证图片记录的基本信息
    let imagesWithDescription = 0
    let imagesWithTags = 0
    let imagesWithMetadata = 0
    
    for (const image of imageRecords) {
      // 检查基本字段
      expect(image.id).toBeDefined()
      expect(image.path).toBeDefined()
      expect(image.filename).toBeDefined()
      expect(image.filesize).toBeGreaterThan(0)
      expect(image.hash).toBeDefined()
      
      // 统计AI分析结果
      if (image.description && image.description.trim().length > 0) {
        imagesWithDescription++
      }
      
      if (image.tagsFlat && image.tagsFlat.trim().length > 0) {
        imagesWithTags++
      }
      
      // 检查元数据
      if (image.width && image.height) {
        imagesWithMetadata++
      }
      
      console.log(`   图片: ${image.filename}`)
      console.log(`     路径: ${image.path}`)
      console.log(`     大小: ${image.filesize} bytes`)
      console.log(`     尺寸: ${image.width}x${image.height}`)
      console.log(`     描述: ${image.description ? image.description.substring(0, 50) + '...' : '无'}`)
      console.log(`     标签: ${image.tagsFlat ? image.tagsFlat.substring(0, 50) + '...' : '无'}`)
    }
    
    console.log(`📊 AI分析结果统计:`)
    console.log(`   - 有描述的图片: ${imagesWithDescription}/${imageRecords.length}`)
    console.log(`   - 有标签的图片: ${imagesWithTags}/${imageRecords.length}`)
    console.log(`   - 有元数据的图片: ${imagesWithMetadata}/${imageRecords.length}`)
    
    // 验证至少有一些图片被成功分析
    expect(imagesWithDescription).toBeGreaterThan(0)
    expect(imagesWithTags).toBeGreaterThan(0)
    expect(imagesWithMetadata).toBeGreaterThan(0)

    // ============= 5. 测试搜索功能 =============
    console.log('🔍 步骤5：测试搜索功能')
    
    // 智能搜索测试
    const searchResults = await imageService.smartSearch('图片')
    console.log(`   智能搜索"图片": ${searchResults.results?.length || 0} 个结果`)
    
    // 标签查询测试
    const tagQueryResults = await imageService.queryImages({
      expr: "tags_flat IS NOT NULL AND tags_flat != ''"
    })
    console.log(`   有标签的图片查询: ${tagQueryResults.results?.length || 0} 个结果`)
    
    expect(searchResults).toBeDefined()
    expect(tagQueryResults).toBeDefined()

    // ============= 6. 清理测试数据 =============
    console.log('🗑️ 步骤6：清理测试数据')
    
    // 删除所有图片记录
    let deletedCount = 0
    for (const image of imageRecords) {
      try {
        const deleted = await imageService.deleteImage(image.id)
        if (deleted) deletedCount++
      } catch (error) {
        console.warn(`   删除图片失败: ${image.id}`, error)
      }
    }
    
    console.log(`   成功删除 ${deletedCount} 个图片记录`)
    
    // 删除图片库
    const libraryDeleteResult = await libraryService.deleteLibrary(libraryId)
    expect(libraryDeleteResult.success).toBe(true)
    
    // 验证删除成功
    const deletedLibrary = await libraryService.getLibrary(libraryId)
    expect(deletedLibrary).toBeNull()
    
    const remainingImages = await imageService.queryImages({})
    expect(remainingImages.results?.length || 0).toBe(0)
    
    console.log('✅ 清理完成：图片库和所有相关数据已删除')

    // ============= 测试总结 =============
    console.log('\n🎯 任务调度测试总结:')
    console.log(`   - 图片库创建: ✅`)
    console.log(`   - 扫描任务创建: ✅`)
    console.log(`   - 任务状态监控: ✅ (检查了 ${statusCheckCount} 次)`)
    console.log(`   - 图片处理: ✅ (${imageRecords.length} 个图片)`)
    console.log(`   - AI分析: ✅ (描述=${imagesWithDescription}, 标签=${imagesWithTags})`)
    console.log(`   - 元数据提取: ✅ (${imagesWithMetadata} 个图片)`)
    console.log(`   - 搜索功能: ✅`)
    console.log(`   - 数据清理: ✅`)
    
    console.log('\n✅ 任务调度流程测试完成！')
  }, 300000) // 5分钟超时

  it('测试任务状态查询和取消功能', async () => {
    console.log('🔧 测试任务状态查询和取消功能')
    
    // 创建图片库
    const libraryId = await libraryService.createLibrary({
      name: '任务取消测试库',
      rootPath: testLibraryPath
    })
    
    // 创建扫描任务
    const taskId = await taskService.scanLibrary(libraryId, testLibraryPath, {
      enableAIAnalysis: false // 禁用AI分析，加快测试速度
    })
    
    // 检查任务状态
    const initialStatus = taskService.getTaskStatus(taskId)
    expect(initialStatus).toBeDefined()
    expect(initialStatus!.status).toBe(TaskStatus.PENDING)
    
    console.log(`   ✅ 任务状态查询正常: ${initialStatus!.status}`)
    
    // 等待任务开始处理
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    // 取消任务
    const cancelled = taskService.cancelTask(taskId)
    expect(cancelled).toBe(true)
    
    console.log('   ✅ 任务取消成功')
    
    // 检查取消后的状态
    const cancelledStatus = taskService.getTaskStatus(taskId)
    if (cancelledStatus) {
      console.log(`   任务取消后状态: ${cancelledStatus.status}`)
    }
    
    // 清理
    await libraryService.deleteLibrary(libraryId)
    
    console.log('✅ 任务状态查询和取消功能测试通过')
  }, 60000) // 1分钟超时

  it('测试任务错误处理和重试机制', async () => {
    console.log('🔧 测试任务错误处理和重试机制')
    
    // 创建图片库
    const libraryId = await libraryService.createLibrary({
      name: '错误处理测试库',
      rootPath: testLibraryPath
    })
    
    // 创建扫描任务，使用不存在的路径来触发错误
    const invalidPath = 'C:\\NonExistent\\Path\\That\\Does\\Not\\Exist'
    
    try {
      const taskId = await taskService.scanLibrary(libraryId, invalidPath)
      
      // 等待任务处理
      let attempts = 0
      const maxAttempts = 30
      
      while (attempts < maxAttempts) {
        const status = taskService.getTaskStatus(taskId)
        if (status) {
          console.log(`   任务状态: ${status.status}`)
          
          if (status.status === TaskStatus.FAILED) {
            expect(status.error).toBeDefined()
            console.log(`   ✅ 错误处理正确: ${status.error}`)
            break
          }
        }
        
        await new Promise(resolve => setTimeout(resolve, 1000))
        attempts++
      }
      
    } catch (error) {
      // 预期会有错误
      console.log(`   ✅ 预期错误被正确捕获: ${error}`)
    }
    
    // 清理
    await libraryService.deleteLibrary(libraryId)
    
    console.log('✅ 错误处理和重试机制测试通过')
  }, 60000) // 1分钟超时
})