import { describe, it, expect, beforeEach, afterEach } from 'vitest'
import * as fs from 'node:fs'
import * as path from 'node:path'
import { createTestDatabase, TestDatabase } from '../testDatabase'
import { ImageService } from '@electron/services/ImageService'
import { AIAnalysisService } from '@electron/services/AIAnalysisService'
import { ImageLibraryTaskService, TaskType, TaskStatus } from '@electron/services/ImageLibraryTaskService'
import { SQLiteLibraryDAO } from '@electron/dao/sqlite/SQLiteLibraryDAO'
import { SqliteVecImageDAO } from '@electron/dao/sqlite-vec/SqliteVecImageDAO'
import { SqliteVecTagDAO } from '@electron/dao/sqlite-vec/SqliteVecTagDAO'
import { OpenAIService } from '@electron/ai/OpenAIService'
import Database from 'better-sqlite3'
import dotenv from 'dotenv'

// 加载环境变量
dotenv.config()

/**
 * ImageLibraryTaskService 简化测试
 * 测试任务调度功能，使用真实数据库和AI服务
 */

describe('ImageLibraryTaskService - 简化测试', () => {
  let testDb: TestDatabase | null = null
  const testLibraryPath = 'C:\\Users\\<USER>\\Pictures\\测试图片2'
  let libraryDAO: SQLiteLibraryDAO | null = null
  let imageService: ImageService | null = null
  let taskService: ImageLibraryTaskService | null = null
  let aiAnalysisService: AIAnalysisService | null = null

  beforeEach(async () => {
    console.log('🔧 初始化测试环境...')
    
    try {
      // 1. 创建测试数据库
      testDb = createTestDatabase('simple-task-test')
      
      // 2. 验证测试目录
      if (!fs.existsSync(testLibraryPath)) {
        throw new Error(`测试目录不存在: ${testLibraryPath}`)
      }

      // 3. 创建 DAO 实例
      const imageDAO = new SqliteVecImageDAO(testDb.vectorDbPath)
      libraryDAO = new SQLiteLibraryDAO(testDb.dbPath)
      const tagDAO = new SqliteVecTagDAO(testDb.vectorDbPath)

      // 4. 创建 AI 服务
      const aiService = new OpenAIService()
      
      // 5. 创建业务服务
      aiAnalysisService = new AIAnalysisService(aiService)
      imageService = new ImageService(imageDAO, tagDAO, aiAnalysisService)
      
      // 6. 创建任务服务
      taskService = new ImageLibraryTaskService(imageDAO, aiAnalysisService)
      
      console.log('✅ 测试环境初始化完成')
    } catch (error) {
      console.error('❌ 初始化测试环境失败:', error)
      throw error
    }
  })

  afterEach(async () => {
    console.log('🧹 清理测试环境...')
    
    try {
      // 停止任务服务
      if (taskService) {
        await taskService.stop()
        taskService = null
      }
      
      // 清理测试数据库
      if (testDb && typeof testDb.cleanup === 'function') {
        testDb.cleanup()
        testDb = null
      }
      
      console.log('✅ 测试环境清理完成')
    } catch (error) {
      console.warn('清理测试环境时出错:', error)
    }
  })

  it('核心流程：添加库→创建扫描任务→查询状态→验证结果', async () => {
    console.log('\n🚀 开始核心流程测试')
    console.log(`📁 测试目录: ${testLibraryPath}`)
    
    // ========== 1. 添加图片库 ==========
    console.log('\n📚 步骤1: 添加图片库')
    
    const libraryId = await libraryDAO.create({
      name: '测试图片库',
      rootPath: testLibraryPath,
      type: 'local',
      status: 'active',
      description: '任务调度测试用图片库',
      settings: JSON.stringify({
        recursive: true,
        includeHidden: false,
        maxDepth: 20,
        supportedFormats: ['.jpg', '.jpeg', '.png', '.webp', '.gif', '.bmp', '.tiff']
      }),
      scanProgress: JSON.stringify({
        total: 0,
        processed: 0,
        failed: 0,
        status: 'idle'
      }),
      statistics: JSON.stringify({
        totalImages: 0,
        totalSize: 0,
        imagesByFormat: {}
      }),
      lastScanAt: ''
    })
    
    expect(libraryId).toBeDefined()
    console.log(`✅ 图片库已创建: ${libraryId}`)
    
    // ========== 2. 创建扫描任务 ==========
    console.log('\n⚡ 步骤2: 创建扫描任务')
    
    const taskId = await taskService.scanLibrary(libraryId, testLibraryPath, {
      recursive: true,
      maxDepth: 2,
      enableAIAnalysis: true,
      analysisTypes: ['tags', 'description']
    })
    
    expect(taskId).toBeDefined()
    console.log(`✅ 扫描任务已创建: ${taskId}`)
    
    // ========== 3. 监控任务状态 ==========
    console.log('\n👀 步骤3: 监控任务状态')
    
    let completed = false
    let checkCount = 0
    const maxChecks = 60 // 最多检查1分钟
    
    while (!completed && checkCount < maxChecks) {
      checkCount++
      
      // 检查主任务状态
      const mainTaskStatus = taskService.getTaskStatus(taskId)
      if (mainTaskStatus) {
        console.log(`   主任务: ${mainTaskStatus.status} (${mainTaskStatus.progress.current}/${mainTaskStatus.progress.total})`)
      }
      
      // 检查所有任务
      const allTasks = taskService.getLibraryTasks(libraryId)
      const pending = allTasks.filter(t => t.status === TaskStatus.PENDING).length
      const processing = allTasks.filter(t => t.status === TaskStatus.PROCESSING).length
      const completed_tasks = allTasks.filter(t => t.status === TaskStatus.COMPLETED).length
      const failed = allTasks.filter(t => t.status === TaskStatus.FAILED).length
      
      console.log(`   任务统计: 待处理=${pending}, 处理中=${processing}, 完成=${completed_tasks}, 失败=${failed}`)
      
      // 检查是否完成
      if (pending === 0 && processing === 0) {
        completed = true
        console.log('✅ 所有任务已完成')
        
        if (failed > 0) {
          console.log(`⚠️ 有 ${failed} 个任务失败`)
          // 显示失败任务详情
          allTasks.filter(t => t.status === TaskStatus.FAILED).forEach(task => {
            console.log(`   失败任务: ${task.type} - ${task.error}`)
          })
        }
        break
      }
      
      await new Promise(resolve => setTimeout(resolve, 2000))
    }
    
    if (!completed) {
      throw new Error(`任务超时，检查了 ${checkCount} 次仍未完成`)
    }
    
    // ========== 4. 验证扫描结果 ==========
    console.log('\n🔍 步骤4: 验证扫描结果')
    
    const images = await imageService.queryImages({})
    const imageCount = images.results?.length || 0
    
    expect(imageCount).toBeGreaterThan(0)
    console.log(`✅ 找到 ${imageCount} 个图片`)
    
    // 统计分析结果
    let withDescription = 0
    let withTags = 0
    let withMetadata = 0
    
    for (const image of images.results || []) {
      if (image.description?.trim()) withDescription++
      if (image.tagsFlat?.trim()) withTags++
      if (image.width && image.height) withMetadata++
      
      console.log(`   📷 ${image.filename}: ${image.width}x${image.height}, 描述=${!!image.description}, 标签=${!!image.tagsFlat}`)
    }
    
    console.log(`📊 分析结果: 描述=${withDescription}, 标签=${withTags}, 元数据=${withMetadata}`)
    
    // 验证基本功能
    expect(withMetadata).toBeGreaterThan(0) // 至少应该有元数据
    
    // ========== 5. 测试搜索 ==========
    console.log('\n🔍 步骤5: 测试搜索功能')
    
    const searchResult = await imageService.smartSearch('图片')
    console.log(`   智能搜索结果: ${searchResult.results?.length || 0} 个`)
    
    // ========== 6. 清理数据 ==========
    console.log('\n🗑️ 步骤6: 清理测试数据')
    
    // 删除图片
    for (const image of images.results || []) {
      await imageService.deleteImage(image.id)
    }
    
    // 删除图片库
    await libraryService.deleteLibrary(libraryId)
    
    console.log('✅ 测试数据已清理')
    
    // ========== 测试总结 ==========
    console.log('\n🎯 测试总结:')
    console.log(`   ✅ 图片库创建成功`)
    console.log(`   ✅ 扫描任务创建成功`)
    console.log(`   ✅ 任务状态监控正常 (检查 ${checkCount} 次)`)
    console.log(`   ✅ 处理了 ${imageCount} 个图片`)
    console.log(`   ✅ 元数据提取: ${withMetadata} 个`)
    console.log(`   ✅ AI描述生成: ${withDescription} 个`)
    console.log(`   ✅ 标签提取: ${withTags} 个`)
    console.log(`   ✅ 搜索功能正常`)
    console.log(`   ✅ 数据清理完成`)
    
    console.log('\n🎉 核心流程测试完成！')
    
  }, 180000) // 3分钟超时

  it('快速测试：仅扫描不分析', async () => {
    console.log('\n⚡ 快速测试：仅扫描文件')
    
    // 创建图片库
    const libraryId = await libraryService.createLibrary({
      name: '快速测试库',
      rootPath: testLibraryPath
    })
    
    // 创建扫描任务（禁用AI分析）
    const taskId = await taskService.scanLibrary(libraryId, testLibraryPath, {
      enableAIAnalysis: false // 禁用AI分析，只扫描文件
    })
    
    console.log(`扫描任务ID: ${taskId}`)
    
    // 等待完成
    let completed = false
    let attempts = 0
    
    while (!completed && attempts < 30) {
      const status = taskService.getTaskStatus(taskId)
      if (status) {
        console.log(`任务状态: ${status.status}`)
        if (status.status === TaskStatus.COMPLETED || status.status === TaskStatus.FAILED) {
          completed = true
        }
      }
      
      if (!completed) {
        await new Promise(resolve => setTimeout(resolve, 1000))
        attempts++
      }
    }
    
    // 验证结果
    const images = await imageService.queryImages({})
    console.log(`找到 ${images.results?.length || 0} 个图片`)
    
    expect(images.results?.length).toBeGreaterThan(0)
    
    // 清理
    for (const image of images.results || []) {
      await imageService.deleteImage(image.id)
    }
    await libraryService.deleteLibrary(libraryId)
    
    console.log('✅ 快速测试完成')
    
  }, 60000) // 1分钟超时

  it('应该能够查询库中的任务', async () => {
    console.log('\n🔍 开始测试：查询库中的任务')
    
    if (!taskService || !libraryService) {
      throw new Error('服务未初始化')
    }

    try {
      // 1. 创建图片库
      const libraryId = await libraryService.createLibrary({
        name: '测试图片库2',
        rootPath: testLibraryPath,
        description: '用于任务查询测试的图片库'
      })

      // 2. 创建一个快速扫描任务
      const taskId = await taskService.scanLibrary(libraryId, testLibraryPath, {
        enableAIAnalysis: false, // 快速扫描，不启用AI
        analysisTypes: []
      })
      
      console.log('📝 创建快速扫描任务:', taskId)

      // 3. 查询该库的所有任务
      const libraryTasks = taskService.getLibraryTasks(libraryId)
      expect(libraryTasks).toBeDefined()
      expect(Array.isArray(libraryTasks)).toBe(true)
      expect(libraryTasks.length).toBeGreaterThan(0)
      
      console.log(`📋 找到 ${libraryTasks.length} 个任务`)
      
      // 4. 验证任务包含我们创建的任务
      const ourTask = libraryTasks.find(task => task.id === taskId)
      expect(ourTask).toBeDefined()
      expect(ourTask?.type).toBe(TaskType.SCAN_LIBRARY)
      
      console.log('✅ 任务查询测试完成')
      
    } catch (error) {
      console.error('❌ 测试失败:', error)
      throw error
    }
  }, 30000) // 30秒超时

  it('应该能够取消任务', async () => {
    console.log('\n🛑 开始测试：取消任务')
    
    if (!taskService || !libraryService) {
      throw new Error('服务未初始化')
    }

    try {
      // 1. 创建图片库
      const libraryId = await libraryService.createLibrary({
        name: '测试图片库3',
        rootPath: testLibraryPath,
        description: '用于任务取消测试的图片库'
      })

      // 2. 创建一个扫描任务
      const taskId = await taskService.scanLibrary(libraryId, testLibraryPath, {
        enableAIAnalysis: true,
        analysisTypes: ['tags', 'description']
      })
      
      console.log('📝 创建扫描任务:', taskId)

      // 3. 等待一下让任务开始
      await new Promise(resolve => setTimeout(resolve, 1000))

      // 4. 取消任务
      console.log('🛑 取消任务...')
      const cancelled = taskService.cancelTask(taskId)
      expect(cancelled).toBe(true)
      
      // 5. 验证任务状态
      const taskStatus = taskService.getTaskStatus(taskId)
      if (taskStatus) {
        expect(['cancelled', 'failed']).toContain(taskStatus.status)
        console.log('✅ 任务取消测试完成，最终状态:', taskStatus.status)
      } else {
        console.log('✅ 任务已从队列中移除')
      }
      
    } catch (error) {
      console.error('❌ 测试失败:', error)
      throw error
    }
  }, 30000) // 30秒超时
})