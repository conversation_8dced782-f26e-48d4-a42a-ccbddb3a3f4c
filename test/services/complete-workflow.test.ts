import { describe, it, expect, beforeEach, afterEach } from 'vitest'
import * as fs from 'node:fs'
import * as path from 'node:path'
import { createTestDatabase } from '../testDatabase'
import { LibraryService } from '@electron/services/LibraryService'
import { ImageService } from '@electron/services/ImageService'
import { AIAnalysisService } from '@electron/services/AIAnalysisService'
import { TaskManager } from '@electron/services/TaskManager'
import { SQLiteLibraryDAO } from '@electron/dao/sqlite/SQLiteLibraryDAO'
import { SqliteVecImageDAO } from '@electron/dao/sqlite-vec/SqliteVecImageDAO'
import { SqliteVecTagDAO } from '@electron/dao/sqlite-vec/SqliteVecTagDAO'
import { OpenAIService } from '@electron/ai/OpenAIService'
import Database from 'better-sqlite3'
import dotenv from 'dotenv'

// 加载环境变量
dotenv.config()

describe('端到端图片库测试：完整流程（真实AI）', () => {
  let testDb: { dbPath: string; vectorDbPath: string; cleanup: () => void }
  const testLibraryPath = '/Users/<USER>/Desktop/测试用文件夹' // 使用真实测试目录
  let libraryService: LibraryService
  let imageService: ImageService
  let aiAnalysisService: AIAnalysisService
  let taskManager: TaskManager

  beforeEach(async () => {
    // 1. 创建测试数据库
    testDb = createTestDatabase('complete-workflow-real-ai')
    
    // 2. 验证测试目录存在
    if (!fs.existsSync(testLibraryPath)) {
      throw new Error(`测试目录不存在: ${testLibraryPath}`)
    }
    
    console.log(`使用真实测试目录: ${testLibraryPath}`)
    
    // 3. 检查AI服务配置
    if (!process.env.AI_VL_BASE_URL || !process.env.AI_CHAT_BASE_URL) {
      throw new Error('需要配置 AI 服务 URL (AI_VL_BASE_URL 和 AI_CHAT_BASE_URL)')
    }
    
    // 4. 创建真实的 AI 服务实例
    const aiService = new OpenAIService({
      vlBaseURL: process.env.AI_VL_BASE_URL,
      vlApiKey: process.env.AI_VL_API_KEY || '-',
      vlModel: process.env.AI_VL_MODEL || 'Qwen2.5-VL-7B-Instruct-GGUF',
      chatBaseURL: process.env.AI_CHAT_BASE_URL,
      chatApiKey: process.env.AI_CHAT_API_KEY || '-',
      chatModel: process.env.AI_CHAT_MODEL || 'llama3.1:8b',
      embeddingModel: process.env.AI_EMBEDDING_MODEL || 'nomic-embed-text-v1-GGUF',
      embeddingBaseURL: process.env.AI_EMBEDDING_BASE_URL,
      embeddingApiKey: process.env.AI_EMBEDDING_KEY,
      dangerouslyAllowBrowser: true
    })
    
    // 5. 测试 AI 服务连接
    const aiConnected = await aiService.testConnection()
    if (!aiConnected) {
      throw new Error('AI 服务连接失败，请检查配置')
    }
    
    console.log('✅ AI 服务连接成功')
    
    // 6. 创建 DAO 实例
    const libraryDAO = new SQLiteLibraryDAO(testDb.dbPath)
    const imageDAO = new SqliteVecImageDAO(testDb.vectorDbPath)
    const tagDAO = new SqliteVecTagDAO(testDb.vectorDbPath)
    
    // 7. 创建 Service 实例
    aiAnalysisService = new AIAnalysisService(aiService)
    imageService = new ImageService(imageDAO, tagDAO, aiAnalysisService)
    taskManager = new TaskManager({ intervalMs: 500 }) // 更快的处理间隔用于测试
    libraryService = new LibraryService(libraryDAO, taskManager, imageService)
  })

  afterEach(async () => {
    // 停止任务管理器
    if (taskManager) {
      taskManager.destroy()
    }

    // 延迟清理，确保所有断言都完成
    await new Promise(resolve => setTimeout(resolve, 1000))
    // 清理测试数据库
    testDb.cleanup()
  })

  it('完整流程测试：导入→扫描→AI分析→搜索→删除', async () => {
    // ============= 1. 导入图片库 =============
    console.log('📁 步骤1：导入图片库')
    
    const libraryId = await libraryService.createLibrary({
      name: '真实AI测试图片库',
      rootPath: testLibraryPath,
      description: '使用真实AI服务进行分析的测试图片库'
    })
    
    expect(libraryId).toBeDefined()
    expect(typeof libraryId).toBe('string')
    
    // 验证图片库已创建
    const library = await libraryService.getLibrary(libraryId)
    expect(library).toBeDefined()
    expect(library!.name).toBe('真实AI测试图片库')
    expect(library!.rootPath).toBe(testLibraryPath)
    
    console.log(`✅ 图片库创建成功，ID: ${libraryId}`)

    // ============= 2. 开始异步扫描图片库 =============
    console.log('🔍 步骤2：开始异步扫描图片库')

    // 启动异步扫描（限制最多3个文件用于测试）
    const scanResult = await libraryService.startScan(libraryId, {
      maxFiles: 3,
      forceRescan: true
    })

    expect(scanResult.success).toBe(true)
    console.log('✅ 扫描任务已启动')

    // ============= 3. 轮询等待扫描完成 =============
    console.log('⏳ 步骤3：等待扫描完成')

    let scanCompleted = false
    let attempts = 0
    const maxAttempts = 120 // 最多等待2分钟 (120 * 1000ms)

    while (!scanCompleted && attempts < maxAttempts) {
      await new Promise(resolve => setTimeout(resolve, 1000)) // 每秒检查一次
      attempts++

      const currentLibrary = await libraryService.getLibrary(libraryId)
      if (currentLibrary) {
        let progress: any = {}
        try {
          // 尝试解析JSON，如果失败则使用默认值
          progress = typeof currentLibrary.scanProgress === 'string'
            ? JSON.parse(currentLibrary.scanProgress || '{}')
            : currentLibrary.scanProgress || {}
        } catch (error) {
          console.warn('Failed to parse scanProgress, using default:', error)
          progress = {}
        }

        console.log(`   扫描进度: ${progress.processed || 0}/${progress.total || 0} (状态: ${progress.status || 'unknown'})`)

        if (progress.status === 'completed') {
          scanCompleted = true
          console.log('✅ 扫描完成')
        } else if (progress.status === 'error') {
          throw new Error('扫描失败')
        }
      }
    }

    if (!scanCompleted) {
      throw new Error('扫描超时')
    }

    // ============= 4. 验证扫描结果 =============
    console.log('🔍 步骤4：验证扫描结果')

    // 获取所有处理的图片
    const allImages = await imageService.queryImages({})
    const processedImages = allImages.results || []

    expect(processedImages.length).toBeGreaterThan(0)
    console.log(`✅ 成功处理 ${processedImages.length} 个图片`)

    // 收集分析结果用于后续验证
    const analysisResults: any[] = []
    for (const imageRecord of processedImages) {
      if (imageRecord.description) {
        analysisResults.push({
          file: path.basename(imageRecord.filePath),
          description: imageRecord.description,
          tags: imageRecord.tags ? JSON.parse(imageRecord.tags) : [],
          tagsFlat: imageRecord.tagsFlat || ''
        })

        console.log(`   图片: ${path.basename(imageRecord.filePath)}`)
        console.log(`      描述: ${imageRecord.description?.substring(0, 100)}...`)
        console.log(`      标签: ${imageRecord.tagsFlat?.substring(0, 50)}...`)
      }
    }

    expect(analysisResults.length).toBeGreaterThan(0)
    console.log(`✅ 成功处理 ${processedImages.length} 个图片，获得 ${analysisResults.length} 个AI分析结果`)

    // ============= 5. 使用"墨镜"来查找图片 =============
    console.log('🕶️ 步骤5：搜索"墨镜"相关图片')
    
    // 使用智能搜索查找墨镜
    const sunglassesSearchResults = await imageService.smartSearch('墨镜')
    console.log(`   智能搜索"墨镜": ${sunglassesSearchResults.results?.length || 0} 个结果`)
    
    // 使用标签查询查找包含墨镜的图片
    const tagQueryResults = await imageService.queryImages({
      expr: "tags_flat LIKE '%sunglasses%' OR tags_flat LIKE '%墨镜%' OR description LIKE '%sunglasses%' OR description LIKE '%墨镜%'"
    })
    console.log(`   标签查询"墨镜": ${tagQueryResults.results?.length || 0} 个结果`)
    
    // 测试其他搜索词
    const glassesSearchResults = await imageService.smartSearch('眼镜')
    console.log(`   智能搜索"眼镜": ${glassesSearchResults.results?.length || 0} 个结果`)
    
    // 验证搜索功能工作正常
    expect(sunglassesSearchResults).toBeDefined()
    expect(tagQueryResults).toBeDefined()
    expect(glassesSearchResults).toBeDefined()
    
    console.log('✅ 搜索功能测试完成')

    // ============= 6. 删除图片库并删除对应的图片 =============
    console.log('🗑️ 步骤6：删除图片库和相关数据')
    
    // 获取所有图片记录用于删除
    const imagesToDelete = await imageService.queryImages({})
    const imageIds = imagesToDelete.results?.map(img => img.id) || []

    console.log(`   找到 ${imageIds.length} 个要删除的图片记录`)

    // 删除所有图片记录
    let deletedCount = 0
    for (const imageId of imageIds) {
      try {
        const deleted = await imageService.deleteImage(imageId)
        if (deleted) deletedCount++
      } catch (error) {
        console.warn(`   删除图片失败: ${imageId}`, error)
      }
    }
    
    console.log(`   成功删除 ${deletedCount} 个图片记录`)
    
    // 删除图片库
    const libraryDeleteResult = await libraryService.deleteLibrary(libraryId)
    expect(libraryDeleteResult.success).toBe(true)
    
    // 验证删除成功
    const deletedLibrary = await libraryService.getLibrary(libraryId)
    expect(deletedLibrary).toBeNull()
    
    const remainingImages = await imageService.queryImages({})
    expect(remainingImages.results?.length || 0).toBe(0)
    
    console.log('✅ 清理完成：图片库和所有相关数据已删除')

    // ============= 验证完整流程 =============
    console.log('🎯 流程验证和总结')
    
    console.log('\n📊 测试结果总结:')
    console.log(`   - 图片库创建: ✅`)
    console.log(`   - 异步扫描: ✅ (${processedImages.length} 个文件)`)
    console.log(`   - AI分析: ✅ (${analysisResults.length} 个成功)`)
    console.log(`   - 搜索功能: ✅`)
    console.log(`   - 数据清理: ✅`)
    
    if (analysisResults.length > 0) {
      console.log('\n🤖 AI分析示例:')
      analysisResults.forEach((result, index) => {
        console.log(`   ${index + 1}. ${result.file}`)
        console.log(`      描述: ${result.description.substring(0, 80)}...`)
        console.log(`      标签数量: ${result.tags.length}`)
      })
    }
    
    console.log('\n✅ 端到端测试完成！')
  }, 300000) // 5分钟超时，因为使用真实AI服务

  it('测试AI分析服务状态和基本功能', async () => {
    console.log('🔧 测试AI服务基本功能')
    
    // 测试服务状态
    const status = await aiAnalysisService.getServiceStatus()
    expect(status.available).toBe(true)
    expect(status.connected).toBe(true)
    
    console.log('   ✅ AI服务状态正常')
    
    // 测试向量生成
    const testText = '这是一个测试文本'
    const embedding = await aiAnalysisService.generateEmbedding(testText)
    expect(embedding.length).toBeGreaterThan(0)
    
    console.log(`   ✅ 向量生成成功 (${embedding.length} 维)`)
    
    // 测试搜索查询解析
    const searchQuery = await aiAnalysisService.parseSearchQuery('找一些青蛙的照片')
    expect(searchQuery.keywords).toBeDefined()
    expect(searchQuery.keywords.length).toBeGreaterThan(0)
    
    console.log(`   ✅ 查询解析成功: ${searchQuery.keywords.join(', ')}`)
    
    console.log('✅ AI服务基本功能测试通过')
  }, 60000) // 1分钟超时

  it('测试图片库验证和错误处理', async () => {
    console.log('🔧 测试错误处理')
    
    // 测试无效路径
    await expect(
      libraryService.createLibrary({
        name: '无效库',
        rootPath: '/non/existent/path/that/does/not/exist'
      })
    ).rejects.toThrow()
    
    console.log('   ✅ 无效路径错误处理正确')
    
    // 测试重复名称
    const libraryId = await libraryService.createLibrary({
      name: '测试重复名称库',
      rootPath: testLibraryPath
    })
    
    await expect(
      libraryService.createLibrary({
        name: '测试重复名称库', // 相同名称
        rootPath: testLibraryPath // 使用相同的有效路径
      })
    ).rejects.toThrow('duplicate with existing library')
    
    // 清理
    await libraryService.deleteLibrary(libraryId)
    
    console.log('   ✅ 重复名称错误处理正确')
    console.log('✅ 错误处理测试通过')
  })
})