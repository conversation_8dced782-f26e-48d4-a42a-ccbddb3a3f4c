import { describe, it, expect, beforeEach, afterEach } from 'vitest'
import * as fs from 'node:fs'
import * as path from 'node:path'
import { ImageLibraryTaskService } from '@electron/services/ImageLibraryTaskService'
import { TaskType, TaskStatus } from '@electron/services/ImageLibraryTaskService'

/**
 * 模拟数据库测试
 * 避免 better-sqlite3 编译问题，使用内存模拟
 */

describe('ImageLibraryTaskService - Mock Database Tests', () => {
  let taskService: ImageLibraryTaskService | null = null
  const testLibraryPath = 'C:\\Users\\<USER>\\Pictures\\测试图片2'

  // 模拟数据库配置
  const mockDbConfig = {
    dbPath: ':memory:', // SQLite 内存数据库
    vectorDbPath: ':memory:'
  }

  beforeEach(async () => {
    console.log('🔧 初始化模拟测试环境...')
    
    try {
      // 验证测试目录
      if (!fs.existsSync(testLibraryPath)) {
        console.warn(`⚠️  测试目录不存在: ${testLibraryPath}`)
        console.log('📁 创建测试目录...')
        fs.mkdirSync(testLibraryPath, { recursive: true })
        
        // 创建一些测试图片文件（空文件用于测试）
        const testImages = ['test1.jpg', 'test2.png', 'test3.gif']
        testImages.forEach(filename => {
          const filePath = path.join(testLibraryPath, filename)
          fs.writeFileSync(filePath, 'mock image data')
        })
        console.log('✅ 测试目录和文件创建完成')
      }

      // 初始化任务服务（使用内存数据库）
      taskService = new ImageLibraryTaskService(mockDbConfig.dbPath, mockDbConfig.vectorDbPath)
      
      console.log('✅ 模拟测试环境初始化完成')
    } catch (error) {
      console.error('❌ 初始化测试环境失败:', error)
      throw error
    }
  })

  afterEach(async () => {
    console.log('🧹 清理模拟测试环境...')
    
    try {
      // 停止任务服务
      if (taskService) {
        await taskService.stop()
        taskService = null
      }
      
      console.log('✅ 模拟测试环境清理完成')
    } catch (error) {
      console.warn('⚠️  清理测试环境时出错:', error)
    }
  })

  it('应该能够创建和监控扫描任务', async () => {
    console.log('\n🚀 开始测试：创建和监控扫描任务')
    
    if (!taskService) {
      throw new Error('任务服务未初始化')
    }

    try {
      // 1. 添加图片库并创建扫描任务
      console.log('📂 添加图片库:', testLibraryPath)
      const taskId = await taskService.scanLibrary(testLibraryPath, {
        enableAI: true,
        priority: 'high'
      })
      
      expect(taskId).toBeDefined()
      expect(typeof taskId).toBe('string')
      console.log('✅ 扫描任务创建成功，任务ID:', taskId)

      // 2. 查询任务状态
      console.log('🔍 查询任务状态...')
      let taskStatus = await taskService.getTaskStatus(taskId)
      expect(taskStatus).toBeDefined()
      expect(['pending', 'running', 'completed', 'failed']).toContain(taskStatus.status)
      console.log('📊 当前任务状态:', taskStatus.status)

      // 3. 监控任务进度（最多等待30秒）
      console.log('⏱️  监控任务进度...')
      const maxWaitTime = 30000 // 30秒
      const startTime = Date.now()
      
      while (Date.now() - startTime < maxWaitTime) {
        taskStatus = await taskService.getTaskStatus(taskId)
        console.log(`📈 任务进度: ${taskStatus.status} (${taskStatus.progress || 0}%)`)
        
        if (taskStatus.status === 'completed') {
          console.log('🎉 任务完成！')
          break
        }
        
        if (taskStatus.status === 'failed') {
          console.log('❌ 任务失败:', taskStatus.error)
          break
        }
        
        // 等待1秒后再次查询
        await new Promise(resolve => setTimeout(resolve, 1000))
      }

      // 4. 验证最终状态
      const finalStatus = await taskService.getTaskStatus(taskId)
      console.log('🏁 最终任务状态:', finalStatus.status)
      
      // 任务应该完成或至少在运行中
      expect(['running', 'completed']).toContain(finalStatus.status)

      console.log('✅ 扫描任务测试完成')
      
    } catch (error) {
      console.error('❌ 测试失败:', error)
      throw error
    }
  }, 60000) // 60秒超时

  it('应该能够查询库中的任务', async () => {
    console.log('\n🔍 开始测试：查询库中的任务')
    
    if (!taskService) {
      throw new Error('任务服务未初始化')
    }

    try {
      // 创建一个扫描任务
      const taskId = await taskService.scanLibrary(testLibraryPath, {
        enableAI: false, // 快速扫描，不启用AI
        priority: 'normal'
      })
      
      console.log('📝 创建快速扫描任务:', taskId)

      // 查询该库的所有任务
      const libraryTasks = await taskService.getLibraryTasks(testLibraryPath)
      expect(libraryTasks).toBeDefined()
      expect(Array.isArray(libraryTasks)).toBe(true)
      expect(libraryTasks.length).toBeGreaterThan(0)
      
      console.log(`📋 找到 ${libraryTasks.length} 个任务`)
      
      // 验证任务包含我们创建的任务
      const ourTask = libraryTasks.find(task => task.id === taskId)
      expect(ourTask).toBeDefined()
      expect(ourTask?.libraryPath).toBe(testLibraryPath)
      
      console.log('✅ 任务查询测试完成')
      
    } catch (error) {
      console.error('❌ 测试失败:', error)
      throw error
    }
  }, 30000) // 30秒超时

  it('应该能够取消任务', async () => {
    console.log('\n🛑 开始测试：取消任务')
    
    if (!taskService) {
      throw new Error('任务服务未初始化')
    }

    try {
      // 创建一个扫描任务
      const taskId = await taskService.scanLibrary(testLibraryPath, {
        enableAI: true,
        priority: 'low'
      })
      
      console.log('📝 创建扫描任务:', taskId)

      // 等待一下让任务开始
      await new Promise(resolve => setTimeout(resolve, 1000))

      // 取消任务
      console.log('🛑 取消任务...')
      const cancelled = await taskService.cancelTask(taskId)
      expect(cancelled).toBe(true)
      
      // 验证任务状态
      const taskStatus = await taskService.getTaskStatus(taskId)
      expect(['cancelled', 'failed']).toContain(taskStatus.status)
      
      console.log('✅ 任务取消测试完成，最终状态:', taskStatus.status)
      
    } catch (error) {
      console.error('❌ 测试失败:', error)
      throw error
    }
  }, 30000) // 30秒超时
})