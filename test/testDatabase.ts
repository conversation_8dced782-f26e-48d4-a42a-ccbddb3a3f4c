import * as fs from 'node:fs'
import * as path from 'node:path'
import * as os from 'node:os'
import Database from 'better-sqlite3'
import { exec } from 'node:child_process'
import { promisify } from 'node:util'

const execAsync = promisify(exec)

/**
 * 测试数据库工厂
 * 为每个测试创建独立的临时 SQLite 数据库
 */
export interface TestDatabase {
  dbPath: string
  vectorDbPath: string
  cleanup: () => void
}

/**
 * 创建测试数据库
 */
export function createTestDatabase(testName: string): TestDatabase {
  const tempDir = os.tmpdir()
  const testId = `${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  
  // 创建临时数据库文件路径
  const dbPath = path.join(tempDir, `test_${testName}_${testId}.db`)
  const vectorDbPath = path.join(tempDir, `test_${testName}_vector_${testId}.db`)
  
  console.log(`创建测试数据库: ${dbPath}`)
  console.log(`创建向量数据库: ${vectorDbPath}`)
  
  // 创建数据库连接并初始化表结构
  initializeDatabase(dbPath, vectorDbPath)
  
  return {
    dbPath,
    vectorDbPath,
    cleanup: () => {
      // 清理临时数据库文件
      try {
        if (fs.existsSync(dbPath)) {
          fs.unlinkSync(dbPath)
          console.log(`已删除测试数据库: ${dbPath}`)
        }
        if (fs.existsSync(vectorDbPath)) {
          fs.unlinkSync(vectorDbPath)
          console.log(`已删除向量数据库: ${vectorDbPath}`)
        }
        
        // 清理可能的WAL和SHM文件
        const walFiles = [
          `${dbPath}-wal`,
          `${dbPath}-shm`,
          `${vectorDbPath}-wal`, 
          `${vectorDbPath}-shm`
        ]
        
        walFiles.forEach(walFile => {
          if (fs.existsSync(walFile)) {
            fs.unlinkSync(walFile)
          }
        })
        
      } catch (error) {
        console.warn('清理测试数据库时出错:', error)
      }
    }
  }
}

/**
 * 初始化数据库表结构
 */
function initializeDatabase(dbPath: string, vectorDbPath: string): void {
  // 1. 创建普通数据库文件
  const db = new Database(dbPath)
  
  try {
    // 开启WAL模式
    db.pragma('journal_mode = WAL')
  } finally {
    db.close()
  }
  
  // 2. 创建向量数据库文件
  const vectorDb = new Database(vectorDbPath)
  
  try {
    // 开启WAL模式
    vectorDb.pragma('journal_mode = WAL')
  } finally {
    vectorDb.close()
  }
  
  console.log('✅ 测试数据库文件创建完成')
}

/**
 * 清理所有测试数据库文件
 */
export async function cleanupAllTestDatabases(): Promise<void> {
  const tempDir = os.tmpdir()
  
  try {
    const files = fs.readdirSync(tempDir)
    const testDbFiles = files.filter(file => 
      file.startsWith('test_') && 
      (file.endsWith('.db') || file.endsWith('.db-wal') || file.endsWith('.db-shm'))
    )
    
    for (const file of testDbFiles) {
      const filePath = path.join(tempDir, file)
      try {
        fs.unlinkSync(filePath)
        console.log(`已清理测试数据库文件: ${file}`)
      } catch (error) {
        console.warn(`清理文件失败: ${file}`, error)
      }
    }
    
    console.log(`✅ 清理完成，共清理 ${testDbFiles.length} 个文件`)
  } catch (error) {
    console.warn('清理测试数据库时出错:', error)
  }
}