# PicMind - AI-Powered Image Management System

An Electron-based image management application with AI-powered search, vector similarity matching, and local SQLite database with vector extensions.

## Features

- 🖼️ **Image Library Management** - Organize and browse your image collections
- 🔍 **AI-Powered Search** - Semantic search using image descriptions and embeddings
- 🎯 **Vector Similarity** - Find visually similar images using sqlite-vec
- 📍 **Location-Based Search** - Search images by geographic location
- 🏷️ **Smart Tagging** - Automatic and manual image tagging
- ⚡ **High Performance** - Optimized SQLite database with vector search capabilities
- 🔒 **Local Storage** - All data stored locally for privacy

## Setup & Development

### Prerequisites
- Node.js (v18 or higher)
- Yarn package manager
- Python (for native module compilation)

### Installation
1. Clone the repository
2. Install dependencies:
   ```bash
   yarn install
   ```

3. Rebuild native modules for Electron:
   ```bash
   yarn electron-rebuild
   ```

### Development

#### Web Development Mode
```bash
yarn dev
```

#### Electron Development Mode
```bash
yarn electron:dev
```

#### Running Tests
```bash
# Run all tests
yarn test

# Run specific test file
yarn test test/sqlite-vec-performance.test.ts
```

### Build & Distribution
```bash
# Clean build (recommended for troubleshooting)
Remove-Item -Recurse -Force dist
Remove-Item -Recurse -Force dist-electron

# Build for production
yarn build

# Build Electron app
yarn electron:build
```

## Database Architecture

### SQLite with Vector Extensions
The application uses SQLite with the `sqlite-vec` extension for high-performance vector similarity search:

- **Primary Database**: Image metadata, tags, and structured data
- **Vector Storage**: 1024-dimensional embeddings for semantic search
- **Hybrid Search**: Combines text, tag, location, and vector similarity
- **Performance**: Optimized for concurrent operations and memory efficiency

### Database Services

#### SQLiteVecDatabaseService
- Core database operations with vector search capabilities
- Handles image insertion, querying, and vector similarity matching
- Supports hybrid search with multiple filter types

#### HybridDatabaseService
- Combines SQLiteVecDatabaseService with configuration management
- Provides unified interface for all database operations
- Manages library configurations and settings

## Performance Benchmarks

Recent performance test results:

| Operation | Performance | Details |
|-----------|-------------|---------|
| **Image Insertion** | 0.47ms per image | 100 images in 47ms |
| **Vector Search** | 1ms | Search 50 images with similarity matching |
| **Hybrid Search** | <1ms | Multi-filter search with location/tags/vectors |
| **Memory Usage** | +4MB | Efficient memory management during operations |
| **Concurrent Operations** | 1ms | 3 simultaneous vector searches |

## Troubleshooting

### Native Module Compatibility Issues

#### Problem: Node.js Version Mismatch
If you encounter errors like:
```
The module was compiled against a different Node.js version using NODE_MODULE_VERSION 127. 
This version of Node.js requires NODE_MODULE_VERSION 123.
```

#### Solution: Rebuild Native Modules

**For Electron Development:**
```bash
# Clean install with yarn
yarn install
npx electron-rebuild
```

**For Testing (Regular Node.js):**
```bash
# Rebuild for regular Node.js
npx electron-rebuild
```

**Complete Reset (if issues persist):**
```bash
# Remove build artifacts and dependencies
Remove-Item -Recurse -Force dist
Remove-Item -Recurse -Force dist-electron
Remove-Item -Recurse -Force node_modules

# Clean reinstall
yarn cache clean
yarn install
yarn electron-rebuild

# Rebuild application
yarn build
```

### Database Connection Issues

#### Problem: "Database not initialized" or "require is not defined"
These errors were resolved by:

1. **Fixed ES Module Imports**: Replaced CommonJS `require()` with ES6 imports
2. **Proper Module Loading**: Updated SQLiteVecDatabaseService to use proper import syntax
3. **Native Module Compatibility**: Ensured better-sqlite3 and sqlite-vec are compiled for correct Node.js version

#### Verification
Run the performance tests to verify database functionality:
```bash
yarn test test/sqlite-vec-performance.test.ts
```

All tests should pass with performance metrics similar to the benchmarks above.

## Technology Stack

### Frontend
- **React 18** with TypeScript
- **Vite** for fast development and building
- **Tailwind CSS** for styling
- **Lucide React** for icons

### Backend (Electron Main Process)
- **Electron** for desktop application framework
- **SQLite** with `sqlite-vec` extension for vector search
- **better-sqlite3** for high-performance database operations
- **Node.js** native modules for system integration

### AI & Vector Search
- **sqlite-vec** for vector similarity search
- **1024-dimensional embeddings** for semantic matching
- **Hybrid search** combining text, tags, location, and vectors

## Project Structure

```
├── src/                    # React frontend source
├── electron/              # Electron main process
│   ├── database/         # Database services
│   ├── services/         # Backend services
│   └── main.ts          # Electron main entry
├── test/                 # Test files
├── dist/                # Built frontend
└── dist-electron/       # Built Electron app
```

## Development Guidelines

### Code Quality
- TypeScript strict mode enabled
- ESLint with React and TypeScript rules
- Comprehensive test coverage for database operations

### Database Best Practices
- Use transactions for batch operations
- Implement proper error handling
- Optimize vector search with appropriate thresholds
- Monitor memory usage during large operations

### Testing
- Performance tests for database operations
- Memory usage monitoring
- Concurrent operation testing
- Vector search accuracy validation

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes with proper tests
4. Ensure all tests pass: `yarn test`
5. Verify Electron app works: `yarn electron:dev`
6. Submit a pull request

## License

This project is licensed under the MIT License.
