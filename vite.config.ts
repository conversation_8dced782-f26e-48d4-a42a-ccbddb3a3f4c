import { defineConfig } from 'vite'
import path from 'node:path'
import electron from 'vite-plugin-electron/simple'
import react from '@vitejs/plugin-react'
import tailwindcss from '@tailwindcss/vite'

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [
    react(),
    tailwindcss(),
    electron({
      main: {
        // Shortcut of `build.lib.entry`.
        entry: 'electron/main.ts',
        vite: {
          resolve: {
            alias: {
              "@": path.resolve(__dirname, "./src"),
              "@shared": path.resolve(__dirname, "./shared"),
              "@electron": path.resolve(__dirname, "./electron"),
              "@assets": path.resolve(__dirname, "./assets"),
            },
          },
          build: {
            target: 'node18',
            minify: false,
            sourcemap: true, // 添加源映射以便调试
            rollupOptions: {
              external: [
                'better-sqlite3',
                'sqlite-vec',
                'electron-trpc'
              ],
            },
          },
          define: {
            // 为 Electron 主进程定义 Node.js 全局变量
            global: 'globalThis',
          },
        },
      },
      preload: {
        // Shortcut of `build.rollupOptions.input`.
        // Preload scripts may contain Web assets, so use the `build.rollupOptions.input` instead `build.lib.entry`.
        input: path.join(__dirname, 'electron/preload.ts'),
        vite: {
          resolve: {
            alias: {
              "@": path.resolve(__dirname, "./src"),
              "@shared": path.resolve(__dirname, "./shared"),
              "@electron": path.resolve(__dirname, "./electron"),
              "@assets": path.resolve(__dirname, "./assets"),
            },
          },
          build: {
            target: 'node18',
            sourcemap: true, // 添加源映射以便调试
            minify: false, // 禁用压缩以便调试
            rollupOptions: {
              output: {
                format: 'es', // 使用ES模块格式
              },
              external: [
                'better-sqlite3',
                'sqlite-vec',
                'electron-trpc'
              ],
            },
          },
        },
      },
      // Ployfill the Electron and Node.js API for Renderer process.
      // If you want use Node.js in Renderer process, the `nodeIntegration` needs to be enabled in the Main process.
      // See 👉 https://github.com/electron-vite/vite-plugin-electron-renderer
      renderer: process.env.NODE_ENV === 'test'
        // https://github.com/electron-vite/vite-plugin-electron-renderer/issues/78#issuecomment-2053600808
        ? undefined
        : {},
    }),
  ],
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
      "@shared": path.resolve(__dirname, "./shared"),
      "@electron": path.resolve(__dirname, "./electron"),
      "@assets": path.resolve(__dirname, "./assets"),
    },
  },
  // 确保正确处理 Node.js 模块
  optimizeDeps: {
    exclude: [
      'better-sqlite3',
      'sqlite-vec'
    ]
  },
  // 确保开发模式下的正确处理
  define: {
    global: 'globalThis',
  },
})
