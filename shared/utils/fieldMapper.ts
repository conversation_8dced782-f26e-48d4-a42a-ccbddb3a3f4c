import humps from 'humps';

/**
 * 字段映射工具类 - 使用 humps 库进行下划线和驼峰转换
 */
export class FieldMapper {
  /**
   * 将数据库下划线字段转换为驼峰字段
   */
  static toCamelCase<T = any>(obj: any): T {
    return humps.camelizeKeys(obj) as T;
  }

  /**
   * 将驼峰字段转换为数据库下划线字段
   */
  static toSnakeCase<T = any>(obj: any): T {
    return humps.decamelizeKeys(obj) as T;
  }

  /**
   * 批量转换数组中的对象字段
   */
  static convertArray<T>(arr: any[], converter: 'toCamelCase' | 'toSnakeCase'): T[] {
    if (!Array.isArray(arr)) return arr;
    
    return arr.map(item => 
      converter === 'toCamelCase' 
        ? this.toCamelCase<T>(item)
        : this.toSnakeCase<T>(item)
    );
  }
}

/**
 * 快捷方法：数据库记录转TypeScript对象
 */
export const dbToTs = <T = any>(obj: any): T => humps.camelizeKeys(obj) as T;

/**
 * 快捷方法：TypeScript对象转数据库记录
 */
export const tsToDb = <T = any>(obj: any): T => humps.decamelizeKeys(obj) as T;

/**
 * 快捷方法：数据库记录数组转TypeScript对象数组
 */
export const dbArrayToTs = <T>(arr: any[]): T[] => 
  arr.map(item => humps.camelizeKeys(item)) as T[];

/**
 * 快捷方法：TypeScript对象数组转数据库记录数组
 */
export const tsArrayToDb = <T>(arr: any[]): T[] => 
  arr.map(item => humps.decamelizeKeys(item)) as T[];