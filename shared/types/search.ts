// 搜索方法类型
export type SearchMethod = 'auto' | 'hybrid' | 'vector'

// 搜索元数据
export interface SearchMetadata {
  method: SearchMethod
  actualMethod: 'hybrid' | 'vector'
  fallbackOccurred: boolean
  duration: number
  threshold?: number
  totalResults: number
  averageSimilarity?: number
  timestamp: string
}

// 增强的图片数据（用于搜索结果）
export interface EnhancedImageData {
  id: string
  url: string
  title: string
  tags: string[]
  description: string
  uploadTime: string
  location: string
  camera: string
  colors: string[]
  aiAnalysis: boolean
  similarity: number
  fileSize: string
  resolution: string
  exif: {
    iso: number
    aperture: string
    shutterSpeed: string
    focalLength: string
  }
  searchMetadata?: {
    matchType: 'keyword' | 'vector' | 'hybrid'
    similarity?: number
    keywordScore?: number
    vectorScore?: number
    relevanceReason?: string
  }
}

// 搜索结果
export interface SearchResult {
  success: boolean
  images: EnhancedImageData[]
  total: number
  loading: boolean
  error?: string
  metadata?: SearchMetadata
}

// 为了兼容现有组件，添加一个类型别名
export type ImageData = EnhancedImageData
