// ==================== 数据库表类型定义 ====================
// 按照实际数据库表结构组织类型定义

// ==================== Images 表 ====================
/**
 * 图片记录类型 - 对应 images 表
 * 使用驼峰命名，与数据库下划线字段映射
 */
export interface ImageRecord {
  id: string;
  libraryId?: string;         // 映射 library_id
  filePath: string;           // 映射 file_path
  fileName: string;           // 映射 file_name (文件名)
  fileHash?: string;          // 映射 file_hash
  fileChecksum?: string;      // 映射 file_checksum
  fileSize?: number;          // 映射 file_size
  fileType?: string;          // 映射 file_type
  dimensions?: string;        // 图片尺寸信息
  description?: string;        // AI 生成的描述
  tags?: string;              // JSON 格式的标签数组
  tagsFlat?: string;         // 映射 tags_flat，扁平化标签 (优化查询)
  structuredMetadata?: string; // 映射 structured_metadata (JSON)
  metadata?: string;          // 其他元数据 (JSON)
  vectorId?: string;          // 映射 vector_id
  
  // 位置信息字段
  latitude?: number;
  longitude?: number;
  altitude?: number;
  locationAddress?: string;   // 映射 location_address
  locationCity?: string;      // 映射 location_city
  locationCountry?: string;   // 映射 location_country
  locationSource?: string;    // 映射 location_source
  
  // 时间信息字段
  capturedAt?: number;       // 映射 captured_at (Unix 时间戳)
  cameraInfo?: string;       // 映射 camera_info (JSON)
  shootingParams?: string;   // 映射 shooting_params (JSON)
  
  createdAt: number;         // 映射 created_at (Unix 时间戳)
  updatedAt: number;         // 映射 updated_at (Unix 时间戳)
  
  // 向量数据 (来自 image_vectors 表的 JOIN)
  descriptionVector?: number[]; // 映射 description_vector
  
  // 搜索相关的动态属性
  similarityScore?: number;   // 相似度分数 (搜索时使用)
}

// 基于OpenAIService.ts中结构化分析结果的分类枚举
export type ImageCategory = 
  // 主题相关分类
  | 'color'           // 颜色 (dominant_colors)
  | 'scene'           // 场景 (scene)
  | 'mood'            // 氛围 (mood)
  | 'time'            // 时间/天气 (time)
  | 'style'           // 风格 (style)
  
  // 标签相关分类
  | 'object'          // 物体 (objects)
  | 'action'          // 动作 (actions)
  | 'clothing'        // 衣着 (clothing)
  | 'relationship'    // 关系 (relationships)
  | 'domain'          // 活动领域 (activity_domain)
  | 'text'            // 文字覆盖 (text_overlay)
  
  // 位置相关分类
  | 'location'        // 地点
  
  // 其他分类
  | 'other'           // 其他/未分类
  | 'general';        // 通用分类

// ==================== Tags 表 ====================
/**
 * 标签记录类型 - 对应 tags 表
 * 使用驼峰命名，与数据库下划线字段映射
 */
export interface TagRecord {
  tagId: string;              // 映射 tag_id
  tagText: string;            // 映射 tag_text
  frequency: number;           // 使用频率
  category?: ImageCategory;    // 标签分类
  createdAt: number;          // 映射 created_at (Unix 时间戳)
  updatedAt: number;          // 映射 updated_at (Unix 时间戳)
  
  // 向量数据 (来自 tag_vectors 表的 JOIN)
  tagEmbedding?: number[];    // 映射 tag_embedding
  
  // 搜索相关的动态属性
  similarityScore?: number;   // 相似度分数 (搜索时使用)
}

/**
 * 标签向量记录类型 - 对应 tag_vectors 表
 */
export interface TagVectorRecord {
  tagId: string;              // 映射 tag_id
  tagEmbedding: number[];     // 映射 tag_embedding
}

/**
 * 标签搜索参数
 */
export interface TagSearchParams {
  text?: string;
  category?: ImageCategory;
  minFrequency?: number;
  limit?: number;
  offset?: number;
}

// ==================== Library Configs 表 ====================
/**
 * 图片库配置类型 - 对应 library_configs 表
 * 使用驼峰命名，与数据库下划线字段映射
 */
export interface LibraryConfig {
  id: string;
  name: string;
  rootPath: string;           // 映射 root_path
  type: 'local' | 'cloud' | 'network';
  status: 'active' | 'offline' | 'removed';
  settings: LibrarySettings | string;            // 解析后的对象或JSON字符串
  scanProgress: LibraryScanProgress | string;    // 解析后的对象或JSON字符串 (映射 scan_progress)
  statistics: LibraryStatistics | string;        // 解析后的对象或JSON字符串
  description?: string;
  tags?: string[] | string;                       // 解析后的数组或JSON字符串
  metadata?: Record<string, any> | string;       // 解析后的对象或JSON字符串
  createdAt: string;          // 映射 created_at (ISO 字符串)
  lastScanAt: string;        // 映射 last_scan_at (ISO 字符串)
  updatedAt?: string;         // 映射 updated_at (ISO 字符串)
}

/**
 * 图片库配置设置类型 (解析 settings JSON)
 */
export interface LibrarySettings {
  recursive: boolean;
  includeHidden: boolean;
  maxDepth: number;
  supportedFormats: string[];
  enableAIAnalysis: boolean;
  autoScanOnStartup: boolean;
  scanInterval?: number;
  includePatterns?: string[];
  excludePatterns?: string[];
}

/**
 * 图片库扫描进度类型 (解析 scan_progress JSON)
 * 使用驼峰命名规范
 */
export interface LibraryScanProgress {
  total: number;
  processed: number;
  failed: number;
  skipped: number;
  duplicates: number;
  scanId?: string;
  status?: 'pending' | 'scanning' | 'paused' | 'completed' | 'error' | 'cancelled';
  currentFile?: string;
  startTime?: string;
  estimatedEndTime?: string;
  elapsedTime?: number;
  processingSpeed?: number;
  errors?: string[];
  scanOptions?: {
    forceRescan?: boolean;
    maxDepth?: number;
    includeSubfolders?: boolean;
    fileTypes?: string[];
  };
}

/**
 * 图片库统计信息类型 (解析 statistics JSON)
 * 使用驼峰命名规范
 */
export interface LibraryStatistics {
  totalImages: number;
  totalSize: number;
  lastFullScan?: string;
  avgProcessingTime?: number;
  imagesByFormat?: Record<string, number>;
  averageFileSize?: number;
  newestImageDate?: string;
  oldestImageDate?: string;
}

// ==================== 查询参数和结果类型 ====================

/**
 * 通用查询参数
 */
export interface QueryParams {
  // 基础分页
  limit?: number;
  offset?: number;
  
  // 搜索条件
  tags?: string[];
  embedding?: number[];
  expr?: string;              // 兼容 Milvus 的表达式
  
  // 时间范围查询
  startTime?: number;
  endTime?: number;
  
  // 位置查询
  locationCity?: string;
  locationCountry?: string;
}

/**
 * 通用查询结果
 */
export interface QueryResult {
  results: ImageRecord[];
  total: number;
  error?: string;
}

/**
 * 图片库配置查询参数
 */
export interface LibraryConfigQueryParams {
  status?: 'active' | 'offline' | 'removed';
  type?: 'local' | 'cloud' | 'network';
  name?: string;
  limit?: number;
  offset?: number;
  expr?: string;
  outputFields?: string[];
}

/**
 * 图片库配置查询结果
 */
export interface LibraryConfigQueryResult extends BaseResult {
  results: LibraryConfig[];
  total: number;
  count: number;
}

/**
 * 图片库配置插入结果
 */
export interface LibraryConfigInsertResult extends BaseResult {
  insertedIds: string[];
  insertedCount: number;
  ids: string[];              // 兼容字段
}

// ==================== 基础结果类型 ====================

/**
 * 基础操作结果类型
 */
export interface BaseResult {
  success: boolean;
  error?: string;
}

/**
 * 存在性检查结果类型
 */
export interface ExistsResult extends BaseResult {
  exists: boolean;
  imageId?: string;
}

/**
 * 连接测试结果类型
 */
export interface ConnectionResult extends BaseResult {
  connected?: boolean;
}

/**
 * 插入操作结果类型
 */
export interface InsertResult extends BaseResult {
  insertedIds: string[];
}

// ==================== 元数据类型 ====================

/**
 * 结构化元数据类型
 */
export interface StructuredMetadata {
  theme?: {
    dominantColors?: string[];
    scene?: string;
    mood?: string;
    time?: string;
    style?: string;
  };
  tags?: {
    objects?: string[];
    actions?: string[];
    clothing?: string[];
    relationships?: string[];
    activityDomain?: string[];
    textOverlay?: string[];
  };
  objects?: Array<{
    name: string;
    attributes: string[];
  }>;
}

/**
 * EXIF 元数据类型
 */
export interface ExifMetadata {
  // 拍摄时间信息
  dateTimeOriginal?: string;
  dateTimeDigitized?: string;
  dateTime?: string;

  // GPS位置信息
  gps?: {
    latitude?: number;
    longitude?: number;
    altitude?: number;
    latitudeRef?: 'N' | 'S';
    longitudeRef?: 'E' | 'W';
    altitudeRef?: 0 | 1;
    timestamp?: string;
    datestamp?: string;
    processingMethod?: string;
    areaInformation?: string;
  };

  // 相机设备信息
  camera?: {
    make?: string;
    model?: string;
    software?: string;
    artist?: string;
    copyright?: string;
  };

  // 拍摄参数
  settings?: {
    exposureTime?: string;
    fNumber?: number;
    iso?: number;
    focalLength?: number;
    flash?: number;
    whiteBalance?: number;
    exposureMode?: number;
    meteringMode?: number;
    orientation?: number;
  };

  // 图像技术信息
  technical?: {
    colorSpace?: number;
    pixelXDimension?: number;
    pixelYDimension?: number;
    compression?: number;
    photometricInterpretation?: number;
    samplesPerPixel?: number;
    bitsPerSample?: number[];
  };
}

/**
 * 图片元数据类型
 */
export interface ImageMetadata {
  filename: string;
  filesize: number;
  uploadTime: string;
  dimensions: string;
  format: string;
  fileChecksum?: string;
  createdAt?: Date;
  modifiedAt?: Date;
  exif?: ExifMetadata;
  location?: {
    latitude?: number;
    longitude?: number;
    address?: string;
    city?: string;
    country?: string;
  };
  capturedAt?: Date;
  similarityScore?: number;
}

// ==================== 向量搜索相关类型 ====================

/**
 * 混合搜索参数
 */
export interface HybridSearchParams {
  query: string;
  keywords: string[];
  embedding: number[];
  limit?: number;
  similarityThreshold?: number;
  expandKeywords?: boolean;
}

/**
 * 相似度选项
 */
export interface SimilarityOptions {
  threshold?: number;
  limit?: number;
}

// ==================== 兼容性类型 (用于前端) ====================

/**
 * 前端兼容的图片记录类型
 * 将数据库字段映射为更友好的前端字段名
 */
export interface FrontendImageRecord {
  id: string;
  imagePath: string;          // 映射自 file_path
  filename: string;
  description: string;
  tags: string[];            // 解析自 JSON
  tagsFlat?: string[];
  embedding: number[];       // 映射自 description_vector
  structuredMetadata?: StructuredMetadata;
  metadata: ImageMetadata;
  
  // 位置信息 (映射字段名)
  latitude?: number;
  longitude?: number;
  altitude?: number;
  locationAddress?: string;   // 映射自 location_address
  locationCity?: string;      // 映射自 location_city
  locationCountry?: string;   // 映射自 location_country
  locationSource?: string;    // 映射自 location_source
  
  // 时间信息 (格式转换)
  capturedAt?: string;        // ISO字符串格式
  cameraInfo?: string;
  shootingParams?: string;
  
  // 搜索相关分数
  score?: number;
  similarityScore?: number;
  finalScore?: number;
  keywordScore?: number;
  vectorScore?: number;
}

/**
 * 数据库原始格式的图片库配置类型 (所有JSON字段都是字符串)
 */
export interface RawLibraryConfig {
  id: string;
  name: string;
  rootPath: string;           // 映射 root_path
  type: 'local' | 'cloud' | 'network';
  status: 'active' | 'offline' | 'removed';
  settings: string;            // JSON 字符串
  scanProgress: string;       // JSON 字符串 (映射 scan_progress)
  statistics: string;          // JSON 字符串
  description?: string;
  tags?: string;               // JSON 数组字符串
  metadata?: string;           // JSON 字符串
  createdAt: string;          // 映射 created_at (ISO 字符串)
  lastScanAt: string;         // 映射 last_scan_at (ISO 字符串)
  updatedAt?: string;         // 映射 updated_at (ISO 字符串)
}

/**
 * 前端兼容的图片库配置类型 (所有JSON字段都已解析)
 */
export interface FrontendLibraryConfig {
  id: string;
  name: string;
  rootPath: string;           // 映射自 root_path
  type: 'local' | 'cloud' | 'network';
  status: 'active' | 'offline' | 'removed';
  settings: LibrarySettings;  // 解析自 JSON
  scanProgress: LibraryScanProgress; // 解析自 JSON
  statistics: LibraryStatistics; // 解析自 JSON
  description?: string;
  tags?: string[];           // 解析自 JSON
  metadata?: Record<string, any>; // 解析自 JSON
  createdAt: string;         // 映射自 created_at
  lastScanAt: string;        // 映射自 last_scan_at
  updatedAt?: string;        // 映射自 updated_at
}