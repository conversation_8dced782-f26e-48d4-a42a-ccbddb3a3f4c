{
  "compilerOptions": {
    "target": "ES2020",
    "useDefineForClassFields": true,
    "lib": ["ES2020", "DOM", "DOM.Iterable"],
    "module": "ESNext",
    "skipLibCheck": true,

    /* Bundler mode */
    "moduleResolution": "bundler",
    "allowImportingTsExtensions": true,
    "resolveJsonModule": true,
    "isolatedModules": true,
    "noEmit": true,
    "jsx": "react-jsx",

    /* Path mapping */
    "baseUrl": ".",
    "paths": {
      "@/*": ["./src/*"],
      "@shared/*": ["./shared/*"],
      "@electron/*": ["./electron/*"]
    },
    
    /* Type Roots */
    "typeRoots": ["./node_modules/@types", "./src/types"],
    "types": ["node", "react", "react-dom"],

    /* Linting */
    "strict": true,
    "noUnusedLocals": false,
    "noUnusedParameters": false,
    "noFallthroughCasesInSwitch": true
  },
  "include": ["src", "electron", "tools", "vitest.config.ts", "src/**/*.d.ts"],
  "references": [{ "path": "./tsconfig.node.json" }]
}
